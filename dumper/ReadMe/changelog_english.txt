Version 1.24.4

We addressed the following issues:
- General: If "inc" was anywhere in the path or the domain to the MySQLDumper-Root-Diretory the file runtime.php couldn't be included.
- Configuration: When saving the Perl configuration file, the selected databases to be backed up have been saved incorrectly.
- Home / directory protection: At creating the protection PHP-Warnings were shown if running PHP lower than 5.3.0.
- Perl: Some cosmetic changes in output
- Perl: Write "DROP VIEW" instead of "DROP TABLE" to backup file if table is a view.
- Perl: Added type of table to log output.

Version 1.24.3

We addressed the following issues:
- Configuration / Databases / Connection Parameter: manual adding of databases: The internal handling was completely modified. The file dbs_manual.php is no longer needed. Manually added databases are now stored in the selected configuration profile. 
- Home / directory protection: Fixed a bug that inverted the resulting message of success / failure. 
- Home / directory protection: If the automatic creation of the directory protection failed, the displayed content of the .htaccess file to be created manually contained some messy <br> html code.
- Home / directory protection: The created .htaccess file was extended with "RewriteEngine off". That fixes some problems if MySQLDumper was installed in a sub-folder of an existing website that also used .htaccess files with "mod_rewrite". 
- Home / directory protection: The functionality for editing an existing protection was revised.
- Home / Databases: The key status was determined incompletely so that only the first index of each table was considered. 
- SQL-Browser / table structure: Added the row "comment" in the display of fields and indexes. 
- SQL-Browser / table structure: The functionality for managing keys was revised. It is now possible to define any kind of keys which was limited to primary keys.
- SQL-Browser: The system table "information_schema" is now displayed and can be browsed. 
- SQL-Browser: A bug was fixed that could prevent the search from working correctly.
- SQL-Browser: For some queries the number of records was not determined correctly.
- SQL-Browser: Records containing the character "|" could not be edited in some cases.
- SQL-Browser: Error handling was improved.
- Restore: When restoring backups that were not created with MySQLDumper, CREATE DATABASE statements were not recognized correctly. 
- File-Administration / Backup Converter: Inside the backup converter there was a buggy routine that caused an error message.
- Backup / Perl: When the configuration option "autodelete" was activated and the folder work/backup contained files not created by MySQLDumper, the perl script crondump.pl issued warning messages.
- Perl: Show perl version, better error handling, nicer font with html
- Perl: Support for FTP over SSL added (NET::FTPSSL perl modul necessary).
- Perl: The optional SQL-Commands (Command Before/After) can now handle multiple Queries.

Version 1.24.2

We addressed the following issues:
- Although MySQLDumper bypasses timeouts by taking care of PHP's max_execution_time, it can happen that building the index of a big table leads to a timeout.
Now you can can configure MySQLDumper to ignore "ENABLE KEYS" statements while restoring a backup. But remember to enable them manually afterwards.
This is done under Home/Databases/Select database/ Button: Enable Keys. This option and a message will only show up, if MySQLDumper detects disabled keys or indexes.  

- When systems use the value 0 for auto_increment fields (e.g. Magento), restoring could change the value which could cause the app to malfunction.
While restoring MySQLDumper now sets the SQL-Mode to "NO_AUTO_VALUE_ON_ZERO" for the restoring session.

- The Perl script crondump.pl threw warnings when backing up views.

- When backing up via the gui the option "optimize tables" has no effect because of an incomplete query.

--------------------
Version 1.24

Changelog of the most important changes compared to version 1.22 (1.23 never left the beta status):

- MySQLDumper 1.24 still is working on PHP4 and PHP5
- new, light and friendly style. The "old" style is still included. 
- better use of RAM
- backup and restore via PHP is about 25 percent faster
- possibility to select tables when doing an backup or restore
- use different configuration profiles to manage different MySQL-Server or -user. 
  This way you can maintain different MySQL-Server with a singel MySQLDumper-Installation.
- the internal SQL-Parser has been improved (more backups from other programms can be importet)
- SQLBrowser: a lot of bugfixes and some improvements (nevertheless it must still be regarded as experimental)
- SQLBrowser: a comfortable fulltext-search lets you find text even when you don't know in what column it can occur
  After editing a record you get back to the hitlist. That really is comfortable when you need to change data.
- Tools: in version 1.22 the export of data as file didn't work. Now it is working again.
- the Web-GUI has been simplified. Some parameters have been removed. (You nearly have no chance to configure something incorrectly :) )
- FTP-Transfer: address up to 3 ftp configurations simultaneously in one backup process
- Tables of type VIEW or MEMORY are now detected and data is not saved but the structure of the table is
- the directory work/structure is no longer needed
- the automatic "structure only" backup has been removed
- better and safer handling of encodings of backup files
- better and safer error-handling
- the backup converter has been rewritten. Now it also automatically converts big files into Multipart files.
- no notices in server-logs
- when adding SQL-Queries to the SQL-Library you can now enter more than one query. If using "commadn before/after backup" 
  these queries will be executed in a row. Succes or failure is written to the log file. 
- When creating a password protection the password strength is visualized.

crodump.pl:
- when you can call crondump.pl in the standard directory "msd_cron" you no longer need to enter the 
  "$absolute_path_of_configdir" manually. An automatic detection was added. 
- better and safer catching of errors
- logging of events is much more precise and gives you clear statements what happened
- automatic deletion now regards Multipart files as one complete backup and works the way you expect it to work
- automatic deletion is done after the backup process. In case of errors this retains your old backups you might need.
- the config parameter - which configuration profile is to be used - can be set in 3 ways. The missing suffix
  ".conf.php" will be added dynamically. 
1. config=mysqldumper.conf.php
2. config=mysqdumper.conf
3. config=mysqldumper

- removed signalhandler:
When crondump.pl was started via a cronjob there was a malfunction. On some, rare server this signalhandler caused a second 
or third instance of the script that never stopped and stuck in the process list. In this case the process must be killed manually.

... and many more small or big bugfixes and cleaning up the code

When you want to know more, just take a look at the changelog of my code changes at Sourceforge. Each change
of the code is documented here:

http://mysqldumper.svn.sourceforge.net/viewvc/mysqldumper/trunk/?view=log
