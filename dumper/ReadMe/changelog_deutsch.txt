Version 1.24.4

Folgende Dinge wurden geändert:
- Allgemein: Wenn "inc" in der Domain oder im Pfad zum MySQLDUmper-Verzeichnis vorkam, konnte die Datei runtime.php nicht eingebunden werden.
- Home / Konfiguration: beim <PERSON> der Perl-Konfigurationsdatei wurden die Auswahl der zu sichernden Datenbanken nicht korrekt gespeichert.
- Home / Verzeichnisschutz: beim Anlegen des Verzeichnisschutzes wurden PHP-Warnungen ausgegeben wenn PHP kleiner 5.3.0 genutzt wird.
- Perl: Einige kosmetische Änderungen in der Ausgabe.
- Perl: Schreibt "DROP VIEW" anstelle von "DROP TABLE" in die Backupdatei wenn es sich um einen VIEW handelt.
- Perl: Ausgabe des Typs einer Tabelle zur Ausgabe hinzugefügt.

Version 1.24.3

Folgende Dinge wurden geändert:

- <PERSON><PERSON> Hinzufügen von Datenbanken: Das interne Handling wurde überarbeitet. Die Datei dbs_manual.php ist nicht mehr notwendig; manuell hinzugefügte Datenbanken werden nun im jeweiligen Konfigurationsprofil gespeichert.
- Home / Verzeichnisschutz: Beim Anlegen war die Ausgabe über Erfolg / Misserfolg vertauscht.
- Home / Verzeichnisschutz: Schlägt das Erstellen des Schutzes fehl, so enthielt die folgende Anzeige der Inhalte der Dateien unschöne <br>'s, die nicht in die Dateien gehören.
- Home / Verzeichnisschutz: Der erstellten htaccess-Datei wurde "RewriteEngine off" hinzugefügt. Das vermindert Probleme, wenn MySQLDumper in einen Unterordner bestehender Systeme installiert wird, die selbst mod_rewrite nutzen.
- Home / Verzeichnisschutz: Funktion zum Editieren eines bestehenden Schutzes wurde überarbeitet.
- Home / Datenbanken: Der Key Status wurde unvollständig ermittelt. Es wurde für jede Tabelle nur der erste Index betrachtet.
- SQLBrowser / Tabellenstruktur: Bei der Anzeige der Felder und Indizes ist die Spalte "Kommentar" hinzugekommen.
- SQLBrowser / Tabellenstruktur: Die Verwaltung für Indizes wurde überarbeitet. Es können nun beliebige Indizes festgelegt werden, was bisher auf Primärschlüssel beschränkt war.
- SQLBrowser: Die Systemtabelle "information_schema" wird nun angezeigt und kann betrachtet werden.
- SQLBrowser: Ein Fehler, welcher die korrekte Funktion der Suche verhindern konnte, wurde korrigiert.
- SQLBrowser: Bei bestimmten Anfragen wurde die Anzahl der Datensätze nicht korrekt ermittelt.
- SQLBrowser: Datensätze, welche in einem Feld das Zeichen "|" enthalten, konnten unter Umständen nicht editiert werden.
- SQLBrowser: Die Fehlerbehandlung wurde verbessert.
- Wiederherstellung: Bei der Wiederherstellung von Backups anderer Skripte wurden "CREATE DATABASE"-Anweisungen nicht korrekt erkannt.
- Verwaltung / Backup-Konverter: Im Backup-Konverter befand sich ein fehlerhafter Funktionsaufruf, der eine Fehlermeldung verursachte.
- Perl: in der Perl-Datei crondump.pl wurden Warnungen ausgegeben, wenn die Option "Autodelete" aktiviert ist und sich Backups anderer Programme im work/backup-Ordner befinden.
- Perl: Anzeige der Perl-Version, Kleinigkeiten bei der Fehlerbehandlung, Schriftart in der HTML-Ausgabe
- Perl: Unterstützung von FTP via SSL (NET::FTPSSL-Perl-Modul notwendig)
- Perl: Die optionalen SQL-Befehle (Command Before/After) können nun mit mehreren, aufeinanderfolgenden Querys konfiguriert werden

Version 1.24.2

Folgende Dinge wurden geändert:

- Trotz der inteligenten Umgehung des PHP-Timeouts, kann es während einer Wiederherstellung beim Aufbau von Indexen großer Tabellen zu einem Timeout kommen.
Jetzt kann man MySQLDumper so konfigurieren, dass während der Wiederherstellung "ENABLE KEYS"-Befehle ignoriert werden.
Nach der Wiederherstellung müssen die so deaktivierten Schlüssel/Indexe manuell wieder aktiviert werden.
Dies geschieht unter Home/Datenbanken/Datenbank wählen/ Button: Enable keys. Die Option und eine entsprechende Nachricht wird automatisch eingeblendet wenn nicht aktivierte Schlüssel/Indexe erkannt werden => nichts zu sehen = nichts zu tun.

- Bei Systemen, die den Wert 0 in auto_increment-Feldern nutzen (z.B. bei Magento), kann die Wiederherstellung den Eintrag mit dem Wert 0 verfälschen, so dass es in der Anwendung (die gesicherte Anwendung, nicht MySQLDumper) zu Fehlfunktionen kommen kann.
Nun setzt MySQLDumper für die Dauer der Wiederherstellung den SQL-Modus auf "NO_AUTO_VALUE_ON_ZERO", so dass auch ungültige Werte aus der Originalanwendung unverfälscht erhalten bleiben.

- Das Perl Skript wirft Warnungen beim Sichern von VIEWS.

- Beim Sichern über die Web-Gui und aktivierter Option "Tabellen optimieren" hat diese aufgrund eines fehlerhaften Befehls 
keine Auswirkung.

--------------------
Version 1.24

Changelog der wichtigsten Änderungen im Vergleich zur Version 1.22 (1.23 hat die Beta-Phase nie verlassen):

- MySQLDumper 1.24 funktioniert nach wie vor sowohl mit PHP4 als auch mit PHP5
- neue, helle und freundliche Optik. Der "alte" Style ist für "Nostaligiker" ebenfalls enthalten.
- bessere Speicherausnutzung
- Backup und Wiederherstellung per PHP sind im Schnitt ca. 25 Prozent schneller
- Möglichkeit beim Sichern und auch beim Wiederherstellen nur bestimmte Tabellen auszuwählen 
- Nutzen von mehreren MySQL-Servern und -Usern über Konfigurationsprofile. 
Damit kann man mehrere Server über eine MySQLDumper-Installation warten und sichern.
- der interne SQL-Parser ist an vielen Stellen verbessert worden (noch mehr Fremdbackups können importiert werden)
- SQLBrowser: jede Menge Bugfxies und kleinere Erweiterungen (dennoch ist der SQLBrowser noch als experimentell einzustufen)
- SQLBrowser: über die Lupe kommt man zu einer durchdachten Vollextsuche. Editiert man einen Datensatz, kommt man zur Trefferliste zurück.
  Das ist recht komfortabel wenn man Stellen finden muss, von denen man nicht genau weiß in welchen Spalten sie vorkommen können.
- Tools: der Export von Daten als Datei funktionierte in 1.22 nicht. Jetzt klappt das wieder.
- die Konfiguration in der WEB-GUI wurde an einigen Stellen nochmals vereinfacht und überflüssige Parameter entfernt
(Du hast kaum noch eine Chance etwas "falsch" einzustellen. :) )
- FTP-Übertragung kann nun optional auf bis zu 3 unterschiedliche Server gleichzeitig erfolgen
- Tabellen vom Typ VIEW und MEMORY werden nun automatisch erkannt und deren Daten korrekterweise nicht mitgesichert, wohl aber deren Struktur.
- das Verzeichnis work/structure wird nicht mehr benötigt
- die automatisch immer mit angelegten Struktur-Backups wurden entfernt
- noch besseres, internes Handling der Kodierung von Backups (Umlautproblematik)
- Fehler (auch beim Sichern) werden noch zuverlässig abgefangen und aussagekräftig im Log notiert
- Konverter: wurde neu geschrieben. Jetzt werden große Dateien beim Konvertieren automatisch in Multipart-Dateien aufgeteilt
- keine Notices in Server-Logs mehr
- Beim Anlegen von gespeicherten SQL-Befehlen können nun mehrere Queries angegeben werden, die bei Nutzung von "Command before/after backup" 
nacheinander ausgeführt werden. Der Erfolg oder Mißerfolg wird om Logfile notiert. 
- Beim Anlegen des Verzeichnisschutzes wird die Stärke des Kennworts visualisiert.  

crodump.pl:
- wenn die crondump.pl im Standardordner "msd_cron" aufgerufen werded kann, braucht man den "$absolute_path_of_configdir" nicht mehr editieren. 
Hier findet nun eine automatische Erkennung statt.
- besseres Abfangen von Fehlern
- es wird präziser mit aussagekräftigen Nachrichten geloggt
- automatisches Löschen betrachtet Multipart-Dateien nun korrekt als 1 vollständiges Backup und funktioniert wie erwartet
- das automatische Löschen wird nicht mehr zu Beginn ausgeführt, sondern erst nach Beenden des Sicherungsvorgangs 
  (somit bleiben im Fehlerfall alte Backups erhalten)
- der Parameter der zu nutzenden Konfigurationsdatei kann im Aufruf nun auf 3 Arten angegeben werden. 
Die fehlende Endung ".conf.php" wird bei Bedarf automatisch ergänzt:
1. config=mysqldumper.conf.php
2. config=mysqdumper.conf
3. config=mysqldumper

- Signalhandler entfernt:
Dieser sollte eigentlich einen Abbruch des Users melden und das Skript beenden. Über einen Cronjob aufgerufen führte dies bei einigen, wenigen 
Servern zu der Fehlfunktion, dass mehrere Instanzen des Skripts gestartet wurden, die lange in der Prozesliste standen und manuell beendet 
werden mussten. 

... und viele weitere kleine und große Bugfixes und jede Menge Aufräumarbeiten im Code.

Wer Du es ganz genau wissen möchtest, dann schaue Dir das changelog auf Sourceforge an. Hier ist akribisch 
jede Änderung am Code dokumentiert:

http://mysqldumper.svn.sourceforge.net/viewvc/mysqldumper/trunk/?view=log
