<?php
$lang['L_HELP_DB']="Dies ist die Liste der vorhandenen Datenbanken.";
$lang['L_HELP_PRAEFIX']="Der Präfix ist eine Zeichenfolge für den Anfang von Tabellen, der als Filter fungiert.";
$lang['L_HELP_ZIP']="Kompression mit GZip - emfohlen ist 'aktiviert'.";
$lang['L_HELP_MEMORYLIMIT']="Das ist die maximale Größe in Bytes, die das Skript an Speicher bekommt.
0 = deaktiviert";
$lang['L_MEMORY_LIMIT']="Speichergrenze";
$lang['L_HELP_AD1']="Wenn aktiviert, dann werden automatisch Backup-Dateien gelöscht.";
$lang['L_HELP_AD3']="Die maximale Anzahl von Dateien, die im Backup-Verzeichnis sein dürfen (für Autodelete).
0 = deaktiviert";
$lang['L_HELP_LANG']="Stellt auf die gewünschte Sprache.";
$lang['L_HELP_EMPTY_DB_BEFORE_RESTORE']="Um überflüssige Daten zu eliminieren, kann man anweisen, die Datenbank vor der Wiederherstellung komplett zu leeren.";
$lang['L_HELP_CRONEXTENDER']="Die Endung des Perlscriptes, Standard ist '.pl'.";
$lang['L_HELP_CRONSAVEPATH']="Der Name der Konfigurationsdatei für das Perlskript.";
$lang['L_HELP_CRONPRINTOUT']="Wenn die Textausgabe abgeschaltet ist, wird kein Text mehr ausgegeben.
Diese Funktion ist unabhängig von der Log-Ausgabe.";
$lang['L_HELP_CRONSAMEDB']="Soll die gleiche Datenbank für Cronjob wie in den Einstellungen benutzt werden?";
$lang['L_HELP_CRONDBINDEX']="Wähle die Datenbank für den Cronjob.";
$lang['L_HELP_FTPTRANSFER']="Wenn aktiviert, wird nach dem Backup die Datei per FTP gesendet.";
$lang['L_HELP_FTPSERVER']="Adresse des FTP-Servers.";
$lang['L_HELP_FTPPORT']="Port des FTP-Servers. Standard: 21.";
$lang['L_HELP_FTPUSER']="Gibt den Benutzernamen der FTP-Verbindung an.";
$lang['L_HELP_FTPPASS']="Gibt das Passwort der FTP-Verbindung an.";
$lang['L_HELP_FTPDIR']="Wohin soll die Datei gesendet werden?";
$lang['L_HELP_SPEED']="Minimale und maximale Geschwindigkeit. Standard ist 50 bis 5000.
(Zu hohe Geschwindigkeiten können zu Timeouts führen!)";
$lang['L_SPEED']="Geschwindigkeitskontrolle";
$lang['L_HELP_CRONEXECPATH']="Der Ort, an dem die Perlskripte liegen.
Ausgangspunkt ist die HTTP-Adresse (also im Browser).
Erlaubt sind absolute und relative Pfadangaben.";
$lang['L_CRON_EXECPATH']="Pfad der Perlskripte";
$lang['L_HELP_CRONCOMPLETELOG']="Wenn die Funktion aktiviert ist, wird die komplette Ausgabe im complete_log geschrieben. 
Diese Funktion ist unabhängig von der Textausgabe.";
$lang['L_HELP_FTP_MODE']="Wenn Probleme bei der FTP-Übertragung auftauchen, versuche den passiven FTP-Modus.";


?>