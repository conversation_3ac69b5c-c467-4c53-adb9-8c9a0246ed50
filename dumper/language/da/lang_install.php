<?php
$lang['L_INSTALLFINISHED']="<br>Installation gennemført  --> <a href=\"index.php\">start MySQLDumper</a><br>";
$lang['L_INSTALL_TOMENU']="Tilbage til hovedmenu";
$lang['L_INSTALLMENU']="Hovedmenu";
$lang['L_STEP']="Trin";
$lang['L_INSTALL']="Installation";
$lang['L_UNINSTALL']="Afinstallation";
$lang['L_TOOLS']="Funktioner";
$lang['L_EDITCONF']="Ret konfiguration";
$lang['L_OSWEITER']="Fortsæt uden at gemme";
$lang['L_ERRORMAN']="<strong>Fejl ved lagring af konfiguration!</strong><br>Redigér venligst filen ";
$lang['L_MANUELL']="manuelt";
$lang['L_CREATEDIRS']="Opret foldere";
$lang['L_INSTALL_CONTINUE']="Fortsæt med installation";
$lang['L_CONNECTTOMYSQL']="Forbind til MySQL ";
$lang['L_DBPARAMETER']="Databaseparametre";
$lang['L_CONFIGNOTWRITABLE']="Kan ikke skrive til fil \"config.php\".
Brug venligst dit FTP-program og giv denne fil passende rettigheder, f.eks. CHMOD 0777.";
$lang['L_DBCONNECTION']="Databaseforbindelse";
$lang['L_CONNECTIONERROR']="Fejl: kan ikke forbinde.";
$lang['L_CONNECTION_OK']="Databaseforbindelse etableret.";
$lang['L_SAVEANDCONTINUE']="Gem og fortsæt installation";
$lang['L_CONFBASIC']="Basisparametre";
$lang['L_INSTALL_STEP2FINISHED']="Databaseparametre blev gemt.";
$lang['L_INSTALL_STEP2_1']="Fortsæt installation med standard-indstillingerne";
$lang['L_LASTSTEP']="Installation afsluttet";
$lang['L_FTPMODE']="Opret nødvendige foldere i safe-mode";
$lang['L_IDOMANUAL']="Jeg opretter selv folderne";
$lang['L_DOFROM']="startende fra";
$lang['L_FTPMODE2']="Opret folderne med FTP:";
$lang['L_CONNECT']="forbind";
$lang['L_DIRS_CREATED']="Folderne er blevet oprettet og har fået tildelt korrekte tilladelser.";
$lang['L_CONNECT_TO']="forbind til";
$lang['L_CHANGEDIR']="skift til folder";
$lang['L_CHANGEDIRERROR']="skift til folder var ikke muligt";
$lang['L_FTP_OK']="FTP-parameter er ok";
$lang['L_CREATEDIRS2']="Opret foldere";
$lang['L_FTP_NOTCONNECTED']="FTP-forbindelse ikke etableret!";
$lang['L_CONNWITH']="Forbindelse med";
$lang['L_ASUSER']="som bruger";
$lang['L_NOTPOSSIBLE']="ikke muligt";
$lang['L_DIRCR1']="opret arbejdsfolder";
$lang['L_DIRCR2']="opret backupdir";
$lang['L_DIRCR4']="opret logdir";
$lang['L_DIRCR5']="opret configurationdir";
$lang['L_INDIR']="nu i dir (folder)";
$lang['L_CHECK_DIRS']="Check mine foldere";
$lang['L_DISABLEDFUNCTIONS']="Deaktiverede Funktioner";
$lang['L_NOFTPPOSSIBLE']="Du har ikke adgang til FTP-funktioner!";
$lang['L_NOGZPOSSIBLE']="Du har ikke adgang til komprimerings-funktioner!";
$lang['L_UI1']="Alle arbejdsfoldere, hvilke kan indeholde backups, vil blive slettet.";
$lang['L_UI2']="Er du sikker på at du vil gøre dette?";
$lang['L_UI3']="nej, afbryd øjeblikkeligt";
$lang['L_UI4']="ja, fortsæt venligst";
$lang['L_UI5']="sletter arbejdsfoldere";
$lang['L_UI6']="alle blev korrekt slettet.";
$lang['L_UI7']="Slet venligst script folderen";
$lang['L_UI8']="et niveau op";
$lang['L_UI9']="Der opstod en fejl, sletning var ikke muligt</p>Fejl med folder ";
$lang['L_IMPORT']="Import Konfiguration";
$lang['L_IMPORT3']="Konfiguration blev indlæst ...";
$lang['L_IMPORT4']="Konfiguration blev gemt.";
$lang['L_IMPORT5']="Start MySQLDumper";
$lang['L_IMPORT6']="Installationsmenu";
$lang['L_IMPORT7']="Upload konfiguration";
$lang['L_IMPORT8']="tilbage til upload";
$lang['L_IMPORT9']="Dette er ikke en konfigurationsbackup!";
$lang['L_IMPORT10']="Konfiguration korrekt uploadet ...";
$lang['L_IMPORT11']="<strong>Fejl: </strong>Der var problemer med at skrive til sql_statements";
$lang['L_IMPORT12']="<strong>Fejl: </strong>Der var problemer med at skrive til config.php";
$lang['L_INSTALL_HELP_PORT']="(tom = Standardport)";
$lang['L_INSTALL_HELP_SOCKET']="(tom = Standard Socket)";
$lang['L_TRYAGAIN']="Prøv igen";
$lang['L_SOCKET']="Socket";
$lang['L_PORT']="Port";
$lang['L_FOUND_DB']="fundet db:";
$lang['L_FM_FILEUPLOAD']="Upload fil";
$lang['L_PASS']="Kodeord";
$lang['L_NO_DB_FOUND_INFO']="Forbindelsen til databasen blev korrekt etableret.<br> Dine brugerdata er gyldige og blev accepteret af MySQL-serveren.<br> Men MySQLDumper kunne ikke finde nogen database.<br> Den automatiske visning af databaser via script er slået fra på visse servere.<br> Du skal indtaste databasenavnet manuelt efter installationen er færdiggjort. Klik på \"konfiguration\" \"Forbindelsesparametr - vis\" og indtast databasenavnet dér.";
$lang['L_SAFEMODEDESC']="Because PHP is running in safe_mode you need to create the following directories manually using your FTP-Programm:";
$lang['L_ENTER_DB_INFO']="First click the button \"Connect to MySQL\". Only if no database could be detected you need to provide a database name here.";


?>