<?php
$lang['L_HELP_DB']="Ceci est la liste des bases de données existantes";
$lang['L_HELP_PRAEFIX']="Le préfixe est une suite pour le début de tables et sert comme filtre.";
$lang['L_HELP_ZIP']="Compression avec GZip - 'activé' est recommandé";
$lang['L_HELP_MEMORYLIMIT']="C'est la taille maximale en octets que la mémoire donne au scripte
0 = désactivé";
$lang['L_MEMORY_LIMIT']="Limite de la mémoire";
$lang['L_HELP_AD1']="Si activé alors les copies de sauvegarde seront automatiquement supprimées.";
$lang['L_HELP_AD3']="le nombre maximum de fichier dans le répertoire des copies de sauvegardes (pour le cas de suppression automatique)
0 = déactivé";
$lang['L_HELP_LANG']="change sur la langue désirée";
$lang['L_HELP_EMPTY_DB_BEFORE_RESTORE']="Pour éliminer les données superflues on peut définir que la base de données soit vidée complètement avant la restauration";
$lang['L_HELP_CRONEXTENDER']="L'extension standard du script Perl est '.pl'";
$lang['L_HELP_CRONSAVEPATH']="Le nom du fichier de configuration pour le script Perl";
$lang['L_HELP_CRONPRINTOUT']="Si la sortie de texte est désactivée, aucun texte n'est donné.
Cette fonction est indépendante du journal.";
$lang['L_HELP_CRONSAMEDB']="Voulez-vous que la même base de données de la configuration soit utilisée pour le script Cron?";
$lang['L_HELP_CRONDBINDEX']="sélectionne la base de données pour le script Cron";
$lang['L_HELP_FTPTRANSFER']="si activé, alors après la sauvegarde la copie de sauvegarde est envoyée par FTP.";
$lang['L_HELP_FTPSERVER']="Adresse FTP du serveur";
$lang['L_HELP_FTPPORT']="Port FTP du serveur, Port Standard: 21";
$lang['L_HELP_FTPUSER']="Entrer le nom de l'utilisateur de la connexion FTP";
$lang['L_HELP_FTPPASS']="entrer le mot de passe de la connexion FTP";
$lang['L_HELP_FTPDIR']="où doit-être envoyé le fichier?";
$lang['L_HELP_SPEED']="Vitesse minimale et maximale, Standard est de 50 jusqu'à 5000
(des vitesses trop élevées peuvent provoquer des temporisations!)";
$lang['L_SPEED']="Contrôle de vitesse";
$lang['L_HELP_CRONEXECPATH']="L'endroit où se trouve les scripts Perl.
Point de départ est l'adresse HTTP (dans le navigateur)
Autorisé sont les chemins absolus et relatifs.";
$lang['L_CRON_EXECPATH']="Chemin du script Perl";
$lang['L_HELP_CRONCOMPLETELOG']="Si la fonction est activée alors la sortie complète est écrite dans le journal 'complete_log'. 
Cette fonction est indépendante de la fonction sortie de texte.";
$lang['L_HELP_FTP_MODE']="Si vous avez des problèmes durant le transfert FTP, essayez en choisissant passif comme mode de transfert.";


?>