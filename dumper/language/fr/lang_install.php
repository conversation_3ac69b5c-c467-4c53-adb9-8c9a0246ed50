<?php
$lang['L_INSTALLFINISHED']="<br>Installation terminée  --> <a href=\"index.php\">start MySQLDumper</a><br>";
$lang['L_INSTALL_TOMENU']="Retour au menu principal";
$lang['L_INSTALLMENU']="Menu principal";
$lang['L_STEP']="Étape";
$lang['L_INSTALL']="Installation";
$lang['L_UNINSTALL']="Désinstallation";
$lang['L_TOOLS']="Outils";
$lang['L_EDITCONF']="Éditer la configuration";
$lang['L_OSWEITER']="continuer sans sauvegarde";
$lang['L_ERRORMAN']="<strong>Erreur lors de l'écriture de la configuration!</strong><br>Veuillez éditer le fichier ";
$lang['L_MANUELL']="manuellement";
$lang['L_CREATEDIRS']="créer répertoire";
$lang['L_INSTALL_CONTINUE']="continuer avec l'installation";
$lang['L_CONNECTTOMYSQL']=" connecter à MySQL ";
$lang['L_DBPARAMETER']="Paramètre de la base de données";
$lang['L_CONFIGNOTWRITABLE']="Impossible d'écrire le fichier \"config.php\".
Veuillez utiliser votre programme FTP et chmoder ce fichier en 0777.";
$lang['L_DBCONNECTION']="Connexion base de données";
$lang['L_CONNECTIONERROR']="Erreur: aucune connexion n'a pu être créée.";
$lang['L_CONNECTION_OK']="Connexion avec la base de données a été établie.";
$lang['L_SAVEANDCONTINUE']="sauvegarder et continuer l'installation";
$lang['L_CONFBASIC']="Configuration de base";
$lang['L_INSTALL_STEP2FINISHED']="Configuration de la base de données a été sauvegardée.";
$lang['L_INSTALL_STEP2_1']="Continuer l'installation avec la configuration standart";
$lang['L_LASTSTEP']="Terminer l'installation";
$lang['L_FTPMODE']="Créer les répertoires par FTP (safe_mode)";
$lang['L_IDOMANUAL']="Créer un répertoire manuellement";
$lang['L_DOFROM']="terminer par";
$lang['L_FTPMODE2']="Créer un répertoire par FTP:";
$lang['L_CONNECT']="connecter";
$lang['L_DIRS_CREATED']="Les répertoires ont été créés avec succès.";
$lang['L_CONNECT_TO']="connexion vers";
$lang['L_CHANGEDIR']="muter vers le répertoire";
$lang['L_CHANGEDIRERROR']="Mutation dans le répertoire non possible";
$lang['L_FTP_OK']="Paramètres FTP sont ok";
$lang['L_CREATEDIRS2']="Créer répertoires";
$lang['L_FTP_NOTCONNECTED']="Connexion Ftp non établie!";
$lang['L_CONNWITH']="Connexion avec";
$lang['L_ASUSER']="comme utilisateur";
$lang['L_NOTPOSSIBLE']="impossible";
$lang['L_DIRCR1']="créer répertoire de travail";
$lang['L_DIRCR2']="créer répertoire de sauvegarde";
$lang['L_DIRCR4']="créer répertoire du journal";
$lang['L_DIRCR5']="créer répertoire de configuration";
$lang['L_INDIR']="je suis dans le répertoire";
$lang['L_CHECK_DIRS']="vérifier";
$lang['L_DISABLEDFUNCTIONS']="Fonctions désactivées";
$lang['L_NOFTPPOSSIBLE']="Il n'y a pas de fonction FTP à disposition!";
$lang['L_NOGZPOSSIBLE']="Il n'y a pas de fonction de compression à disposition!";
$lang['L_UI1']="Supprimer tous les répertoires de travail avec les sauvegardes incluses.";
$lang['L_UI2']="Êtes-vous sûr d´exécuter l'opération ?";
$lang['L_UI3']="Non, arrêter immédiatement";
$lang['L_UI4']="Oui, veuillez continuer";
$lang['L_UI5']="Supprimer répertoire de travail";
$lang['L_UI6']="tout a été supprimé avec succès.";
$lang['L_UI7']="Veuillez supprimer le répertoire de script";
$lang['L_UI8']="un niveau supérieur";
$lang['L_UI9']="Une erreur est apparue, suppression impossible</p>Erreur dans le répertoire ";
$lang['L_IMPORT']="Importer la configuration";
$lang['L_IMPORT3']="La configuration a été chargée...";
$lang['L_IMPORT4']="La configuration a été sauvegardée.";
$lang['L_IMPORT5']="Démarrer MySQLDumper";
$lang['L_IMPORT6']="Menu d'installation;";
$lang['L_IMPORT7']="Télécharger vers le serveur la configuration";
$lang['L_IMPORT8']="retourner vers le téléchargement";
$lang['L_IMPORT9']="Ceci n'est pas une sauvegarde de la configuration !";
$lang['L_IMPORT10']="La configuration a été téléchargée vers le serveur avec succès ...";
$lang['L_IMPORT11']="<strong>Erreur: </strong>Il y a des problèmes d'écriture pour les sql_statements";
$lang['L_IMPORT12']="<strong>Erreur: </strong>Il y a des problèmes d'écriture pour le fichier config.php";
$lang['L_INSTALL_HELP_PORT']="(vide = Port standart)";
$lang['L_INSTALL_HELP_SOCKET']="(vide = Socket standart)";
$lang['L_TRYAGAIN']="Réessayer";
$lang['L_SOCKET']="Socket";
$lang['L_PORT']="Port";
$lang['L_FOUND_DB']="Base de données trouvée";
$lang['L_FM_FILEUPLOAD']="Télécharger un fichier vers le serveur";
$lang['L_PASS']="Mot de passe";
$lang['L_NO_DB_FOUND_INFO']="La connexion avec la base de données a été établie avec succès.<br>
Vos données utilisateur sont valides et ont été acceptées par le serveur MySQL.<br>
Mais, MySQLDumper n'a pas été capable de trouver une base de données.<br>
La détection automatique via le script est dans quelques cas bloquée par certains serveurs.<br>
Vous devez alors entrer manuellement le nom de votre base de données après la fin de l'installation.
Cliquer sur \"configuration\" \"Affichage des paramêtres de connexion\" et entrer le nom de votre base de données.";
$lang['L_SAFEMODEDESC']="La configuration de ce serveur étant avec l'option \"safe_mode=on\", les listes suivantes doivent être envoyées manuellement. Utilisez votre logiciel FTP pour effectuer cette opération):";
$lang['L_ENTER_DB_INFO']="First click the button \"Connect to MySQL\". Only if no database could be detected you need to provide a database name here.";


?>