<?php
$lang['L_INSTALLFINISHED']="<br>Installation completed  --> <a href=\"index.php\">start MySQLDumper</a><br>";
$lang['L_INSTALL_TOMENU']="Back to main menu";
$lang['L_INSTALLMENU']="Main menu";
$lang['L_STEP']="Step";
$lang['L_INSTALL']="Installation";
$lang['L_UNINSTALL']="Uninstall";
$lang['L_TOOLS']="Tools";
$lang['L_EDITCONF']="Edit configuration";
$lang['L_OSWEITER']="Continue without saving";
$lang['L_ERRORMAN']="<strong>Error while saving the Configuration!</strong><br>Please edit the File ";
$lang['L_MANUELL']="manually";
$lang['L_CREATEDIRS']="Create Directories";
$lang['L_INSTALL_CONTINUE']="Continue with installation";
$lang['L_CONNECTTOMYSQL']="Connect to MySQL ";
$lang['L_DBPARAMETER']="Database Parameters";
$lang['L_CONFIGNOTWRITABLE']="I cannot write to file \"config.php\".
Please use your FTP program and chmod this file to 0777.";
$lang['L_DBCONNECTION']="Database Connection";
$lang['L_CONNECTIONERROR']="Error: unable to connect.";
$lang['L_CONNECTION_OK']="Database connection was established.";
$lang['L_SAVEANDCONTINUE']="Save and continue installation";
$lang['L_CONFBASIC']="Basic Parameter";
$lang['L_INSTALL_STEP2FINISHED']="Database parameters were saved successfully.";
$lang['L_INSTALL_STEP2_1']="Continue installation with the default settings";
$lang['L_LASTSTEP']="Installation Finish";
$lang['L_FTPMODE']="Create necessary directories in safe-mode";
$lang['L_IDOMANUAL']="I create the directories myself";
$lang['L_DOFROM']="starting from";
$lang['L_FTPMODE2']="Create the dirs with FTP:";
$lang['L_CONNECT']="connect";
$lang['L_DIRS_CREATED']="The directories are created and correctly permissioned.";
$lang['L_CONNECT_TO']="connect to";
$lang['L_CHANGEDIR']="change to dir";
$lang['L_CHANGEDIRERROR']="change to dir was not possible";
$lang['L_FTP_OK']="FTP parameter are ok";
$lang['L_CREATEDIRS2']="Create directories";
$lang['L_FTP_NOTCONNECTED']="FTP connection not established!";
$lang['L_CONNWITH']="Connection with";
$lang['L_ASUSER']="as user";
$lang['L_NOTPOSSIBLE']="not possible";
$lang['L_DIRCR1']="create workdir";
$lang['L_DIRCR2']="create backupdir";
$lang['L_DIRCR4']="create logdir";
$lang['L_DIRCR5']="create configurationdir";
$lang['L_INDIR']="now in dir";
$lang['L_CHECK_DIRS']="Check my directories";
$lang['L_DISABLEDFUNCTIONS']="Disabled Functions";
$lang['L_NOFTPPOSSIBLE']="You don't have FTP functions !";
$lang['L_NOGZPOSSIBLE']="You don't have compression functions !";
$lang['L_UI1']="All working directories which can contain backups will be deleted.";
$lang['L_UI2']="Are you sure you want that?";
$lang['L_UI3']="no, cancel immediately";
$lang['L_UI4']="yes, please continue";
$lang['L_UI5']="delete working directories";
$lang['L_UI6']="all was deleted successfully.";
$lang['L_UI7']="Please delete the script directory";
$lang['L_UI8']="one level up";
$lang['L_UI9']="An error occured, deleting was not possible</p>Error with directory ";
$lang['L_IMPORT']="Import Configuration";
$lang['L_IMPORT3']="Configuration was loaded ...";
$lang['L_IMPORT4']="Configuration was saved.";
$lang['L_IMPORT5']="Start MySQLDumper";
$lang['L_IMPORT6']="Installation Menu";
$lang['L_IMPORT7']="Upload configuration";
$lang['L_IMPORT8']="back to upload";
$lang['L_IMPORT9']="This is not a configuration backup !";
$lang['L_IMPORT10']="Configuration was uploaded successfully ...";
$lang['L_IMPORT11']="<strong>Error: </strong>There were problems writing sql_statements";
$lang['L_IMPORT12']="<strong>Error: </strong>There were problems writing config.php";
$lang['L_INSTALL_HELP_PORT']="(empty = Default Port)";
$lang['L_INSTALL_HELP_SOCKET']="(empty = Default Socket)";
$lang['L_TRYAGAIN']="Try again";
$lang['L_SOCKET']="Socket";
$lang['L_PORT']="Port";
$lang['L_FOUND_DB']="found db";
$lang['L_FM_FILEUPLOAD']="Upload file";
$lang['L_PASS']="Password";
$lang['L_NO_DB_FOUND_INFO']="The connection to the database was successfully established.<br>
Your userdata is valid and was accepted by the MySQL-Server.<br>
But MySQLDumper was not able to find any database.<br>
The automatic detection via script is blocked on some servers.<br>
You must enter your database name manually after the installation is finished.
Click on \"configuration\" \"Connection Parameter - display\" and enter the database name there.";
$lang['L_SAFEMODEDESC']="Because PHP is running in safe_mode you need to create the following directories manually using your FTP-Programm:";
$lang['L_ENTER_DB_INFO']="First click the button \"Connect to MySQL\". Only if no database could be detected you need to provide a database name here.";


?>