/*
    @MySQLDumper STYLESHEET
    @name			msd_green
    @copyright		MySQLDumper - <PERSON> <http://www.mysqldumper.de>
    <AUTHOR> <http://www.vorderdeck.de>, <PERSON>, <PERSON> <http://www.gresshoener.de>
    @date			2006-04-11 16:26:00
    @lastmodified	2009-09-15 00:21:30
    @media			screen and projection
*/
*,.normargin {
	margin: 0;
	padding: 0;
}

img {
	border: 0;
}

body {
	font: 12px/1.5 Verdana, Helvetica, sans-serif; /* Important IE Bugfix: No spaces in font-attribute between px and slash */
	color: #4E5665;
	background: #C7D1D0;
}

body.content {
	min-width: 540px;
}

.menu-frame {
	font: 12px/1.5 Verdana, Helvetica, sans-serif;
	background-color: #738C88;
	color: #eee;
}

/* LINKS */
a {
	color: #4E5665;
	text-decoration: underline;
	font: 12px verdana, sans-serif
}

a:hover {
	color: #E87B00;
	text-decoration: none
}

a.ul {
	text-decoration: underline;
}

/* general */
#pagetitle {
	font-size: 1.5em;
	font-weight: bold;
	background: url(pics/pagetitle.gif);
	background-repeat: repeat-x;
	letter-spacing: 2px;
	color: white;
	padding: 2px 6px;
	border-bottom: 1px solid #A9AFBB;
	height: 26px;
	overflow: hidden;
}

#server0 {
	position: absolute;
	bottom: 4px;
	text-align: center;
	left: 10px;
	color: white;
}

#server1 {
	position: absolute;
	right: 16px;
	text-align: center;
	padding: 10px;
}

a.server {
	color: #333333;
	font-size: 10px;
}

a.server:hover {
	text-decoration: underline;
	color: white;
}

.version {
	display: block;
	position: absolute;
	top: 31px;
	left: 0;
	width: 190px;
	z-index: 5;
	text-align: center;
	font-size: 11px;
	font-weight: bold;
}

.small {
	font-size: 11px;
}

.ssmall {
	font-size: 10px;
}

.success {
	font-weight: bold;
}

.error {
	color: red;
	font-weight: bold;
}

.explain {
	text-decoration: none;
	border-bottom: 1px dotted;
}

.explain:hover {
	cursor: help;
}

.active_db {
	font-weight: bold;
	border-bottom: 1px dotted;
	color: #9AA2B1;
}

table td {
	text-align: left;
	vertical-align: top;
	padding: 0 6px;
}

table th {
	padding: 0 6px;
}

table.bdr,.bdr {
	border: 1px solid #ddd !important;
	border-collapse: collapse !important;
}

/* MENU */
#wrapmenu {
	background: url(pics/pagetitle.gif) #A9AFBB;
	background-repeat: repeat-x;
}

#db-select {
	font: 12px Verdana, Helvetica, sans-serif;
	color: #4E5665;
	background: #A9AFBB;
}

#menu {
	position:absolute;
	margin: 0px;
	top: 222px;
	left:0;
	width:190px;
	height:160px;
	background-color: #738C88;
	border-top: 1px solid #9AA2B1
}

#menu ul {
	list-style: none;
}

#menu ul li {
	height: 20px;
}

#menu ul li a {
	display: block;
	position: relative; /*fuer den ie*/
	font: 11px/20px verdana, sans-serif;
	text-decoration: none;
	color: #4E5665;
	background: url(pics/mainnavi.gif) repeat-x 0 -20px;
	padding: 0 15px;
	height: 20px;
	text-align: left;
}

#menu ul li a:hover {
	color: #fff;
	background: url(pics/mainnavi.gif) repeat-x 0 0px;
}

/*der aktive link in der navi*/
#menu ul li.active a {
	font-weight: bold;
	color: #fff;
	background: url(pics/mainnavi.gif) repeat-x 0 -40px;
	display: block;
}

#menu p,#menu p a {
	color: #fff;
	line-height: 1.5;
	font-size: 11px;
}

#version {
	position:absolute;
	top:30px;
	left:0;
	margin:0;
	padding:0;
	height:191px;
	color: #4E5665;
	z-index:10;
	border-top:1px solid #A9AFBB;
}
.version-line
{
	position:absolute;
	top:-1px;
	left:0;
	width:190px;
	text-align:center;
	font: 11px/20px verdana, sans-serif;
}

#version a:link,
#version a:active,
#version a:hover,
#version a:visited
{ 	color: #4E5665; }
	

#selectConfig 
{
	position:absolute;
	top:380px;	
	left:0;
	width:190px;
	height: 196px;
	z-index:5;
}

table.bdr,.bdr {
	border: 1px solid #4E5665 !important;
	border-collapse: collapse !important;
}

#db-selectbox,#config-selectbox {
	position: relative;
	top: 0px;
	width: 190px;
	background: transparent;
	text-align: center;
	font: 11px/1.5 Verdana, Helvetica, sans-serif;
	color: white;
}

#db-selectbox a,#config-selectbox a {
	font: 11px/1.5 Verdana, Helvetica, sans-serif;
	color: white;
	text-decoration: underline;
}

#db-selectbox a:hover,#config-selectbox a:hover {
	text-decoration: none
}

fieldset {
	margin: 10px;
	padding: 5px;
	border: 1px solid #ddd;
	color: #256777;
}

body.content legend {
	font-weight: bold;
}

body.menu fieldset p {
	margin: 5px 0 0;
	text-align: center;
}

#configSelect legend,#dbSelect legend,#dbSelect a {
	font: 11px/1.5 Verdana, Helvetica, sans-serif;
	color: #eee;
}

/* MAIN */
#topnavi {
	list-style: none;
	margin: 10px 0 20px;
	padding: 5px 0px;
}

#topnavi li {
	float: left;
	margin-right: 6px;
}

#topnavi li a {
	float: left;
	font: 0.8em verdana, arial, sans-serif;
	border: 1px solid #4E5665;
	background-color: #A9B2B1;
	color: #4E5665;
	margin: 3px;
	padding: 3px 23px 4px 23px;
	cursor: pointer;
	text-decoration: none;
	white-space: nowrap;
}

#topnavi li a span {
	display: none;
}

#content {
	margin-left: 12px;
	background-color: #C7D1D0;
}

#content p {
	margin-bottom: 8px;
}

/*Tabellen */
table tr.dbrow {
	background: #E4E9E8;
}

table tr.dbrow1 {
	background: #D5DDDC;
}

table tr.dbrowsel {
	background: #fff;
}

table td.treffer {
	background: #000;
}

/* Treffer bei der MySQL-Suche */
table tr.dbrow .treffer,table tr.dbrow1 .treffer {
	color: yellow;
	background-color: #738C88;
}

table.bordersmall,table.border {
	border: 2px solid #738C88;
	width: 84%;
}

table.border th,table.bordersmall th {
	font-size: 11px;
	color: #738C88;
}

table tr.thead th,table tr.thead td {
	background-color: #A5B6B4;
	color: #738C88;
	border: 1px solid #738C88;
}

table.border tr.thead,table.bordersmall tr.thead {
	height: 24px;
}

table td.sum {
	background-color: red;
	font-weight: bold;
	text-align: right;
}

table.bordersmall input,table.bordersmall select,table.bordersmall td,#sqlheaderbox select
	{
	border: 1px solid #738C88;
	font-size: 11px;
}

.tdcompact {
	width: 100px;
	height: 16px;
	overflow: hidden;
	font-size: 11px;
}

.tdnormal {
	white-space: nowrap;
	padding: 4px;
}

.sqlheadmenu a {
	
}

/* FORM-Elements */
#content .Formbutton,#content .Formtext {
	white-space: nowrap;
	margin: 6px;
	font: 10px verdana, sans-serif;
	border: 1px solid #4E5665;
	background: #A9B2B1;
	color: #4E5665;
	padding: 2px 10px;
	cursor: pointer;
	overflow: visible;
	text-decoration: none;
}

#content a.Formbutton {
	padding: 2px 23px 3px 23px;
}

label {
	cursor: pointer;
}

.Formbutton {
	cursor: pointer;
}

.Formbutton:disabled {
	cursor: default !important;
}

#content .Formtext {
	padding: 2px;
	overflow: hidden;
	cursor: text;
}

#content .SQLbutton {
	font-size: 11px;
	background: #E4E9E8;
	cursor: pointer;
}

#content textarea #thta {
	font-size: 11px;
	color: blue;
	width: 400px;
	height: 300px;
	overflow: scroll;
}

#content textarea {
	width: 100%;
	background: #B3C2C0;
}

input.radio,input.checkbox {
	background-color: transparent;
}

/* Colors for Formelements */
input.text,input.small {
	background: #B3C2C0;
	color: #4E5665;
}

select {
	background: #B3C2C0;
	color: #4E5665;
}

textarea {
	background: #B3C2C0;
	color: #4E5665;
}

/* for Geckos */
input[disabled] {
	color: #888 !important;
}

/*horizontales Men� */
#hormenu {
	
}

#hormenu ul {
	border-bottom: thin solid #D5DDDC;
	margin-bottom: 8px;
	width: 80%;
}

#hormenu ul li {
	display: inline;
}

#hormenu ul li a {
	font: 11px/20px verdana, sans-serif;
	text-decoration: none;
	color: #4E5665;
	padding: 0 14px;
	border: 0;
}

#hormenu ul li a:hover {
	color: #E87B00;
}

#hormenu ul li#active a {
	font: bold 11px/20px verdana, sans-serif;
	text-decoration: none;
	color: #E87B00;
	float: left;
	padding: 0 14px;
}

#hormenu ul li#active a:hover {
	color: #E87B00;
}

/* spezielle Elemente */
.MySQLbox {
	font-size: 10pt;
	padding: 6px;
	background: #000;
	color: #fff;
	border: thin solid #999999;
	height: 200px;
	width: 80%;
	text-align: left;
	overflow: auto;
}

#content #mysqlbox {
	border: 1px solid #738C88;
	background: #D5DDDC;
}

#content #sqlheaderbox,.sqlbox-warning {
	white-space: nowrap;
	vertical-align: top;
	padding: 8px;
	border: 1px solid #738C88;
	color: #256777;
	line-height: 22px;
}

#sqlheaderbox .Formbutton {
	line-height: 12px;
	padding: 2px;
	margin: 0px;
}

#sqltextarea {
	width: 100% !important;
	overflow: auto;
	background-color: #B3C2C0;
}

#content #sqlheaderbox {
	white-space: nowrap;
	height: 20px;
	vertical-align: middle;
	padding-top: 2px;
}

#content #sqleditbox {
	border: 1px solid #738C88;
	background: #E4E9E8;
	margin-bottom: 10px;
}

#content #sqleditbox form {
	margin: 10px;
}

#content #sqleditbox p {
	background: #A5B6B4;
	font-weight: bold;
	text-align: center;
}

#content #sqlnewbox {
	border: 1px solid #738C88;
	background: #E4E9E8;
}

#content #sqlnewbox p {
	background: #A5B6B4;
	font-weight: bold;
	text-align: center;
}

#content #sqloutbox {
	font-size: 11px;
	width: 700px;
	padding: 6px;
	background: #D5DDDC;
	border: 1px solid #738C88;
	overflow: auto;
	height: 240px;
}

#content p.autodel {
	font-size: 11px;
	border-bottom: 1px dashed #fff;
	margin-bottom: 12px;
}

#content .Logbox {
	font: 12px/1.2 "Courier New", Courier, monospace;
	padding: 6px;
	border: thin solid #738C88;
	height: 320px;
	width: 90%;
	text-align: left;
	overflow: auto;
}

#content .Logbox span {
	color: #738C88;
}

#content .backupmsg {
	padding-left: 20px;
	font-size: 11px;
}

#content .backupmsg .success,#content .backupmsg a {
	color: #999;
	font-size: 11px;
}

#content .backupmsg .error {
	color: red;
}

/* TEXT */
h1 {
	font-size: 16px;
}

h5 {
	font-size: 14px;
	margin: 12px 0;
}

h6 {
	font-size: 11px;
	background: #D1D4DC;
	border: 1px solid #9AA2B1;
	text-indent: 12px;
	margin: 8px 0;
}

/* Config */
#configleft {
	float: left;
	width: 160px;
	margin-top: 24px;
}

#configleft .Formbutton {
	width: 130px;
	margin: 8px 0;
}

#configleft .ConfigButton {
	width: 130px;
	font-size: 11px;
	background: #E4E9E8;
	cursor: pointer;
}

#configleft .ConfigButtonSelected {
	width: 130px;
	font-size: 11px;
	background: #D5DDDC;
}

#configright {
	font-size: 11px;
	margin-left: 160px;
}

#configright fieldset {
	border: 1px solid #D5DDDC;
	padding: 12px;
	width: 90%;
}

#configright legend {
	color: #646968;
	margin: 8px 0;
}

#footer {
	font-size: 11px;
	text-align: center;
	border-top: 1px dashed #eee;
	margin-top: 36px;
}

#footer a {
	font-size: 11px;
}