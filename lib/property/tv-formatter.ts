interface PropertyTVData {
  id: string;
  propertyType: string;
  project: string;
  status: string;
  mainImage: string;
  url: string;
  stats: {
    bedrooms: number;
    totalArea: number;
    beachDistance: number;
    completion: string;
  };
  price: {
    thb: number;
    oldThb?: number;
  };
}

export function formatPropertyForTV(propertyData: any): PropertyTVData {
  return {
    id: propertyData.number,
    propertyType: propertyData.type,
    project: propertyData.project?.name || "",
    status: "Сдан",
    mainImage: propertyData.mainImage,
    url: `https://yourdomain.com/property/${propertyData.id}`,
    stats: {
      bedrooms: propertyData.bedrooms,
      totalArea: propertyData.areaCommon,
      beachDistance: propertyData.re_distanceSea,
      completion: propertyData.completion || "2023"
    },
    price: {
      thb: propertyData.price,
      oldThb: propertyData.oldPrice
    }
  };
} 