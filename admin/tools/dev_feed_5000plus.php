<?php

define('DRUPAL_ROOT', getcwd());

// Настройка окружения
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Параметры фильтрации
$target_areas = [
    'bangtao' => ['Bangtao/Laguna'],
    'layan' => ['Layan Beach'],
    'nai_thon' => ['Nai Thon Beach']
];

$min_price_usd = 150000;
$thb_to_usd = 34;
$min_price_thb = $min_price_usd * $thb_to_usd;
$max_distance_to_sea = 1500; // метров
$min_bedrooms = 2;
$max_bedrooms = 5;
$min_area = 70; // м²

$feed_lang = 'en';
$dealType = isset($argv[1]) ? $argv[1] : 'sale';
$xml_feed_path = 'property_feed_5000plus_' . $dealType . '.xml';

// Статистика
$total_count = 0;
$included_count = 0;
$area_stats = array_fill_keys(array_keys($target_areas), 0);

$query = db_select('_phuket_Property', 'p')
    ->fields('p')
    ->condition('published', 1)
    ->condition('isSaled', 0)
    ->condition('number', 5000, '>=')
    ->condition('dealType', $dealType);

$db_result = $query->execute();

$xml_head = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL;
$xml_head .= '<objects>' . PHP_EOL;
$xml_head .= '    <tranio>' . PHP_EOL;
$xml_head .= '        <feed_version>1.1</feed_version>' . PHP_EOL;
$xml_head .= '    </tranio>' . PHP_EOL;

$xml_footer = '</objects>' . PHP_EOL;

file_put_contents($xml_feed_path, $xml_head);

$debug_stats = [
    'total' => 0,
    'area_failed' => 0,
    'price_failed' => 0,
    'bedrooms_failed' => 0,
    'size_failed' => 0,
    'distance_failed' => 0
];

while ($row = $db_result->fetch()) {
    $total_count++;
    $debug_stats['total']++;
    
    $propertyDto = unserialize($row->propertyDto);
    
    // Отладочная информация о структуре объекта
    echo "\nProperty #" . $propertyDto->number . ":\n";
    if (isset($propertyDto->re_subLocality)) {
        echo "SubLocality structure:\n";
        print_r($propertyDto->re_subLocality);
    } else {
        echo "No re_subLocality data\n";
    }
    
    // Проверка района
    if (empty($propertyDto->re_subLocality) || empty($propertyDto->re_subLocality->name->{$feed_lang})) {
        echo "Skipping: no sublocality data\n";
        continue;
    }

    $area_matches = false;
    $property_location = $propertyDto->re_subLocality->name->{$feed_lang};
    echo "Checking sublocation: " . $property_location . "\n";
    
    foreach ($target_areas as $area_key => $area_variants) {
        foreach ($area_variants as $variant) {
            if ($property_location === $variant) {
                $area_matches = true;
                $area_stats[$area_key]++;
                echo "Match found for area: " . $area_key . "\n";
                break 2;
            }
        }
    }
    
    if (!$area_matches) {
        continue;
    }

    // Проверка цены
    $price_thb = ($row->dealType == 'rent') ? $row->price_rent : $row->price_sale;
    if ($price_thb < $min_price_thb) {
        echo "Skipping: price too low (" . number_format($price_thb) . " THB)\n";
        continue;
    }

    // Проверка спален
    if ($propertyDto->bedrooms < $min_bedrooms || $propertyDto->bedrooms > $max_bedrooms) {
        $debug_stats['bedrooms_failed']++;
        continue;
    }

    // Проверка площади
    if ($propertyDto->interior_size < $min_area) {
        $debug_stats['size_failed']++;
        continue;
    }

    // Проверка расстояния до моря
    if (!empty($propertyDto->distance_to_sea) && $propertyDto->distance_to_sea > $max_distance_to_sea) {
        $debug_stats['distance_failed']++;
        continue;
    }

    $included_count++;

    $xml_listing = '    <object>' . PHP_EOL;
    $xml_listing .= sprintf('        <id>BP%s</id>' . PHP_EOL, $propertyDto->number);
    $xml_listing .= sprintf('        <old_id>%s</old_id>' . PHP_EOL, $propertyDto->id);
    $xml_listing .= sprintf('        <external_url>%s</external_url>' . PHP_EOL, $GLOBALS['base_url'] . '/property/' . $propertyDto->id);
    $xml_listing .= sprintf('        <address>%s %s</address>' . PHP_EOL, $propertyDto->re_subLocality->name->{$feed_lang}, 'Thailand');
    $xml_listing .= '        <type>b</type>' . PHP_EOL;
    $xml_listing .= sprintf('        <purpose>%s</purpose>' . PHP_EOL, $dealType == 'rent' ? 'r' : 's');
    $xml_listing .= '        <currency>u</currency>' . PHP_EOL;
    $xml_listing .= '        <hide_price>0</hide_price>' . PHP_EOL;

    // Цены
    if ($dealType == 'rent') {
        $price_usd = round($row->price_rent / $thb_to_usd);
        $xml_listing .= sprintf('        <price_rent_weekly>%s</price_rent_weekly>' . PHP_EOL, $price_usd);
        $xml_listing .= '        <price_mid>0</price_mid>' . PHP_EOL;
        $xml_listing .= '        <price_high>0</price_high>' . PHP_EOL;
        $xml_listing .= sprintf('        <price_low>%s</price_low>' . PHP_EOL, $price_usd);
        $xml_listing .= '        <price_sell>0</price_sell>' . PHP_EOL;
    } else {
        $price_usd = round($row->price_sale / $thb_to_usd);
        $xml_listing .= '        <price_rent_weekly>0</price_rent_weekly>' . PHP_EOL;
        $xml_listing .= '        <price_mid>0</price_mid>' . PHP_EOL;
        $xml_listing .= '        <price_high>0</price_high>' . PHP_EOL;
        $xml_listing .= '        <price_low>0</price_low>' . PHP_EOL;
        $xml_listing .= sprintf('        <price_sell>%s</price_sell>' . PHP_EOL, $price_usd);
    }

    $xml_listing .= sprintf('        <bedrooms>%s</bedrooms>' . PHP_EOL, $propertyDto->bedrooms);
    $xml_listing .= sprintf('        <guests>%s</guests>' . PHP_EOL, $propertyDto->bedrooms * 2);
    $xml_listing .= sprintf('        <bathrooms>%s</bathrooms>' . PHP_EOL, $propertyDto->bathrooms);

    if (!empty($propertyDto->re_distanceSea)) {
        $xml_listing .= sprintf('        <distance_sea>%s</distance_sea>' . PHP_EOL, $propertyDto->re_distanceSea);
    }

    // Фотографии
    if (!empty($propertyDto->photos)) {
        $xml_listing .= '        <photos>' . PHP_EOL;
        foreach ($propertyDto->photos as $photo) {
            $photo_url = $GLOBALS['base_url'] . '/sites/default/files/' . $photo->uri;
            $xml_listing .= sprintf('            <url>%s</url>' . PHP_EOL, $photo_url);
        }
        $xml_listing .= '        </photos>' . PHP_EOL;
    }

    // Описания с форматированием HTML
    if (!empty($propertyDto->description->ru)) {
        $description_ru = '<p>' . $propertyDto->description->ru . '</p>' . PHP_EOL;
        $description_ru .= '<h3>На вилле:</h3>' . PHP_EOL;
        $description_ru .= '<ul>';
        if (!empty($propertyDto->re_pool)) $description_ru .= '<li>бассейн</li>';
        if (!empty($propertyDto->bedrooms)) $description_ru .= sprintf('<li>%d спальни</li>', $propertyDto->bedrooms);
        if (!empty($propertyDto->bathrooms)) $description_ru .= sprintf('<li>%d ванные комнаты</li>', $propertyDto->bathrooms);
        if (!empty($propertyDto->re_internet)) $description_ru .= '<li>беспроводной интернет wifi</li>';
        if (!empty($propertyDto->re_kitchen)) $description_ru .= '<li>полностью оборудованная кухня</li>';
        if (!empty($propertyDto->re_aircon)) $description_ru .= '<li>кондиционирование всех помещений</li>';
        $description_ru .= '</ul>';
        
        $xml_listing .= sprintf('        <description_full><![CDATA[ %s ]]></description_full>' . PHP_EOL, $description_ru);
    }

    if (!empty($propertyDto->description->en)) {
        $description_en = '<p>' . $propertyDto->description->en . '</p>' . PHP_EOL;
        $description_en .= '<h3>The villa:</h3>' . PHP_EOL;
        $description_en .= '<ul>';
        if (!empty($propertyDto->re_pool)) $description_en .= '<li>swimming pool</li>';
        if (!empty($propertyDto->bedrooms)) $description_en .= sprintf('<li>%d bedrooms</li>', $propertyDto->bedrooms);
        if (!empty($propertyDto->bathrooms)) $description_en .= sprintf('<li>%d bathrooms</li>', $propertyDto->bathrooms);
        if (!empty($propertyDto->re_internet)) $description_en .= '<li>wireless internet wifi</li>';
        if (!empty($propertyDto->re_kitchen)) $description_en .= '<li>fully equipped kitchen</li>';
        if (!empty($propertyDto->re_aircon)) $description_en .= '<li>air conditioning in all rooms</li>';
        $description_en .= '</ul>';
        
        $xml_listing .= sprintf('        <description_en_full><![CDATA[ %s ]]></description_en_full>' . PHP_EOL, $description_en);
    }

    // Дополнительные удобства
    if (!empty($propertyDto->re_supermarket)) {
        $xml_listing .= '        <supermarket>1</supermarket>' . PHP_EOL;
    }

    $xml_listing .= '    </object>' . PHP_EOL;
    
    file_put_contents($xml_feed_path, $xml_listing, FILE_APPEND);
}

file_put_contents($xml_feed_path, $xml_footer, FILE_APPEND);

echo "\nСтатистика выгрузки:\n";
echo "Всего обработано: $total_count объектов\n";
echo "Включено в выгрузку: $included_count объектов\n\n";
echo "По районам:\n";
foreach ($area_stats as $area => $count) {
    echo "- $area: $count объектов\n";
}
echo "\nКурс: 1 USD = $thb_to_usd THB\n";
echo "Минимальная цена: $min_price_usd USD ($min_price_thb THB)\n";
echo "Дата выгрузки: " . date('Y-m-d H:i:s') . "\n";

drupal_exit(); 