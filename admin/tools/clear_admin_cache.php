<?php
/**
 * Скрипт для очистки кэша административных разделов
 * 
 * Этот скрипт должен запускаться при проблемах с административной панелью
 * после внедрения кэширования.
 */

// Определяем корень Drupal
define('DRUPAL_ROOT', dirname(dirname(dirname(__FILE__))));

// Подключаем загрузчик Drupal
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';

// Запускаем полный bootstrap
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Проверяем права доступа
if (!user_access('administer site configuration')) {
  die('Access denied');
}

// Список кэшей для очистки
$caches_to_clear = array(
  'cache',
  'cache_block',
  'cache_page',
  'cache_menu',
  'cache_filter',
  'cache_form',
  'cache_bootstrap',
  'cache_path',
);

// Очистка всех указанных кэшей
foreach ($caches_to_clear as $cache_bin) {
  cache_clear_all('*', $cache_bin, TRUE);
  echo "Очищен кэш: $cache_bin<br>";
}

// Очистка кэша маршрутов
drupal_static_reset('menu_get_item');
drupal_static_reset('drupal_match_path');
drupal_static_reset('arg');
drupal_static_reset('drupal_lookup_path');
echo "Сброшены статические кэши маршрутов<br>";

// Запрос на сброс кэша конкретных путей
$admin_paths = array(
  's45/admin',
  'admin',
  'user',
);

// Очистка кэша для конкретных путей
foreach ($admin_paths as $path) {
  cache_clear_all(url($path, array('absolute' => TRUE)), 'cache_page');
  cache_clear_all($path, 'cache_path');
  echo "Очищен кэш для пути: $path<br>";
}

// Сброс кэша запросов _s45_events 
$s45_cache_keys = db_select('cache', 'c')
  ->fields('c', array('cid'))
  ->condition('cid', 's45_events_%', 'LIKE')
  ->execute()
  ->fetchCol();

foreach ($s45_cache_keys as $key) {
  cache_clear_all($key, 'cache');
  echo "Удален кэш событий: $key<br>";
}

// Дополнительная очистка сессий для текущего пользователя
if (!empty($_SESSION)) {
  foreach ($_SESSION as $key => $value) {
    if (strpos($key, 'compo_') === 0 || $key === '_S45' || $key === 'Compo') {
      unset($_SESSION[$key]);
      echo "Очищена сессия: $key<br>";
    }
  }
}

// Перезагрузка информации о компонентах
if (class_exists('Site45\Base\CompoScanner')) {
  \Site45\Base\CompoScanner::create()->getForAllDirs();
  echo "Обновлена информация о компонентах<br>";
}

// Очистка переменных, связанных с кэшированием
variable_set('s45_events_cache_ttl', 0); // Временно отключаем кэширование событий
variable_set('enable_phuket_query_cache', 0); // Временно отключаем кэш запросов
echo "Отключено кэширование запросов к событиям<br>";

// Вывод времени очистки и ссылки для возврата в админку
echo "<p>Кэш очищен: " . date('Y-m-d H:i:s') . "</p>";
echo "<p><a href='/s45/admin/'>Вернуться в админку</a></p>";

// Регистрируем функцию для восстановления кэша через 1 час
register_shutdown_function(function() {
  variable_set('s45_cache_reset_time', time() + 3600); // Через 1 час
});

echo "<p>Кэш будет автоматически восстановлен через 1 час.</p>"; 