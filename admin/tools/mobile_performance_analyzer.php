<?php
/**
 * Анализатор производительности мобильной версии IndreamsPhuket
 * 
 * Диагностирует проблемы:
 * - Медленная загрузка (висит на 20%)
 * - Не грузятся фотографии объектов
 * - Ошибки загрузки ресурсов
 */

$logFile = __DIR__ . '/../logs/mobile_performance_' . date('Y-m-d_H-i-s') . '.txt';
$errorCount = 0;

// Домены для тестирования (мобильные поддомены не настроены, используем основные с мобильными User-Agent)
$domains = [
    'desktop' => [
        'indreamsphuket.ru' => 'https://indreamsphuket.ru',
        'indreamsphuket.com' => 'https://indreamsphuket.com'
    ],
    'mobile' => [
        'indreamsphuket.ru (mobile)' => 'https://indreamsphuket.ru',
        'indreamsphuket.com (mobile)' => 'https://indreamsphuket.com'
    ]
];

/**
 * Логирование с подсчетом ошибок
 */
function logMessage($message, $file, $isError = false) {
    global $errorCount;
    if ($isError) $errorCount++;
    
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] " . ($isError ? 'ERROR: ' : '') . "$message\n";
    echo $logEntry;
    file_put_contents($file, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * HTTP анализ с User-Agent для мобильных устройств
 */
function analyzeHttpResponse($url, $userAgent = 'desktop') {
    $userAgents = [
        'desktop' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'mobile' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        'android' => 'Mozilla/5.0 (Linux; Android 12; SM-G996B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 60,
        CURLOPT_CONNECTTIMEOUT => 15,
        CURLOPT_USERAGENT => $userAgents[$userAgent],
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HEADER => true,
        CURLOPT_VERBOSE => false
    ]);
    
    $start = microtime(true);
    $response = curl_exec($ch);
    $end = microtime(true);
    
    $info = curl_getinfo($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    // Разделяем заголовки и тело
    $header_size = $info['header_size'];
    $headers = substr($response, 0, $header_size);
    $body = substr($response, $header_size);
    
    return [
        'url' => $url,
        'user_agent' => $userAgent,
        'total_time' => round($end - $start, 3),
        'connect_time' => round($info['connect_time'], 3),
        'starttransfer_time' => round($info['starttransfer_time'], 3),
        'http_code' => $info['http_code'],
        'size_download' => $info['size_download'],
        'speed_download' => round($info['speed_download']),
        'content_type' => $info['content_type'],
        'error' => $error,
        'headers' => $headers,
        'body' => $body,
        'response_size' => strlen($body)
    ];
}

/**
 * Анализ контента на наличие ошибок
 */
function analyzePageContent($body, $url) {
    $issues = [];
    
    // Поиск изображений
    if (preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $body, $matches)) {
        $images = $matches[1];
        $issues['total_images'] = count($images);
        
        // Проверяем первые 3 изображения
        $broken_images = 0;
        foreach (array_slice($images, 0, 3) as $img_src) {
            if (!filter_var($img_src, FILTER_VALIDATE_URL)) {
                if (strpos($img_src, '/') === 0) {
                    $img_url = parse_url($url, PHP_URL_SCHEME) . '://' . parse_url($url, PHP_URL_HOST) . $img_src;
                } else {
                    continue;
                }
            } else {
                $img_url = $img_src;
            }
            
            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => $img_url,
                CURLOPT_NOBODY => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false
            ]);
            curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code >= 400) {
                $broken_images++;
            }
        }
        $issues['broken_images'] = $broken_images;
    }
    
    // Размер контента
    $issues['content_size_mb'] = round(strlen($body) / 1024 / 1024, 2);
    
    return $issues;
}

/**
 * Проверка системных ресурсов
 */
function checkSystemHealth() {
    $health = [];
    
    // Load average
    if (file_exists('/proc/loadavg')) {
        $loadavg = trim(file_get_contents('/proc/loadavg'));
        $health['load_average'] = $loadavg;
    }
    
    // Процессы веб-сервера
    exec('ps aux | grep -E "(apache2|httpd)" | grep -v grep | wc -l', $apache_count);
    exec('ps aux | grep mysql | grep -v grep | wc -l', $mysql_count);
    
    $health['apache_processes'] = intval($apache_count[0]);
    $health['mysql_processes'] = intval($mysql_count[0]);
    
    return $health;
}

// Начинаем анализ
logMessage("=== АНАЛИЗ ПРОИЗВОДИТЕЛЬНОСТИ МОБИЛЬНОЙ ВЕРСИИ ===", $logFile);
logMessage("Время запуска: " . date('Y-m-d H:i:s'), $logFile);

// 1. Проверка системного здоровья
logMessage("\n=== СИСТЕМНОЕ ЗДОРОВЬЕ ===", $logFile);
$health = checkSystemHealth();
foreach ($health as $key => $value) {
    logMessage("$key: $value", $logFile);
}

// 2. Тестирование каждого домена
logMessage("\n=== АНАЛИЗ ДОМЕНОВ ===", $logFile);

foreach ($domains as $device_type => $domain_list) {
    logMessage("\n--- $device_type версии ---", $logFile);
    
    foreach ($domain_list as $domain => $url) {
        logMessage("\n=== Тестирование $domain ===", $logFile);
        
        $userAgent = ($device_type === 'mobile') ? 'mobile' : 'desktop';
        
        // Выполняем 2 запроса для сравнения
        for ($attempt = 1; $attempt <= 2; $attempt++) {
            logMessage("Попытка #$attempt...", $logFile);
            
            $result = analyzeHttpResponse($url, $userAgent);
            
            if (!empty($result['error'])) {
                logMessage("ОШИБКА подключения: {$result['error']}", $logFile, true);
                continue;
            }
            
            logMessage("HTTP код: {$result['http_code']}", $logFile, $result['http_code'] >= 400);
            logMessage("Общее время: {$result['total_time']}s", $logFile, $result['total_time'] > 3);
            logMessage("Время до первого байта: {$result['starttransfer_time']}s", $logFile, $result['starttransfer_time'] > 2);
            logMessage("Размер ответа: " . number_format($result['response_size']) . " байт", $logFile);
            logMessage("Скорость: " . number_format($result['speed_download']) . " байт/с", $logFile);
            
            // Анализ контента только для успешных запросов
            if ($result['http_code'] === 200 && !empty($result['body'])) {
                $content_issues = analyzePageContent($result['body'], $url);
                
                logMessage("\n--- Анализ контента ---", $logFile);
                foreach ($content_issues as $issue => $count) {
                    $isError = ($issue === 'broken_images' && $count > 0);
                    logMessage("$issue: $count", $logFile, $isError);
                }
                
                // Поиск ошибок в HTML
                if (preg_match('/error|fail|exception/i', $result['body'])) {
                    logMessage("ОБНАРУЖЕНЫ ошибки в HTML контенте", $logFile, true);
                }
                
                // Проверка мобильной адаптации
                if ($device_type === 'mobile') {
                    if (!preg_match('/viewport/i', $result['body'])) {
                        logMessage("ОТСУТСТВУЕТ meta viewport", $logFile, true);
                    }
                }
                
                // Проверка изображений недвижимости
                if (preg_match_all('/\/files\/[^"\']+\.(jpg|jpeg|png|gif)/i', $result['body'], $property_images)) {
                    logMessage("Найдено изображений недвижимости: " . count($property_images[0]), $logFile);
                    
                    // Проверяем первое изображение недвижимости
                    if (!empty($property_images[0])) {
                        $first_property_image = $property_images[0][0];
                        if (strpos($first_property_image, '/') === 0) {
                            $full_image_url = parse_url($url, PHP_URL_SCHEME) . '://' . parse_url($url, PHP_URL_HOST) . $first_property_image;
                        } else {
                            $full_image_url = $first_property_image;
                        }
                        
                        $img_check = analyzeHttpResponse($full_image_url, $userAgent);
                        if ($img_check['http_code'] >= 400) {
                            logMessage("ОШИБКА: Изображение недвижимости недоступно: {$img_check['http_code']}", $logFile, true);
                        } else {
                            logMessage("Изображение недвижимости загружается корректно", $logFile);
                        }
                    }
                }
            }
            
            // Анализ заголовков ответа
            if (preg_match('/set-cookie/i', $result['headers'])) {
                logMessage("ПРЕДУПРЕЖДЕНИЕ: Устанавливаются cookies (может мешать кэшированию)", $logFile, true);
            }
            
            // Пауза между попытками
            if ($attempt < 2) sleep(2);
        }
    }
}

// 3. Специальная проверка ресурсов мобильной версии
logMessage("\n=== ПРОВЕРКА МОБИЛЬНЫХ РЕСУРСОВ ===", $logFile);

$mobile_resources = [
    'https://indreamsphuket.ru/sites/all/themes/phuket/css/mobile.css',
    'https://indreamsphuket.com/sites/all/themes/phuket/css/mobile.css',
    'https://indreamsphuket.ru/sites/all/modules/__s45/s45_base/res/js/Api.js'
];

foreach ($mobile_resources as $resource_url) {
    logMessage("\nПроверка: $resource_url", $logFile);
    $result = analyzeHttpResponse($resource_url, 'mobile');
    
    logMessage("HTTP код: {$result['http_code']}", $logFile, $result['http_code'] >= 400);
    logMessage("Размер: " . number_format($result['response_size']) . " байт", $logFile);
    
    if (!empty($result['error'])) {
        logMessage("ОШИБКА: {$result['error']}", $logFile, true);
    }
}

// 4. Итоговая статистика
logMessage("\n=== ИТОГОВАЯ СТАТИСТИКА ===", $logFile);
logMessage("Общее количество ошибок: $errorCount", $logFile, $errorCount > 0);
logMessage("Время завершения: " . date('Y-m-d H:i:s'), $logFile);

if ($errorCount > 0) {
    logMessage("\n=== РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ ===", $logFile);
    logMessage("1. Проверить логи Apache/Nginx на наличие ошибок 404/500", $logFile);
    logMessage("2. Убедиться что все изображения доступны", $logFile);
    logMessage("3. Проверить JavaScript консоль в браузере", $logFile);
    logMessage("4. Оптимизировать медленные SQL запросы", $logFile);
    logMessage("5. Настроить правильное кэширование", $logFile);
}

echo "\n=== АНАЛИЗ ЗАВЕРШЕН ===\n";
echo "Обнаружено ошибок: $errorCount\n";
echo "Детальный отчет: $logFile\n";
?> 