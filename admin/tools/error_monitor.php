<?php
/**
 * Простой мониторинг ошибок сайта
 * Анализирует основные лог-файлы и выводит статистику
 */

$logFile = __DIR__ . '/../logs/error_summary_' . date('Y-m-d_H-i-s') . '.txt';

function logMessage($message, $file) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    echo $logEntry;
    file_put_contents($file, $logEntry, FILE_APPEND | LOCK_EX);
}

function analyzeLogFile($path, $description, $errorPatterns = []) {
    if (!file_exists($path) || !is_readable($path)) {
        return ['status' => 'not_found', 'errors' => 0];
    }
    
    $fileSize = filesize($path);
    $lastModified = filemtime($path);
    
    // Читаем последние 50 строк
    $lines = [];
    $handle = fopen($path, 'r');
    if ($handle) {
        fseek($handle, max(0, $fileSize - 8192)); // Читаем последние ~8KB
        while (($line = fgets($handle)) !== false) {
            $lines[] = $line;
        }
        fclose($handle);
    }
    
    $recentLines = array_slice($lines, -50);
    $errorCount = 0;
    $recentErrors = [];
    
    foreach ($recentLines as $line) {
        foreach ($errorPatterns as $pattern) {
            if (stripos($line, $pattern) !== false) {
                $errorCount++;
                $recentErrors[] = trim($line);
                break;
            }
        }
    }
    
    return [
        'status' => 'ok',
        'size_mb' => round($fileSize / 1024 / 1024, 2),
        'last_modified' => date('Y-m-d H:i:s', $lastModified),
        'errors' => $errorCount,
        'recent_errors' => array_slice($recentErrors, -5) // Последние 5 ошибок
    ];
}

logMessage("=== МОНИТОРИНГ ОШИБОК САЙТА ===", $logFile);

// Анализируем основные лог-файлы
$logs = [
    'PHP Errors' => [
        'path' => '/var/log/php_errors.log',
        'patterns' => ['Fatal error', 'Warning', 'Notice', 'Parse error']
    ],
    'Nginx Errors' => [
        'path' => '/var/log/nginx/error.log',
        'patterns' => ['error', 'crit', 'alert', 'emerg']
    ],
    'Apache Errors' => [
        'path' => '/var/log/httpd/error_log',
        'patterns' => ['error', 'warn', 'crit']
    ],
    'MySQL Slow Queries' => [
        'path' => '/var/log/mariadb/slow-query.log',
        'patterns' => ['Query_time']
    ],
    'Drupal Site Logs' => [
        'path' => __DIR__ . '/../logs/cache_maintenance.log',
        'patterns' => ['ERROR', 'WARNING', 'Notice']
    ]
];

$totalErrors = 0;
foreach ($logs as $name => $config) {
    logMessage("\n=== $name ===", $logFile);
    $analysis = analyzeLogFile($config['path'], $name, $config['patterns']);
    
    if ($analysis['status'] === 'not_found') {
        logMessage("Файл не найден: {$config['path']}", $logFile);
        continue;
    }
    
    logMessage("Размер: {$analysis['size_mb']} MB", $logFile);
    logMessage("Обновлен: {$analysis['last_modified']}", $logFile);
    logMessage("Ошибок в последних записях: {$analysis['errors']}", $logFile);
    
    $totalErrors += $analysis['errors'];
    
    if (!empty($analysis['recent_errors'])) {
        logMessage("Последние ошибки:", $logFile);
        foreach ($analysis['recent_errors'] as $error) {
            logMessage("  • " . substr($error, 0, 100) . "...", $logFile);
        }
    }
}

// Проверяем системные ресурсы
logMessage("\n=== СИСТЕМНЫЕ РЕСУРСЫ ===", $logFile);

// Load average
if (file_exists('/proc/loadavg')) {
    $loadavg = trim(file_get_contents('/proc/loadavg'));
    logMessage("Load Average: $loadavg", $logFile);
}

// Память
if (file_exists('/proc/meminfo')) {
    $meminfo = file_get_contents('/proc/meminfo');
    preg_match_all('/^(\w+):\s+(\d+)\s+kB$/m', $meminfo, $matches);
    $memory = array_combine($matches[1], $matches[2]);
    
    $total = $memory['MemTotal'];
    $available = $memory['MemAvailable'];
    $used_percent = round((($total - $available) / $total) * 100, 1);
    
    logMessage("Память: {$used_percent}% использовано", $logFile);
}

// Дисковое пространство
$disk_usage = shell_exec('df -h / | tail -1');
if ($disk_usage) {
    logMessage("Диск: " . trim($disk_usage), $logFile);
}

// Процессы
exec('ps aux | grep -E "(apache2|httpd)" | grep -v grep | wc -l', $apache_count);
exec('ps aux | grep mysql | grep -v grep | wc -l', $mysql_count);
logMessage("Apache процессов: " . intval($apache_count[0]), $logFile);
logMessage("MySQL процессов: " . intval($mysql_count[0]), $logFile);

// Итоговая статистика
logMessage("\n=== ИТОГО ===", $logFile);
logMessage("Всего ошибок в логах: $totalErrors", $logFile);

if ($totalErrors > 20) {
    logMessage("🔴 КРИТИЧНО: Много ошибок в логах!", $logFile);
} elseif ($totalErrors > 5) {
    logMessage("🟡 ВНИМАНИЕ: Есть ошибки, требуют проверки", $logFile);
} else {
    logMessage("🟢 ХОРОШО: Мало ошибок в логах", $logFile);
}

logMessage("Отчет сохранен: $logFile", $logFile);

echo "\n=== РЕКОМЕНДАЦИИ ПО МОНИТОРИНГУ ===\n";
echo "1. Grafana + Prometheus - мощный, но сложный\n";
echo "2. Netdata - простой, красивый, из коробки\n";
echo "3. Zabbix - профессиональный, средней сложности\n";
echo "4. Uptime Kuma - очень простой, для базового мониторинга\n";
echo "5. Логи в Telegram - самый простой вариант\n";
?> 