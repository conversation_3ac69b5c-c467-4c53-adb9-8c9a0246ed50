<?php
/**
 * Детальный анализ производительности IndreamsPhuket.ru и IndreamsPhuket.com
 * 
 * Тестирует производительность главных страниц, анализирует системные ресурсы,
 * проверяет конфигурацию PHP и веб-сервера
 */

// Конфигурация
$domains = [
    'indreamsphuket.ru' => [
        'http' => 'http://indreamsphuket.ru',
        'https' => 'https://indreamsphuket.ru'
    ],
    'indreamsphuket.com' => [
        'http' => 'http://indreamsphuket.com',
        'https' => 'https://indreamsphuket.com'
    ]
];

$logFile = __DIR__ . '/../logs/performance_report_' . date('Y-m-d_H-i-s') . '.txt';

/**
 * Записывает сообщение в лог и выводит на экран
 */
function logMessage($message, $file) {
    $timestamp = date('Y-m-d H:i:s');
    $logEntry = "[$timestamp] $message\n";
    echo $logEntry;
    file_put_contents($file, $logEntry, FILE_APPEND | LOCK_EX);
}

/**
 * Получает время выполнения HTTP запроса с детальной статистикой
 */
function getHttpPerformance($url) {
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_USERAGENT => 'Performance Monitor/1.0',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_HEADER => true,
        CURLOPT_NOBODY => false
    ]);
    
    $start = microtime(true);
    $response = curl_exec($ch);
    $end = microtime(true);
    
    $info = curl_getinfo($ch);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'url' => $url,
        'total_time' => round($end - $start, 3),
        'connect_time' => round($info['connect_time'], 3),
        'namelookup_time' => round($info['namelookup_time'], 3),
        'pretransfer_time' => round($info['pretransfer_time'], 3),
        'starttransfer_time' => round($info['starttransfer_time'], 3),
        'redirect_time' => round($info['redirect_time'], 3),
        'http_code' => $info['http_code'],
        'size_download' => $info['size_download'],
        'speed_download' => round($info['speed_download']),
        'redirect_count' => $info['redirect_count'],
        'error' => $error,
        'response_size' => strlen($response)
    ];
}

/**
 * Проверяет системные ресурсы
 */
function getSystemResources() {
    $stats = [];
    
    // Загрузка системы
    if (file_exists('/proc/loadavg')) {
        $loadavg = file_get_contents('/proc/loadavg');
        $stats['load_average'] = trim($loadavg);
    }
    
    // Использование памяти
    if (file_exists('/proc/meminfo')) {
        $meminfo = file_get_contents('/proc/meminfo');
        preg_match_all('/^(\w+):\s+(\d+)\s+kB$/m', $meminfo, $matches);
        $memory = array_combine($matches[1], $matches[2]);
        
        $stats['memory_total'] = round($memory['MemTotal'] / 1024, 2) . ' MB';
        $stats['memory_available'] = round($memory['MemAvailable'] / 1024, 2) . ' MB';
        $stats['memory_used'] = round(($memory['MemTotal'] - $memory['MemAvailable']) / 1024, 2) . ' MB';
        $stats['memory_usage_percent'] = round((($memory['MemTotal'] - $memory['MemAvailable']) / $memory['MemTotal']) * 100, 1) . '%';
    }
    
    // Процессы Apache и MySQL
    $processes = [];
    exec('ps aux | grep -E "(apache2|httpd)" | grep -v grep', $apache_processes);
    exec('ps aux | grep mysql | grep -v grep', $mysql_processes);
    
    $stats['apache_processes'] = count($apache_processes);
    $stats['mysql_processes'] = count($mysql_processes);
    
    // Детали процессов
    if (!empty($apache_processes)) {
        $stats['apache_details'] = array_slice($apache_processes, 0, 3); // Показываем первые 3
    }
    if (!empty($mysql_processes)) {
        $stats['mysql_details'] = $mysql_processes;
    }
    
    return $stats;
}

/**
 * Получает конфигурацию PHP
 */
function getPhpConfig() {
    return [
        'version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'opcache_enabled' => extension_loaded('opcache') ? 'Yes' : 'No',
        'extensions' => [
            'curl' => extension_loaded('curl') ? 'Yes' : 'No',
            'gd' => extension_loaded('gd') ? 'Yes' : 'No',
            'mysql' => extension_loaded('mysql') ? 'Yes' : 'No',
            'mysqli' => extension_loaded('mysqli') ? 'Yes' : 'No',
            'pdo' => extension_loaded('pdo') ? 'Yes' : 'No'
        ]
    ];
}

/**
 * Проверяет доступность логов
 */
function checkLogAvailability() {
    $logs = [
        'apache_error' => [
            '/var/log/apache2/error.log',
            '/var/log/httpd/error_log',
            '/usr/local/apache/logs/error_log'
        ],
        'apache_access' => [
            '/var/log/apache2/access.log',
            '/var/log/httpd/access_log',
            '/usr/local/apache/logs/access_log'
        ],
        'nginx_error' => [
            '/var/log/nginx/error.log'
        ],
        'nginx_access' => [
            '/var/log/nginx/access.log'
        ],
        'php_error' => [
            '/var/log/php_errors.log',
            '/var/log/php/error.log'
        ]
    ];
    
    $available_logs = [];
    foreach ($logs as $type => $paths) {
        foreach ($paths as $path) {
            if (file_exists($path) && is_readable($path)) {
                $available_logs[$type] = $path;
                break;
            }
        }
    }
    
    return $available_logs;
}

/**
 * Получает последние записи из лога
 */
function getRecentLogEntries($logPath, $lines = 10) {
    if (!file_exists($logPath) || !is_readable($logPath)) {
        return "Лог недоступен: $logPath";
    }
    
    $command = "tail -n $lines " . escapeshellarg($logPath);
    return shell_exec($command);
}

// Начинаем тестирование
logMessage("=== НАЧАЛО ДЕТАЛЬНОГО АНАЛИЗА ПРОИЗВОДИТЕЛЬНОСТИ ===", $logFile);
logMessage("Время запуска: " . date('Y-m-d H:i:s'), $logFile);

// 1. Системные ресурсы
logMessage("\n=== СИСТЕМНЫЕ РЕСУРСЫ ===", $logFile);
$systemStats = getSystemResources();
foreach ($systemStats as $key => $value) {
    if (is_array($value)) {
        logMessage("$key:", $logFile);
        foreach ($value as $item) {
            logMessage("  - $item", $logFile);
        }
    } else {
        logMessage("$key: $value", $logFile);
    }
}

// 2. Конфигурация PHP
logMessage("\n=== КОНФИГУРАЦИЯ PHP ===", $logFile);
$phpConfig = getPhpConfig();
foreach ($phpConfig as $key => $value) {
    if (is_array($value)) {
        logMessage("$key:", $logFile);
        foreach ($value as $subKey => $subValue) {
            logMessage("  $subKey: $subValue", $logFile);
        }
    } else {
        logMessage("$key: $value", $logFile);
    }
}

// 3. Тестирование производительности доменов
logMessage("\n=== ТЕСТИРОВАНИЕ ПРОИЗВОДИТЕЛЬНОСТИ ДОМЕНОВ ===", $logFile);

foreach ($domains as $domain => $urls) {
    logMessage("\n--- Тестирование $domain ---", $logFile);
    
    foreach ($urls as $protocol => $url) {
        logMessage("\nТестирование $protocol://$domain", $logFile);
        
        // Выполняем 3 запроса для получения средних значений
        $results = [];
        for ($i = 1; $i <= 3; $i++) {
            logMessage("Запрос #$i...", $logFile);
            $result = getHttpPerformance($url);
            $results[] = $result;
            
            logMessage("  HTTP код: {$result['http_code']}", $logFile);
            logMessage("  Общее время: {$result['total_time']}s", $logFile);
            logMessage("  Время подключения: {$result['connect_time']}s", $logFile);
            logMessage("  Время до первого байта: {$result['starttransfer_time']}s", $logFile);
            logMessage("  Размер ответа: " . number_format($result['response_size']) . " байт", $logFile);
            logMessage("  Скорость загрузки: " . number_format($result['speed_download']) . " байт/с", $logFile);
            
            if (!empty($result['error'])) {
                logMessage("  ОШИБКА: {$result['error']}", $logFile);
            }
            
            // Пауза между запросами
            if ($i < 3) sleep(2);
        }
        
        // Вычисляем средние значения
        $avgTotalTime = array_sum(array_column($results, 'total_time')) / count($results);
        $avgStartTransfer = array_sum(array_column($results, 'starttransfer_time')) / count($results);
        $avgSize = array_sum(array_column($results, 'response_size')) / count($results);
        
        logMessage("\n  СРЕДНИЕ ЗНАЧЕНИЯ:", $logFile);
        logMessage("  Среднее общее время: " . round($avgTotalTime, 3) . "s", $logFile);
        logMessage("  Среднее время до первого байта: " . round($avgStartTransfer, 3) . "s", $logFile);
        logMessage("  Средний размер ответа: " . number_format($avgSize) . " байт", $logFile);
    }
}

// 4. Проверка доступности логов
logMessage("\n=== ДОСТУПНЫЕ ЛОГИ ===", $logFile);
$availableLogs = checkLogAvailability();
if (empty($availableLogs)) {
    logMessage("Системные логи недоступны для чтения", $logFile);
} else {
    foreach ($availableLogs as $type => $path) {
        logMessage("$type: $path", $logFile);
    }
}

// 5. Анализ последних записей в логах
logMessage("\n=== ПОСЛЕДНИЕ ЗАПИСИ В ЛОГАХ ===", $logFile);
foreach ($availableLogs as $type => $path) {
    logMessage("\n--- $type ($path) ---", $logFile);
    $recentEntries = getRecentLogEntries($path, 5);
    if (!empty($recentEntries)) {
        logMessage($recentEntries, $logFile);
    } else {
        logMessage("Нет записей или лог недоступен", $logFile);
    }
}

// 6. Дополнительные проверки
logMessage("\n=== ДОПОЛНИТЕЛЬНЫЕ ПРОВЕРКИ ===", $logFile);

// Проверка места на диске
$diskUsage = shell_exec('df -h / 2>/dev/null');
if ($diskUsage) {
    logMessage("Использование диска:", $logFile);
    logMessage($diskUsage, $logFile);
}

// Проверка времени сервера
logMessage("Время сервера: " . date('Y-m-d H:i:s T'), $logFile);
logMessage("Timezone: " . date_default_timezone_get(), $logFile);

// Проверка статуса веб-сервисов
$services = ['apache2', 'nginx', 'mysql'];
foreach ($services as $service) {
    $status = shell_exec("systemctl is-active $service 2>/dev/null");
    if ($status) {
        logMessage("Статус $service: " . trim($status), $logFile);
    }
}

logMessage("\n=== АНАЛИЗ ЗАВЕРШЕН ===", $logFile);
logMessage("Время завершения: " . date('Y-m-d H:i:s'), $logFile);
logMessage("Лог сохранен в: $logFile", $logFile);

echo "\n=== КРАТКИЙ ОТЧЕТ ===\n";
echo "Детальный отчет сохранен в: $logFile\n";
echo "Для просмотра: cat $logFile\n";
?> 