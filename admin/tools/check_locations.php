<?php
define('DRUPAL_ROOT', getcwd());

$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

$output_file = 'property_locations.txt';
$handle = fopen($output_file, 'w');

$query = db_select('_phuket_Property', 'p')
    ->fields('p')
    ->condition('published', 1)
    ->condition('isSaled', 0)
    ->condition('number', 5000, '>=')
    ->condition('dealType', 'sale');

$result = $query->execute();

fwrite($handle, "=== Анализ локаций объектов ===\n\n");

while ($row = $result->fetch()) {
    $propertyDto = unserialize($row->propertyDto);
    
    $output = sprintf(
        "ID: %s\n" .
        "Main location: %s\n" .
        "Sub location: %s\n" .
        "Price: %s THB\n" .
        "Bedrooms: %s\n" .
        "Area: %s m²\n" .
        "Distance to sea: %s m\n" .
        "Available fields: %s\n" .
        "-------------------\n\n",
        $propertyDto->number,
        $propertyDto->re_locality->name->en,
        $propertyDto->re_subLocality->name->en,
        number_format($row->price_sale),
        $propertyDto->bedrooms,
        $propertyDto->interior_size,
        $propertyDto->distance_to_sea,
        implode(", ", array_keys(get_object_vars($propertyDto)))
    );
    
    fwrite($handle, $output);
}

fclose($handle);
echo "Анализ сохранен в файл: $output_file\n";
