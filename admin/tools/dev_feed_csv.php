<?php

/**
 * @file
 *
 * <AUTHOR>
 * e-mail: <EMAIL>
 * site: http://bocharov.pw/
 * drupal.org: https://www.drupal.org/u/jkey
 * kwork.ru: https://kwork.ru/user/loopback
 * Telegram: https://t.me/IfThenTrue
 * Date: 19.10.2022
 *
 *  Выгрузка объектов недвижимости для FaceBook feed commerce
 * $feed_lang
 * $dealType  - передается парамтром скрипту [rent|sale] если ничего не выбранно
 * тогда выгрузит все в один файл.
 * Иначе будет произведена выгрузка только конкретного типа
 *
 *
 * Документация по полям для ФБ
 *
 * @link https://developers.facebook.com/docs/marketing-api/real-estate-ads/get-started#home-listing-fields
 */



/**
 * Root directory of Drupal installation.
 */
define('DRUPAL_ROOT', getcwd());

// Обязательно надо задать . Иначе кривизна самописа не позволит получить внешнинй алиас страницы.
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';

//  Дополнение , для ссылки на картинку. Инаце пц.
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);
require_once DRUPAL_ROOT . '/FBFeedCsv.php';


if (isset($argc) && $argc == 2 ){
  $dealType = $argv[1];
}

$csv_feed_path = 'fb_feed_csv_28a56914db63e7bc9b77ba0a705531e1.csv';
if (!is_null($dealType)) {
  $csv_feed_path = 'fb_feed_csv_' . $dealType . '_28a56914db63e7bc9b77ba0a705531e1.csv';
}

$csvfeed = new FBFeedCsv(DRUPAL_ROOT . '/' . $csv_feed_path);

switch ($dealType) {
  case 'rent':
    $csvfeed->createFeed_rent();
    break;
  case 'sale':
    $csvfeed->createFeed_sale();
    break;
}

drupal_exit();
