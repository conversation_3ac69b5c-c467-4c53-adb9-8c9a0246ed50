<?php
/**
 * Check Sitemap Accessibility for Search Engines
 *
 * This script tests your sitemaps to verify they are accessible to search engines.
 * It simulates requests from Google, Bing, and other search engines and reports the results.
 *
 * <AUTHOR> Phuket team
 */

// Configuration: specify your domains and sitemap paths
$sitemaps = [
    'https://indreamsphuket.com/sitemap.xml',
    'https://indreamsphuket.com/sitemap_en.xml',
    'https://indreamsphuket.com/sitemap_ru.xml',
    'https://indreamsphuket.ru/sitemap.xml',
    'https://th.indreamsphuket.com/sitemap.xml',
    'https://ch.indreamsphuket.com/sitemap.xml'
];

// Search engine user agents to test
$userAgents = [
    'Google' => 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
    'Bing' => 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)',
    'Yandex' => 'Mozilla/5.0 (compatible; YandexBot/3.0; +http://yandex.com/bots)',
    'Baidu' => 'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)',
    'Normal Browser' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
];

echo "===================================================================\n";
echo "  SITEMAP ACCESSIBILITY TEST FOR SEARCH ENGINES\n";
echo "===================================================================\n\n";

$results = [];
$anyFailed = false;

// Test each sitemap with each user agent
foreach ($sitemaps as $sitemapUrl) {
    echo "Testing sitemap: $sitemapUrl\n";
    echo str_repeat('-', strlen("Testing sitemap: $sitemapUrl")) . "\n";
    
    $results[$sitemapUrl] = [];
    
    foreach ($userAgents as $engine => $userAgent) {
        echo "  As $engine: ";
        
        $result = testSitemapAccess($sitemapUrl, $userAgent);
        $results[$sitemapUrl][$engine] = $result;
        
        if ($result['successful']) {
            echo "SUCCESS (HTTP {$result['http_code']})\n";
        } else {
            echo "FAILED (HTTP {$result['http_code']})\n";
            echo "  Error: {$result['error']}\n";
            $anyFailed = true;
        }
    }
    
    echo "\n";
}

echo "===================================================================\n";
echo "  SUMMARY REPORT\n";
echo "===================================================================\n\n";

// Generate summary report
foreach ($sitemaps as $sitemapUrl) {
    $allSuccess = true;
    $failedEngines = [];
    
    foreach ($userAgents as $engine => $userAgent) {
        if (!$results[$sitemapUrl][$engine]['successful']) {
            $allSuccess = false;
            $failedEngines[] = $engine;
        }
    }
    
    if ($allSuccess) {
        echo "✅ $sitemapUrl: Accessible to all search engines\n";
    } else {
        echo "❌ $sitemapUrl: Not accessible to: " . implode(', ', $failedEngines) . "\n";
    }
}

echo "\n";

// Recommendations based on results
if ($anyFailed) {
    echo "===================================================================\n";
    echo "  RECOMMENDATIONS\n";
    echo "===================================================================\n\n";
    
    echo "1. Verify Cloudflare settings:\n";
    echo "   - Check that the firewall rules were created successfully\n";
    echo "   - Verify the page rules for sitemap bypass are active\n";
    echo "   - Ensure Bot Fight Mode is disabled or configured to allow search engines\n\n";
    
    echo "2. Check robots.txt:\n";
    echo "   - Ensure your robots.txt file doesn't block access to sitemaps\n";
    echo "   - Verify sitemap declarations in robots.txt\n\n";
    
    echo "3. Check server configuration:\n";
    echo "   - Verify .htaccess rules don't block bot access\n";
    echo "   - Check server logs for any 403 or 5xx errors\n\n";
    
    echo "4. Manual verification:\n";
    echo "   - Submit sitemaps directly in Google Search Console\n";
    echo "   - Use the URL Inspection tool to check individual URLs\n\n";
}

echo "For Cloudflare settings, run cloudflare_setup_sitemap_rule.php to apply fixes.\n\n";

/**
 * Test accessibility of a sitemap URL with a specific user agent
 *
 * @param string $url The sitemap URL to test
 * @param string $userAgent The user agent string to use
 * @return array Test result with status, HTTP code, and error message if any
 */
function testSitemapAccess($url, $userAgent) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $isXml = false;
    if (!empty($response)) {
        // Check if the response is XML (contains XML declaration or urlset tag)
        $isXml = preg_match('/<\?xml|<urlset|<sitemapindex/i', $response) === 1;
    }
    
    return [
        'successful' => ($httpCode >= 200 && $httpCode < 300 && $isXml),
        'http_code' => $httpCode,
        'is_xml' => $isXml,
        'error' => $error ?: ($httpCode >= 400 ? "HTTP error $httpCode" : ($isXml ? "" : "Response is not valid XML"))
    ];
} 