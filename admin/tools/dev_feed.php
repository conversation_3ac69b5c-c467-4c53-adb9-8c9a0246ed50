<?php

/**
 * @file
 *
 * <AUTHOR>
 * e-mail: <EMAIL>
 * site: http://bocharov.pw/
 * drupal.org: https://www.drupal.org/u/jkey
 * kwork.ru: https://kwork.ru/user/loopback
 * Telegram: https://t.me/IfThenTrue
 * Date: 19.10.2022
 *
 *  Выгрузка объектов недвижимости для FaceBook feed commerce
 * $feed_lang
 * $dealType  - передается парамтром скрипту [rent|sale] если ничего не выбранно
 * тогда выгрузит все в один файл.
 * Иначе будет произведена выгрузка только конкретного типа
 *
 *
 * Документация по полям для ФБ
 *
 * @link https://developers.facebook.com/docs/marketing-api/real-estate-ads/get-started#home-listing-fields
 */

use Site45\Sets\Phuket\Query\OptionSearch\PhuketOptionQuery;
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyDto;

/**
 * Root directory of Drupal installation.
 */
define('DRUPAL_ROOT', getcwd());

// Обязательно надо задат . Иначе кривизна самописа не позволит получить внешнинй алиас страницы.
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';

//  Дополнение , для ссылки на картинку. Инаце пц.
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

$feed_lang = 'en';
$dealType = NULL; // rent | sale

$title_dealType = ''; // Для заголвка XML
$xml_feed_path = 'fb_feed_a9c4ded340b43efff7e5ca2b6dacdfd4.xml';

if (isset($argc) && $argc == 2 ){
  $dealType = $argv[1];
}

if (!is_null($dealType)) {
  $xml_feed_path = 'fb_feed_' . $dealType . '_a9c4ded340b43efff7e5ca2b6dacdfd4.xml';
}

//  Выгребаем объекты недвижимости аренду и на продажу, которые не проданны.
$query = db_select('_phuket_Property', 'p')
  ->fields('p')
  ->condition('published', 1)
  ->condition('isSaled', 0);

if (!is_null($dealType)) {
  $query->condition('dealType', $dealType);
}
//$query->range(0,5);
// !АХТУНГ т.к. в процессе пуско-наладки выяснилось что скрипту не хватает
// памяти на проде чтобы все перрваривать скопом. поэтому переключено на
// последовательное чтение записей из БД
//$db_result = $query->execute()->fetchAll();
$db_result = $query->execute();

switch ($dealType) {
  case 'rent':
    $title_dealType = 'Objects rent';
    break;
  case 'sale':
    $title_dealType = 'Objects sale';
    break;
}

// Извращаться через либу XML небудем формат примтивный смысла нет.
$xml_head = '<?xml version="1.0" encoding="utf-8"?>' . PHP_EOL;
$xml_head .= '<listings>' . PHP_EOL;
$xml_head .= '<title>indreamsphuket.com (en) feed ' . $title_dealType . '</title>' . PHP_EOL;
$xml_head .= '<link rel="self" href="' . $GLOBALS['base_url'] . '"/>' . PHP_EOL;
$xml_footer = '</listings>' . PHP_EOL;

// Создаем снова новый файл. Старый грохаем.
file_put_contents($xml_feed_path, $xml_head);
$xml_listings = '';

while ($row = $db_result->fetch()) {
  //foreach ($db_result as $row) {

  $name = unserialize($row->name);
  $propertyTeaserDto = unserialize($row->propertyTeaserDto);
  $propertyDto = unserialize($row->propertyDto);
  //  $propertyDto2 = PhuketPropertyDto::create(@unserialize($row->propertyDto));
  //  PhuketOptionQuery::create()->load($propertyDto->re_country);
  $dbg = 0;


  $feed_price = 0.0;
  $feed_availability = 'unknow';
  //  тут анализируем какой тии продажа или аренда и в зависмости от этого грузим цену.
  //  Supported values are: for_sale, for_rent, sale_pending, recently_sold, off_market, available_soon. For
  switch ($row->dealType) {
    case 'rent':
      $feed_price = $row->price_rent;
      $feed_availability = 'for_rent';
      break;
    case 'sale':
      $feed_price = $row->price_sale;
      $feed_availability = 'for_sale';
      break;
  }


  // @docs facebook
  // $feed_property_type Optional for Advantage+ catalog ads.
  // Type of property. Supported values for Advantage+ catalog ads:
  // apartment, condo, house, land, manufactured, other,
  // townhouse.
  //
  // Supported values for commerce:
  // apartment, builder_floor, condo, house, house_in_condominium,
  // house_in_villa, loft, penthouse, studio, townhouse, other.

  // Варианты которые есть в БД:
  // villa apartment townhouse penthouse (null) hotel
  // Маппинг типов Сайта и ФБ
  switch ($row->propertyType) {
    case 'villa':
      $feed_property_type = 'house_in_villa'; // villa
      break;
    case 'hotel':
    case NULL:
      $feed_property_type = 'other';
      break;

    default :
      $feed_property_type = $row->propertyType; // villa
  }


  // Required for Advantage+ catalog ads and commerce.
  // Если нету у недвижки координат тогда выкидываем из выгрузки. т.к.
  // ФБ ругается на это и не грузит
  $feed_lat = $row->lat;
  $feed_lng = $row->lng;
  if (is_null($feed_lat) || $feed_lat == 0 || is_null($feed_lng) || $feed_lng == 0) {
    continue;
  }

  $feed_baths = $propertyDto->bathrooms;
  $feed_beds = $propertyDto->bedrooms;
  $feed_units = 1;

  $sysPath = 'property/' . $propertyDto->id;
  // $alias = Path::create()->getAlias($sysPath);

  // Получить внешню ссылку на страницу объекта недвижимости.
  // Дергаем функцию самописа
  $page_alias = s45_path_url('property/' . $propertyDto->id);
  // [ID DTO] -
  $feed_id = $propertyDto->number; // 318
  $feed_id2 = $propertyDto->id; // 5ec23687-F825-F7BA-0C34-PhuketProper

  $feed_title = $propertyDto->name->{$feed_lang};

  if (!empty($propertyDto->description->{$feed_lang})) {
    $feed_description = $propertyDto->description->{$feed_lang};  // ru/ en/ zh-hans/ th/
  }
  else {
    $feed_description = $propertyDto->project->text->{$feed_lang};
  }

  $feed_link = $page_alias;
  $fb_condition = '';

  $feed_builtYear = $propertyDto->re_builtYear;
  if (!is_null($feed_builtYear) && $feed_builtYear != '') {
    $dbg = 0;
    if($feed_builtYear > date('Y')){
      $feed_builtYear = NULL;
    }
  }
  else {
    $feed_builtYear = NULL;
  }

  // Тут бардак. Как получить картинку.
  //$photoDto = $propertyDto->photos[0];
  $xml_images = '';

  $photoDto = $propertyDto->photos[0];
  $img_url = s45_imgSrcR($photoDto, S45_IMST_1900X1000_SCALE);
  $fb_image_link = $img_url; //  $propertyDto->photos[0]->(id/name/size/dir/mine)

  $xml_img = '<image>' . PHP_EOL;
  $xml_img .= '<url>' . $fb_image_link . '</url>' . PHP_EOL;
  //  $xml_listing .=     '<tag>Gym</tag>'. PHP_EOL;
  $xml_img .= '</image>' . PHP_EOL;
  $xml_images .= $xml_img . PHP_EOL;


  $feed_country = $propertyDto->re_country->name->{$feed_lang};

  if (is_null($feed_country) || $feed_country == '') {
    $feed_country = 'Thailand';
  }

  $feed_region = $propertyDto->re_locality->name->{$feed_lang};
  $feed_city = $propertyDto->re_subLocality->name->{$feed_lang};


  if (is_null($feed_country) || $feed_country == '') {
    $dbg = 0;
  }

  if (is_null($feed_city) || $feed_city == '') {
    $dbg = 0;
  }

  if (is_null($feed_region) || $feed_region == '') {
    $dbg = 0;
  }


  // ------------ XML listing create
  $xml_listing = '<listing>' . PHP_EOL;
  // т.к. в данных бардак то юзать придеться
  $xml_listing .= '<home_listing_id>' . $feed_id . '</home_listing_id>' . PHP_EOL;  // !Required
  $xml_listing .= '<availability>' . $feed_availability . '</availability>' . PHP_EOL; // !Required
  $xml_listing .= '<property_type>' . $feed_property_type . '</property_type>' . PHP_EOL;
  $xml_listing .= '<url>' . $feed_link . '</url>' . PHP_EOL;
  $xml_listing .= '<name> <![CDATA[ ' . $feed_title . ' ]]></name>' . PHP_EOL;
  $xml_listing .= '<price>' . $feed_price . ' THB</price>' . PHP_EOL;
  //  Это ФБ не понимает
  //   $xml_listing .= '<description> <![CDATA[ ' . $feed_description . ' ]]></description>' . PHP_EOL;
  $xml_listing .= '<description> <![CDATA[ ' . drupal_html_to_text($feed_description) . ' ]]></description>' . PHP_EOL;

  if ($feed_baths > 0) {
    $xml_listing .= '<num_baths>' . $feed_baths . '</num_baths>' . PHP_EOL;
  }

  if ($feed_beds > 0) {
    $xml_listing .= '<num_beds>' . $feed_beds . '</num_beds>' . PHP_EOL;
  }

  if (!is_null($feed_builtYear)) {
    $xml_listing .= '<year_built>' . $feed_builtYear . '</year_built>' . PHP_EOL;
  }

  //  $xml_listing .= '<num_units>' . $feed_units . '</num_units>';
  $xml_listing .= '<latitude>' . $feed_lat . '</latitude>' . PHP_EOL;           // !Required for Advantage+ catalog ads and commerce.
  $xml_listing .= '<longitude>' . $feed_lng . '</longitude>' . PHP_EOL;         // !Required for Advantage+ catalog ads and commerce.
  // $xml_listing .=  '<neighborhood>Palo Alto</neighborhood>'. PHP_EOL;
  $xml_listing .= $xml_images;                                                  // !Required for Advantage+ catalog ads and commerce.
  // ADRESSS start
  $xml_listing .= '<address format="simple">' . PHP_EOL;

  //  $xml_listing .= '<component name="addr1">675 El Camino Real</component>' . PHP_EOL; // !Required
  //  $xml_listing .= '<component name="addr2">Apt 1</component>' . PHP_EOL;
  //  $xml_listing .= '<component name="addr3">Downstairs</component>' . PHP_EOL;
  //  $xml_listing .= '<component name="unit_number">1223</component>' . PHP_EOL;
  $xml_listing .= '<component name="city">' . $feed_city . '</component>' . PHP_EOL;  // !Required
  //$xml_listing .= '<component name="city_id">12345</component>' . PHP_EOL;
  $xml_listing .= '<component name="region">' . $feed_region . '</component>' . PHP_EOL; // !Required
  //$xml_listing .= '<component name="province"/>' . PHP_EOL;
  //  $xml_listing .= '<component name="postal_code">12345</component>' . PHP_EOL; // !Required for Advantage+ catalog ads and commerce.
  $xml_listing .= '<component name="country">' . $feed_country . '</component>' . PHP_EOL; // !Required
  $xml_listing .= '</address>' . PHP_EOL;
  // ADRES END

  $xml_listing .= '</listing>' . PHP_EOL;

  // $xml_listings .= $xml_listing;
  file_put_contents($xml_feed_path, $xml_listing, FILE_APPEND);
  unset($xml_listing);
}

//$xml_feed = $xml_head . $xml_listings . $xml_footer;
file_put_contents($xml_feed_path, $xml_footer, FILE_APPEND);

echo 'Finished create xlm feed file' . PHP_EOL;
drupal_exit();
