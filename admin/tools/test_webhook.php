<?php
// Включаем вывод ошибок для отладки
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Записываем входящие данные в лог
$input = file_get_contents('php://input');
$headers = getallheaders();
$get_params = $_GET;

$log_data = [
    'timestamp' => date('Y-m-d H:i:s'),
    'input' => $input,
    'headers' => $headers,
    'get' => $get_params
];

file_put_contents('webhook_debug.log', json_encode($log_data) . "\n", FILE_APPEND);

// Отправляем простой ответ Telegram
header('Content-Type: application/json');
echo json_encode(['ok' => true]); 