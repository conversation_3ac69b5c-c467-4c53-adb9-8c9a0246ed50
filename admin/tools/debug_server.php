<?php
// Debug script to check server variables
define('DRUPAL_ROOT', getcwd());
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

echo "HTTP_HOST: " . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : 'not set') . "\n";
echo "SERVER_NAME: " . (isset($_SERVER['SERVER_NAME']) ? $_SERVER['SERVER_NAME'] : 'not set') . "\n";
echo "Current Time: " . date('Y-m-d H:i:s') . "\n";
echo "Current GMT Time: " . gmdate('Y-m-d\TH:i:s\Z') . "\n";
echo "Timezone: " . date_default_timezone_get() . "\n";

// Check if base_url is set in settings.php
global $base_url;
echo "Base URL: " . (isset($base_url) ? $base_url : 'not set') . "\n"; 