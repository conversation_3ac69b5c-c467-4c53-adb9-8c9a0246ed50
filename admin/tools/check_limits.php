<?php
define('DRUPAL_ROOT', getcwd());
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';

$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

echo "=== PHP Upload Limits ===\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . " seconds\n";
echo "max_input_time: " . ini_get('max_input_time') . " seconds\n\n";

echo "=== MySQL/MariaDB Limits ===\n";
$result = db_query("SHOW VARIABLES LIKE '%max%'");
foreach ($result as $row) {
    if (in_array($row->Variable_name, ['max_allowed_packet', 'max_connections', 'max_user_connections'])) {
        echo $row->Variable_name . ": " . $row->Value . "\n";
    }
}

echo "\n=== Directory Permissions ===\n";
$upload_path = 'sites/default/files';
echo "Upload directory ($upload_path) writable: " . (is_writable($upload_path) ? 'Yes' : 'No') . "\n";
echo "Upload directory permissions: " . substr(sprintf('%o', fileperms($upload_path)), -4) . "\n";

echo "\n=== PHP Information ===\n";
echo "PHP Version: " . phpversion() . "\n";
echo "Loaded PHP INI: " . php_ini_loaded_file() . "\n";