/**
 * ИСПРАВЛЕННЫЙ CLOUDFLARE WORKER ДЛЯ INDREAMSPHUKET
 * Версия: 2.1 (исправлены пути к файлам)
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  
  // 🎯 ИСПРАВЛЕНО: Проверяем ВСЕ пути к изображениям
  const isImageRequest = /\.(jpe?g|png|gif|webp)$/i.test(url.pathname) && (
    url.pathname.includes('/sites/default/files/') ||  // Стандартный Drupal
    url.pathname.includes('/files/site4/FileStore4/') || // Основные файлы
    url.pathname.includes('/files/site4/styles/')        // Обработанные изображения
  )
  
  if (!isImageRequest) {
    return fetch(request)
  }
  
  // Не трансформируем админские запросы
  if (url.pathname.includes('/s45/admin/') || 
      url.pathname.includes('/admin/')) {
    return fetch(request)
  }
  
  // Избегаем бесконечного цикла
  const viaHeader = request.headers.get('Via') || ''
  if (viaHeader.includes('image-resizing')) {
    return fetch(request)
  }
  
  try {
    return await optimizeImage(request)
  } catch (error) {
    console.error('Worker error:', error)
    return fetch(request) // Fallback к оригиналу
  }
}

async function optimizeImage(request) {
  const url = new URL(request.url)
  const acceptHeader = request.headers.get('Accept') || ''
  const userAgent = request.headers.get('User-Agent') || ''
  
  // 🚀 УЛУЧШЕНИЕ: Определяем лучший формат (AVIF > WebP > JPEG)
  let format = null
  let quality = 85
  
  if (acceptHeader.includes('image/avif')) {
    format = 'avif'
    quality = 80 // AVIF сжимает лучше
  } else if (acceptHeader.includes('image/webp')) {
    format = 'webp'
    quality = 85
  }
  
  // 📱 УЛУЧШЕНИЕ: Адаптивное качество для мобильных
  const isMobile = /Mobile|Android|iPhone/i.test(userAgent)
  if (isMobile) {
    quality = Math.max(quality - 10, 70) // Снижаем качество для мобильных
  }
  
  // 🎨 УЛУЧШЕНИЕ: Умное качество по типу изображения
  if (url.pathname.includes('thumb') || url.pathname.includes('small')) {
    quality = Math.max(quality - 15, 60) // Миниатюры
  } else if (url.pathname.includes('hero') || url.pathname.includes('main')) {
    quality = Math.min(quality + 5, 95) // Главные изображения
  }
  
  // Если формат не нужно менять, возвращаем оригинал
  if (!format) {
    return fetch(request)
  }
  
  // 🔧 Применяем трансформацию
  const transformedRequest = new Request(request)
  const response = await fetch(transformedRequest, {
    cf: {
      image: {
        format: format,
        quality: quality,
        fit: 'scale-down',
        metadata: 'none',
        // 📏 Ограничиваем максимальный размер
        width: 1920,
        height: 1080
      }
    }
  })
  
  // 🏆 Улучшаем заголовки кэширования
  const newResponse = new Response(response.body, response)
  
  // Агрессивное кэширование (1 год)
  newResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  newResponse.headers.set('Cloudflare-CDN-Cache-Control', 'max-age=31536000')
  
  // Информационные заголовки
  newResponse.headers.set('X-Worker-Version', '2.1')
  newResponse.headers.set('X-Optimized-Format', format)
  newResponse.headers.set('X-Optimized-Quality', quality.toString())
  newResponse.headers.set('X-Device-Type', isMobile ? 'mobile' : 'desktop')
  
  // Заголовки для адаптивных изображений
  newResponse.headers.set('Vary', 'Accept, User-Agent')
  
  return newResponse
} 