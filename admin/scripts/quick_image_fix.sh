#!/bin/bash
# 
# БЫСТРАЯ ОПТИМИЗАЦИЯ КРИТИЧЕСКИХ ИЗОБРАЖЕНИЙ
# Безопасный скрипт для немедленного улучшения
#

echo "🚀 БЫСТРАЯ ОПТИМИЗАЦИЯ ИЗОБРАЖЕНИЙ"
echo "=================================="
echo "Время: $(date)"
echo

# Проверяем наличие jpegoptim
if ! command -v jpegoptim &> /dev/null; then
    echo "❌ jpegoptim не установлен. Устанавливаем..."
    yum install -y jpegoptim 2>/dev/null || apt-get install -y jpegoptim 2>/dev/null
fi

# Создаем резервную копию (только имена файлов)
echo "💾 Создаем список файлов для отката..."
find files/site4/FileStore4/ -name "*.jpg" -size +500k > /tmp/optimized_files_backup.txt
echo "  📋 Список сохранен в /tmp/optimized_files_backup.txt"

# Сжимаем самые большие файлы (только 20 штук для теста)
echo "📦 Сжимаем 20 самых больших JPEG файлов..."
echo "  (Качество: 80%, удаляем метаданные)"
echo

count=0
find files/site4/FileStore4/ -name "*.jpg" -size +500k -exec ls -la {} \; | sort -k5 -nr | head -20 | while read line; do
    file=$(echo "$line" | awk '{print $9}')
    size_before=$(stat -c%s "$file" 2>/dev/null || echo "0")
    size_mb_before=$(echo "scale=2; $size_before/1024/1024" | bc -l)
    
    echo "  🔄 Обрабатываю: $(basename "$file") (${size_mb_before} MB)"
    
    if jpegoptim --strip-all --max=80 "$file" 2>/dev/null; then
        size_after=$(stat -c%s "$file" 2>/dev/null || echo "0")
        size_mb_after=$(echo "scale=2; $size_after/1024/1024" | bc -l)
        savings=$(echo "scale=1; ($size_before-$size_after)*100/$size_before" | bc -l)
        echo "    ✅ ${size_mb_before} MB → ${size_mb_after} MB (экономия ${savings}%)"
    else
        echo "    ⚠️ Ошибка оптимизации"
    fi
    
    count=$((count + 1))
done

echo
echo "✅ ОПТИМИЗАЦИЯ ЗАВЕРШЕНА!"
echo "========================"
echo "📊 Общие результаты:"
echo "  📁 FileStore4 размер: $(du -sh files/site4/FileStore4/ | cut -f1)"
echo "  📁 Styles размер: $(du -sh files/site4/styles/ | cut -f1)"
echo "  📋 Обработано файлов: 20 (тестовый запуск)"
echo
echo "💡 СЛЕДУЮЩИЕ ШАГИ:"
echo "  1. Исправить Cloudflare Worker (ПРИОРИТЕТ)"
echo "  2. Включить Polish в Cloudflare"
echo "  3. Если результат хороший - запустить для всех файлов"
echo
echo "🔄 ОТКАТ (если нужен):"
echo "  Файлы изменены напрямую, но список в /tmp/optimized_files_backup.txt" 