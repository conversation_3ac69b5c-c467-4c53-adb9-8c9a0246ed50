#!/usr/bin/env php
<?php
/**
 * @file
 * Скрипт для развертывания оптимизаций производительности S45 Phuket
 * 
 * Использование:
 * php admin/scripts/performance_optimization_deploy.php [action]
 * 
 * Доступные действия:
 * - install: Установка всех оптимизаций
 * - indexes: Создание индексов
 * - cache-table: Создание денормализованной таблицы
 * - rebuild-cache: Перестройка кэша
 * - status: Показать статус оптимизаций
 * - test: Тестирование производительности
 */

// Путь к корню Drupal
define('DRUPAL_ROOT', dirname(dirname(__DIR__)));
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

class S45PerformanceOptimizer {
  
  private $log_messages = array();
  
  public function __construct() {
    $this->log("=== S45 Performance Optimizer ===");
    $this->log("Время запуска: " . date('Y-m-d H:i:s'));
  }
  
  /**
   * Главная функция выполнения
   */
  public function execute($action = 'status') {
    switch ($action) {
      case 'install':
        $this->installAll();
        break;
      case 'indexes':
        $this->createIndexes();
        break;
      case 'cache-table':
        $this->createCacheTable();
        break;
      case 'rebuild-cache':
        $this->rebuildCache();
        break;
      case 'status':
        $this->showStatus();
        break;
      case 'test':
        $this->performanceTest();
        break;
      default:
        $this->showHelp();
    }
    
    $this->showLog();
  }
  
  /**
   * Установка всех оптимизаций
   */
  public function installAll() {
    $this->log("\n--- Установка всех оптимизаций ---");
    
    $start_time = microtime(true);
    
    // 1. Создание индексов
    $this->createIndexes();
    
    // 2. Создание денормализованной таблицы
    $this->createCacheTable();
    
    // 3. Первичное заполнение кэша
    $this->rebuildCache();
    
    // 4. Регистрация в cron
    $this->setupCronTasks();
    
    $duration = round((microtime(true) - $start_time) * 1000, 2);
    $this->log("✅ Все оптимизации установлены за {$duration} мс");
  }
  
  /**
   * Создание индексов для оптимизации
   */
  public function createIndexes() {
    $this->log("\n--- Создание индексов ---");
    
    $indexes = array(
      '_phuket_Property' => array(
        'idx_published_subloc_dealtype' => 'published, subLocality, dealType',
        'idx_subloc_published' => 'subLocality, published',
        'idx_dealtype_published' => 'dealType, published',
      ),
      '_s45_events' => array(
        'idx_arname_arid_islast' => 'arName, arId, isLast',
        'idx_islast_created' => 'isLast, created',
        'idx_arname_islast' => 'arName, isLast',
      ),
    );
    
    foreach ($indexes as $table => $table_indexes) {
      $this->log("Обработка таблицы: {$table}");
      
      foreach ($table_indexes as $index_name => $fields) {
        // Проверяем существование индекса
        $exists = db_query("SHOW INDEX FROM {$table} WHERE Key_name = :name", 
                          array(':name' => $index_name))->fetchField();
        
        if (!$exists) {
          try {
            db_query("ALTER TABLE {$table} ADD INDEX {$index_name} ({$fields})");
            $this->log("  ✅ Создан индекс: {$index_name}");
          } catch (Exception $e) {
            $this->log("  ❌ Ошибка создания индекса {$index_name}: " . $e->getMessage());
          }
        } else {
          $this->log("  ⚪ Индекс уже существует: {$index_name}");
        }
      }
    }
  }
  
  /**
   * Создание денормализованной таблицы
   */
  public function createCacheTable() {
    $this->log("\n--- Создание денормализованной таблицы ---");
    
    if (db_table_exists('_phuket_PopularLocations')) {
      $this->log("⚪ Таблица _phuket_PopularLocations уже существует");
      return;
    }
    
    $schema = array(
      'description' => 'Денормализованная таблица для быстрого получения популярных локаций',
      'fields' => array(
        'subLocality' => array(
          'type' => 'varchar',
          'length' => 110,
          'not null' => TRUE,
          'description' => 'ID подлокации',
        ),
        'total_count' => array(
          'type' => 'int',
          'unsigned' => TRUE,
          'not null' => TRUE,
          'default' => 0,
          'description' => 'Общее количество объектов',
        ),
        'sale_count' => array(
          'type' => 'int',
          'unsigned' => TRUE,
          'not null' => TRUE,
          'default' => 0,
          'description' => 'Количество объектов на продажу',
        ),
        'rent_count' => array(
          'type' => 'int',
          'unsigned' => TRUE,
          'not null' => TRUE,
          'default' => 0,
          'description' => 'Количество объектов в аренду',
        ),
        'longtime_count' => array(
          'type' => 'int',
          'unsigned' => TRUE,
          'not null' => TRUE,
          'default' => 0,
          'description' => 'Количество объектов в долгосрочную аренду',
        ),
        'cached_data' => array(
          'type' => 'text',
          'size' => 'big',
          'description' => 'Кэшированные данные локации (сериализованные)',
        ),
        'updated' => array(
          'type' => 'int',
          'unsigned' => TRUE,
          'not null' => TRUE,
          'default' => 0,
          'description' => 'Время последнего обновления',
        ),
      ),
      'primary key' => array('subLocality'),
      'indexes' => array(
        'total_count' => array('total_count'),
        'sale_count' => array('sale_count'),
        'rent_count' => array('rent_count'),
        'longtime_count' => array('longtime_count'),
        'updated' => array('updated'),
      ),
    );
    
    try {
      db_create_table('_phuket_PopularLocations', $schema);
      $this->log("✅ Создана таблица _phuket_PopularLocations");
    } catch (Exception $e) {
      $this->log("❌ Ошибка создания таблицы: " . $e->getMessage());
    }
  }
  
  /**
   * Перестройка кэша
   */
  public function rebuildCache() {
    $this->log("\n--- Перестройка кэша популярных локаций ---");
    
    if (!db_table_exists('_phuket_PopularLocations')) {
      $this->log("❌ Таблица _phuket_PopularLocations не существует");
      return;
    }
    
    $start_time = microtime(true);
    
    try {
      // Очищаем таблицу
      db_truncate('_phuket_PopularLocations')->execute();
      
      // Получаем данные
      $query = "
        SELECT 
          p.subLocality,
          COUNT(*) as total_count,
          SUM(CASE WHEN p.dealType = 'sale' THEN 1 ELSE 0 END) as sale_count,
          SUM(CASE WHEN p.dealType = 'rent' THEN 1 ELSE 0 END) as rent_count,
          SUM(CASE WHEN p.dealType = 'longtime' THEN 1 ELSE 0 END) as longtime_count,
          (SELECT propertyDto FROM _phuket_Property WHERE subLocality = p.subLocality AND published = 1 LIMIT 1) as sample_dto
        FROM _phuket_Property p 
        WHERE p.published = 1 AND p.subLocality IS NOT NULL 
        GROUP BY p.subLocality
        HAVING total_count > 0
        ORDER BY total_count DESC
      ";
      
      $results = db_query($query);
      
      $insert = db_insert('_phuket_PopularLocations')
        ->fields(array(
          'subLocality', 
          'total_count', 
          'sale_count', 
          'rent_count', 
          'longtime_count',
          'cached_data',
          'updated'
        ));
      
      $processed = 0;
      foreach ($results as $row) {
        $propDto = @unserialize($row->sample_dto);
        
        $cached_data = null;
        if ($propDto && isset($propDto->re_subLocality)) {
          $cached_data = serialize($propDto);
        }
        
        $insert->values(array(
          'subLocality' => $row->subLocality,
          'total_count' => $row->total_count,
          'sale_count' => $row->sale_count,
          'rent_count' => $row->rent_count,
          'longtime_count' => $row->longtime_count,
          'cached_data' => $cached_data,
          'updated' => REQUEST_TIME,
        ));
        $processed++;
      }
      
      $insert->execute();
      
      // Очищаем Drupal cache
      cache_clear_all('phuket_popdirs', 'cache', TRUE);
      
      $duration = round((microtime(true) - $start_time) * 1000, 2);
      $this->log("✅ Кэш перестроен: {$processed} локаций за {$duration} мс");
      
      variable_set('s45_phuket_location_cache_updated', REQUEST_TIME);
      
    } catch (Exception $e) {
      $this->log("❌ Ошибка перестройки кэша: " . $e->getMessage());
    }
  }
  
  /**
   * Настройка cron задач
   */
  public function setupCronTasks() {
    $this->log("\n--- Настройка cron задач ---");
    
    // Устанавливаем переменные для автоматического обновления
    variable_set('s45_phuket_location_cache_ttl', 86400); // 24 часа
    variable_set('s45_phuket_auto_cache_rebuild', TRUE);
    
    $this->log("✅ Настроено автоматическое обновление кэша каждые 24 часа");
  }
  
  /**
   * Показать статус оптимизаций
   */
  public function showStatus() {
    $this->log("\n--- Статус оптимизаций ---");
    
    // Проверяем индексы
    $critical_indexes = array(
      '_phuket_Property' => array('idx_published_subloc_dealtype', 'idx_subloc_published'),
      '_s45_events' => array('idx_arname_arid_islast', 'idx_islast_created'),
    );
    
    $indexes_status = array();
    foreach ($critical_indexes as $table => $indexes) {
      foreach ($indexes as $index) {
        $exists = db_query("SHOW INDEX FROM {$table} WHERE Key_name = :name", 
                          array(':name' => $index))->fetchField();
        $indexes_status[] = $exists ? "✅" : "❌";
        $this->log(($exists ? "✅" : "❌") . " Индекс {$table}.{$index}");
      }
    }
    
    // Проверяем денормализованную таблицу
    $cache_table_exists = db_table_exists('_phuket_PopularLocations');
    $this->log(($cache_table_exists ? "✅" : "❌") . " Таблица _phuket_PopularLocations");
    
    if ($cache_table_exists) {
      $cache_records = db_query("SELECT COUNT(*) FROM _phuket_PopularLocations")->fetchField();
      $last_update = variable_get('s45_phuket_location_cache_updated', 0);
      $this->log("  📊 Записей в кэше: {$cache_records}");
      $this->log("  🕒 Последнее обновление: " . ($last_update ? date('Y-m-d H:i:s', $last_update) : 'никогда'));
    }
    
    // Общая статистика
    $total_properties = db_query("SELECT COUNT(*) FROM _phuket_Property WHERE published = 1")->fetchField();
    $unique_locations = db_query("SELECT COUNT(DISTINCT subLocality) FROM _phuket_Property WHERE published = 1 AND subLocality IS NOT NULL")->fetchField();
    
    $this->log("\n📊 Общая статистика:");
    $this->log("  Всего активных объектов: {$total_properties}");
    $this->log("  Уникальных локаций: {$unique_locations}");
    
    // Размеры таблиц
    $sizes = db_query("
      SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb' 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name IN ('_s45_events', '_phuket_Property', '_phuket_PopularLocations') 
      ORDER BY size_mb DESC
    ")->fetchAllKeyed();
    
    $this->log("\n💾 Размеры таблиц:");
    foreach ($sizes as $table => $size) {
      $this->log("  {$table}: {$size} MB");
    }
  }
  
  /**
   * Тестирование производительности
   */
  public function performanceTest() {
    $this->log("\n--- Тестирование производительности ---");
    
    // Тест 1: Оригинальный запрос
    $start_time = microtime(true);
    $original_query = "
      SELECT p.subLocality, COUNT(p.id) AS kolich 
      FROM _phuket_Property p 
      INNER JOIN _phuket_Option o ON p.subLocality = o.id 
      WHERE (p.published = '1') AND (p.subLocality IS NOT NULL) 
      GROUP BY p.subLocality 
      ORDER BY kolich DESC 
      LIMIT 10
    ";
    $original_result = db_query($original_query)->fetchAll();
    $original_time = round((microtime(true) - $start_time) * 1000, 2);
    $this->log("⏱️  Оригинальный запрос: {$original_time} мс ({count} результатов)", array('count' => count($original_result)));
    
    // Тест 2: Оптимизированный запрос (если таблица существует)
    if (db_table_exists('_phuket_PopularLocations')) {
      $start_time = microtime(true);
      $optimized_query = "
        SELECT subLocality, total_count 
        FROM _phuket_PopularLocations 
        WHERE total_count > 0 
        ORDER BY total_count DESC 
        LIMIT 10
      ";
      $optimized_result = db_query($optimized_query)->fetchAll();
      $optimized_time = round((microtime(true) - $start_time) * 1000, 2);
      $improvement = round((($original_time - $optimized_time) / $original_time) * 100, 1);
      $this->log("🚀 Оптимизированный запрос: {$optimized_time} мс ({count} результатов)", array('count' => count($optimized_result)));
      $this->log("📈 Улучшение: {$improvement}%");
    } else {
      $this->log("⚠️  Денормализованная таблица не создана, оптимизированный тест невозможен");
    }
    
    // Тест 3: Кэш Drupal
    $cache_key = 'phuket_popdirs_test_sale';
    cache_clear_all($cache_key, 'cache');
    
    $start_time = microtime(true);
    $cache = cache_get($cache_key);
    if (!$cache) {
      // Имитируем кэширование
      cache_set($cache_key, $original_result, 'cache', REQUEST_TIME + 3600);
    }
    $cache_time = round((microtime(true) - $start_time) * 1000, 2);
    $this->log("💾 Drupal cache (первый запрос): {$cache_time} мс");
    
    // Второй запрос к кэшу
    $start_time = microtime(true);
    $cache = cache_get($cache_key);
    $cache_hit_time = round((microtime(true) - $start_time) * 1000, 2);
    $this->log("⚡ Drupal cache (hit): {$cache_hit_time} мс");
  }
  
  /**
   * Показать справку
   */
  public function showHelp() {
    $this->log("\n📖 Справка по использованию:");
    $this->log("php admin/scripts/performance_optimization_deploy.php [action]");
    $this->log("");
    $this->log("Доступные действия:");
    $this->log("  install      - Установка всех оптимизаций");
    $this->log("  indexes      - Создание индексов");
    $this->log("  cache-table  - Создание денормализованной таблицы");
    $this->log("  rebuild-cache - Перестройка кэша");
    $this->log("  status       - Показать статус оптимизаций");
    $this->log("  test         - Тестирование производительности");
  }
  
  /**
   * Добавить сообщение в лог
   */
  private function log($message, $vars = array()) {
    if (!empty($vars)) {
      $message = strtr($message, $vars);
    }
    $this->log_messages[] = $message;
    echo $message . "\n";
  }
  
  /**
   * Показать весь лог
   */
  private function showLog() {
    $this->log("\n=== Завершено в " . date('Y-m-d H:i:s') . " ===");
  }
}

// Запуск скрипта
$action = isset($argv[1]) ? $argv[1] : 'status';
$optimizer = new S45PerformanceOptimizer();
$optimizer->execute($action); 