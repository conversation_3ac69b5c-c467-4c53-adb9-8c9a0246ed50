#!/bin/bash
#
# ТЕСТИРОВАНИЕ CLOUDFLARE WORKER ЧЕРЕЗ ВНЕШНИЕ ПРОКСИ
#

echo "🌐 ТЕСТИРОВАНИЕ WORKER ЧЕРЕЗ ВНЕШНИЕ СЕРВИСЫ"
echo "================================================="
echo "Время: $(date)"
echo

# Функция для парсинга заголовков из JSON ответа
parse_headers() {
    local response="$1"
    echo "$response" | grep -o '"[^"]*": "[^"]*"' | grep -E "(Content-Type|X-Worker|X-Optimized|CF-)" | head -10
}

# 1. ТЕСТ .COM ДОМЕНА - основные файлы
echo "🔍 1. ТЕСТИРУЕМ indreamsphuket.COM - основные файлы"
echo "URL: /files/site4/FileStore4/phuket/files/tmp/"
com_result=$(curl -s -X GET \
  "https://api.hackertarget.com/httpheaders/?q=https://indreamsphuket.com/files/site4/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" \
  -H "Accept: image/webp")

echo "Ответ API:"
echo "$com_result" | head -20
echo

if echo "$com_result" | grep -q "image/webp"; then
    echo "✅ .COM основные файлы: WebP РАБОТАЕТ!"
elif echo "$com_result" | grep -q "image/jpeg"; then
    echo "❌ .COM основные файлы: WebP НЕ РАБОТАЕТ (получили JPEG)"
else
    echo "⚠️  .COM основные файлы: Неопределенный результат"
fi
echo

# 2. ТЕСТ .COM ДОМЕНА - стили
echo "🔍 2. ТЕСТИРУЕМ indreamsphuket.COM - стили"
echo "URL: /files/site4/styles/"
com_styles_result=$(curl -s -X GET \
  "https://api.hackertarget.com/httpheaders/?q=https://indreamsphuket.com/files/site4/styles/S45_IMST_600X600_CROP/public/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" \
  -H "Accept: image/webp")

echo "Ответ API:"
echo "$com_styles_result" | head -20
echo

if echo "$com_styles_result" | grep -q "image/webp"; then
    echo "✅ .COM стили: WebP РАБОТАЕТ!"
elif echo "$com_styles_result" | grep -q "image/jpeg"; then
    echo "❌ .COM стили: WebP НЕ РАБОТАЕТ (получили JPEG)"
else
    echo "⚠️  .COM стили: Неопределенный результат"
fi
echo

# 3. ТЕСТ .RU ДОМЕНА - основные файлы
echo "🔍 3. ТЕСТИРУЕМ indreamsphuket.RU - основные файлы"
ru_result=$(curl -s -X GET \
  "https://api.hackertarget.com/httpheaders/?q=https://indreamsphuket.ru/files/site4/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" \
  -H "Accept: image/webp")

echo "Ответ API:"
echo "$ru_result" | head -20
echo

if echo "$ru_result" | grep -q "image/webp"; then
    echo "✅ .RU основные файлы: WebP РАБОТАЕТ!"
elif echo "$ru_result" | grep -q "image/jpeg"; then
    echo "❌ .RU основные файлы: WebP НЕ РАБОТАЕТ (получили JPEG)"
else
    echo "⚠️  .RU основные файлы: Неопределенный результат"
fi
echo

# 4. ТЕСТ .RU ДОМЕНА - стили
echo "🔍 4. ТЕСТИРУЕМ indreamsphuket.RU - стили"
ru_styles_result=$(curl -s -X GET \
  "https://api.hackertarget.com/httpheaders/?q=https://indreamsphuket.ru/files/site4/styles/S45_IMST_600X600_CROP/public/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" \
  -H "Accept: image/webp")

echo "Ответ API:"
echo "$ru_styles_result" | head -20
echo

if echo "$ru_styles_result" | grep -q "image/webp"; then
    echo "✅ .RU стили: WebP РАБОТАЕТ!"
elif echo "$ru_styles_result" | grep -q "image/jpeg"; then
    echo "❌ .RU стили: WebP НЕ РАБОТАЕТ (получили JPEG)"
else
    echo "⚠️  .RU стили: Неопределенный результат"
fi
echo

# 5. ИТОГОВЫЙ СТАТУС
echo "🏆 ИТОГОВЫЙ СТАТУС WORKER:"
echo "================================"

webp_count=0
if echo "$com_result" | grep -q "image/webp"; then webp_count=$((webp_count + 1)); fi
if echo "$com_styles_result" | grep -q "image/webp"; then webp_count=$((webp_count + 1)); fi
if echo "$ru_result" | grep -q "image/webp"; then webp_count=$((webp_count + 1)); fi
if echo "$ru_styles_result" | grep -q "image/webp"; then webp_count=$((webp_count + 1)); fi

if [ $webp_count -eq 4 ]; then
    echo "🎉 ОТЛИЧНО: ВСЕ 4 ТЕСТА ПРОШЛИ! Worker работает на 100%"
elif [ $webp_count -ge 2 ]; then
    echo "🟨 ХОРОШО: $webp_count/4 тестов прошли. Worker частично работает"
elif [ $webp_count -eq 1 ]; then
    echo "🟠 ПЛОХО: Только $webp_count/4 тестов прошли. Нужна доработка"
else
    echo "❌ КРИТИЧНО: НИ ОДИН ТЕСТ НЕ ПРОШЕЛ! Worker не работает"
fi

echo
echo "📋 ДЕТАЛИ:"
echo "- .COM основные файлы: $(if echo "$com_result" | grep -q "image/webp"; then echo "✅ WebP"; else echo "❌ JPEG"; fi)"
echo "- .COM стили: $(if echo "$com_styles_result" | grep -q "image/webp"; then echo "✅ WebP"; else echo "❌ JPEG"; fi)"
echo "- .RU основные файлы: $(if echo "$ru_result" | grep -q "image/webp"; then echo "✅ WebP"; else echo "❌ JPEG"; fi)"
echo "- .RU стили: $(if echo "$ru_styles_result" | grep -q "image/webp"; then echo "✅ WebP"; else echo "❌ JPEG"; fi)"

echo
echo "🔧 ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ:"
echo "================================"

# Проверяем наличие Worker заголовков
if echo "$com_result$com_styles_result$ru_result$ru_styles_result" | grep -q "X-Worker\|X-Optimized\|CF-"; then
    echo "✅ Обнаружены заголовки Worker/Cloudflare"
else
    echo "❌ НЕ обнаружены заголовки Worker"
fi

# Проверяем скорость активации
echo "ℹ️  Время с момента деплоя: недавно"
echo "ℹ️  Cloudflare может кэшировать до 5-15 минут"

echo
echo "✨ Тестирование завершено!" 