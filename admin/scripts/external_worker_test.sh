#!/bin/bash
#
# ТЕСТИРОВАНИЕ CLOUDFLARE WORKER ЧЕРЕЗ ВНЕШНИЕ СЕРВИСЫ
# (без локальных настроек /etc/hosts)
#

echo "🌐 ТЕСТИРОВАНИЕ CLOUDFLARE WORKER С ВНЕШНЕЙ ПЕРСПЕКТИВЫ"
echo "======================================================="
echo "Время: $(date)"
echo

# 1. Проверяем DNS резолюцию через внешние DNS
echo "🔍 Тест 1: DNS резолюция через внешние серверы"
echo "----------------------------------------------"
echo "DNS через Google (*******):"
nslookup indreamsphuket.com ******* | grep "Address:" | tail -2
echo
echo "DNS через Cloudflare (*******):"
nslookup indreamsphuket.com ******* | grep "Address:" | tail -2
echo

# 2. Прямой тест к Cloudflare Edge (обходим /etc/hosts)
echo "🔍 Тест 2: Прямое подключение к Cloudflare Edge"
echo "-----------------------------------------------"
cf_test=$(curl -k -s -I -H "Host: indreamsphuket.com" -m 10 \
    --resolve indreamsphuket.com:443:************ \
    "https://indreamsphuket.com/" 2>/dev/null)

if echo "$cf_test" | grep -i "cf-ray" > /dev/null; then
    echo "✅ Успешное подключение к Cloudflare!"
    echo "CF-Ray: $(echo "$cf_test" | grep -i "cf-ray" | cut -d: -f2 | tr -d ' \r')"
    echo "CF-Cache-Status: $(echo "$cf_test" | grep -i "cf-cache-status" | cut -d: -f2 | tr -d ' \r')"
    echo "Server: $(echo "$cf_test" | grep -i "^server:" | cut -d: -f2 | tr -d ' \r')"
else
    echo "❌ Не удалось подключиться к Cloudflare напрямую"
    echo "Полный ответ:"
    echo "$cf_test" | head -10
fi
echo

# 3. Тест Worker с изображением .COM - основные файлы
echo "🔍 Тест 3: Worker .COM - основные файлы (/files/site4/FileStore4/)"
echo "----------------------------------------------------------------"
worker_test1=$(curl -k -s -I -H "Host: indreamsphuket.com" -H "Accept: image/webp" -m 15 \
    --resolve indreamsphuket.com:443:************ \
    "https://indreamsphuket.com/files/site4/FileStore4/default/files/img/location/location1.jpg" 2>/dev/null)

if echo "$worker_test1" | grep -i "content-type" > /dev/null; then
    content_type=$(echo "$worker_test1" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r')
    echo "✅ Ответ получен:"
    echo "Content-Type: $content_type"
    
    if echo "$content_type" | grep -i "webp" > /dev/null; then
        echo "🎨 ✅ WebP конвертация РАБОТАЕТ!"
    elif echo "$content_type" | grep -i "jpeg\|jpg" > /dev/null; then
        echo "🎨 ❌ WebP конвертация НЕ работает (получили JPEG)"
    else
        echo "🎨 ⚠️  Неопределенный тип: $content_type"
    fi
    
    # Проверяем Worker заголовки
    if echo "$worker_test1" | grep -i "x-worker\|x-optimized" > /dev/null; then
        echo "🔧 ✅ Worker заголовки найдены!"
        echo "$worker_test1" | grep -i "x-worker\|x-optimized"
    else
        echo "🔧 ❌ Worker заголовки НЕ найдены"
    fi
else
    echo "❌ Не удалось получить ответ"
fi
echo

# 4. Тест Worker .COM - стили изображений
echo "🔍 Тест 4: Worker .COM - стили (/files/site4/styles/)"
echo "---------------------------------------------------"
worker_test2=$(curl -k -s -I -H "Host: indreamsphuket.com" -H "Accept: image/webp" -m 15 \
    --resolve indreamsphuket.com:443:************ \
    "https://indreamsphuket.com/files/site4/styles/S45_IMST_600X600_CROP/public/FileStore4/default/files/img/location/location1.jpg" 2>/dev/null)

if echo "$worker_test2" | grep -i "content-type" > /dev/null; then
    content_type2=$(echo "$worker_test2" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r')
    echo "✅ Ответ получен:"
    echo "Content-Type: $content_type2"
    
    if echo "$content_type2" | grep -i "webp" > /dev/null; then
        echo "🎨 ✅ WebP конвертация РАБОТАЕТ!"
    elif echo "$content_type2" | grep -i "jpeg\|jpg" > /dev/null; then
        echo "🎨 ❌ WebP конвертация НЕ работает (получили JPEG)"
    else
        echo "🎨 ⚠️  Неопределенный тип: $content_type2"
    fi
else
    echo "❌ Не удалось получить ответ"
fi
echo

# 5. Тест Worker .RU домен
echo "🔍 Тест 5: Worker .RU домен"
echo "----------------------------"
worker_test3=$(curl -k -s -I -H "Host: indreamsphuket.ru" -H "Accept: image/webp" -m 15 \
    --resolve indreamsphuket.ru:443:************ \
    "https://indreamsphuket.ru/files/site4/FileStore4/default/files/img/location/location1.jpg" 2>/dev/null)

if echo "$worker_test3" | grep -i "content-type" > /dev/null; then
    content_type3=$(echo "$worker_test3" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r')
    echo "✅ Ответ получен:"
    echo "Content-Type: $content_type3"
    
    if echo "$content_type3" | grep -i "webp" > /dev/null; then
        echo "🎨 ✅ WebP конвертация РАБОТАЕТ!"
    elif echo "$content_type3" | grep -i "jpeg\|jpg" > /dev/null; then
        echo "🎨 ❌ WebP конвертация НЕ работает (получили JPEG)"
    else
        echo "🎨 ⚠️  Неопределенный тип: $content_type3"
    fi
else
    echo "❌ Не удалось получить ответ"
fi
echo

# 6. Тест старого пути (sites/default/files) для сравнения
echo "🔍 Тест 6: Старый путь /sites/default/files/ (для сравнения)"
echo "-----------------------------------------------------------"
worker_test4=$(curl -k -s -I -H "Host: indreamsphuket.com" -H "Accept: image/webp" -m 15 \
    --resolve indreamsphuket.com:443:************ \
    "https://indreamsphuket.com/sites/default/files/img/noimage.jpg" 2>/dev/null)

if echo "$worker_test4" | grep -i "content-type" > /dev/null; then
    content_type4=$(echo "$worker_test4" | grep -i "content-type" | cut -d: -f2 | tr -d ' \r')
    echo "✅ Ответ получен:"
    echo "Content-Type: $content_type4"
    
    if echo "$content_type4" | grep -i "webp" > /dev/null; then
        echo "🎨 ✅ WebP конвертация РАБОТАЕТ! (Старый путь функционирует)"
    elif echo "$content_type4" | grep -i "jpeg\|jpg" > /dev/null; then
        echo "🎨 ❌ WebP конвертация НЕ работает (получили JPEG)"
    else
        echo "🎨 ⚠️  Неопределенный тип: $content_type4"
    fi
else
    echo "❌ Не удалось получить ответ"
fi
echo

# 7. ИТОГОВЫЙ СТАТУС
echo "🏆 ИТОГОВЫЙ СТАТУС WORKER:"
echo "================================"

webp_count=0
test_names=(".COM основные файлы" ".COM стили" ".RU домен" "Старый путь")
test_results=("$content_type" "$content_type2" "$content_type3" "$content_type4")

for i in {0..3}; do
    if echo "${test_results[$i]}" | grep -i "webp" > /dev/null; then
        webp_count=$((webp_count + 1))
        echo "✅ ${test_names[$i]}: WebP работает"
    elif echo "${test_results[$i]}" | grep -i "jpeg\|jpg" > /dev/null; then
        echo "❌ ${test_names[$i]}: WebP НЕ работает"
    else
        echo "⚠️  ${test_names[$i]}: Неопределенный результат"
    fi
done

echo
if [ $webp_count -eq 4 ]; then
    echo "🎉 ОТЛИЧНО: ВСЕ 4 ТЕСТА ПРОШЛИ! Worker работает на 100%"
elif [ $webp_count -ge 2 ]; then
    echo "🟨 ХОРОШО: $webp_count/4 тестов прошли. Worker частично работает"
elif [ $webp_count -eq 1 ]; then
    echo "🟠 ПЛОХО: Только $webp_count/4 тестов прошли. Нужна доработка"
else
    echo "❌ КРИТИЧНО: НИ ОДИН ТЕСТ НЕ ПРОШЕЛ! Worker не работает"
fi

echo
echo "🔧 РЕКОМЕНДАЦИИ:"
echo "================"
if [ $webp_count -lt 4 ]; then
    echo "1. Проверьте роуты в Cloudflare Dashboard"
    echo "2. Убедитесь, что есть роуты для /files/site4/*"
    echo "3. Подождите 5-15 минут для активации новых роутов"
    echo "4. Проверьте код Worker на ошибки"
fi

echo
echo "🌐 ОНЛАЙН ИНСТРУМЕНТЫ ДЛЯ ПРОВЕРКИ:"
echo "==================================="
echo "1. https://www.whatsmydns.net/#A/indreamsphuket.com"
echo "2. https://www.webpagetest.org/"
echo "3. https://tools.keycdn.com/curl"
echo "4. https://www.shortpixel.com/online-image-compression"
echo
echo "✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО" 