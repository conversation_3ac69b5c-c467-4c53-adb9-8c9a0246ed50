#!/usr/bin/env php
<?php
/**
 * @file
 * Тестирование Cloudflare Worker для оптимизации изображений
 * 
 * Проверяет:
 * - Работает ли WebP конвертация
 * - Изменяются ли размеры изображений
 * - Кэшируются ли результаты
 * - Сжимаются ли файлы
 */

// Устанавливаем переменные окружения для CLI
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';

define('DRUPAL_ROOT', dirname(dirname(__DIR__)));
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

class CloudflareWorkerTester {
  
  private $base_url = 'https://indreamsphuket.com';
  private $test_images = array();
  
  public function __construct() {
    echo "🔍 ТЕСТИРОВАНИЕ CLOUDFLARE WORKER ДЛЯ ОПТИМИЗАЦИИ ИЗОБРАЖЕНИЙ\n";
    echo "=========================================================\n";
    echo "Время: " . date('Y-m-d H:i:s') . "\n";
    echo "Домен: {$this->base_url}\n\n";
    
    $this->findTestImages();
  }
  
  /**
   * Найти тестовые изображения
   */
  private function findTestImages() {
    echo "📁 Поиск тестовых изображений...\n";
    
    $images = array();
    
    // Найти файлы разного размера
    exec("find files/site4/FileStore4/ -name '*.jpg' | head -5", $images);
    
    foreach ($images as $path) {
      if (file_exists($path)) {
        $size = filesize($path);
        $size_mb = round($size / 1024 / 1024, 2);
        
        $this->test_images[] = array(
          'path' => $path,
          'url' => str_replace(DRUPAL_ROOT . '/', '', $path),
          'size' => $size,
          'size_mb' => $size_mb
        );
        
        echo "  ✅ {$path} ({$size_mb} MB)\n";
      }
    }
    
    if (empty($this->test_images)) {
      echo "  ❌ Тестовые изображения не найдены!\n";
      exit(1);
    }
    
    echo "  📊 Найдено " . count($this->test_images) . " тестовых изображений\n\n";
  }
  
  /**
   * Тест WebP поддержки
   */
  public function testWebPSupport() {
    echo "🎨 ТЕСТ: WebP КОНВЕРТАЦИЯ\n";
    echo "-------------------------\n";
    
    foreach ($this->test_images as $i => $image) {
      $url = $this->base_url . '/' . $image['url'];
      
      echo "Тестирование: " . basename($image['path']) . " ({$image['size_mb']} MB)\n";
      
      // Тест 1: Обычный запрос
      $result1 = $this->makeRequest($url);
      echo "  📄 Обычный запрос: {$result1['content_type']} ({$result1['size_kb']} KB)\n";
      
      // Тест 2: С Accept: image/webp
      $result2 = $this->makeRequest($url, array('Accept: image/webp'));
      echo "  🎨 С WebP заголовком: {$result2['content_type']} ({$result2['size_kb']} KB)\n";
      
      // Тест 3: С параметрами размера
      $result3 = $this->makeRequest($url . '?w=400&h=300');
      echo "  📐 С размерами (400x300): {$result3['content_type']} ({$result3['size_kb']} KB)\n";
      
      // Анализ результатов
      if ($result2['content_type'] === 'image/webp') {
        $savings = round((($result1['size_kb'] - $result2['size_kb']) / $result1['size_kb']) * 100, 1);
        echo "  ✅ WebP работает! Экономия: {$savings}%\n";
      } else {
        echo "  ⚠️ WebP НЕ работает (возвращается: {$result2['content_type']})\n";
      }
      
      if ($result3['size_kb'] < $result1['size_kb']) {
        $resize_savings = round((($result1['size_kb'] - $result3['size_kb']) / $result1['size_kb']) * 100, 1);
        echo "  ✅ Ресайз работает! Экономия: {$resize_savings}%\n";
      } else {
        echo "  ⚠️ Ресайз НЕ работает\n";
      }
      
      echo "\n";
      
      // Тестируем только первые 2 изображения, чтобы не нагружать
      if ($i >= 1) break;
    }
  }
  
  /**
   * Тест кэширования
   */
  public function testCaching() {
    echo "💾 ТЕСТ: КЭШИРОВАНИЕ CLOUDFLARE\n";
    echo "------------------------------\n";
    
    $image = $this->test_images[0];
    $url = $this->base_url . '/' . $image['url'] . '?w=800&h=600&test_cache=1';
    
    echo "Тестирование кэширования: " . basename($image['path']) . "\n";
    
    // Первый запрос
    $start1 = microtime(true);
    $result1 = $this->makeRequest($url, array(), true);
    $time1 = round((microtime(true) - $start1) * 1000, 2);
    
    echo "  🔄 Первый запрос: {$time1} мс (cf-cache-status: {$result1['cf_cache_status']})\n";
    
    // Второй запрос (должен быть из кэша)
    sleep(1);
    $start2 = microtime(true);
    $result2 = $this->makeRequest($url, array(), true);
    $time2 = round((microtime(true) - $start2) * 1000, 2);
    
    echo "  ⚡ Второй запрос: {$time2} мс (cf-cache-status: {$result2['cf_cache_status']})\n";
    
    if ($result2['cf_cache_status'] === 'HIT' || $time2 < $time1 * 0.5) {
      $speedup = round($time1 / $time2, 1);
      echo "  ✅ Кэширование работает! Ускорение: {$speedup}x\n";
    } else {
      echo "  ⚠️ Кэширование НЕ работает или работает неполно\n";
    }
    
    echo "\n";
  }
  
  /**
   * Тест Polish (автосжатие)
   */
  public function testPolish() {
    echo "✨ ТЕСТ: CLOUDFLARE POLISH (автосжатие)\n";
    echo "--------------------------------------\n";
    
    foreach ($this->test_images as $i => $image) {
      $url = $this->base_url . '/' . $image['url'];
      
      echo "Тестирование Polish: " . basename($image['path']) . "\n";
      
      $result = $this->makeRequest($url, array(), true);
      
      echo "  📊 Content-Type: {$result['content_type']}\n";
      echo "  📏 Размер: {$result['size_kb']} KB\n";
      echo "  ✨ CF-Polish: {$result['cf_polished']}\n";
      echo "  💾 CF-Cache-Status: {$result['cf_cache_status']}\n";
      
      if ($result['cf_polished'] === 'originals') {
        echo "  ✅ Polish активен!\n";
      } else {
        echo "  ⚠️ Polish не активен или не применился\n";
      }
      
      echo "\n";
      
      // Тестируем только первое изображение
      if ($i >= 0) break;
    }
  }
  
  /**
   * Выполнить HTTP запрос и получить информацию
   */
  private function makeRequest($url, $headers = array(), $detailed = false) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD запрос
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    if (!empty($headers)) {
      curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    // Парсим заголовки
    $headers_array = array();
    $header_lines = explode("\n", $response);
    foreach ($header_lines as $line) {
      if (strpos($line, ':') !== false) {
        $parts = explode(':', $line, 2);
        $key = strtolower(trim($parts[0]));
        $value = trim($parts[1]);
        $headers_array[$key] = $value;
      }
    }
    
    $result = array(
      'content_type' => isset($headers_array['content-type']) ? $headers_array['content-type'] : 'unknown',
      'size_kb' => isset($headers_array['content-length']) ? round($headers_array['content-length'] / 1024, 1) : 0,
      'http_code' => $http_code
    );
    
    if ($detailed) {
      $result['cf_cache_status'] = isset($headers_array['cf-cache-status']) ? $headers_array['cf-cache-status'] : 'unknown';
      $result['cf_polished'] = isset($headers_array['cf-polished']) ? $headers_array['cf-polished'] : 'none';
      $result['cf_ray'] = isset($headers_array['cf-ray']) ? $headers_array['cf-ray'] : 'none';
    }
    
    return $result;
  }
  
  /**
   * Общий отчет и рекомендации
   */
  public function generateReport() {
    echo "📋 ОТЧЕТ И РЕКОМЕНДАЦИИ\n";
    echo "======================\n";
    
    echo "🎯 НА ОСНОВЕ ТЕСТОВ:\n";
    echo "  1. Проверьте код вашего Worker'а в Cloudflare Dashboard\n";
    echo "  2. Убедитесь, что включены правильные Transform Rules\n";
    echo "  3. Активируйте Polish для автоматического сжатия\n\n";
    
    echo "⚡ БЫСТРЫЕ УЛУЧШЕНИЯ:\n";
    echo "  1. Добавьте обработку параметров ?w= и ?h= в Worker\n";
    echo "  2. Включите автоконвертацию в WebP/AVIF\n";
    echo "  3. Настройте долгое кэширование (1 год) для изображений\n\n";
    
    echo "🚀 ОЖИДАЕМЫЕ РЕЗУЛЬТАТЫ ПОСЛЕ НАСТРОЙКИ:\n";
    echo "  - WebP: 25-50% экономии трафика\n";
    echo "  - Ресайз: 50-80% экономии для больших изображений\n";
    echo "  - Кэш: 90% ускорение повторных загрузок\n";
    echo "  - Polish: 10-30% дополнительного сжатия\n\n";
    
    echo "💡 КОД ДЛЯ ИНТЕГРАЦИИ:\n";
    echo "```php\n";
    echo "// Добавить в s45_base.lib.inc\n";
    echo "function s45_imgSrc_cloudflare(\$fileDto, \$width = 800, \$height = 600) {\n";
    echo "  \$url = s45_imgSrc(\$fileDto);\n";
    echo "  \$separator = strpos(\$url, '?') ? '&' : '?';\n";
    echo "  return \$url . \$separator . \"w={\$width}&h={\$height}&cf=1\";\n";
    echo "}\n";
    echo "```\n\n";
  }
}

// Запуск тестов
$action = isset($argv[1]) ? $argv[1] : 'all';
$tester = new CloudflareWorkerTester();

switch ($action) {
  case 'webp':
    $tester->testWebPSupport();
    break;
    
  case 'cache':
    $tester->testCaching();
    break;
    
  case 'polish':
    $tester->testPolish();
    break;
    
  case 'all':
  default:
    $tester->testWebPSupport();
    $tester->testCaching();
    $tester->testPolish();
    $tester->generateReport();
    break;
}

echo "✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО\n";
echo "Подробный анализ см. в файле: CLOUDFLARE_WORKER_ANALYSIS.md\n"; 