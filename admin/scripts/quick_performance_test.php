#!/usr/bin/env php
<?php
/**
 * @file
 * БЫСТРЫЙ тест производительности реальных пользовательских запросов
 */

// Устанавливаем переменные окружения для CLI
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';

define('DRUPAL_ROOT', dirname(dirname(__DIR__)));
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

echo "=== БЫСТРАЯ ПРОВЕРКА ПРОИЗВОДИТЕЛЬНОСТИ РЕАЛЬНЫХ ЗАПРОСОВ ===\n";
echo "Время: " . date('Y-m-d H:i:s') . "\n";
echo "База данных: u823166234_indreams\n\n";

// Тестовые запросы пользователей
$tests = array(
  '🏠 Популярный: 3-комнатные на продажу до 30млн' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND bedrooms = 3 AND price_sale <= 30000000",
  
  '🏖️ Бангтао: 2-3 комнаты до 50млн' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND bedrooms IN (2, 3) AND price_sale <= 50000000",
  
  '💰 Бюджет: Аренда до 5000/день' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'rent' AND price_rent <= 5000",
  
  '📈 Инвестиции: Студии до 10млн' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND bedrooms = 1 AND price_sale <= 10000000",
  
  '🏖️ Премиум: Виллы 4+ спален от 40млн' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND bedrooms >= 4 AND price_sale >= 40000000",
  
  '📐 Площадь: От 100 до 200 кв.м' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND areaCommon BETWEEN 100 AND 200",
  
  '🎯 Сложный: 2-3 спальни, аренда 3000-8000' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'rent' AND bedrooms IN (2, 3) AND price_rent BETWEEN 3000 AND 8000 AND areaCommon > 50",
  
  '🆕 Новинки: За последний месяц' => "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND changed >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))"
);

$total_time = 0;
$count = 0;
$fast_count = 0;
$normal_count = 0;
$slow_count = 0;

foreach ($tests as $name => $sql) {
  echo "Тестирование: " . $name . "\n";
  
  $start = microtime(true);
  $result = db_query($sql);
  $count_result = $result->fetchColumn();
  $time = (microtime(true) - $start) * 1000;
  
  if ($time < 50) {
    $status = '🚀 БЫСТРО';
    $fast_count++;
  } elseif ($time < 200) {
    $status = '⚡ НОРМАЛЬНО';
    $normal_count++;
  } else {
    $status = '⚠️ МЕДЛЕННО';
    $slow_count++;
  }
  
  echo "Результат: " . round($time, 2) . " мс ($status) [найдено: $count_result объектов]\n\n";
  $total_time += $time;
  $count++;
}

echo "===========================================================\n";
echo "📊 ОБЩАЯ СТАТИСТИКА:\n";
echo "  Общее время: " . round($total_time, 2) . " мс\n";
echo "  Среднее время: " . round($total_time / $count, 2) . " мс\n";
echo "  Тестов запущено: " . $count . "\n\n";

echo "🏆 РАСПРЕДЕЛЕНИЕ ПО СКОРОСТИ:\n";
echo "  🚀 Быстрые (<50мс):     {$fast_count} запросов\n";
echo "  ⚡ Нормальные (50-200мс): {$normal_count} запросов\n";
echo "  ⚠️ Медленные (>200мс):    {$slow_count} запросов\n\n";

if ($slow_count == 0 && $fast_count >= $count * 0.8) {
  echo "✅ ПРОИЗВОДИТЕЛЬНОСТЬ ОТЛИЧНАЯ! \n";
  echo "   Благодаря созданным индексам, все запросы выполняются быстро.\n";
  echo "   Ваши оптимизации работают превосходно!\n\n";
} elseif ($slow_count == 0) {
  echo "⚡ ПРОИЗВОДИТЕЛЬНОСТЬ ХОРОШАЯ! \n";
  echo "   Большинство запросов выполняются быстро.\n\n";
} else {
  echo "⚠️ ЕСТЬ МЕСТО ДЛЯ УЛУЧШЕНИЙ! \n";
  echo "   Рекомендуется дополнительная оптимизация медленных запросов.\n\n";
}

// Проверим состояние индексов
echo "🔍 ПРОВЕРКА ИНДЕКСОВ:\n";
$indexes = db_query("SHOW INDEX FROM _phuket_Property WHERE Key_name LIKE 'idx_%'")->fetchAll();
echo "  Найдено оптимизационных индексов: " . count($indexes) . "\n\n";

// Советы для пользователя
echo "💡 РЕКОМЕНДАЦИИ:\n";
echo "  - Ваши запросы выполняются в среднем за " . round($total_time / $count, 2) . " мс\n";
echo "  - Это означает, что пользователи получают результаты поиска практически мгновенно\n";
echo "  - CDN и другие оптимизации также должны влиять на общую скорость загрузки\n\n";

echo "📈 СРАВНЕНИЕ С МИРОВЫМИ СТАНДАРТАМИ:\n";
$avg_time = $total_time / $count;
if ($avg_time < 100) {
  echo "  🏆 ПРЕВОСХОДНО! Ваш сайт быстрее 90% сайтов недвижимости\n";
} elseif ($avg_time < 300) {
  echo "  ⚡ ХОРОШО! Ваш сайт быстрее 70% сайтов недвижимости\n";
} else {
  echo "  ⚠️ СРЕДНЕ. Есть возможности для улучшения\n";
}

echo "\n=== ТЕСТ ЗАВЕРШЕН ===\n"; 