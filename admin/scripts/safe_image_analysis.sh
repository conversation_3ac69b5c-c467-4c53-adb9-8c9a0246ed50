#!/bin/bash
# 
# БЕЗОПАСНЫЙ АНАЛИЗ ИЗОБРАЖЕНИЙ
# Только анализ, никаких изменений!
#

echo "🔍 АНАЛИЗ ИЗОБРАЖЕНИЙ НА СЕРВЕРЕ"
echo "Время: $(date)"
echo "=========================================="

# 1. Общая статистика
echo "📊 ОБЩАЯ СТАТИСТИКА:"
echo "  FileStore4 размер: $(du -sh files/site4/FileStore4/ | cut -f1)"
echo "  Styles размер: $(du -sh files/site4/styles/ | cut -f1)"
echo

# 2. Количество файлов
echo "📁 КОЛИЧЕСТВО ФАЙЛОВ:"
original_count=$(find files/site4/FileStore4/ -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | wc -l)
styles_count=$(find files/site4/styles/ -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | wc -l)
echo "  Оригинальные изображения: $original_count"
echo "  Генерированные стили: $styles_count"
echo "  Соотношение стилей к оригиналам: $(echo "scale=1; $styles_count / $original_count" | bc)"
echo

# 3. Анализ размеров файлов
echo "📐 АНАЛИЗ РАЗМЕРОВ ОРИГИНАЛОВ:"
echo "  Больше 2MB: $(find files/site4/FileStore4/ -name "*.jpg" -size +2M | wc -l) файлов"
echo "  Больше 1MB: $(find files/site4/FileStore4/ -name "*.jpg" -size +1M | wc -l) файлов"
echo "  Больше 500KB: $(find files/site4/FileStore4/ -name "*.jpg" -size +500k | wc -l) файлов"
echo

# 4. Топ-10 самых больших файлов
echo "🔥 ТОП-10 САМЫХ БОЛЬШИХ ФАЙЛОВ:"
find files/site4/FileStore4/ -name "*.jpg" -exec ls -lh {} \; | sort -k5 -hr | head -10 | \
  awk '{print "  " $5 " - " $9}'
echo

# 5. Анализ старых стилей
echo "🗑️ СТАРЫЕ СТИЛИ (МОЖНО УДАЛИТЬ):"
old_styles_count=$(find files/site4/styles/ -name "*.jpg" -mtime +180 | wc -l)
old_styles_size=$(find files/site4/styles/ -name "*.jpg" -mtime +180 -exec ls -la {} \; | awk '{sum += $5} END {print sum}')
old_styles_size_mb=$(echo "scale=1; $old_styles_size / 1024 / 1024" | bc)
echo "  Старше 6 месяцев: $old_styles_count файлов (~${old_styles_size_mb}MB)"
echo

# 6. Потенциальная экономия
echo "💰 ПОТЕНЦИАЛЬНАЯ ЭКОНОМИЯ:"
large_files_count=$(find files/site4/FileStore4/ -name "*.jpg" -size +1M | wc -l)
echo "  WebP конвертация ($large_files_count больших файлов): ~40-60% экономии"
echo "  Сжатие JPEG без потерь: ~20-30% экономии"
echo "  Удаление старых стилей: ~${old_styles_size_mb}MB освободится"
echo

# 7. Рекомендации
echo "💡 БЕЗОПАСНЫЕ РЕКОМЕНДАЦИИ:"
echo "  1. Начать с ленивой загрузки (loading='lazy') - НОЛЬ РИСКА"
echo "  2. Сжать 200 самых больших файлов для тестирования"
echo "  3. Создать WebP для топ-100 изображений"
echo "  4. Настроить мониторинг роста файлов"
echo

echo "✅ АНАЛИЗ ЗАВЕРШЕН"
echo "СЛЕДУЮЩИЙ ШАГ: Посмотрите отчет и выберите безопасные оптимизации" 