#!/bin/bash
#
# ПРОВЕРКА СТАТУСА ВСЕХ ОПТИМИЗАЦИЙ
#

echo "🔍 ПОЛНАЯ ПРОВЕРКА ОПТИМИЗАЦИЙ INDREAMSPHUKET"
echo "=============================================="
echo "Время: $(date)"
echo

# 1. БАЗА ДАННЫХ
echo "📊 1. БАЗА ДАННЫХ:"
echo "   Тестируем производительность..."
start_time=$(date +%s%3N)
result=$(mysql -u root -p123789 u823166234_indreams -e "SELECT COUNT(*) FROM _phuket_Property WHERE published = 1 AND dealType = 'sale' AND bedrooms = 3 AND price_sale <= 30000000" 2>/dev/null)
end_time=$(date +%s%3N)
db_time=$((end_time - start_time))
echo "   ✅ Время выполнения: ${db_time}мс"
echo "   ✅ Найдено объектов: $(echo "$result" | tail -n1)"
echo

# 2. ПОПУЛЯРНЫЕ ЛОКАЦИИ (наша оптимизация)
echo "📍 2. ПОПУЛЯРНЫЕ ЛОКАЦИИ:"
start_time=$(date +%s%3N)
cache_result=$(mysql -u root -p123789 u823166234_indreams -e "SELECT COUNT(*) FROM _phuket_PopularLocations WHERE total_count > 0" 2>/dev/null)
end_time=$(date +%s%3N)
cache_time=$((end_time - start_time))
echo "   ✅ Кэш таблица: $(echo "$cache_result" | tail -n1) локаций"
echo "   ✅ Время выполнения: ${cache_time}мс"
echo

# 3. CLOUDFLARE WORKER
echo "🌐 3. CLOUDFLARE WORKER:"
echo "   Тестируем .ru домен..."
ru_result=$(curl -s -I -H "Accept: image/webp" --resolve indreamsphuket.ru:443:104.26.3.171 "https://indreamsphuket.ru/files/site4/styles/S45_IMST_600X600_CROP/public/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" | grep "Content-Type" | grep -o "webp\|jpeg")
if [ "$ru_result" = "webp" ]; then
    echo "   ✅ .ru домен: WebP работает!"
else
    echo "   ❌ .ru домен: WebP НЕ работает (получили: $ru_result)"
fi

echo "   Тестируем .com домен..."
com_result=$(curl -s -I -H "Accept: image/webp" --resolve indreamsphuket.com:443:104.26.3.171 "https://indreamsphuket.com/files/site4/styles/S45_IMST_600X600_CROP/public/FileStore4/phuket/files/tmp/20250529_102727_3944_the-wynn-luxury-villas-pasak-102.jpg" | grep "Content-Type" | grep -o "webp\|jpeg")
if [ "$com_result" = "webp" ]; then
    echo "   ✅ .com домен: WebP работает!"
else
    echo "   ❌ .com домен: WebP НЕ работает (получили: $com_result)"
fi
echo

# 4. РАЗМЕРЫ ФАЙЛОВ
echo "💾 4. РАЗМЕРЫ ФАЙЛОВ:"
file_size=$(du -sh files/site4/FileStore4/ | cut -f1)
styles_size=$(du -sh files/site4/styles/ | cut -f1)
echo "   📁 FileStore4: $file_size"
echo "   📁 Styles: $styles_size"
file_count=$(find files/site4/FileStore4/ -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" | wc -l)
echo "   📸 Всего изображений: $file_count"
echo

# 5. ИНДЕКСЫ БАЗЫ ДАННЫХ
echo "📈 5. ИНДЕКСЫ БАЗЫ ДАННЫХ:"
indexes=$(mysql -u root -p123789 u823166234_indreams -e "SHOW INDEX FROM _phuket_Property WHERE Key_name LIKE 'idx_%'" 2>/dev/null | wc -l)
if [ $indexes -gt 0 ]; then
    echo "   ✅ Оптимизированные индексы: $(($indexes - 1))"
else
    echo "   ❌ Оптимизированные индексы НЕ найдены"
fi
echo

# 6. ИТОГОВЫЙ СТАТУС
echo "🏆 ИТОГОВЫЙ СТАТУС:"
echo "================================"

if [ $db_time -lt 10 ]; then
    echo "✅ База данных: ОТЛИЧНО (${db_time}мс)"
elif [ $db_time -lt 50 ]; then
    echo "🟨 База данных: ХОРОШО (${db_time}мс)"
else
    echo "❌ База данных: МЕДЛЕННО (${db_time}мс)"
fi

if [ "$ru_result" = "webp" ] && [ "$com_result" = "webp" ]; then
    echo "✅ Cloudflare Worker: ОБА ДОМЕНА РАБОТАЮТ"
elif [ "$ru_result" = "webp" ] || [ "$com_result" = "webp" ]; then
    echo "🟨 Cloudflare Worker: ЧАСТИЧНО РАБОТАЕТ"
else
    echo "❌ Cloudflare Worker: НЕ РАБОТАЕТ"
fi

if [ $cache_time -lt 5 ]; then
    echo "✅ Кэш локаций: ОТЛИЧНО (${cache_time}мс)"
else
    echo "🟨 Кэш локаций: ТРЕБУЕТ ВНИМАНИЯ (${cache_time}мс)"
fi

echo
echo "📋 РЕКОМЕНДАЦИИ:"
if [ "$com_result" != "webp" ]; then
    echo "🔧 1. Подождать активацию Worker для .com домена (может занять до 15 минут)"
fi

if [ $db_time -gt 10 ]; then
    echo "🔧 2. Проверить индексы базы данных"
fi

echo "🔧 3. Рассмотреть дополнительные оптимизации из файла CLOUDFLARE_WORKER_ADVANCED_OPTIMIZATIONS.md"

echo
echo "✨ Проверка завершена!" 