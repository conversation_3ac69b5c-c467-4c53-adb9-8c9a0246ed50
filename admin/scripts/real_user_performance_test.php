#!/usr/bin/env php
<?php
/**
 * @file
 * Тестирование производительности РЕАЛЬНЫХ пользовательских запросов
 * 
 * Имитирует типичные поисковые сценарии пользователей сайта недвижимости
 * 
 * Использование:
 * php admin/scripts/real_user_performance_test.php [action]
 * 
 * Действия:
 * - before: Тест ДО создания индексов (показывает текущую скорость)
 * - after: Тест ПОСЛЕ создания индексов (показывает улучшения)
 * - compare: Сравнение до и после
 * - live: Непрерывный мониторинг производительности
 */

// Устанавливаем переменные окружения для CLI
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';

define('DRUPAL_ROOT', dirname(dirname(__DIR__)));
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

class RealUserPerformanceTester {
  
  private $test_results = array();
  private $scenarios = array();
  
  public function __construct() {
    $this->initializeTestScenarios();
    echo "=== ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ РЕАЛЬНЫХ ПОЛЬЗОВАТЕЛЬСКИХ ЗАПРОСОВ ===\n";
    echo "Время: " . date('Y-m-d H:i:s') . "\n";
    echo "База данных: u823166234_indreams\n\n";
  }
  
  /**
   * Инициализация тестовых сценариев на основе реальных запросов пользователей
   */
  private function initializeTestScenarios() {
    $this->scenarios = array(
      
      'popular_3br_sale' => array(
        'name' => '🏠 ПОПУЛЯРНЫЙ: 3-комнатные на продажу до 30млн',
        'description' => 'Самый частый запрос: семьи ищут 3-комнатные квартиры',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property 
                  WHERE published = 1 
                    AND dealType = 'sale' 
                    AND bedrooms = 3 
                    AND price_sale <= 30000000",
        'expected_count' => '~300 объектов'
      ),
      
      'bangtao_luxury' => array(
        'name' => '🏖️ ПЛЯЖ БАНГТАО: 2-3 комнаты до 50млн',
        'description' => 'Пример из вашего запроса: премиум локация, семейное жилье',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property p 
                  WHERE p.published = 1 
                    AND p.dealType = 'sale' 
                    AND p.bedrooms IN (2, 3) 
                    AND p.price_sale <= 50000000",
        'expected_count' => 'топ объекты'
      ),
      
      'budget_rent' => array(
        'name' => '💰 БЮДЖЕТ: Аренда до 5000/день',
        'description' => 'Массовый сегмент: туристы ищут недорогое жилье',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property 
                  WHERE published = 1 
                    AND dealType = 'rent' 
                    AND price_rent <= 5000",
        'expected_count' => '~400 объектов'
      ),
      
      'investment_studio' => array(
        'name' => '📈 ИНВЕСТИЦИИ: Студии на продажу до 10млн',
        'description' => 'Инвесторы ищут недорогие студии для сдачи',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property 
                  WHERE published = 1 
                    AND dealType = 'sale' 
                    AND bedrooms = 1 
                    AND price_sale <= 10000000",
        'expected_count' => '~250 объектов'
      ),
      
      'premium_villas' => array(
        'name' => '🏖️ ПРЕМИУМ: Виллы 4+ спален от 40млн',
        'description' => 'VIP сегмент: состоятельные покупатели ищут виллы',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property p 
                  WHERE p.published = 1 
                    AND p.dealType = 'sale' 
                    AND p.bedrooms >= 4 
                    AND p.price_sale >= 40000000",
        'expected_count' => 'топ-15 самых дорогих'
      ),
      
      'area_search' => array(
        'name' => '📐 ПО ПЛОЩАДИ: От 100 до 200 кв.м на продажу',
        'description' => 'Поиск по площади - популярный фильтр',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property 
                  WHERE published = 1 
                    AND dealType = 'sale' 
                    AND areaCommon BETWEEN 100 AND 200",
        'expected_count' => '~500 объектов'
      ),
      
      'complex_filter' => array(
        'name' => '🎯 СЛОЖНЫЙ ФИЛЬТР: 2-3 спальни, аренда 3000-8000, с площадью',
        'description' => 'Реальный комплексный поиск туристов',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property p 
                  WHERE p.published = 1 
                    AND p.dealType = 'rent' 
                    AND p.bedrooms IN (2, 3)
                    AND p.price_rent BETWEEN 3000 AND 8000
                    AND p.areaCommon > 50",
        'expected_count' => 'лучшие по цене'
      ),
      
      'new_listings' => array(
        'name' => '🆕 НОВИНКИ: Недавно добавленные объекты',
        'description' => 'Пользователи часто ищут новые предложения',
        'sql' => "SELECT COUNT(*) FROM _phuket_Property p 
                  WHERE p.published = 1 
                    AND p.changed >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY))",
        'expected_count' => 'последние за месяц'
      )
      
    );
  }
  
  /**
   * Тестирование ДО создания индексов
   */
  public function testBefore() {
    echo "🔍 ТЕСТИРОВАНИЕ ДО СОЗДАНИЯ ИНДЕКСОВ\n";
    echo "Показывает ТЕКУЩУЮ скорость запросов\n\n";
    
    $this->runAllTests('before');
    $this->showSummary('before');
  }
  
  /**
   * Тестирование ПОСЛЕ создания индексов
   */
  public function testAfter() {
    echo "🚀 ТЕСТИРОВАНИЕ ПОСЛЕ СОЗДАНИЯ ИНДЕКСОВ\n";
    echo "Показывает УЛУЧШЕННУЮ скорость запросов\n\n";
    
    $this->runAllTests('after');
    $this->showSummary('after');
  }
  
  /**
   * Сравнение результатов до и после
   */
  public function compare() {
    if (empty($this->test_results['before']) || empty($this->test_results['after'])) {
      echo "❌ ОШИБКА: Нужно запустить тесты 'before' и 'after' перед сравнением\n";
      echo "Запустите:\n";
      echo "  php real_user_performance_test.php before\n";
      echo "  php real_user_performance_test.php after\n\n";
      return;
    }
    
    echo "📊 СРАВНЕНИЕ ПРОИЗВОДИТЕЛЬНОСТИ\n\n";
    
    printf("%-50s %-12s %-12s %-15s\n", "СЦЕНАРИЙ", "ДО (мс)", "ПОСЛЕ (мс)", "УЛУЧШЕНИЕ");
    echo str_repeat("=", 90) . "\n";
    
    foreach ($this->scenarios as $key => $scenario) {
      $before = $this->test_results['before'][$key]['time'];
      $after = $this->test_results['after'][$key]['time'];
      $improvement = round((($before - $after) / $before) * 100, 1);
      $improvement_text = $improvement > 0 ? "+{$improvement}%" : "{$improvement}%";
      
      $color = $improvement > 50 ? "🚀" : ($improvement > 20 ? "⚡" : ($improvement > 0 ? "✅" : "⚠️"));
      
      printf("%-50s %-12.2f %-12.2f %s %-10s\n", 
             substr($scenario['name'], 0, 47), 
             $before, 
             $after, 
             $color,
             $improvement_text
      );
    }
    
    echo str_repeat("=", 90) . "\n";
    
    // Общая статистика
    $total_before = array_sum(array_column($this->test_results['before'], 'time'));
    $total_after = array_sum(array_column($this->test_results['after'], 'time'));
    $overall_improvement = round((($total_before - $total_after) / $total_before) * 100, 1);
    
    echo "\n📈 ОБЩАЯ СТАТИСТИКА:\n";
    echo "  Общее время ДО:     " . round($total_before, 2) . " мс\n";
    echo "  Общее время ПОСЛЕ:  " . round($total_after, 2) . " мс\n";
    echo "  Общее улучшение:    {$overall_improvement}%\n\n";
  }
  
  /**
   * Непрерывный мониторинг
   */
  public function liveMonitoring() {
    echo "📡 ЖИВОЙ МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ\n";
    echo "Обновляется каждые 30 секунд. Нажмите Ctrl+C для остановки.\n\n";
    
    $iteration = 1;
    while (true) {
      echo "--- Итерация #{$iteration} в " . date('H:i:s') . " ---\n";
      
      // Тестируем 3 ключевых запроса
      $key_tests = array('popular_3br_sale', 'bangtao_luxury', 'complex_filter');
      foreach ($key_tests as $test_key) {
        $time = $this->runSingleTest($test_key);
        echo "  {$this->scenarios[$test_key]['name']}: " . round($time, 2) . " мс\n";
      }
      
      echo "\n";
      $iteration++;
      sleep(30);
    }
  }
  
  /**
   * Запуск всех тестов
   */
  private function runAllTests($phase) {
    $total_time = 0;
    $test_count = 0;
    
    foreach ($this->scenarios as $key => $scenario) {
      echo "Тестирование: " . $scenario['name'] . "\n";
      echo "Описание: " . $scenario['description'] . "\n";
      
      $time = $this->runSingleTest($key);
      $this->test_results[$phase][$key] = array(
        'time' => $time,
        'scenario' => $scenario
      );
      
      $status = $time < 50 ? "🚀 БЫСТРО" : ($time < 200 ? "⚡ НОРМАЛЬНО" : ($time < 1000 ? "⚠️ МЕДЛЕННО" : "🐌 ОЧЕНЬ МЕДЛЕННО"));
      echo "Результат: " . round($time, 2) . " мс ($status)\n";
      echo "Ожидаемый результат: " . $scenario['expected_count'] . "\n\n";
      
      $total_time += $time;
      $test_count++;
    }
    
    echo "✅ ВСЕ ТЕСТЫ ЗАВЕРШЕНЫ\n";
    echo "Общее время: " . round($total_time, 2) . " мс\n";
    echo "Среднее время: " . round($total_time / $test_count, 2) . " мс\n\n";
  }
  
  /**
   * Запуск одного теста с измерением времени
   */
  private function runSingleTest($test_key) {
    $scenario = $this->scenarios[$test_key];
    
    $start_time = microtime(true);
    
    try {
      $result = db_query($scenario['sql']);
      
      // Принудительно получаем все результаты
      if (method_exists($result, 'fetchAll')) {
        $result->fetchAll();
      } else {
        // Для COUNT запросов
        $result->fetchColumn();
      }
    } catch (Exception $e) {
      echo "❌ ОШИБКА в запросе {$test_key}: " . $e->getMessage() . "\n";
      return 9999; // Возвращаем очень большое время в случае ошибки
    }
    
    $end_time = microtime(true);
    return ($end_time - $start_time) * 1000; // Возвращаем время в миллисекундах
  }
  
  /**
   * Показать подробную сводку
   */
  private function showSummary($phase) {
    echo "📊 ПОДРОБНАЯ СВОДКА ТЕСТОВ ({$phase})\n\n";
    
    $times = array_column($this->test_results[$phase], 'time');
    $avg_time = array_sum($times) / count($times);
    $min_time = min($times);
    $max_time = max($times);
    
    echo "⚡ Самый быстрый запрос: " . round($min_time, 2) . " мс\n";
    echo "🐌 Самый медленный запрос: " . round($max_time, 2) . " мс\n";
    echo "📈 Среднее время: " . round($avg_time, 2) . " мс\n\n";
    
    // Категоризация запросов по скорости
    $fast = $normal = $slow = $very_slow = 0;
    foreach ($times as $time) {
      if ($time < 50) $fast++;
      elseif ($time < 200) $normal++;
      elseif ($time < 1000) $slow++;
      else $very_slow++;
    }
    
    echo "🏆 РАСПРЕДЕЛЕНИЕ ПО СКОРОСТИ:\n";
    echo "  🚀 Быстрые (<50мс):     {$fast} запросов\n";
    echo "  ⚡ Нормальные (50-200мс): {$normal} запросов\n";
    echo "  ⚠️ Медленные (200мс-1с):  {$slow} запросов\n";
    echo "  🐌 Очень медленные (>1с): {$very_slow} запросов\n\n";
    
    // Рекомендации
    if ($very_slow > 0 || $slow > 3) {
      echo "🔥 КРИТИЧЕСКИЕ РЕКОМЕНДАЦИИ:\n";
      echo "  - Необходимо срочно создать индексы для поиска\n";
      echo "  - Рассмотреть денормализацию данных\n";
      echo "  - Внедрить кэширование результатов\n\n";
    } elseif ($slow > 0) {
      echo "⚡ РЕКОМЕНДАЦИИ ДЛЯ УЛУЧШЕНИЯ:\n";
      echo "  - Создать дополнительные составные индексы\n";
      echo "  - Оптимизировать сложные JOIN запросы\n\n";
    } else {
      echo "✅ ПРОИЗВОДИТЕЛЬНОСТЬ ОТЛИЧНАЯ!\n\n";
    }
  }
  
  /**
   * Проверка существования индексов
   */
  public function checkIndexes() {
    echo "🔍 ПРОВЕРКА СУЩЕСТВУЮЩИХ ИНДЕКСОВ\n\n";
    
    $indexes = db_query("SHOW INDEX FROM _phuket_Property WHERE Key_name LIKE 'idx_%'")->fetchAll();
    
    if (empty($indexes)) {
      echo "❌ Оптимизационные индексы НЕ НАЙДЕНЫ\n";
      echo "Рекомендуется запустить:\n";
      echo "  mysql -u root -p u823166234_indreams < admin/scripts/immediate_safe_optimizations.sql\n\n";
    } else {
      echo "✅ Найдены оптимизационные индексы:\n";
      foreach ($indexes as $index) {
        echo "  - " . $index->Key_name . " (" . $index->Column_name . ")\n";
      }
      echo "\n";
    }
  }
}

// Запуск скрипта
$action = isset($argv[1]) ? $argv[1] : 'before';
$tester = new RealUserPerformanceTester();

switch ($action) {
  case 'before':
    $tester->checkIndexes();
    $tester->testBefore();
    break;
    
  case 'after':
    $tester->checkIndexes();
    $tester->testAfter();
    break;
    
  case 'compare':
    $tester->compare();
    break;
    
  case 'live':
    $tester->liveMonitoring();
    break;
    
  default:
    echo "Использование: php real_user_performance_test.php [before|after|compare|live]\n";
    echo "\n";
    echo "Команды:\n";
    echo "  before   - Тест ДО создания индексов (показывает текущую скорость)\n";
    echo "  after    - Тест ПОСЛЕ создания индексов (показывает улучшения)\n";
    echo "  compare  - Сравнение результатов до и после\n";
    echo "  live     - Непрерывный мониторинг производительности\n";
} 