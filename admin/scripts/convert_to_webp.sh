#!/bin/bash

# Директории с изображениями
IMAGE_DIRS=(
  "sites/default/files"
  "sites/all/themes"
)

# Параметры качества WebP
QUALITY=80
COUNT=0
SIZE_ORIGINAL=0
SIZE_WEBP=0

echo "===== Начинаю конвертацию изображений в WebP (качество: $QUALITY%) ====="

for dir in "${IMAGE_DIRS[@]}"; do
  if [ -d "$dir" ]; then
    echo "Обрабатываю директорию: $dir"
    
    # Находим все JPG/PNG файлы
    find "$dir" -type f \( -name "*.jpg" -o -name "*.jpeg" -o -name "*.png" \) | while read img; do
      # Проверяем, существует ли уже WebP версия
      webp_file="${img}.webp"
      
      if [ ! -f "$webp_file" ]; then
        echo "Конвертирую: $img"
        
        # Сохраняем размер оригинала
        orig_size=$(stat -c%s "$img")
        SIZE_ORIGINAL=$((SIZE_ORIGINAL + orig_size))
        
        # Конвертируем в WebP
        cwebp -quiet -q $QUALITY "$img" -o "$webp_file"
        
        # Проверяем успешность и размер WebP
        if [ -f "$webp_file" ]; then
          webp_size=$(stat -c%s "$webp_file")
          SIZE_WEBP=$((SIZE_WEBP + webp_size))
          
          # Процент сжатия
          if [ $orig_size -ne 0 ]; then
            compression=$((100 - (webp_size * 100 / orig_size)))
            echo "  Сжатие: $compression% (Было: ${orig_size}B, Стало: ${webp_size}B)"
          fi
          
          COUNT=$((COUNT + 1))
        else
          echo "  ОШИБКА конвертации: $img"
        fi
      fi
    done
  else
    echo "Директория не найдена: $dir"
  fi
done

# Выводим итоговую статистику
echo "===== Статистика конвертации ====="
echo "Конвертировано файлов: $COUNT"

if [ $SIZE_ORIGINAL -ne 0 ]; then
  total_compression=$((100 - (SIZE_WEBP * 100 / SIZE_ORIGINAL)))
  echo "Общее сжатие: $total_compression%"
  echo "Было: $(echo "scale=2; $SIZE_ORIGINAL/1048576" | bc) MB"
  echo "Стало: $(echo "scale=2; $SIZE_WEBP/1048576" | bc) MB"
  echo "Экономия: $(echo "scale=2; ($SIZE_ORIGINAL-$SIZE_WEBP)/1048576" | bc) MB"
fi

echo "===== Конвертация завершена ====="
