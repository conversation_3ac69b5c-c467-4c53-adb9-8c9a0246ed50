<?php

/**
 * @file
 * Used to aggregate syndicated content (RSS, RDF, and Atom).
 */

/**
 * Denotes that a feed's items should never expire.
 */
define('AGGREGATOR_CLEAR_NEVER', 0);

/**
 * Implements hook_help().
 */
function aggregator_help($path, $arg) {
  switch ($path) {
    case 'admin/help#aggregator':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The Aggregator module is an on-site syndicator and news reader that gathers and displays fresh content from RSS-, RDF-, and Atom-based feeds made available across the web. Thousands of sites (particularly news sites and blogs) publish their latest headlines in feeds, using a number of standardized XML-based formats. For more information, see the online handbook entry for <a href="@aggregator-module">Aggregator module</a>.', array('@aggregator-module' => 'http://drupal.org/documentation/modules/aggregator', '@aggregator' => url('aggregator'))) . '</p>';
      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<dl>';
      $output .= '<dt>' . t('Viewing feeds') . '</dt>';
      $output .= '<dd>' . t('Feeds contain published content, and may be grouped in categories, generally by topic. Users view feed content in the <a href="@aggregator">main aggregator display</a>, or by <a href="@aggregator-sources">their source</a> (usually via an RSS feed reader). The most recent content in a feed or category can be displayed as a block through the <a href="@admin-block">Blocks administration page</a>.', array('@aggregator' => url('aggregator'), '@aggregator-sources' => url('aggregator/sources'), '@admin-block' => url('admin/structure/block'))) . '</a></dd>';
      $output .= '<dt>' . t('Adding, editing, and deleting feeds') . '</dt>';
      $output .= '<dd>' . t('Administrators can add, edit, and delete feeds, and choose how often to check each feed for newly updated items on the <a href="@feededit">Feed aggregator administration page</a>.', array('@feededit' => url('admin/config/services/aggregator'))) . '</dd>';
      $output .= '<dt>' . t('OPML integration') . '</dt>';
      $output .= '<dd>' . t('A <a href="@aggregator-opml">machine-readable OPML file</a> of all feeds is available. OPML is an XML-based file format used to share outline-structured information such as a list of RSS feeds. Feeds can also be <a href="@import-opml">imported via an OPML file</a>.', array('@aggregator-opml' => url('aggregator/opml'), '@import-opml' => url('admin/config/services/aggregator'))) . '</dd>';
      $output .= '<dt>' . t('Configuring cron') . '</dt>';
      $output .= '<dd>' . t('A correctly configured <a href="@cron">cron maintenance task</a> is required to update feeds automatically.', array('@cron' => 'http://drupal.org/cron')) . '</dd>';
      $output .= '</dl>';
      return $output;
    case 'admin/config/services/aggregator':
      $output = '<p>' . t('Thousands of sites (particularly news sites and blogs) publish their latest headlines and posts in feeds, using a number of standardized XML-based formats. Formats supported by the aggregator include <a href="@rss">RSS</a>, <a href="@rdf">RDF</a>, and <a href="@atom">Atom</a>.', array('@rss' => 'http://cyber.law.harvard.edu/rss/', '@rdf' => 'http://www.w3.org/RDF/', '@atom' => 'http://www.atomenabled.org')) . '</p>';
      $output .= '<p>' . t('Current feeds are listed below, and <a href="@addfeed">new feeds may be added</a>. For each feed or feed category, the <em>latest items</em> block may be enabled at the <a href="@block">blocks administration page</a>.', array('@addfeed' => url('admin/config/services/aggregator/add/feed'), '@block' => url('admin/structure/block'))) . '</p>';
      return $output;
    case 'admin/config/services/aggregator/add/feed':
      return '<p>' . t('Add a feed in RSS, RDF or Atom format. A feed may only have one entry.') . '</p>';
    case 'admin/config/services/aggregator/add/category':
      return '<p>' . t('Categories allow feed items from different feeds to be grouped together. For example, several sport-related feeds may belong to a category named <em>Sports</em>. Feed items may be grouped automatically (by selecting a category when creating or editing a feed) or manually (via the <em>Categorize</em> page available from feed item listings). Each category provides its own feed page and block.') . '</p>';
    case 'admin/config/services/aggregator/add/opml':
      return '<p>' . t('<acronym title="Outline Processor Markup Language">OPML</acronym> is an XML format used to exchange multiple feeds between aggregators. A single OPML document may contain a collection of many feeds. Drupal can parse such a file and import all feeds at once, saving you the effort of adding them manually. You may either upload a local file from your computer or enter a URL where Drupal can download it.') . '</p>';
  }
}

/**
 * Implements hook_theme().
 */
function aggregator_theme() {
  return array(
    'aggregator_wrapper' => array(
      'variables' => array('content' => NULL),
      'file' => 'aggregator.pages.inc',
      'template' => 'aggregator-wrapper',
    ),
    'aggregator_categorize_items' => array(
      'render element' => 'form',
      'file' => 'aggregator.pages.inc',
    ),
    'aggregator_feed_source' => array(
      'variables' => array('feed' => NULL),
      'file' => 'aggregator.pages.inc',
      'template' => 'aggregator-feed-source',
    ),
    'aggregator_block_item' => array(
      'variables' => array('item' => NULL, 'feed' => 0),
    ),
    'aggregator_summary_items' => array(
      'variables' => array('summary_items' => NULL, 'source' => NULL),
      'file' => 'aggregator.pages.inc',
      'template' => 'aggregator-summary-items',
    ),
    'aggregator_summary_item' => array(
      'variables' => array('item' => NULL),
      'file' => 'aggregator.pages.inc',
      'template' => 'aggregator-summary-item',
    ),
    'aggregator_item' => array(
      'variables' => array('item' => NULL),
      'file' => 'aggregator.pages.inc',
      'template' => 'aggregator-item',
    ),
    'aggregator_page_opml' => array(
      'variables' => array('feeds' => NULL),
      'file' => 'aggregator.pages.inc',
    ),
    'aggregator_page_rss' => array(
      'variables' => array('feeds' => NULL, 'category' => NULL),
      'file' => 'aggregator.pages.inc',
    ),
  );
}

/**
 * Implements hook_menu().
 */
function aggregator_menu() {
  $items['admin/config/services/aggregator'] = array(
    'title' => 'Feed aggregator',
    'description' => "Configure which content your site aggregates from other sites, how often it polls them, and how they're categorized.",
    'page callback' => 'aggregator_admin_overview',
    'access arguments' => array('administer news feeds'),
    'weight' => 10,
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/add/feed'] = array(
    'title' => 'Add feed',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_feed'),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/add/category'] = array(
    'title' => 'Add category',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_category'),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/add/opml'] = array(
    'title' => 'Import OPML',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_opml'),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_ACTION,
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/remove/%aggregator_feed'] = array(
    'title' => 'Remove items',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_admin_remove_feed', 5),
    'access arguments' => array('administer news feeds'),
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/update/%aggregator_feed'] = array(
    'title' => 'Update items',
    'page callback' => 'aggregator_admin_refresh_feed',
    'page arguments' => array(5),
    'access arguments' => array('administer news feeds'),
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/list'] = array(
    'title' => 'List',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['admin/config/services/aggregator/settings'] = array(
    'title' => 'Settings',
    'description' => 'Configure the behavior of the feed aggregator, including when to discard feed items and how to present feed items and categories.',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_admin_form'),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_TASK,
    'file' => 'aggregator.admin.inc',
  );
  $items['aggregator'] = array(
    'title' => 'Feed aggregator',
    'page callback' => 'aggregator_page_last',
    'access arguments' => array('access news feeds'),
    'weight' => 5,
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/sources'] = array(
    'title' => 'Sources',
    'page callback' => 'aggregator_page_sources',
    'access arguments' => array('access news feeds'),
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/categories'] = array(
    'title' => 'Categories',
    'page callback' => 'aggregator_page_categories',
    'access callback' => '_aggregator_has_categories',
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/rss'] = array(
    'title' => 'RSS feed',
    'page callback' => 'aggregator_page_rss',
    'access arguments' => array('access news feeds'),
    'type' => MENU_CALLBACK,
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/opml'] = array(
    'title' => 'OPML feed',
    'page callback' => 'aggregator_page_opml',
    'access arguments' => array('access news feeds'),
    'type' => MENU_CALLBACK,
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/categories/%aggregator_category'] = array(
    'title callback' => '_aggregator_category_title',
    'title arguments' => array(2),
    'page callback' => 'aggregator_page_category',
    'page arguments' => array(2),
    'access arguments' => array('access news feeds'),
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/categories/%aggregator_category/view'] = array(
    'title' => 'View',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['aggregator/categories/%aggregator_category/categorize'] = array(
    'title' => 'Categorize',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_page_category_form', 2),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_TASK,
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/categories/%aggregator_category/configure'] = array(
    'title' => 'Configure',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_category', 2),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_TASK,
    'weight' => 1,
    'file' => 'aggregator.admin.inc',
  );
  $items['aggregator/sources/%aggregator_feed'] = array(
    'page callback' => 'aggregator_page_source',
    'page arguments' => array(2),
    'access arguments' => array('access news feeds'),
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/sources/%aggregator_feed/view'] = array(
    'title' => 'View',
    'type' => MENU_DEFAULT_LOCAL_TASK,
    'weight' => -10,
  );
  $items['aggregator/sources/%aggregator_feed/categorize'] = array(
    'title' => 'Categorize',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_page_source_form', 2),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_TASK,
    'file' => 'aggregator.pages.inc',
  );
  $items['aggregator/sources/%aggregator_feed/configure'] = array(
    'title' => 'Configure',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_feed', 2),
    'access arguments' => array('administer news feeds'),
    'type' => MENU_LOCAL_TASK,
    'weight' => 1,
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/edit/feed/%aggregator_feed'] = array(
    'title' => 'Edit feed',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_feed', 6),
    'access arguments' => array('administer news feeds'),
    'file' => 'aggregator.admin.inc',
  );
  $items['admin/config/services/aggregator/edit/category/%aggregator_category'] = array(
    'title' => 'Edit category',
    'page callback' => 'drupal_get_form',
    'page arguments' => array('aggregator_form_category', 6),
    'access arguments' => array('administer news feeds'),
    'file' => 'aggregator.admin.inc',
  );

  return $items;
}

/**
 * Title callback: Returns a title for aggregator category pages.
 *
 * @param $category
 *   An aggregator category.
 *
 * @return
 *   A string with the aggregator category title.
 */
function _aggregator_category_title($category) {
  return $category['title'];
}

/**
 * Determines whether there are any aggregator categories.
 *
 * @return
 *   TRUE if there is at least one category and the user has access to them;
 *   FALSE otherwise.
 */
function _aggregator_has_categories() {
  return user_access('access news feeds') && (bool) db_query_range('SELECT 1 FROM {aggregator_category}', 0, 1)->fetchField();
}

/**
 * Implements hook_permission().
 */
function aggregator_permission() {
  return array(
    'administer news feeds' => array(
      'title' => t('Administer news feeds'),
    ),
    'access news feeds' => array(
      'title' => t('View news feeds'),
    ),
  );
}

/**
 * Implements hook_cron().
 *
 * Queues news feeds for updates once their refresh interval has elapsed.
 */
function aggregator_cron() {
  $result = db_query('SELECT * FROM {aggregator_feed} WHERE queued = 0 AND checked + refresh < :time AND refresh <> :never', array(
    ':time' => REQUEST_TIME,
    ':never' => AGGREGATOR_CLEAR_NEVER
  ));
  $queue = DrupalQueue::get('aggregator_feeds');
  foreach ($result as $feed) {
    if ($queue->createItem($feed)) {
      // Add timestamp to avoid queueing item more than once.
      db_update('aggregator_feed')
        ->fields(array('queued' => REQUEST_TIME))
        ->condition('fid', $feed->fid)
        ->execute();
    }
  }

  // Remove queued timestamp after 6 hours assuming the update has failed.
  db_update('aggregator_feed')
    ->fields(array('queued' => 0))
    ->condition('queued', REQUEST_TIME - (3600 * 6), '<')
    ->execute();
}

/**
 * Implements hook_cron_queue_info().
 */
function aggregator_cron_queue_info() {
  $queues['aggregator_feeds'] = array(
    'worker callback' => 'aggregator_refresh',
    'time' => 60,
  );
  return $queues;
}

/**
 * Implements hook_block_info().
 */
function aggregator_block_info() {
  $blocks = array();
  $result = db_query('SELECT cid, title FROM {aggregator_category} ORDER BY title');
  foreach ($result as $category) {
    $blocks['category-' . $category->cid]['info'] = t('!title category latest items', array('!title' => $category->title));
  }
  $result = db_query('SELECT fid, title FROM {aggregator_feed} WHERE block <> 0 ORDER BY fid');
  foreach ($result as $feed) {
    $blocks['feed-' . $feed->fid]['info'] = t('!title feed latest items', array('!title' => $feed->title));
  }
  return $blocks;
}

/**
 * Implements hook_block_configure().
 */
function aggregator_block_configure($delta = '') {
  list($type, $id) = explode('-', $delta);
  if ($type == 'category') {
    $value = db_query('SELECT block FROM {aggregator_category} WHERE cid = :cid', array(':cid' => $id))->fetchField();
    $form['block'] = array(
      '#type' => 'select',
      '#title' => t('Number of news items in block'),
      '#default_value' => $value,
      '#options' => drupal_map_assoc(range(2, 20)),
    );
    return $form;
  }
}

/**
 * Implements hook_block_save().
 */
function aggregator_block_save($delta = '', $edit = array()) {
  list($type, $id) = explode('-', $delta);
  if ($type == 'category') {
    db_update('aggregator_category')
      ->fields(array('block' => $edit['block']))
      ->condition('cid', $id)
      ->execute();
  }
}

/**
 * Implements hook_block_view().
 *
 * Generates blocks for the latest news items in each category and feed.
 */
function aggregator_block_view($delta = '') {
  if (user_access('access news feeds')) {
    $block = array();
    list($type, $id) = explode('-', $delta);
    $result = FALSE;
    switch ($type) {
      case 'feed':
        if ($feed = db_query('SELECT fid, title, block FROM {aggregator_feed} WHERE block <> 0 AND fid = :fid', array(':fid' => $id))->fetchObject()) {
          $block['subject'] = check_plain($feed->title);
          $result = db_query_range("SELECT * FROM {aggregator_item} WHERE fid = :fid ORDER BY timestamp DESC, iid DESC", 0, $feed->block, array(':fid' => $id));
          $read_more = theme('more_link', array('url' => 'aggregator/sources/' . $feed->fid, 'title' => t("View this feed's recent news.")));
        }
        break;

      case 'category':
        if ($category = db_query('SELECT cid, title, block FROM {aggregator_category} WHERE cid = :cid', array(':cid' => $id))->fetchObject()) {
          $block['subject'] = check_plain($category->title);
          $result = db_query_range('SELECT i.* FROM {aggregator_category_item} ci LEFT JOIN {aggregator_item} i ON ci.iid = i.iid WHERE ci.cid = :cid ORDER BY i.timestamp DESC, i.iid DESC', 0, $category->block, array(':cid' => $category->cid));
          $read_more = theme('more_link', array('url' => 'aggregator/categories/' . $category->cid, 'title' => t("View this category's recent news.")));
        }
        break;
    }

    $items = array();
    if (!empty($result)) {
      foreach ($result as $item) {
        $items[] = theme('aggregator_block_item', array('item' => $item));
      }
    }

    // Only display the block if there are items to show.
    if (count($items) > 0) {
      $block['content'] = theme('item_list', array('items' => $items)) . $read_more;
    }
    return $block;
  }
}

/**
 * Adds/edits/deletes aggregator categories.
 *
 * @param $edit
 *   An associative array describing the category to be added/edited/deleted.
 */
function aggregator_save_category($edit) {
  $link_path = 'aggregator/categories/';
  if (!empty($edit['cid'])) {
    $link_path .= $edit['cid'];
    if (!empty($edit['title'])) {
      db_merge('aggregator_category')
        ->key(array('cid' => $edit['cid']))
        ->fields(array(
          'title' => $edit['title'],
          'description' => $edit['description'],
        ))
        ->execute();
      $op = 'update';
    }
    else {
      db_delete('aggregator_category')
        ->condition('cid', $edit['cid'])
        ->execute();
      // Remove category from feeds.
      db_delete('aggregator_category_feed')
        ->condition('cid', $edit['cid'])
        ->execute();
      // Remove category from feed items.
      db_delete('aggregator_category_item')
        ->condition('cid', $edit['cid'])
        ->execute();
      // Make sure there is no active block for this category.
      if (module_exists('block')) {
        db_delete('block')
          ->condition('module', 'aggregator')
          ->condition('delta', 'category-' . $edit['cid'])
          ->execute();
      }
      $edit['title'] = '';
      $op = 'delete';
    }
  }
  elseif (!empty($edit['title'])) {
    // A single unique id for bundles and feeds, to use in blocks.
    $link_path .= db_insert('aggregator_category')
      ->fields(array(
        'title' => $edit['title'],
        'description' => $edit['description'],
        'block' => 5,
      ))
      ->execute();
    $op = 'insert';
  }
  if (isset($op)) {
    menu_link_maintain('aggregator', $op, $link_path, $edit['title']);
  }
}

/**
 * Add/edit/delete an aggregator feed.
 *
 * @param $edit
 *   An associative array describing the feed to be added/edited/deleted.
 */
function aggregator_save_feed($edit) {
  if (!empty($edit['fid'])) {
    // An existing feed is being modified, delete the category listings.
    db_delete('aggregator_category_feed')
      ->condition('fid', $edit['fid'])
      ->execute();
  }
  if (!empty($edit['fid']) && !empty($edit['title'])) {
    db_update('aggregator_feed')
      ->condition('fid', $edit['fid'])
      ->fields(array(
        'title' => $edit['title'],
        'url' => $edit['url'],
        'refresh' => $edit['refresh'],
        'block' => $edit['block'],
      ))
      ->execute();
  }
  elseif (!empty($edit['fid'])) {
    $iids = db_query('SELECT iid FROM {aggregator_item} WHERE fid = :fid', array(':fid' => $edit['fid']))->fetchCol();
    if ($iids) {
      db_delete('aggregator_category_item')
        ->condition('iid', $iids, 'IN')
        ->execute();
    }
    db_delete('aggregator_feed')->
      condition('fid', $edit['fid'])
      ->execute();
    db_delete('aggregator_item')
      ->condition('fid', $edit['fid'])
      ->execute();
    // Make sure there is no active block for this feed.
    if (module_exists('block')) {
      db_delete('block')
        ->condition('module', 'aggregator')
        ->condition('delta', 'feed-' . $edit['fid'])
        ->execute();
    }
  }
  elseif (!empty($edit['title'])) {
    $edit['fid'] = db_insert('aggregator_feed')
      ->fields(array(
        'title' => $edit['title'],
        'url' => $edit['url'],
        'refresh' => $edit['refresh'],
        'block' => $edit['block'],
        'link' => '',
        'description' => '',
        'image' => '',
      ))
      ->execute();

  }
  if (!empty($edit['title'])) {
    // The feed is being saved, save the categories as well.
    if (!empty($edit['category'])) {
      foreach ($edit['category'] as $cid => $value) {
        if ($value) {
          db_insert('aggregator_category_feed')
            ->fields(array(
              'fid' => $edit['fid'],
              'cid' => $cid,
            ))
            ->execute();
        }
      }
    }
  }
}

/**
 * Removes all items from a feed.
 *
 * @param $feed
 *   An object describing the feed to be cleared.
 */
function aggregator_remove($feed) {
  _aggregator_get_variables();
  // Call hook_aggregator_remove() on all modules.
  module_invoke_all('aggregator_remove', $feed);
  // Reset feed.
  db_update('aggregator_feed')
    ->condition('fid', $feed->fid)
    ->fields(array(
      'checked' => 0,
      'hash' => '',
      'etag' => '',
      'modified' => 0,
    ))
    ->execute();
}

/**
 * Gets the fetcher, parser, and processors.
 *
 * @return
 *   An array containing the fetcher, parser, and processors.
 */
function _aggregator_get_variables() {
  // Fetch the feed.
  $fetcher = variable_get('aggregator_fetcher', 'aggregator');
  if ($fetcher == 'aggregator') {
    include_once DRUPAL_ROOT . '/' . drupal_get_path('module', 'aggregator') . '/aggregator.fetcher.inc';
  }
  $parser = variable_get('aggregator_parser', 'aggregator');
  if ($parser == 'aggregator') {
    include_once DRUPAL_ROOT . '/' . drupal_get_path('module', 'aggregator') . '/aggregator.parser.inc';
  }
  $processors = variable_get('aggregator_processors', array('aggregator'));
  if (in_array('aggregator', $processors)) {
    include_once DRUPAL_ROOT . '/' . drupal_get_path('module', 'aggregator') . '/aggregator.processor.inc';
  }
  return array($fetcher, $parser, $processors);
}

/**
 * Checks a news feed for new items.
 *
 * @param $feed
 *   An object describing the feed to be refreshed.
 */
function aggregator_refresh($feed) {
  // Store feed URL to track changes.
  $feed_url = $feed->url;

  // Fetch the feed.
  list($fetcher, $parser, $processors) = _aggregator_get_variables();
  $success = module_invoke($fetcher, 'aggregator_fetch', $feed);

  // We store the hash of feed data in the database. When refreshing a
  // feed we compare stored hash and new hash calculated from downloaded
  // data. If both are equal we say that feed is not updated.
  $hash = hash('sha256', $feed->source_string);

  if ($success && ($feed->hash != $hash)) {
    // Parse the feed.
    if (module_invoke($parser, 'aggregator_parse', $feed)) {
      // Update feed with parsed data.
      db_merge('aggregator_feed')
        ->key(array('fid' => $feed->fid))
        ->fields(array(
          'url' => $feed->url,
          'link' => empty($feed->link) ? $feed->url : $feed->link,
          'description' => empty($feed->description) ? '' : $feed->description,
          'image' => empty($feed->image) ? '' : $feed->image,
          'hash' => $hash,
          'etag' => empty($feed->etag) ? '' : $feed->etag,
          'modified' => empty($feed->modified) ? 0 : $feed->modified,
        ))
        ->execute();

      // Log if feed URL has changed.
      if ($feed->url != $feed_url) {
        watchdog('aggregator', 'Updated URL for feed %title to %url.', array('%title' => $feed->title, '%url' => $feed->url));
      }

      watchdog('aggregator', 'There is new syndicated content from %site.', array('%site' => $feed->title));
      drupal_set_message(t('There is new syndicated content from %site.', array('%site' => $feed->title)));

      // If there are items on the feed, let all enabled processors do their work on it.
      if (@count($feed->items)) {
        foreach ($processors as $processor) {
          module_invoke($processor, 'aggregator_process', $feed);
        }
      }
    }
  }
  else {
    drupal_set_message(t('There is no new syndicated content from %site.', array('%site' => $feed->title)));
  }

  // Regardless of successful or not, indicate that this feed has been checked.
  db_update('aggregator_feed')
    ->fields(array('checked' => REQUEST_TIME, 'queued' => 0))
    ->condition('fid', $feed->fid)
    ->execute();

  // Expire old feed items.
  if (function_exists('aggregator_expire')) {
    aggregator_expire($feed);
  }
}

/**
 * Loads an aggregator feed.
 *
 * @param $fid
 *   The feed id.
 *
 * @return
 *   An object describing the feed.
 */
function aggregator_feed_load($fid) {
  $feeds = &drupal_static(__FUNCTION__);
  if (!isset($feeds[$fid])) {
    $feeds[$fid] = db_query('SELECT * FROM {aggregator_feed} WHERE fid = :fid', array(':fid' => $fid))->fetchObject();
  }

  return $feeds[$fid];
}

/**
 * Loads an aggregator category.
 *
 * @param $cid
 *   The category id.
 *
 * @return
 *   An associative array describing the category.
 */
function aggregator_category_load($cid) {
  $categories = &drupal_static(__FUNCTION__);
  if (!isset($categories[$cid])) {
    $categories[$cid] = db_query('SELECT * FROM {aggregator_category} WHERE cid = :cid', array(':cid' => $cid))->fetchAssoc();
  }

  return $categories[$cid];
}

/**
 * Returns HTML for an individual feed item for display in the block.
 *
 * @param $variables
 *   An associative array containing:
 *   - item: The item to be displayed.
 *   - feed: Not used.
 *
 * @ingroup themeable
 */
function theme_aggregator_block_item($variables) {
  // Display the external link to the item.
  return '<a href="' . check_url($variables['item']->link) . '">' . check_plain($variables['item']->title) . "</a>\n";
}

/**
 * Renders the HTML content safely, as allowed.
 *
 * @param $value
 *   The content to be filtered.
 *
 * @return
 *   The filtered content.
 */
function aggregator_filter_xss($value) {
  return filter_xss($value, preg_split('/\s+|<|>/', variable_get('aggregator_allowed_html_tags', '<a> <b> <br> <dd> <dl> <dt> <em> <i> <li> <ol> <p> <strong> <u> <ul>'), -1, PREG_SPLIT_NO_EMPTY));
}

/**
 * Checks and sanitizes the aggregator configuration.
 *
 * Goes through all fetchers, parsers and processors and checks whether they
 * are available. If one is missing, resets to standard configuration.
 *
 * @return
 *   TRUE if this function resets the configuration; FALSE if not.
 */
function aggregator_sanitize_configuration() {
  $reset = FALSE;
  list($fetcher, $parser, $processors) = _aggregator_get_variables();
  if (!module_exists($fetcher)) {
    $reset = TRUE;
  }
  if (!module_exists($parser)) {
    $reset = TRUE;
  }
  foreach ($processors as $processor) {
    if (!module_exists($processor)) {
      $reset = TRUE;
      break;
    }
  }
  if ($reset) {
    variable_del('aggregator_fetcher');
    variable_del('aggregator_parser');
    variable_del('aggregator_processors');
    return TRUE;
  }
  return FALSE;
}

/**
 * Helper function for drupal_map_assoc.
 *
 * @param $count
 *   Items count.
 *
 * @return
 *   A string that is plural-formatted as "@count items".
 */
function _aggregator_items($count) {
  return format_plural($count, '1 item', '@count items');
}
