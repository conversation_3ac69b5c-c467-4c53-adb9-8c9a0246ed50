<?php

/**
 * Implements hook_menu().
 */
function aggregator_test_menu() {
  $items['aggregator/test-feed'] = array(
    'title' => 'Test feed static last modified date',
    'description' => "A cached test feed with a static last modified date.",
    'page callback' => 'aggregator_test_feed',
    'access arguments' => array('access content'),
    'type' => MENU_CALLBACK,
  );
  return $items;
}

/**
 * Page callback. Generates a test feed and simulates last-modified and etags.
 *
 * @param $use_last_modified
 *   Set TRUE to send a last modified header.
 * @param $use_etag
 *   Set TRUE to send an etag.
 */
function aggregator_test_feed($use_last_modified = FALSE, $use_etag = FALSE) {
  $last_modified = strtotime('Sun, 19 Nov 1978 05:00:00 GMT');
  $etag = drupal_hash_base64($last_modified);

  $if_modified_since = isset($_SERVER['HTTP_IF_MODIFIED_SINCE']) ? strtotime($_SERVER['HTTP_IF_MODIFIED_SINCE']) : FALSE;
  $if_none_match = isset($_SERVER['HTTP_IF_NONE_MATCH']) ? stripslashes($_SERVER['HTTP_IF_NONE_MATCH']) : FALSE;

  // Send appropriate response. We respond with a 304 not modified on either
  // etag or on last modified.
  if ($use_last_modified) {
    drupal_add_http_header('Last-Modified', gmdate(DATE_RFC7231, $last_modified));
  }
  if ($use_etag) {
    drupal_add_http_header('ETag', $etag);
  }
  // Return 304 not modified if either last modified or etag match.
  if ($last_modified == $if_modified_since || $etag == $if_none_match) {
    drupal_add_http_header('Status', '304 Not Modified');
    return;
  }

  // The following headers force validation of cache:
  drupal_add_http_header('Expires', 'Sun, 19 Nov 1978 05:00:00 GMT');
  drupal_add_http_header('Cache-Control', 'must-revalidate');
  drupal_add_http_header('Content-Type', 'application/rss+xml; charset=utf-8');

  // Read actual feed from file.
  $file_name = DRUPAL_ROOT . '/' . drupal_get_path('module', 'aggregator') . '/tests/aggregator_test_rss091.xml';
  $handle = fopen($file_name, 'r');
  $feed = fread($handle, filesize($file_name));
  fclose($handle);

  print $feed;
}
