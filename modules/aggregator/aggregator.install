<?php

/**
 * @file
 * Install, update and uninstall functions for the aggregator module.
 */

/**
 * Implements hook_uninstall().
 */
function aggregator_uninstall() {
  variable_del('aggregator_allowed_html_tags');
  variable_del('aggregator_summary_items');
  variable_del('aggregator_clear');
  variable_del('aggregator_category_selector');
  variable_del('aggregator_fetcher');
  variable_del('aggregator_parser');
  variable_del('aggregator_processors');
  variable_del('aggregator_teaser_length');
}

/**
 * Implements hook_schema().
 */
function aggregator_schema() {
  $schema['aggregator_category'] = array(
    'description' => 'Stores categories for aggregator feeds and feed items.',
    'fields' => array(
      'cid'  => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique aggregator category ID.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Title of the category.',
      ),
      'description' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Description of the category',
      ),
      'block' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => 'The number of recent items to show within the category block.',
      )
    ),
    'primary key' => array('cid'),
    'unique keys' => array(
      'title' => array('title'),
    ),
  );

  $schema['aggregator_category_feed'] = array(
    'description' => 'Bridge table; maps feeds to categories.',
    'fields' => array(
      'fid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => "The feed's {aggregator_feed}.fid.",
      ),
      'cid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {aggregator_category}.cid to which the feed is being assigned.',
      )
    ),
    'primary key' => array('cid', 'fid'),
    'indexes' => array(
      'fid' => array('fid'),
    ),
    'foreign keys' => array(
      'aggregator_category' => array(
        'table' => 'aggregator_category',
        'columns' => array('cid' => 'cid'),
      ),
    ),
  );

  $schema['aggregator_category_item'] = array(
    'description' => 'Bridge table; maps feed items to categories.',
    'fields' => array(
      'iid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => "The feed item's {aggregator_item}.iid.",
      ),
      'cid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {aggregator_category}.cid to which the feed item is being assigned.',
      )
    ),
    'primary key' => array('cid', 'iid'),
    'indexes' => array(
      'iid' => array('iid'),
    ),
    'foreign keys' => array(
      'aggregator_category' => array(
        'table' => 'aggregator_category',
        'columns' => array('cid' => 'cid'),
      ),
    ),
  );

  $schema['aggregator_feed'] = array(
    'description' => 'Stores feeds to be parsed by the aggregator.',
    'fields' => array(
      'fid' => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique feed ID.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Title of the feed.',
      ),
      'url' => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'URL to the feed.',
      ),
      'refresh' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'How often to check for new feed items, in seconds.',
      ),
      'checked' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Last time feed was checked for new items, as Unix timestamp.',
      ),
      'queued' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Time when this feed was queued for refresh, 0 if not queued.',
      ),
      'link' => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'The parent website of the feed; comes from the <link> element in the feed.',
      ),
      'description' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => "The parent website's description; comes from the <description> element in the feed.",
      ),
      'image' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'An image representing the feed.',
      ),
      'hash' => array(
        'type' => 'varchar',
        'length' => 64,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Calculated hash of the feed data, used for validating cache.',
      ),
      'etag' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Entity tag HTTP response header, used for validating cache.',
      ),
      'modified' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'When the feed was last modified, as a Unix timestamp.',
      ),
      'block' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'size' => 'tiny',
        'description' => "Number of items to display in the feed's block.",
      )
    ),
    'primary key' => array('fid'),
    'indexes' => array(
      'url'  => array(array('url', 255)),
      'queued' => array('queued'),
    ),
    'unique keys' => array(
      'title' => array('title'),
    ),
  );

  $schema['aggregator_item'] = array(
    'description' => 'Stores the individual items imported from feeds.',
    'fields' => array(
      'iid'  => array(
        'type' => 'serial',
        'not null' => TRUE,
        'description' => 'Primary Key: Unique ID for feed item.',
      ),
      'fid' => array(
        'type' => 'int',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'The {aggregator_feed}.fid to which this item belongs.',
      ),
      'title' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Title of the feed item.',
      ),
      'link' => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'Link to the feed item.',
      ),
      'author' => array(
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
        'default' => '',
        'description' => 'Author of the feed item.',
      ),
      'description' => array(
        'type' => 'text',
        'not null' => TRUE,
        'size' => 'big',
        'description' => 'Body of the feed item.',
      ),
      'timestamp' => array(
        'type' => 'int',
        'not null' => FALSE,
        'description' => 'Posted date of the feed item, as a Unix timestamp.',
      ),
      'guid' => array(
        'type' => 'text',
        'not null' => TRUE,
        'description' => 'Unique identifier for the feed item.',
      )
    ),
    'primary key' => array('iid'),
    'indexes' => array(
      'fid' => array('fid'),
      'timestamp' => array('timestamp'),
    ),
    'foreign keys' => array(
      'aggregator_feed' => array(
        'table' => 'aggregator_feed',
        'columns' => array('fid' => 'fid'),
      ),
    ),
  );

  return $schema;
}

/**
 * @addtogroup updates-6.x-to-7.x
 * @{
 */

/**
 * Add hash column to aggregator_feed table.
 */
function aggregator_update_7000() {
  db_add_field('aggregator_feed', 'hash', array('type' => 'varchar', 'length' => 64, 'not null' => TRUE, 'default' => ''));
}

/**
 * Add aggregator teaser length to settings from old global default teaser length
 */
function aggregator_update_7001() {
  variable_set('aggregator_teaser_length', variable_get('teaser_length'));
}

/**
 * Add queued timestamp.
 */
function aggregator_update_7002() {
  db_add_field('aggregator_feed', 'queued', array(
    'type' => 'int',
    'not null' => TRUE,
    'default' => 0,
    'description' => 'Time when this feed was queued for refresh, 0 if not queued.',
  ));
  db_add_index('aggregator_feed', 'queued', array('queued'));
}

/**
 * @} End of "addtogroup updates-6.x-to-7.x"
 */

/**
 * @addtogroup updates-7.x-extra
 * @{
 */

/**
 * Increase the length of {aggregator_feed}.url.
 */
function aggregator_update_7003() {
  db_drop_unique_key('aggregator_feed', 'url');
  db_change_field('aggregator_feed', 'url', 'url', array('type' => 'text', 'not null' => TRUE, 'description' => 'URL to the feed.'));
  db_change_field('aggregator_feed', 'link', 'link', array('type' => 'text', 'not null' => TRUE, 'description' => 'The parent website of the feed; comes from the <link> element in the feed.'));
  db_change_field('aggregator_item', 'link', 'link', array('type' => 'text', 'not null' => TRUE, 'description' => 'Link to the feed item.'));
  db_change_field('aggregator_item', 'guid', 'guid', array('type' => 'text', 'not null' => TRUE, 'description' => 'Unique identifier for the feed item.'));
  db_add_index('aggregator_feed', 'url', array(array('url', 255)));
}

/**
 * Add index on timestamp.
 */
function aggregator_update_7004() {
  if (!db_index_exists('aggregator_item', 'timestamp')) {
    db_add_index('aggregator_item', 'timestamp', array('timestamp'));
  }
}

/**
 * @} End of "addtogroup updates-7.x-extra"
 */
