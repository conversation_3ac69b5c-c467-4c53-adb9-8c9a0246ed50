<?php

use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;
use Site45\Base\Store;
use Site45\Sets\Phuket\Query\UserSearch\PhuketUserQuery;
use Site45\Sets\Phuket\Query\ProjectSearch\PhuketProjectQuery;
use Site45\Base\SiteConf;


function s45_phuket_pdf() {

  // режим отображения my clean guest
  
  $propertyNumber = arg(1);
  $display = arg(2) ? arg(2) : 'guest';

  $searchResult = PhuketPropertyQuery::create(array('number' => $propertyNumber, 'mode' => 'full'))->exec();
  if(isset($searchResult->rows[0])){
    $propertyDto = $searchResult->rows[0];
    
    $propertyDto = PhuketPropertyQuery::create(NULL, FALSE)->load($propertyDto->id);
    
//    Много заголовков в проекте не красиво. 
//    if(isset($propertyDto->project->id)){
//      $projectDto = PhuketProjectQuery::create()->load($propertyDto->project->id);
//      if($projectDto->text->ru){
//      $propertyDto->description->ru .= $projectDto->text->ru;
//      }
//    }
    
    
//    dsm($propertyDto);
  } else {
    return 'Не найден '.$propertyNumber;
  }
  
  
  $contactId = 43; //  по умолчанию Ксения Иголкина
  //$contactId = Store::create()->get('currentManager');
  if($display == 'my'){
    $contactId = $GLOBALS['user']->uid;
  }
  if($display == 'clean'){
    $contactId = 0;
  }

  $phones = '';
  $email  = '';
  $agentName = '';
  if($contactId AND ($contactUser = PhuketUserQuery::load($contactId))){
    $phones = $contactUser->phone.($contactUser->phone2 ? ', '.$contactUser->phone2 : '');
    $email  = $contactUser->email;
    $agentName = s45_lang($contactUser->name);
    $agentPhoto = $contactUser->photo;
  }
  
  $cssPath = 'sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Compo/Common/PhuketPdf/PhuketPdf.css';
  
  $header = s45_render('PhuketPdf', array(
    'section' => 'header',
    'display' => $display,
    'propertyDto' => $propertyDto,
    'phones' => $phones,
    'email' => $email,
    'agentName' => $agentName,
    'agentPhoto' => $agentPhoto,
   ));
  $content = s45_render('PhuketPdf', array(
    'section' => 'content',
    'display' => $display,
    'propertyDto' => $propertyDto,
  ));
  $footer = s45_render('PhuketPdf', array(
    'section' => 'footer',
    'display' => $display,
    'propertyDto' => $propertyDto,
  ));
  
  
//  $cssPath = drupal_get_path('module', 's45_phuket').'/pdf/pdf.css';
//  
//  ob_start();
//  include drupal_get_path('module', 's45_phuket').'/pdf/pdf_header.tpl.php';
//  $header =  ob_get_clean();
//  
//  ob_start();
//  include drupal_get_path('module', 's45_phuket').'/pdf/pdf_content.tpl.php';
//  $content =  ob_get_clean();
//  
//  ob_start();
//  include drupal_get_path('module', 's45_phuket').'/pdf/pdf_footer.tpl.php';
//  $footer =  ob_get_clean();
//  




  
  s45_add_mpdf();

  // ИСПРАВЛЕНИЕ: Используем правильный путь для файловой системы
  // Поскольку SiteConf::getId() может возвращать пустую строку, используем жестко заданный путь
  $dir = 'public://FileStore4/phuket/pdf/'.$propertyNumber;
  $file_name = $dir.'/property-'.$display.'-'.$propertyNumber.'-'.$GLOBALS['language']->language.'.pdf';
  
  // Delete existing PDF file if it exists to ensure fresh generation
  if (file_exists($file_name)) {
    unlink($file_name);
  }
  
  // Add timestamp to URL to force browser cache refresh
  $timestamp = time();
  $file_realpath = file_create_url($file_name) . '?t=' . $timestamp;

  //настраиваем класс mPDF для работы
  $headerH = 35;
  $footerH = 20;
  if($display == 'clean'){
    $headerH = 10;
    $footerH = 10;
  }
  $footerH = 0;

  $mpdf = new mPDF('','A4',10,'sans-serif',10,10,$headerH,$footerH,4,9);
  $mpdf->SetDisplayMode('fullwidth');
  $mpdf->SetHTMLHeader($header);
  //$mpdf->SetHTMLFooter($footer);
  $stylesheet = file_get_contents($cssPath);
  $mpdf->WriteHTML($stylesheet, 1);

  // ИСПРАВЛЕНИЕ: Преобразуем HTTP URL изображений в локальные пути только для PDF (не для debug)
  $content_for_pdf = s45_phuket_pdf_fix_image_urls($content);

  $mpdf->WriteHTML($content_for_pdf, 2);

  // ИСПРАВЛЕНИЕ: Создаем директорию напрямую и сохраняем PDF
  $real_dir = drupal_realpath($dir);
  if (!$real_dir) {
    // Если drupal_realpath не работает, используем прямой путь
    $real_dir = '/var/www/www-root/data/www/indreamsphuket.com/sites/default/files/FileStore4/phuket/pdf/'.$propertyNumber;
  }
  
  if (!file_exists($real_dir)) {
    mkdir($real_dir, 0755, true);
  }
  
  if (file_exists($real_dir) && is_writable($real_dir)) {
    $real_file_name = $real_dir.'/property-'.$display.'-'.$propertyNumber.'-'.$GLOBALS['language']->language.'.pdf';
    $mpdf->Output($real_file_name, 'F');
  }
  
  if(arg(3) <> 'debug'){
    // Use 302 redirect instead of 301 to prevent aggressive caching
    drupal_goto($file_realpath, array(), 302);
  }
  drupal_add_css($cssPath);
  $html = $header.$content.$footer;
  $html .= '<div><a href="'.$file_realpath.'" target="_blank">ПДФ</a></div>';
  return $html;
}

/**
 * Функция для преобразования HTTP URL изображений в локальные пути для mPDF
 */
function s45_phuket_pdf_fix_image_urls($html) {
  // Паттерн для поиска img тегов с src
  $pattern = '/(<img[^>]+src=["\'])([^"\']+)(["\'][^>]*>)/i';

  return preg_replace_callback($pattern, function($matches) {
    $img_tag_start = $matches[1];
    $url = $matches[2];
    $img_tag_end = $matches[3];

    // Преобразуем URL в локальный путь
    $local_path = s45_phuket_pdf_url_to_local_path($url);

    return $img_tag_start . $local_path . $img_tag_end;
  }, $html);
}

/**
 * Преобразует HTTP URL в локальный путь файла
 */
function s45_phuket_pdf_url_to_local_path($url) {
  // Если это уже локальный путь, возвращаем как есть
  if (strpos($url, '/') === 0 && strpos($url, 'http') === false) {
    return $url;
  }

  // Удаляем домен и протокол
  $url = str_replace('https://indreamsphuket.com/', '', $url);
  $url = str_replace('https://indreamsphuket.ru/', '', $url);
  $url = str_replace('http://indreamsphuket.com/', '', $url);
  $url = str_replace('http://indreamsphuket.ru/', '', $url);
  $url = str_replace('http://./', '', $url);

  // Удаляем параметры запроса (например, ?itok=...)
  $url = preg_replace('/\?.*$/', '', $url);

  // Если URL начинается с files/, добавляем полный путь к сайту
  if (strpos($url, 'files/') === 0) {
    $local_path = '/var/www/www-root/data/www/indreamsphuket.com/' . $url;
  } else {
    // Для других случаев пробуем найти файл через drupal_realpath
    $stream_wrapper_url = 'public://' . str_replace('files/site4/', '', $url);
    $local_path = drupal_realpath($stream_wrapper_url);

    if (!$local_path || !file_exists($local_path)) {
      // Fallback - пробуем прямой путь
      $local_path = '/var/www/www-root/data/www/indreamsphuket.com/' . $url;
    }
  }

  // Проверяем существование файла
  if (file_exists($local_path)) {
    return $local_path;
  }

  // Если файл не найден, возвращаем оригинальный URL
  return $url;
}
