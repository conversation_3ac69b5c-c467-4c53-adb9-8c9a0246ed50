<?php

/* @var $this PhuketPdf */
$descrFull = s45_lang($this->propertyDto->description) . ($this->propertyDto->project->text ? s45_lang($this->propertyDto->project->text) : '');
$descrClear = $descrFull;
$descrClear = str_replace('????', '', $descrClear);
// $descrClear = str_replace('h1', 'p', $descrClear);
// $descrClear = str_replace('H1', 'p', $descrClear);
// $descrClear = str_replace('h2', 'p', $descrClear);
// $descrClear = str_replace('H1', 'p', $descrClear);
$descrClear = str_replace('<p>', '', $descrClear);
$descrClear = str_replace('????', '', $descrClear);
// $descrClear = str_replace('</p>', '||', $descrClear);
// $descrClear = strip_tags($descrClear, '');

// $descrClearA = explode('||', $descrClear);

// if ($this->propertyDto->dealType->id == 'sale') {
//   $maxLen = 1600;
// } else {
//   $maxLen = 1300;
// }

// $descr = '';
// if ($descrClearA) {
//   foreach ($descrClearA as $paragraph) {
//     if (mb_strlen($descr . $paragraph) <= $maxLen) {
//       $descr .= '<p>' . $paragraph . '</p>';
//     }
//   }
// }

$descr = $descrClear;

// $descrIsShort = FALSE;
// if (mb_strlen($descr) < 500) {
//   $descrIsShort = TRUE;
// }

//$descr = '<p>'. str_replace('||', '</p><p>', $descrClear).'</p>';
//$descr = truncate_utf8($descr, $maxLen);

/*** карта ***/
if ($this->propertyDto->re_latitude and $this->propertyDto->re_longitude) {
  $size = '500x300';
  if ($this->pdfMessage) {
    $size = '500x247';
  }
  
  // Определяем язык
  $lang = isset($GLOBALS['language']->language) ? $GLOBALS['language']->language : 'en';
  
  // Используем кэшированную карту
  $staticMapUrl = $this->getCachedStaticMap(
    $this->propertyDto->re_latitude, 
    $this->propertyDto->re_longitude, 
    $lang,
    $size
  );
}



?>

<component>
  <?php if ($this->display <> 'clean') : ?>
    <?php if (($this->section == 'header') or !$this->section) : ?>
      <?php include $this->dir . '/res/inc/header.inc'; ?>
    <?php endif; ?>
  <?php endif; ?>
  <?php if (($this->section == 'content') or !$this->section) : ?>

    <div class="PhuketPdf-Name">
      <?php print '<span>ID</span>' . $this->propertyDto->number . ' ' . s45_lang($this->propertyDto->name); ?>
    </div>

    <div class="PhuketPdf-Top">
      <div class="PhuketPdf-TopImage">
        <img src="<?php print s45_imgSrc($this->propertyDto->photos[0], S45_IMST_500X300_CROP); ?>">
      </div>
      <div class="PhuketPdf-TopMap">
        <?php if ($this->pdfMessage) : ?>
          <div class="PhuketPdf-Message"><?php print $this->pdfMessage; ?></div>
        <?php endif; ?>
        <img src="<?php print $staticMapUrl; ?>">
      </div>
    </div>

    <?php include $this->dir . '/res/inc/chars.inc'; ?>

    <?php if ($this->propertyDto->dealType->id <> 'sale') : ?>
      <?php include $this->dir . '/res/inc/price.inc'; ?>
    <?php endif; ?>




    <?php //if ($descrIsShort) : 
      $photoOnFirst = 4;
      $maxPhoto = 12;
      // if($this->propertyDto->dealType->id == 'sale'){
      //   $photoOnFirst = 4;
      //   $maxPhoto = 12;
      // }
    ?>
    <?php if (1) : ?>
      <div class="PhuketPdf-Gallery PhuketPdf-GalleryMain">
        <?php for ($i = 1; $i <= $photoOnFirst; $i++) : ?>
          <div class="PhuketPdf-GalleryImage">
            <?php if (isset($this->propertyDto->photos[$i])) : ?>
              <img src="<?php print s45_imgSrc($this->propertyDto->photos[$i], S45_IMST_500X300_CROP); ?>">
            <?php endif; ?>
          </div>
        <?php endfor; ?>
      </div>
    <?php endif; ?>

    <pagebreak></pagebreak>

    <?php include $this->dir . '/res/inc/photos.inc'; ?>

    <pagebreak></pagebreak>

    <div class="PhuketPdf-Descr">
      <?php if ($descr) : ?>
        <?php print $descr; ?>
      <?php endif; ?>
    </div>
    <?php if ($this->display <> 'clean') : ?>
      <?php
      $url = s45_url('property/' . $this->propertyDto->id);
      ?>
      <div class="PhuketPdf-Url">
        <a href="<?php print $url; ?>">
          <?php print $url; ?>
        </a>
      </div>
    <?php endif; ?>

  <?php endif; ?>

  <?php if ($this->display <> 'clean') : ?>
    <?php if (($this->section == 'footer') or !$this->section) : ?>
      <?php include $this->dir . '/res/inc/footer.inc'; ?>
    <?php endif; ?>
  <?php endif; ?>

</component>
