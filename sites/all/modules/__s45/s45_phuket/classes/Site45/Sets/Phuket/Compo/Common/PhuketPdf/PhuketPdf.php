<?php

use Site45\Base\Store;
use Site45\Compo\Compo;
use Site45\DtoLib\Base\LangVO;
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyDto;
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;


/**
 * Description of PhuketPdf
 *
 * <AUTHOR>
 */
class PhuketPdf extends Compo
{

  public $content_contactYourAgent;
  public $section;
  public $display; // my clean guest
  public $phones;
  public $email;
  public $agentName;
  public $agentPhoto;
  public $pdfMessage;
  public $currencyList;

  /**
   * @var PhuketPropertyDto 
   */
  public $propertyDto;

  protected function beforeRender($props)
  {
    $this->section = isset($props->section) ? $props->section : NULL;
    $this->display = isset($props->display) ? $props->display : 'guest';
    $this->propertyDto = isset($props->propertyDto) ? $props->propertyDto : NULL;
    if ($this->section == 'header') {
      $this->phones = isset($props->phones) ? $props->phones : NULL;
      $this->email = isset($props->email) ? $props->email : NULL;
      $this->agentName = isset($props->agentName) ? $props->agentName : NULL;
      $this->agentPhoto = isset($props->agentPhoto) ? $props->agentPhoto : NULL;
    }
    $this->pdfMessage = s45_lang($this->propertyDto->pdfMessage);
    $this->pdfMessage = str_replace('????', '', $this->pdfMessage);

    $this->currencyList = Store::create()->get('currencyList');
  }

  //  protected function getPanel2() {
  //    $panel = array(
  //      'style' => 'CompoStd',
  //      'menu' => array(
  //        'editor' => array(
  //          'iconClass' => 'fas fa-cog',
  //          'title' => 'Редактировать',
  //          'func' => 'CompoPanel2_editor("'.$this->id.'")',
  //          'access' => S45_ACCESS_COMPO_EDIT,
  //        ),
  //        'delete' => array(
  //          'iconClass' => 'fas fa-trash-alt',
  //          'title' => 'Удалить',
  //          'func' => 'CompoPanel2_delete("'.$this->id.'")',
  //          'access' => S45_ACCESS_COMPO_DELETE,
  //        ),
  //      ),
  //    );
  //    return $panel;
  //  }

  function __construct()
  {

    $this->settings_published = 1;

    $this->content_contactYourAgent = LangVO::create(array(
      'ru' => 'Свяжитесь с Вашим агентом',
      'en' => 'Contact your agent',
      'zh-hans' => '与您的代理商联系',
    ));
  }

  /**
   * Получить закэшированную статическую карту или создать новую
   */
  public function getCachedStaticMap($lat, $lng, $lang = 'en', $size = '500x300') {
    if (!$lat || !$lng) {
      return null;
    }
    
    // Создаем уникальный ключ для кэша
    $cache_key = 'static_map_' . md5($lat . '_' . $lng . '_' . $size . '_' . $lang);
    
    // Проверяем кэш
    $cache = cache_get($cache_key, 'cache');
    if ($cache && !empty($cache->data)) {
      // Проверяем, существует ли локальный файл
      if (file_exists($cache->data)) {
        // ИСПРАВЛЕНИЕ: Возвращаем HTTP URL для HTML, локальный путь для PDF
        if (strpos($_SERVER['REQUEST_URI'], '/debug') !== false) {
          // Для HTML версии возвращаем HTTP URL
          $url = file_create_url($cache->data);
          // Исправляем некорректный base_url
          $url = str_replace('http://./', 'https://indreamsphuket.com/', $url);
          $url = str_replace('http://./files/', 'https://indreamsphuket.com/files/', $url);
          // Для русской версии
          if (isset($GLOBALS['language']) && $GLOBALS['language']->language == 'ru') {
            $url = str_replace('https://indreamsphuket.com/', 'https://indreamsphuket.ru/', $url);
          }
          return $url;
        } else {
          // Для PDF возвращаем локальный путь
          return drupal_realpath($cache->data);
        }
      }
    }
    
    // Генерируем URL для статической карты
    $key = 'AIzaSyDnB9dsfAjQh9cS6YPspK9FKLYxbJduDK4';
    $staticMapsParams = array(
      'markers' => '|' . $lat . ',' . $lng,
      'zoom' => 10,
      'size' => $size,
      'language' => $lang,
      'key' => $key,
    );
    
    $staticMapUrl = 'https://maps.googleapis.com/maps/api/staticmap?' . drupal_http_build_query($staticMapsParams);
    
    // Создаем локальную директорию для кэша карт
    $cache_dir = 'public://cached_maps';
    if (!is_dir($cache_dir)) {
      drupal_mkdir($cache_dir, NULL, TRUE);
    }
    
    // Формируем путь к локальному файлу
    $filename = md5($staticMapUrl) . '.png';
    $local_path = $cache_dir . '/' . $filename;
    
    // Загружаем изображение, если его нет локально
    if (!file_exists($local_path)) {
      $image_data = file_get_contents($staticMapUrl);
      if ($image_data) {
        file_put_contents($local_path, $image_data);
        
        // Кэшируем путь на 365 дней (1 год)
        cache_set($cache_key, $local_path, 'cache', time() + (365 * 24 * 60 * 60));
        
        // ИСПРАВЛЕНИЕ: Возвращаем правильный формат URL
        if (strpos($_SERVER['REQUEST_URI'], '/debug') !== false) {
          $url = file_create_url($local_path);
          // Исправляем некорректный base_url
          $url = str_replace('http://./', 'https://indreamsphuket.com/', $url);
          $url = str_replace('http://./files/', 'https://indreamsphuket.com/files/', $url);
          // Для русской версии
          if (isset($GLOBALS['language']) && $GLOBALS['language']->language == 'ru') {
            $url = str_replace('https://indreamsphuket.com/', 'https://indreamsphuket.ru/', $url);
          }
          return $url;
        } else {
          return drupal_realpath($local_path);
        }
      }
    } else {
      // Обновляем кэш
      cache_set($cache_key, $local_path, 'cache', time() + (365 * 24 * 60 * 60));
      
      // ИСПРАВЛЕНИЕ: Возвращаем правильный формат URL
      if (strpos($_SERVER['REQUEST_URI'], '/debug') !== false) {
        $url = file_create_url($local_path);
        // Исправляем некорректный base_url
        $url = str_replace('http://./', 'https://indreamsphuket.com/', $url);
        $url = str_replace('http://./files/', 'https://indreamsphuket.com/files/', $url);
        // Для русской версии
        if (isset($GLOBALS['language']) && $GLOBALS['language']->language == 'ru') {
          $url = str_replace('https://indreamsphuket.com/', 'https://indreamsphuket.ru/', $url);
        }
        return $url;
      } else {
        return drupal_realpath($local_path);
      }
    }
    
    // Fallback - возвращаем прямой URL
    return $staticMapUrl;
  }
}
