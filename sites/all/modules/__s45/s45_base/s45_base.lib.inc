<?php

use Site45\DtoLib\Base\LangVO;
use Site45\QueryLib\Site\SiteConfLoadQuery;
use Site45\Base\CompoApi2;
use Site45\Base\JsonRepo;
use Site45\Base\SiteConf;
use Site45\DtoLib\Base\FileDto;
use Site45\DtoLib\Site\SiteConfDto;


function s45_dsm($obj, $mes = NULL) {
  if($GLOBALS['user']->uid == 1){
    dsm($obj, $mes);
  }
}

function s45_dpq($query) {
  if($GLOBALS['user']->uid == 1){
    dpq($query);
  }
}


/**
 * Список языков.
 * 
 * Пока заглушка для Пхукета
 * 
 * @return type
 */
function s45_langList() {
  $fullLangList = language_list();
  $phuketLangList = array(
    'ru' => $fullLangList['ru'],
    'en' => $fullLangList['en'],
//    'zh-hans' => $fullLangList['zh-hans'],
  );
  return $phuketLangList;
}


/**
 * Получение алиаса из системного пути
 * 
 * @param type $sysPath
 * @param type $options
 * @return type
 */
function s45_url($sysPath, $options = array()) {
  if(function_exists('s45_path_url')){
    return s45_path_url($sysPath, $options);
  }
  return url($sysPath, $options);
}


/**
 * Определение - переключения моб версии
 * 
 * @param SiteConfDto $siteConfDto
 */
function s45_setAgent($siteConfDto) {
  
  // нет отдельной моб версии - выход
  if(!$siteConfDto->mobile){
    return;
  }

  // определяем и сохраняем тип агента/браузера
  $iphone = strpos($_SERVER['HTTP_USER_AGENT'],"iPhone");
  $android = strpos($_SERVER['HTTP_USER_AGENT'],"Android");
  $agent = ($iphone || $android) ? 'mobile' : 'desktop';
  setcookie('s45_agent', $agent); // для скрипта
  $_SESSION['s45']['agent'] = $agent;

  
  $domain = $_SERVER['HTTP_HOST'];
  
  if((substr($domain, 0,2) == 'm.') AND ($agent == 'desktop')){
    header('Location: http://'.substr($domain, 2));
    exit();
  }
  
  if((substr($domain, 0,2) <> 'm.') AND ($agent == 'mobile')){
    header('Location: http://m.'.$domain);
    exit();
  }
  

}


function s45_guid($arName = NULL){

  $arName = $arName ? $arName : sprintf('%04X%04X%04X', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
  
  $part1 = dechex(time());
  $part2 = sprintf('-%04X-%04X-%04X-', mt_rand(0, 65535), mt_rand(0, 65535), mt_rand(0, 65535));
  
  $part3 = substr($arName.'000000000000', 0, 12);
  
  $guid = $part1.$part2.$part3;
  
  return $guid;
}


function s45_attr($attr) {
  return drupal_attributes($attr);
}


/**
 * 
 * @param FileDto $fileDto
 * @param string $styleName
 * @return type
 */
function s45_imgSrcR(&$fileDto, $styleName = '') {
  
  $fileDto = FileDto::create($fileDto);
  
  if(strpos('qwerty'.$fileDto->id, 'http') AND strpos('qwerty'.$fileDto->id, 'Site45/Sets')){
    // файл размещенный в компоненте
    $src = $fileDto->id;
  }else{
    if(strpos($fileDto->id, 'pictures')){
      // это для файлов в папке пикчерс - фото агентов 
      $tmp = explode('files/site4/', $fileDto->id);
      $filePath = 'public://'.$tmp[1];
    }else{
      // файлы размещенные в аплод-папке и в экстерналс
      if(strpos('qwerty'.$fileDto->id, 'http')){
        // если это абс урл это файл перенесенный со старого сайта
        $fileName = s45_inreams_old_file_name($fileDto->id);    
        $filePath = S45_SITES_DIR.'/'.SiteConf::getId().'/files/old/'.$fileName;
      } else {
        // это файл загруженный через админку
        $filePath = S45_SITES_DIR.'/'.SiteConf::getId().'/files/tmp/'.$fileDto->id;
      }
    }
    

    
    if($styleName){
      $src = image_style_url($styleName, $filePath);
    }else{
      $src = file_create_url($filePath);
    }
    
    // ИСПРАВЛЕНИЕ: Удаляем двойную точку из URL которая появляется из-за некорректного base_url
    // Проблема возникает при генерации PDF когда $GLOBALS['base_url'] содержит некорректное значение
    $src = str_replace('indreamsphuket.com./', 'indreamsphuket.com/', $src);
    $src = str_replace('indreamsphuket.ru./', 'indreamsphuket.ru/', $src);

    // ДОПОЛНИТЕЛЬНОЕ ИСПРАВЛЕНИЕ: Исправляем некорректный base_url вида "http://."
    $src = str_replace('http://./', 'https://indreamsphuket.com/', $src);
    $src = str_replace('http://./files/', 'https://indreamsphuket.com/files/', $src);

    // Исправляем для русской версии
    if (isset($GLOBALS['language']) && $GLOBALS['language']->language == 'ru') {
      $src = str_replace('https://indreamsphuket.com/', 'https://indreamsphuket.ru/', $src);
    }

    // if(!file_exists($filePath)){
    //   // если файла нету локально - берем с другого хостинга
    //   //s45_dsm('- '.$filePath);
    
    //   $src = str_replace('https://indreamsphuket.ru', 'https://indreamsimg.it4realty.com', $src);
    //   $src = str_replace('https://indreamsphuket.com', 'https://indreamsimg.it4realty.com', $src);
    //   $src = str_replace('https://ch.indreamsphuket.com', 'https://indreamsimg.it4realty.com', $src);
    //   $src = str_replace('https://th.indreamsphuket.com', 'https://indreamsimg.it4realty.com', $src);
      
    // }

  }


  
  // s45_dsm($src);

  return $src;
}

function s45_inreams_old_file_name($url){
    
    $url = str_replace('www.indreamsphuket.ru', 'indreams.it4realty.com', $url);
    $url = str_replace('https', 'http', $url);
    $url = str_replace(' ', '%20', $url);

    $url_parameters = drupal_parse_url($url);

    $extension = strtolower(pathinfo($url_parameters['path'], PATHINFO_EXTENSION));
    $fileName = md5($url).'.'.$extension;
    return $fileName;
}


/**
 * 
 * @param FileDto $fileDto
 * @param string $styleName
 * @return type
 */
function s45_imgSrc(&$fileDto, $styleName = '') {

  return s45_imgSrcR($fileDto, $styleName);
  
  $fileDto = FileDto::create($fileDto);
  
  if(strpos('qwerty'.$fileDto->id, 'http')){
    $url = $fileDto->id;
    // временно для индримс
    if(strpos('qwerty'.$fileDto->id, 'https://www.indreamsphuket.ru')){
      $url = str_replace('www.indreamsphuket.ru', 'indreams.it4realty.com', $url);
      $url = str_replace('https', 'http', $url);
      $url = str_replace(' ', '%20', $url);
    }
    $filePath = imagecache_external_generate_path($url);
  } else {
    $filePath = S45_SITES_DIR.'/'.SiteConf::getId().'/files/'.$fileDto->dir.'/'.$fileDto->id;
  }
  
  
  $imagePath = 'public://FileStore4/default/files/img/noimage.jpg';
  if(file_exists($filePath)){
    $imagePath = $filePath;
  }
  
  if($styleName){
    $src = image_style_url($styleName, $imagePath);;
  }else{
    $src = file_create_url($imagePath);
  }
  return $src;
}


function s45_toObject($param) {
  return json_decode(json_encode($param));
}


function s45_toArray($param) {
  return json_decode(json_encode($param), TRUE);
}


/**
 * Временное добавление ресурсов всех используемых компонентов
 */
function s45_addAllRes() {
  
  // ресурсы Compo
  drupal_add_js(S45_COMPO_DIR.'/Compo.js');
  drupal_add_css(S45_COMPO_DIR.'/Compo.css');
  
  // не грузим весь дизайн для экранов
  if(in_array(arg(1), array('PhuketPropScreen', 'PhuketScreens'))){
    return;
  }

  // все компоненты из списка подключенных
  $compoInfoList = JsonRepo::open(S45_SITES_DIR.'/'.SiteConf::getId().'/_repo/CompoInfo.s45.json');
  foreach ($compoInfoList as $compoName => $compoInfo) {
    $cssPath = $compoInfo->dir.'/'.$compoName.'.css';
    if(file_exists($cssPath)){
      drupal_add_css($cssPath);
    }
    $jsPath = $compoInfo->dir.'/'.$compoName.'.js';
    if(file_exists($jsPath)){
      drupal_add_js($jsPath);
    }
    // подключение библиотек вендоров
    $resPath = $compoInfo->dir.'/'.$compoName.'.res.inc';
    if(file_exists($resPath)){
      include_once $resPath;
    }
  }
  
}


function s45_render($compoId, $props = NULL){
  
  $renderedCompo = CompoApi2::exec($compoId, 'GetRendered', array('props' => $props));

  if(property_exists($renderedCompo, 'html')){  
    return $renderedCompo->html;
  }

  return null;
}

/**
 * Айди текущего сайта
 * 
 * @return type
 */
function s45_getSiteId() {
  $siteId = SiteConfLoadQuery::create()->setDomain()->exec()->id;
  return $siteId;
}


/**
 * Путь к файловому хранилищу текущего сайта
 * @return type
 */
function s45_getSiteDir(){
  $siteId = SiteConfLoadQuery::create()->exec()->id;
  return S45_SITES_DIR.'/'.$siteId;
}


function s45_testText($testTextLen, $langCode) {
  
  $testText = array(
    'ru' => 'Задача организации, в особенности же дальнейшее развитие различных форм деятельности обеспечивает широкому кругу (специалистов) участие в формировании существенных финансовых и административных условий. Идейные соображения высшего порядка, а также сложившаяся структура организации способствует подготовки и реализации соответствующий условий активизации. Таким образом начало повседневной работы по формированию позиции обеспечивает широкому кругу (специалистов) участие в формировании соответствующий условий активизации. Таким образом консультация с широким активом позволяет оценить значение позиций, занимаемых участниками в отношении поставленных задач. Задача организации, в особенности же консультация с широким активом требуют от нас анализа новых предложений. С другой стороны постоянный количественный рост и сфера нашей активности в значительной степени обуславливает создание форм развития.',
    'en' => 'The organization’s task, in particular the further development of various forms of activity, ensures a wide circle of (specialists) participation in the formation of significant financial and administrative conditions. Ideological considerations of a higher order, as well as the existing structure of the organization, contribute to the preparation and implementation of the relevant conditions for activation. Thus, the beginning of daily work on the formation of a position ensures a wide circle of (specialists) participation in the formation of the corresponding conditions for activation. Thus, consultation with a broad asset allows us to assess the value of the positions taken by participants in relation to the tasks. The organization’s task, especially consultation with a broad asset, requires us to analyze new proposals. On the other hand, constant quantitative growth and the scope of our activity to a large extent determine the development of forms of development.',
    'zh-hans' => '该组织的任务，特别是各种活动的进一步发展，确保了（专家）广泛的参与，从而形成了重要的财务和行政条件。更高层次的意识形态考虑以及组织的现有结构，为激活的相关条件的准备和实施做出了贡献。因此，开始每天的职位形成工作可确保（专家）参与相应条件的激活的广泛圈子。因此，咨询广泛的资产可以使我们评估参与者就任务所承担的职位的价值。该组织的任务，特别是与广泛资产的咨询，要求我们分析新提案。另一方面，持续的定量增长和我们活动的范围在很大程度上决定了发展形式的发展。',
  );
  
  if($langCode == 'zh-hans'){
    $testTextLen = (int) ($testTextLen / 2);
  }
  
  $countText = ' ('.$testTextLen.')';
  $text = truncate_utf8($testText[$langCode], $testTextLen - strlen($countText)).$countText;
  
  return $text;
}


/**
 * Получить строку из мультиязычного объекта
 * 
 * @global type $language
 * @param LangVO $langObject
 * @param string $alter
 * @param string $langCode
 * @param int $testTextLen
 * @return string
 */
function s45_lang(&$langObject, $alter = NULL, $langCode = NULL, $testTextLen = NULL){

  if(is_string($langObject)){
    return $langObject;
  }
  
  // определяем на каком языке выдавать
  if(!$langCode OR !is_string($langCode)){
    global $language;
    $langCode = $language->language;
  }
  
  // если есть на указанном языке
  if(isset($langObject->{$langCode}) AND trim($langObject->{$langCode})){
    return $langObject->{$langCode};
  }
  
  // если есть UND
  if(isset($langObject->und)){
    return $langObject->und;
  }
  
  // если это китайский но нет перевода
  if($langCode == 'zh-hans'){
    if(!isset($langObject->$langCode) OR (!$langObject->$langCode)){
      if(isset($langObject->en) AND $langObject->en){
        return $langObject->en;
      }
    }
  }

  // если это тайсуий но нет перевода
  if($langCode == 'th'){
    if(!isset($langObject->$langCode) OR (!$langObject->$langCode)){
      if(isset($langObject->en) AND $langObject->en){
        return $langObject->en;
      }
    }
  }
  
  // если нету альтера но есть на русском, тогда альтер это рус
//  if(is_null($alter) AND isset($langObject->ru)){
//    $alter = $langObject->ru;
//  }
  
  // возвращаем альтер
  if(!is_null($alter)){
    return $alter.'';
  }
  
  // удалить?
  if($testTextLen){
    return s45_testText($testTextLen, $langCode);
  }

  return '????';
}


/**
 * Пока упрощенно определяем админ или нет
 * 
 * @return type
 */
function s45_isAdmin() {
  return $GLOBALS['user']->uid;
}


/**
 * Для использования в шаблоне для печати свойств класса
 * в т ч несуществующих
 * 
 * @param type $param
 */
function s45_print(&$param) {
  print $param;
}


function s45_fileList($dir, $mask = NULL) {
  
  $fileListAll = scandir($dir);
  $fileList = array();
  
  if($mask){
    foreach ($fileListAll as $key => $fileName) {
      if(strpos('qwerty'.$fileName, $mask)){
        $fileList[] = $fileName;
      }
    }
  }else{
    array_shift($fileListAll);
    array_shift($fileListAll);
    $fileList = $fileListAll;
  }
  
  return $fileList;
}


function s45_trans($text, $lowerCase = FALSE, $delDots = FALSE){
  
  $text = trim($text);
  $text = strip_tags($text);
  
  $cyr = array(
    'а','б','в','г','д','е','ё','ж','з','и','й','к','л','м','н','о','п',
    'р','с','т','у','ф','х','ц','ч','ш','щ','ъ','ы','ь','э','ю','я',
    'А','Б','В','Г','Д','Е','Ё','Ж','З','И','Й','К','Л','М','Н','О','П',
    'Р','С','Т','У','Ф','Х','Ц','Ч','Ш','Щ','Ъ','Ы','Ь','Э','Ю','Я', 
    ' ', '!', '?',
  );
  
  $lat = array(
    'a','b','v','g','d','e','io','zh','z','i','y','k','l','m','n','o','p',
    'r','s','t','u','f','h','ts','ch','sh','sht','a','i','y','e','yu','ya',
    'A','B','V','G','D','E','Io','Zh','Z','I','Y','K','L','M','N','O','P',
    'R','S','T','U','F','H','Ts','Ch','Sh','Sht','A','I','Y','e','Yu','Ya', 
    '-', '-', '-'
  );
  
  $text = str_replace($cyr, $lat, $text);
  
  if($delDots){
    $text = preg_replace('~[^-a-zA-Z0-9_]+~u', '-', $text);
  } else {
      $text = preg_replace('~[^-a-zA-Z0-9_\.]+~u', '-', $text);
  }
  
  if($lowerCase){
    $text = mb_strtolower($text);
  }

  $text = str_replace('--', '-', $text);
  $text = str_replace('--', '-', $text);
  $text = str_replace('--', '-', $text);
  
  return $text;
}

