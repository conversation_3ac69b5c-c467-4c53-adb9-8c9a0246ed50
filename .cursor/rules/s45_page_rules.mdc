---
description: 
globs: 
alwaysApply: true
---
# s45_page Module Cursor Rules

<rule>
name: s45_page_preprocess_html
description: Preprocess HTML variables (hook_preprocess_html)
filters:
  - type: file_content
    pattern: "function s45_page_preprocess_html"
actions:
  - type: suggest
    message: |
      - Изучите `s45_page_preprocess_html()`: добавление HTML-атрибутов `s45-page-id` и `s45-debug`.
      - Проверьте условия `in_array(arg(0), ['s45', 's45test'])` и скрытие админ-меню.
      - Убедитесь, что атрибуты безопасны для вывода и не приводят к XSS.
</rule>

<rule>
name: s45_page_menu
description: Регистрация маршрута основной страницы (hook_menu)
filters:
  - type: file_content
    pattern: "function s45_page_menu"
actions:
  - type: suggest
    message: |
      - Изучите `s45_page_menu()`: маршрут `s45` с callback `s45_page` и ролью `s45guest`.
      - Убедитесь, что параметры `type`, `menu_name` и `access arguments` соответствуют требованиям безопасности.
</rule>

<rule>
name: s45_page
description: Callback основной страницы (hook_menu page callback)
filters:
  - type: file_content
    pattern: "function s45_page\(\)"
actions:
  - type: suggest
    message: |
      - Откройте `s45_page()`: логика установки SiteId, сброса кеша, загрузки конфигурации сайта.
      - Проверьте вызовы `SiteConfLoadQuery`, `s45_phuket_seo_setForPage()`, `s45_addAllRes()` и `s45_setAgent()`.
      - Убедитесь, что `s45_render()` вызывается с корректным `siteCompo`.
</rule>

<rule>
name: s45_page_seo_tmp
description: Временные SEO-редиректы (hook)</nfilters:
  - type: file_content
    pattern: "function s45_page_seo_tmp"
actions:
  - type: suggest
    message: |
      - Изучите `s45_page_seo_tmp()` для редиректов на корневой домен.
      - Проверьте использование `header('Location:')` и отсутствие последующего вывода.
      - Рекомендуется заменить временные редиректы на hook_menu_alter или proper redirect API.
</rule>

<rule>
name: s45_page_clearOldSes
description: Очистка устаревших сессий в БД
filters:
  - type: file_content
    pattern: "function s45_page_clearOldSes"
actions:
  - type: suggest
    message: |
      - Откройте `s45_page_clearOldSes()`: `db_delete('sessions')` для очищения старых сессий.
      - Убедитесь, что timestamps рассчитываются правильно и запросы используют placeholders.
      - Проверьте влияние на производительность и рекомендовать Cron вместо hook.
</rule>

<rule>
name: s45_page_robots
description: Serve robots.txt via hook menu
filters:
  - type: file_path
    pattern: "s45_page\.robots\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_page.robots.inc` и функцию `s45_robots()`.
      - Проверьте, что возвращаемая строка содержит корректный контент для robots.txt.
      - Убедитесь, что заголовки `Content-Type` для robots.txt заданы через hook_menu `type` и Drupal.
</rule>
