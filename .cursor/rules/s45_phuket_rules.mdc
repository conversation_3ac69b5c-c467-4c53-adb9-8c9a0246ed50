---
description:
globs:
alwaysApply: false
---
# s45_phuket Module Cursor Rules

<rule>
name: s45_phuket_module_file
description: Основной файл модуля s45_phuket
filters:
  - type: file_path
    pattern: "s45_phuket\.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket.module`: регистрация hook_menu(), hook_form(), hook_theme() и hook_permission().
      - Проверьте использование Drupal API: db_query(), drupal_set_message(), l(), url(), theme functions.
      - Убедитесь, что все SQL-запросы используют плейсхолдеры и безопасны.
      - Проверьте access callbacks и роли.
</rule>

<rule>
name: s45_phuket_lib
description: Основные вспомогательные функции в s45_phuket_lib.inc
filters:
  - type: file_path
    pattern: "s45_phuket_lib\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_lib.inc`: функции обработки данных недвижимости, файлов и URL.
      - Проверьте корректность сериализации, десериализации, использование json_encode/json_decode.
      - Убедитесь, что все пользовательские данные экранируются при выводе.
</rule>

<rule>
name: s45_phuket_utils
description: Утилиты в s45_phuket_utils.inc
filters:
  - type: file_path
    pattern: "s45_phuket_utils\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_utils.inc`: функции для скачивания фото, работы со временными файлами.
      - Проверьте защиту file operations: file_exists(), file_get_contents(), обработку ошибок.
      - Убедитесь в правиле DRY и повторном использовании кода.
</rule>

<rule>
name: s45_phuket_redirect
description: Логика редиректов в s45_phuket_redirect.inc
filters:
  - type: file_path
    pattern: "s45_phuket_redirect\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_redirect.inc` для анализа правил редиректов свойств.
      - Проверьте использование `drupal_goto()`, правильность HTTP-статусов (301/302).
      - Убедитесь в отсутствии заголовков после вывода тела страницы.
</rule>

<rule>
name: s45_phuket_seo
description: SEO логика в s45_phuket_seo.inc
filters:
  - type: file_path
    pattern: "s45_phuket_seo\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_seo.inc`: генерация meta-тегов и оптимизация контента.
      - Проверьте правильность использования `t()`, динамических значений и кеширования.
      - Убедитесь, что HTML-вывод безопасен и корректен.
</rule>

<rule>
name: s45_phuket_sitemap
description: Sitemap генерация в s45_phuket_sitemap.inc
filters:
  - type: file_path
    pattern: "s45_phuket_sitemap\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_sitemap.inc` для анализа создания XML sitemap.
      - Проверьте stream-processing для больших XML и сохранение в `public://`.
      - Убедитесь в использовании db_query() с плейсхолдерами.
</rule>

<rule>
name: s45_phuket_pdf
description: Генерация PDF в s45_phuket_pdf.inc
filters:
  - type: file_path
    pattern: "s45_phuket_pdf\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_pdf.inc`: использование mPDF или TCPDF.
      - Проверьте обработку входных параметров, sanitization и сохранение файлов.
      - Убедитесь в освобождении ресурсов после генерации (garbage collection).
</rule>

<rule>
name: s45_phuket_export
description: Экспорт данных в s45_phuket_export.inc
filters:
  - type: file_path
    pattern: "s45_phuket_export\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_export.inc` для анализа CSV/XML экспортов.
      - Проверьте chunked processing и memory usage для больших наборов.
      - Убедитесь в защите от CSV-injection и XSS.
</rule>

<rule>
name: s45_phuket_search_json
description: JSON API поиска в s45_phuket_search_json.inc
filters:
  - type: file_path
    pattern: "s45_phuket_search_json\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_search_json.inc`: формирование JSON-ответа для поиска.
      - Проверьте использование `db_select()` и фильтрацию `_GET` параметров через s45_toObject().
      - Убедитесь в безопасности output при работе с JSON.
</rule>

<rule>
name: s45_phuket_form_api
description: Form API реализации в s45_phuket_form_api.inc и _api2.inc
filters:
  - type: file_path
    pattern: "s45_phuket_form_api(?:2)?\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_form_api.inc` и `s45_phuket_form_api2.inc`: генерация форм через Form API.
      - Проверьте валидацию, submit handlers и использование `t()`.
      - Убедитесь в безопасности input и экранировании.
</rule>

<rule>
name: s45_phuket_perenos
description: Сценарии миграции в s45_phuket_perenos.inc и _perenos2.inc
filters:
  - type: file_path
    pattern: "s45_phuket_perenos(?:2)?\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите скрипты `s45_phuket_perenos.inc` и `_perenos2.inc` для миграции данных.
      - Проверьте использование db_query(), memory_limit и max_execution_time.
      - Убедитесь в логировании ошибок и rollback при неудаче.
</rule>

<rule>
name: s45_phuket_query
description: Кастомные SQL-запросы в s45_phuket_query.inc
filters:
  - type: file_path
    pattern: "s45_phuket_query\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_query.inc`: функции формирования сложных SQL-запросов.
      - Проверьте использование placeholders и db_select().
      - Оцените возможность оптимизации и кэширования.
</rule>
