---
description: 
globs: 
alwaysApply: true
---
# Compo Editor PHP Rules

<rule>
name: compo_editor_php
filters:
  - type: file_path
    pattern: "editor.php$"
actions:
  - type: suggest
    message: |
      - Файл editor.php реализует редактор для компонента.
      - Используйте только безопасные методы работы с пользовательским вводом (Form API, валидация, check_plain()).
      - Не допускайте прямого доступа к $_POST/$_GET — только через $form_state['values'].
      - Документируйте все публичные методы и параметры.
      - Не храните бизнес-логику в editor.php — только UI и валидация.
      - Для сохранения используйте API компонента, а не прямую работу с БД.
</rule>
## Безопасная работа с DTO/AR и API-ответами
- При работе с DTO/AR и формировании API-ответов всегда проверяйте существование и тип каждого свойства перед использованием.
- Не обращайтесь к вложенным объектам/массивам без проверки на is_object/is_array и isset.
- Для новых полей — аналогично: сначала проверка, потом использование.
- Это предотвращает фатальные ошибки и обеспечивает стабильность API даже при неполных или изменённых данных. 