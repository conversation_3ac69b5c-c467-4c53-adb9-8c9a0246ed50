---
description: 
globs: 
alwaysApply: true
---
# Base Components and Utilities Cursor Rules

<rule>
name: compo_api2
description: API execution for Compo components
filters:
  - type: file_path
    pattern: "CompoApi2\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `CompoApi2.php` и изучите метод `exec($compoId, $method, $data)`.
      - Проверьте, как используются `PageId::set()`, `CompoRepo` для загрузки класса компонента.
      - Обратите внимание на обработку формата входных данных и вывод JSON через `drupal_json_output()`.
      - Убедитесь в проверке прав доступа и логировании через `Logger`.
</rule>

<rule>
name: compo_scanner
description: Автоматическое сканирование и регистрация компонентов
filters:
  - type: file_path
    pattern: "CompoScanner\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `CompoScanner.php`: класс `CompoScanner::getForAllDirs()` сканирует каталоги компонентов.
      - Проверьте фильтрацию директорий и регистрацию в `CompoInfo.s45.json`.
      - Убедитесь, что пути формируются корректно и учитывают мультисайт.
</rule>

<rule>
name: compo_maker
description: Генерация шаблонов компонентов
filters:
  - type: file_path
    pattern: "CompoMaker\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `CompoMaker.php`: методы `make()` и `copyTemplate()` создают файлы `.php`, `.tpl.php`, `.js`, `.css`.
      - Проверьте настройки шаблонов и работу с файловой системой (`file_exists()`, `file_put_contents()`).
      - Убедитесь, что при повторном запуске не затираются существующие компоненты без confirmation.
</rule>

<rule>
name: query_from_events
description: Базовый класс для построения запросов Event Sourcing
filters:
  - type: file_path
    pattern: "QueryFromEvents\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `QueryFromEvents.php`: базовый класс для запросов к денормализованным таблицам.
      - Проверьте методы `exec()`, `load()` и хуки `handle<ARName><EventName>()`.
      - Оцените использование транзакций, фильтров (`$this->filter`), сортировки и пагинации.
</rule>

<rule>
name: query_from_events_last
description: Запрос последнего события для агрегата
filters:
  - type: file_path
    pattern: "QueryFromEventsLast\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `QueryFromEventsLast.php`: класс для получения только последнего события.
      - Проверьте переопределение метода `exec()` или `getLastEvent()`.
      - Убедитесь, что `$this->limit = 1` и `isLast` флаг правильно используются.
</rule>

<rule>
name: compo_repo
description: Репозиторий компонентов
filters:
  - type: file_path
    pattern: "CompoRepo\.php$"
actions:
  - type: suggest
    message: |
      - Изучите `CompoRepo.php`: методы `getById()` и `getByClass()` для загрузки компонента.
      - Проверьте кэширование объектов компонентов и обработку ошибок при отсутствии файла.
</rule>

<rule>
name: json_repo
description: Работа с JSON репозиториями
filters:
  - type: file_path
    pattern: "JsonRepo\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `JsonRepo.php`: методы `open()`, `save()`, `create()` для работы с `.s45.json`.
      - Проверьте обработку ошибок парсинга JSON (`json_last_error()`).
      - Убедитесь в безопасных операциях `file_exists()` и `file_put_contents()`.
</rule>

<rule>
name: page_id
description: Управление идентификатором страницы
filters:
  - type: file_path
    pattern: "PageId\.php$"
actions:
  - type: suggest
    message: |
      - Изучите `PageId.php`: метод `set($id)` и `get()` для контекста компонента.
      - Проверьте взаимодействие с `arg()` и влияние на маршрутизацию компонентов.
</rule>

<rule>
name: site_conf
description: Конфигурация сайта
filters:
  - type: file_path
    pattern: "SiteConf\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `SiteConf.php`: методы `getId()`, `getDomain()`, `loadConfig()`.
      - Убедитесь в корректном определении `$siteId` и пути к файловому хранилищу.
</rule>

<rule>
name: yandex_translator
description: Интеграция с Yandex Translator
tags: i18n
filters:
  - type: file_path
    pattern: "YandexTranslator\.php$"
actions:
  - type: suggest
    message: |
      - Изучите `YandexTranslator.php`: методы `translate($text, $toLang)`.
      - Проверьте использование API-ключей, кэширование результатов и обработку ошибок HTTP.
</rule>
