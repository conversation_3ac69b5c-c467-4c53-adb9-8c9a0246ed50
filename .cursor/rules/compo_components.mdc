---
description: 
globs: 
alwaysApply: false
---
# Compo Component Files Cursor Rules

<rule>
name: compo_component_class
description: Ensure Compo component class structure
filters:
  - type: file_content
    pattern: "class .* extends .*Compo"
actions:
  - type: suggest
    message: |
      - Проверьте, что класс наследуется от `Site45\\Compo\\Compo`.
      - Объявите все публичные свойства, используемые в шаблоне.
      - Реализуйте метод `beforeRender($props)` для подготовки данных.
      - Документируйте класс и методы PHPDoc (`@param`, `@return`).
</rule>

<rule>
name: compo_component_template
description: Template file for Compo component
filters:
  - type: file_path
    pattern: "\.tpl\.php$"
actions:
  - type: suggest
    message: |
      - Убедитесь, что файл шаблона соответствует имени компонента и находится рядом с классом.
      - Экранируйте выводимые данные (`check_plain()`, `filter_xss()`).
      - Используйте `<?php print $variable; ?>` для вывода, избегайте неподготовленных вставок.
</rule>

<rule>
name: compo_component_class_d2
description: Structure rules for D2 variant Compo components
filters:
  - type: file_content
    pattern: "class .*D2 extends .*Compo"
actions:
  - type: suggest
    message: |
      - Классы с суффиксом `D2` обозначают вариант компонента второго поколения.
      - Убедитесь, что класс наследуется от `Site45\\Compo\\Compo`.
      - Объявите все публичные свойства, используемые в шаблоне.
      - Реализуйте `beforeRender($props)` и `afterRender()` при необходимости.
      - Документируйте отличие D2-версии в PHPDoc.
</rule>

<rule>
name: compo_component_template_d2
description: Template rules for D2 variant components
filters:
  - type: file_path
    pattern: ".*D2\\.tpl\\.php$"
actions:
  - type: suggest
    message: |
      - Имя шаблона должно совпадать с именем класса компонента с суффиксом `D2`.
      - Экранируйте выводимые данные (`check_plain()`, `filter_xss()`).
      - Используйте `drupal_attributes()` и безопасный вывод.
      - Убедитесь, что шаблон находится в папке компонента.
</rule>

<rule>
name: compo_component_assets_d2
description: Naming and usage rules for D2 component assets
filters:
  - type: file_path
    pattern: ".*D2\\.(js|css)$"
actions:
  - type: suggest
    message: |
      - JS/CSS файлы с `D2` должны называться в соответствии с компонентом (например `PhuketPropMapD2.js`).
      - Используйте современный JS (ES6+) без глобальных переменных.
      - Подключайте стили через `getCompoRes()` или `drupal_add_css()`, а скрипты через `drupal_add_js()`.
      - Проверьте, что пути к ассетам корректны и файлы существуют.
</rule>

<rule>
name: compo_component_js
description: JavaScript best practices for Compo components
filters:
  - type: file_path
    pattern: "/Compo/.*\\.js$"
actions:
  - type: suggest
    message: |
      - Убедитесь, что скрипты компонента используют ES6+ синтаксис и модули или IIFE для изоляции.
      - Избегайте глобальных переменных; экспортируйте методы через namespace либо передавайте через API компонента.
      - Документируйте публичные функции JSDoc (/** @param, @return */).
      - Подключайте файл через `getCompoRes()` или `drupal_add_js()`, избегайте inline-скриптов.
</rule>

<rule>
name: compo_component_css
description: CSS best practices for Compo components
filters:
  - type: file_path
    pattern: "/Compo/.*\\.css$"
actions:
  - type: suggest
    message: |
      - Используйте неймспейс для стилей компонента через класс `.s45-Compo` и имя компонента.
      - Избегайте сбросов стилей глобально; ограничивайте селекторы ближайшим контейнером.
      - Документируйте блоки стилей через комментарии (/* Component styles */).
      - Подключайте файл через `getCompoRes()` или `drupal_add_css()`, избегайте inline-стилей.
</rule>
