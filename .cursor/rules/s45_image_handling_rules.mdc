---
description: 
globs: 
alwaysApply: true
---
# S45 Image Handling Rules

<rule>
name: s45_image_upload_component
description: Правила для стандартного компонента загрузки изображений FormImage
filters:
  - type: file_path
    pattern: "s45_base/classes/Site45/Sets/Form/Compo/FormImage/FormImage\\.php$"
  - type: file_path
    pattern: "s45_base/classes/Site45/Sets/Form/Compo/FormImage/FormImage\\.tpl\\.php$"
  - type: file_content
    pattern: "s45_render\\s*\\(\\s*'FormImage'"
actions:
  - type: suggest
    message: |
      ## FormImage Component Usage
      
      - Компонент `FormImage` является стандартным решением для загрузки изображений в формах админки.
      - **Бэкенд (`FormImage.php`):**
        - Метод `apiUpload` обрабатывает загрузку через AJAX.
        - Метод `getUploaded` создает объект `FileDto` для загруженного файла, генерируя уникальный `id`.
        - **Критично:** `getUploaded` формирует `id` в формате даты с временем и рандомным числом:
          ```php
          $newFile->id = date('Ymd_His').'_'. rand(1000, 9999).'_'.s45_trans($file['name'], TRUE);
          ```
        - Файлы временно сохраняются в директории `public://tmp/`.
      - **Шаблон (`FormImage.tpl.php`):**
        - Отображает превью загруженных изображений.
        - Генерирует скрытые поля для каждого файла, передаваемые при основном сабмите формы.
        - **Важно:** Для каждого файла сохраняются поля `id`, `name`, `dir`, `mime`.
        - Пример:
          ```php
          <input 
            type="hidden" 
            value="<?php print isset($fileDto->id) ? $fileDto->id : ''; ?>" 
            name="<?php print $this->fieldName; ?>[<?php print $index; ?>][id]"
          >
          ```
      - **Интеграция:** Компонент вызывается через `s45_render('FormImage', $props)`. В `$props` передается `$fieldName` (например, `'photos'`), который будет использоваться как имя массива в `$_POST`.
</rule>

<rule>
name: s45_image_data_structure
description: Структура данных для хранения информации об изображениях (FileDto, photos)
filters:
  - type: file_path
    pattern: ".*AR\\.php$"
  - type: file_path
    pattern: ".*Dto\\.php$"
  - type: file_content
    pattern: "\\$this->photos\\s*="
  - type: file_content
    pattern: "FileDto"
actions:
  - type: suggest
    message: |
      ## Image Data Structure (FileDto)
      
      - Информация об изображениях, связанных с сущностями (например, `PhuketPropertyAR`), хранится в виде массива объектов, обычно в свойстве `$photos`.
      - Каждый элемент массива должен быть объектом `FileDto` (или совместимым `stdClass`).
      - **Ключевые свойства `FileDto`:**
        - `id`: **Главное поле для идентификации** - уникальный ID файла (часто формат: дата_время_рандом_имя).
        - `name`: Оригинальное имя файла.
        - `mime`: MIME-тип файла (e.g., `image/jpeg`).
        - `dir`: Директория хранения относительно `public://` (например, `phuket/property/photos/YYYY/MM`).
      - **Архитектурные особенности:**
        - Система использует поле `id` для определения пути к файлу динамически
        - Это позволяет гибко обрабатывать различные типы файлов (загруженные, статические, внешние URL)
        - Через один обработчик s45_imgSrcR система может работать с разными источниками файлов
      - **Получение данных:** Данные для `$photos` обычно приходят из компонента `FormImage` при сохранении основной сущности.
      - **Сохранение:** Массив `$photos` сохраняется как часть основного объекта AR при его сериализации в EventStore.
</rule>

<rule>
name: s45_image_url_generation
description: Использование хелпера s45_imgSrc для генерации URL изображений
filters:
  - type: file_content
    pattern: "s45_imgSrc\\("
  - type: file_path
    pattern: "s45_base\\.lib\\.inc$"
actions:
  - type: suggest
    message: |
      ## Image URL Generation (`s45_imgSrc`)
      
      - Для генерации URL изображений **всегда** используйте хелпер `s45_imgSrc($fileDto, $styleName)`.
      - **Параметры:**
        - `$fileDto`: Объект `FileDto` (или совместимый `stdClass`), содержащий информацию о файле. **Главное поле - `id`**.
        - `$styleName`: Строка, имя стиля изображения Drupal (например, `'S45_IMST_500X300_CROP'`). См. `s45_imagestyles.module`.
      - **Принцип работы функции s45_imgSrcR:**
        ```php
        function s45_imgSrcR(&$fileDto, $styleName = '') {
          // 1. Проверка и получение id файла
          $fileID = $fileDto->id;
          
          // 2. Разветвление логики в зависимости от формата id
          // Статический файл из компонента
          if(strpos('qwerty'.$fileID, 'http') AND strpos('qwerty'.$fileID, 'Site45/Sets')) {
            // Обработка статических файлов
          } 
          // Файл фото агента
          else if(strpos($fileID, 'pictures')) {
            // Обработка файлов агентов
          } 
          // Внешний URL (например, со старого сайта)
          else if(strpos('qwerty'.$fileID, 'http')) {
            // Кэширование внешних файлов
            $filePath = s45_inreams_old_file_name($fileID);
          } 
          // Локальный загруженный файл
          else {
            // Определение пути по id
            $filePath = S45_SITES_DIR.'/'.SiteConf::getId().'/files/tmp/'.$fileID;
          }
          
          // 3. Применение стиля изображения
          if($styleName) {
            return image_style_url($styleName, $filePath);
          }
          
          // 4. Возврат прямого URL, если стиль не указан
          return file_create_url($filePath);
        }
        ```
      - **Зависимости:** Корректная работа функции полностью зависит от правильности поля `id` в объекте `$fileDto`.
      - **Особенности для PDF:** mPDF требует локальные пути к файлам, поэтому для вставки изображений в PDF нужно использовать `drupal_realpath()` с результатом `s45_imgSrc()`.
</rule>

<rule>
name: s45_image_styles_usage
description: Использование и определение стилей изображений Drupal
filters:
  - type: file_path
    pattern: "s45_imagestyles\\.module$"
  - type: file_content
    pattern: "S45_IMST_"
  - type: file_content
    pattern: "image_style_url\\("
actions:
  - type: suggest
    message: |
      ## Drupal Image Styles Usage
      
      - Система использует стандартные Drupal Image Styles для создания различных версий изображений (размеры, обрезка).
      - **Определение стилей:** Новые стили определяются в модуле `s45_imagestyles.module` с помощью `hook_image_default_styles()`.
      - **Именование:** Используются константы с префиксом `S45_IMST_` (например, `S45_IMST_1900X1000_SCALE`, `S45_IMST_500X300_CROP`).
      - **Применение:** Имя стиля передается вторым аргументом в функцию `s45_imgSrc($fileDto, $styleName)`.
      - **Генерация:** Drupal автоматически генерирует файл стиля при первом запросе URL этого стиля и сохраняет его в `sites/default/files/styles/STYLE_NAME/public/...`.
      - **Очистка кэша:** После добавления/изменения стилей необходимо очистить кэш Drupal (`drush cc all` или через UI).
      - **Стили, часто используемые в PDF:**
        - `S45_IMST_1900X1000_SCALE` - для крупных фото в полном размере
        - `S45_IMST_500X300_CROP` - для миниатюр и галереи
        - `S45_IMST_100X100_CROP` - для маленьких миниатюр (иконок)
</rule>

<rule>
name: s45_image_template_display
description: Правила отображения изображений в шаблонах Compo (.tpl.php)
filters:
  - type: file_path
    pattern: "\\.tpl\\.php$"
  - type: file_content
    pattern: "<img\\s+src="
  - type: file_content
    pattern: "s45_imgSrc"
actions:
  - type: suggest
    message: |
      ## Displaying Images in Templates
      
      - В шаблонах `.tpl.php` для вывода изображений используйте тег `<img>` и функцию `s45_imgSrc`.
      - **Стандартный шаблон:**
        ```php
        <?php if (!empty($photoObject) && $photoObject->id): ?>
          <img 
            src="<?php print s45_imgSrc($photoObject, 'S45_IMST_STYLE_NAME'); ?>" 
            alt="<?php print check_plain(s45_lang(isset($photoObject->alt) ? $photoObject->alt : '')); ?>"
          >
        <?php else: ?>
          <?php // Placeholder или сообщение об отсутствии фото ?>
        <?php endif; ?>
        ```
      - **Обязательно проверяйте** наличие объекта `$photoObject` и его свойства `id` перед вызовом `s45_imgSrc`.
      - **Для адаптивных изображений** используйте:
        ```php
        <picture>
          <source media="(min-width: 992px)" srcset="<?php print s45_imgSrc($photoObject, 'S45_IMST_1900X1000_SCALE'); ?>">
          <source media="(min-width: 768px)" srcset="<?php print s45_imgSrc($photoObject, 'S45_IMST_800X600_SCALE'); ?>">
          <img src="<?php print s45_imgSrc($photoObject, 'S45_IMST_400X300_SCALE'); ?>" alt="...">
        </picture>
        ```
      - **Для вывода в PDF** используйте SVG, PNG или JPEG (не WebP).
      - Экранируйте атрибуты (`alt`, `title`) с помощью `check_plain()` или аналогов.
</rule>

<rule>
name: s45_image_pdf_generation
description: Правила и проблемы вставки изображений в PDF-файлы (mPDF)
filters:
  - type: file_path
    pattern: "s45_phuket_pdf\\.inc$"
  - type: file_path
    pattern: "PhuketPdf\\.tpl\\.php$"
actions:
  - type: suggest
    message: |
      ## Images in PDF Generation (mPDF)
      
      - Генерация PDF использует библиотеку `mPDF` версии 6.0.
      - **Основные принципы:**
        - mPDF требует локальные физические пути к файлам, не URL
        - Шаблоны генерации PDF делятся на модульные секции в `/res/inc/`
        - Изображения обрабатываются через `s45_imgSrc()` с последующим преобразованием
      
      - **Процесс добавления изображения в PDF:**
        ```php
        // 1. Получение объекта фото
        $photoObject = $this->propertyDto->photos[0];
        
        // 2. Генерация URL через s45_imgSrc
        $imageUrl = s45_imgSrc($photoObject, 'S45_IMST_1900X1000_SCALE');
        
        // 3. Преобразование в локальный путь (для mPDF)
        $imagePath = drupal_realpath(file_build_uri(str_replace(file_create_url('public://'), '', $imageUrl)));
        
        // 4. Вставка в PDF
        <img src="<?php echo $imagePath; ?>" style="width: 100%;">
        ```
      
      - **Отладка проблем с изображениями:**
        - Используйте режим `/debug` в URL для проверки HTML-версии
        - Включите отладку mPDF: `$mpdf->showImageErrors = true; $mpdf->debug = true;`
        - Проверяйте физическое существование файлов на сервере
      
      - **Тестирование на всех языках:**
        - Особое внимание уделите английской версии PDF
        - Проверяйте шаблоны в режиме debug и без него
        - Тестируйте в разных режимах (clean, guest, my)
</rule>

<rule>
name: s45_image_data_persistence
description: Обеспечение корректного сохранения данных изображений через Event Sourcing и Read Model
filters:
  - type: file_path
    pattern: "EventStore\\.php$"
  - type: file_path
    pattern: "AR\\.php$"
  - type: file_path
    pattern: "PhuketPropertyAR\\.php$"
  - type: file_path
    pattern: "PhuketPropertyQuery\\.php$"
  - type: file_path
    pattern: "PhuketAdminPropertyEditor\\.php$"
  - type: file_path
    pattern: "FormImage\\.php$"
actions:
  - type: suggest
    message: |
      ## Event Sourcing и сохранение данных изображений
      
      **Жизненный цикл данных о файлах в Event Sourcing системе:**
      
      1. **Загрузка через админку**:
         - Пользователь загружает изображение через FormImage
         - FormImage создает объект FileDto с уникальным id и сохраняет файл во временную директорию
         - Данные о файле передаются в форму редактирования объекта (например, PhuketAdminPropertyEditor)
      
      2. **Сохранение агрегата**:
         - Создается или загружается объект PhuketPropertyAR
         - Устанавливаются данные, включая массив photos с объектами FileDto
         - Вызывается метод save(), который генерирует событие PhuketPropertySaved
         - EventStore сохраняет событие в таблицу _s45_events
      
      3. **Обновление Read Model**:
         - PhuketPropertyQuery обрабатывает событие через handlePhuketPropertySaved
         - Создается denormalized запись в таблице _phuket_Property
         - Данные о файлах сохраняются в propertyDto (сериализованный объект)
      
      4. **Загрузка для отображения**:
         - PhuketPropertyQuery::load() загружает данные из кэша или из таблицы _phuket_Property
         - Свойство photos десериализуется и становится доступным для шаблонов
         - Шаблоны используют s45_imgSrc() для генерации URL на основе id
      
      **Основные архитектурные принципы**:
         - Конвенция использования id для идентификации файлов
         - Динамическое определение физического пути на основе id
         - Единый механизм обработки разных типов источников файлов
         - Устойчивость к изменениям физического хранилища
</rule>

<rule>
name: s45_image_id_conventions
description: Соглашения об именовании и использовании id файлов
filters:
  - type: file_content
    pattern: "FileDto|s45_imgSrc|s45_imgSrcR"
actions:
  - type: suggest
    message: |
      ## Соглашения об использовании ID файлов
      
      Система индентификации файлов в s45 основана на следующих принципах:
      
      1. **Уникальность идентификации**:
         - Каждый файл должен иметь уникальный id
         - Основной формат для загружаемых файлов: `YYYYmmdd_HHiiss_RAND_filename`
      
      2. **Типы идентификаторов**:
         - Полный URL (начинающийся с http/https) для внешних файлов
         - Путь относительно корня сайта для статических файлов компонентов
         - Идентификатор в формате даты+рандом для загруженных файлов
      
      3. **Распознавание типа файла**:
         ```php
         // Статический файл из компонента
         if(strpos($id, 'http') && strpos($id, 'Site45/Sets'))
         
         // Фото агента
         else if(strpos($id, 'pictures'))
         
         // Внешний URL
         else if(strpos($id, 'http'))
         
         // Загруженный файл
         else
         ```
      
      4. **Физическое хранение**:
         - Загруженные файлы: `/files/tmp/{id}`
         - Кэшированные внешние файлы: `/files/old/{MD5хеш}.jpg`
         - Статические файлы компонентов: определяются относительно корня сайта
      
      5. **Best Practices**:
         - Всегда генерируйте уникальный id через FormImage::getUploaded()
         - Никогда не используйте прямые пути в шаблонах, только s45_imgSrc()
         - Для PDF используйте форматы файлов, совместимые с mPDF (JPEG, PNG, GIF)
         - Всегда проверяйте существование id перед вызовом s45_imgSrc()
</rule>

<rule>
name: s45_image_event_sourcing
description: Особенности Event Sourcing для изображений
filters:
  - type: file_path
    pattern: "EventStore\\.php$|EventQuery\\.php$"
actions:
  - type: suggest
    message: |
      ## Event Sourcing и сериализация данных изображений
      
      - **Сериализация в Event Sourcing**:
        ```php
        // Корректный метод сохранения в EventStore::addEvent()
        $this->eventTable->payload = serialize($payload);
        
        // Корректный метод восстановления в EventQuery::exec()
        $event->payload = unserialize($eventRow->payload);
        ```
      
      - **Потенциальные ошибки**:
        - Использование `json_decode(json_encode($payload))` вместо `serialize($payload)`
        - Смешивание типов сериализации между сохранением и загрузкой
        - Неправильная обработка ошибок десериализации через `@unserialize()`
      
      - **Влияние на работу с изображениями**:
        - Некорректная сериализация может привести к потере данных о файлах
        - Особенно чувствительны сложные структуры (массивы объектов FileDto)
        - Debug-режим PDF может работать корректно, а обычный - нет, из-за разных путей загрузки данных
      
      - **Обработка обратной совместимости**:
        - При исправлении процесса сериализации необходимо обновить read-модель
        - Обработка смешанных форматов может потребовать условной логики:
          ```php
          $data = @unserialize($raw);
          if ($data === false) {
            // Попытка интерпретировать как JSON
            $data = json_decode($raw);
          }
          ```
        - Для некоторых случаев может потребоваться пересохранение существующих объектов
</rule>