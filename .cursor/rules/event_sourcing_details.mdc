---
description: 
globs: 
alwaysApply: true
---
# Event Sourcing в S45 Framework

<rule>
name: event_sourcing_core
description: Основные компоненты Event Sourcing в S45
filters:
  - type: file_path
    pattern: "Event(Store|Query|Dto|Handler)?\\.php$"
actions:
  - type: suggest
    message: |
      Event Sourcing в S45 построен на трех ключевых компонентах:
      
      1. **Aggregate Root (AR)** - Базовый класс `Site45\Event\AR` для сущностей, отслеживающих изменения.
      2. **EventStore** - Сохраняет события в таблице `_s45_events` с флагом `isLast`.
      3. **EventQuery** - Извлекает события из таблицы для восстановления состояния объектов.
</rule>

<rule>
name: event_sourcing_ar
description: Использование AR в Event Sourcing
filters:
  - type: file_path
    pattern: "AR\\.php$"
actions:
  - type: suggest
    message: |
      Active Record (AR) в Event Sourcing:
      
      - Класс наследуется от `Site45\Event\AR`
      - Методы `load()`, `save()`, `delete()` генерируют события
      - AR определяет своё состояние на основе истории событий
      - Изменение состояния AR приводит к созданию нового события
</rule>

<rule>
name: event_store
description: Механизм хранения событий через EventStore
filters:
  - type: file_path
    pattern: "EventStore\\.php$"
actions:
  - type: suggest
    message: |
      EventStore отвечает за сохранение событий:
      
      - `EventStore::addEvent(arName, arId, type, payload)` добавляет новое событие
      - При добавлении нового события для конкретного arId:
        - Устанавливает флаг `isLast=0` для всех существующих событий этого arId
        - Устанавливает флаг `isLast=1` для нового события
      - Использует `db_transaction()` для атомарных обновлений
</rule>

<rule>
name: event_query
description: Запросы к событиям через EventQuery
filters:
  - type: file_path
    pattern: "EventQuery\\.php$"
actions:
  - type: suggest
    message: |
      EventQuery извлекает события из EventStore:
      
      - `EventQuery::getLastEvent(arName, arId)` для получения последнего события
      - `EventQuery::getEvents(arName, arId)` для получения всех событий
      - `EventQuery::getEventsByType(arName, arId, type)` для фильтрации по типу
      - Часто используется репозиториями для загрузки состояния объектов
</rule>

<rule>
name: read_model_handlers
description: Обработчики событий и обновление Read Model
filters:
  - type: file_path
    pattern: "QueryFromEvents\\.php$"
actions:
  - type: suggest
    message: |
      Обновление Read Model через обработчики событий:
      
      - `QueryFromEvents` обрабатывает поток событий и обновляет денормализованные таблицы
      - Для каждого типа события существует метод-обработчик `handle<ARName><EventType>`
      - Например, `handlePropertySaved(EventDto $event)` обновляет запись в Read Model
      - Метод `exec()` запрашивает данные из Read Model, а не из лога событий
      - Обработка событий происходит при создании запроса (`create()`) или в фоновом режиме
</rule>

<rule>
name: event_dto_structure
description: Структура EventDto в системе
filters:
  - type: file_path
    pattern: "EventDto\\.php$"
actions:
  - type: suggest
    message: |
      EventDto содержит все данные о событии:
      
      - `id`: Уникальный идентификатор события
      - `arName`: Имя типа агрегата ('PhuketProperty', 'CompoConfig')
      - `arId`: UUID агрегата, к которому относится событие
      - `type`: Тип события ('Saved', 'Deleted', 'Updated')
      - `payload`: Сериализованное состояние объекта на момент события
      - `isLast`: Флаг (0/1), указывающий, является ли событие последним
      - `created`: Временная метка создания события
</rule>

<rule>
name: event_sourcing_pattern
description: Шаблон использования Event Sourcing
filters:
  - type: file_path
    pattern: "Repo\\.php$"
actions:
  - type: suggest
    message: |
      Типичный паттерн использования Event Sourcing в S45:
      
      1. **Загрузка объекта:**
         ```php
         $event = EventQuery::getLastEvent('PhuketProperty', $propertyId);
         $propertyAR = unserialize($event->payload);
         ```
      
      2. **Изменение и сохранение:**
         ```php
         $propertyAR->title = $newTitle;
         EventStore::addEvent('PhuketProperty', $propertyAR->id, 'Saved', serialize($propertyAR));
         ```
      
      3. **Обновление Read Model:**
         ```php
         function handlePropertySaved(EventDto $event) {
           $property = unserialize($event->payload);
           // Обновление записи в таблице _phuket_Property
           db_merge('_phuket_Property')
             ->key(['id' => $property->id])
             ->fields([...])
             ->execute();
         }
         ```
</rule>

<rule>
name: event_sourcing_optimization
description: Оптимизации для работы с Event Sourcing
filters:
  - type: file_path
    pattern: "(EventQuery|QueryFromEvents)\\.php$"
actions:
  - type: suggest
    message: |
      Оптимизации Event Sourcing в S45:
      
      - Кэширование последних событий через `cache_get`/`cache_set`
      - Отслеживание последней обработанной позиции через `variable_get`/`variable_set`
      - Денормализованные Read Model таблицы оптимизированы для быстрого поиска
      - Пакетная обработка новых событий в фоновом режиме
      - Периодический запуск через cron для обновления Read Model
</rule>

<rule>
name: data_serialization_techniques
description: Техники сериализации данных в Event Sourcing
filters:
  - type: file_path
    pattern: "Event(Store|Query)\\.php$"
actions:
  - type: suggest
    message: |
      ## Различные техники сериализации в системе
      
      В системе используются разные способы сериализации данных для разных целей:
      
      ### 1. `json_decode(json_encode($payload))`
      - Это техника **нормализации структуры данных**, а не сериализации
      - Преобразует все массивы в объекты stdClass
      - Приводит структуру к единому формату
      - Используется для очистки и стандартизации структур
      - Может терять PHP-специфичные типы данных и структуры
      - **Не является** альтернативой `serialize()`
      
      ### 2. `serialize($payload)`
      - Это полноценная **PHP-сериализация**
      - Сохраняет все PHP-специфичные типы данных и структуры классов
      - Сохраняет информацию о классе, включая приватные и защищенные свойства
      - Работает в паре с `unserialize()`
      - Результат можно использовать только в PHP
      
      ### Важно понимать
      - Эти методы **не взаимозаменяемы** и решают разные задачи
      - `json_decode(json_encode())` используется для очистки/нормализации структур 
      - `serialize()` используется для полноценного сохранения состояния PHP-объектов
      - Для правильной работы системы важно использовать тот метод, который ожидается в конкретном контексте
</rule>

<rule>
name: json_decode_encode_pattern
description: Использование json_decode(json_encode()) как архитектурного решения
filters:
  - type: file_content
    pattern: "json_decode\\(json_encode\\("
actions:
  - type: suggest
    message: |
      ## Использование json_decode(json_encode()) в архитектуре S45
      
      Механизм `json_decode(json_encode($object))` — **не баг, а осознанное архитектурное решение**:
      
      1. **Унификация типов данных:**
         - Используется в функции `s45_toObject($param)` для преобразования разнородных структур (массивы PHP, stdClass, объекты) в однородные объекты `stdClass`.
         - Обеспечивает согласованную структуру данных, независимо от источника.
      
      2. **Глубокое клонирование объектов:**
         - В редакторах (`PhuketAdminPropertyEditor` и др.) используется для создания глубокой копии объекта.
         - Предотвращает случайное изменение оригинального объекта при редактировании копии.
      
      3. **Нормализация вложенных структур:**
         - Рекурсивно преобразует все вложенные структуры, что гарантирует однородность объектов на любом уровне вложенности.
         - Особенно важно для объектов с фотографиями, ценами и другими сложными свойствами.
      
      4. **Транспортный механизм:**
         - Создаёт "чистый" объект без методов, только с данными, идеальный для сериализации.
         - Упрощает доступ к свойствам через стандартный синтаксис `$object->property`.
      
      5. **Согласованность в системе:**
         - Более 60 компонентов используют `$props = s45_toObject($props)` в методе `beforeRender()`.
         - Создаёт архитектурную согласованность во всей системе.
      
      **Не путать** с серилизацией для хранения в базе данных — для этого используются другие механизмы.
</rule>

<rule>
name: s45_toObject_usage
description: Использование s45_toObject() в компонентах
filters:
  - type: file_content
    pattern: "s45_toObject\\("
actions:
  - type: suggest
    message: |
      ## Функция s45_toObject() в компонентах
      
      `s45_toObject($param)` — это ключевая функция для обработки входных данных в компонентах:
      
      1. **Стандартное применение:**
         ```php
         public function beforeRender($props) {
           $props = s45_toObject($props);
           // Теперь можно использовать $props->property вместо проверок массива
         }
         ```
      
      2. **Назначение:**
         - Преобразует параметры из массивов в объекты для единообразного доступа
         - Нормализует структуру данных, независимо от источника (API, форма, кэш)
         - Обеспечивает совместимость между разными компонентами
      
      3. **Реализация:**
         ```php
         function s45_toObject($param) {
           return json_decode(json_encode($param));
         }
         ```
      
      4. **Преимущества:**
         - Упрощает доступ к вложенным структурам
         - Устраняет необходимость проверять тип (массив/объект)
         - Обеспечивает предсказуемость поведения в шаблонах
         
      Этот механизм является частью архитектурного паттерна системы и используется почти во всех компонентах.
</rule>

<rule>
name: pdf_image_id_usage
description: Использование ID вместо URI в объектах изображений
filters:
  - type: file_content
    pattern: "s45_imgSrc|s45_imgSrcR"
actions:
  - type: suggest
    message: |
      ## Использование ID вместо URI в объектах изображений
      
      Важная архитектурная особенность работы с изображениями в системе:
      
      1. **Свойство `id` вместо `uri`:**
         - Система использует **`$fileDto->id`** как основной идентификатор изображения
         - Функция `s45_imgSrc()` работает именно с `id`, а не с полем `uri`
         - Это **не ошибка**, а сознательное архитектурное решение
      
      2. **Преимущества такого подхода:**
         - Позволяет обрабатывать как внутренние, так и внешние изображения единообразно
         - Упрощает миграцию изображений между системами
         - Отделяет логику хранения от логики отображения
      
      3. **Обработка разных типов источников:**
         - Если `id` содержит "http" — обрабатывается как внешний URL
         - Иначе интерпретируется как внутренний идентификатор файла
         - Для внешних URL используется функция `s45_inreams_old_file_name()` для кэширования
      
      4. **Особенности в PDF:**
         - При генерации PDF в английской версии могут отсутствовать изображения из-за несоответствия между ожидаемым форматом `id` и фактическим
         - Это происходит из-за особенностей сериализации, а не из-за самого механизма использования `id`
      
      При разработке новых компонентов следует придерживаться этого паттерна и использовать `id` как основное свойство для идентификации изображений.
</rule>
