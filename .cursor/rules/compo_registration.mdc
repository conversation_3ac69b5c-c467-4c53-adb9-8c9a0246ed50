---
description: 
globs: 
alwaysApply: true
---
# Compo Component Registration Rule

<rule>
name: compo_registration
filters:
  - type: file_extension
    pattern: "\\.(php|tpl\\.php)$"
actions:
  - type: suggest
    message: |
      ## Как регистрируются компоненты Compo в IndreamsPhuket
      
      1. Для автоматической регистрации компонентов используется класс `Site45\\Base\\CompoScanner`.
      2. Для пересканирования и регистрации всех компонентов используйте URL `/compomake/scan` (или пункт меню "Compo Make" в админке).
      3. Сканер ищет компоненты во всех директориях, указанных в конфиге сайта, и формирует файл базы подключённых компонентов: `CompoInfo.s45.json`.
      4. После сканирования все новые компоненты становятся доступны для CompoRouter и могут быть вызваны по имени.
      5. Если компонент не найден, проверьте:
         - Имя класса и папки совпадают
         - В шаблоне есть ключевое слово `component`
         - Нет ошибок в структуре файлов
      6. Не рекомендуется редактировать `CompoInfo.s45.json` вручную — используйте автоматическое сканирование.
      7. Пример записи в базе компонентов:
         ```json
         "PhuketAdminScreens": {
           "type": "CompoInfo",
           "dir": "sites/all/modules/__s45/Compo/Admin/PhuketAdminScreens",
           "id": "PhuketAdminScreens"
         }
         ```
      8. После добавления нового компонента всегда запускайте `/compomake/scan` для обновления базы.
</rule>
