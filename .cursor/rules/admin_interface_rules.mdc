---
description: 
globs: 
alwaysApply: true
---
# Admin Interface Rules

<rule>
name: admin_compo_php
filters:
  - type: file_extension
    pattern: "\.php$"
actions:
  - type: suggest
    message: |
      - Классы админских компонентов должны наследоваться от Compo, иметь публичные свойства для всех данных, используемых в шаблоне.
      - Все действия с данными (сохранение, удаление) должны идти через API компонента или сервисы, а не напрямую через $_POST/$_GET.
      - Для форм используйте Form API, валидацию, drupal_get_form, drupal_render.
      - Все публичные методы и параметры должны быть задокументированы.
      - Не храните бизнес-логику в шаблоне — только вывод.
</rule>

<rule>
name: admin_compo_tpl
filters:
  - type: file_extension
    pattern: "\.tpl\.php$"
actions:
  - type: suggest
    message: |
      - В шаблонах админских компонентов используйте только публичные свойства класса.
      - Все выводимые данные должны быть экранированы через check_plain()/filter_xss().
      - Для вывода форм используйте drupal_render/drupal_render_children.
      - Не допускайте inline-скриптов и стилей.
      - Документируйте структуру шаблона.
</rule>

<rule>
name: admin_compo_js
filters:
  - type: file_extension
    pattern: "\.js$"
actions:
  - type: suggest
    message: |
      - JS для админских компонентов должен быть неймспейсирован по имени компонента.
      - Используйте ES6+, JSDoc, избегайте глобальных переменных.
      - Для работы с формами используйте jQuery.on('submit'), блокировку кнопок, ajax через s45_apiSendForm.
      - Для интеграции с Google Maps используйте deferred loading и кэширование.
      - Не допускайте утечек данных и XSS.
</rule>

<rule>
name: admin_login_form
filters:
  - type: file_path
    pattern: "s45_phuket_login\.module$|s45-phuket-login\.tpl\.php$"
actions:
  - type: suggest
    message: |
      - Для кастомных форм логина используйте Form API, hook_form_alter, hook_theme, hook_user_login.
      - Все тексты должны быть многоязычными через LangVO и s45_lang.
      - Для вывода используйте drupal_render, drupal_render_children.
      - Не допускайте прямого доступа к $_POST/$_GET — только через $form_state['values'].
      - Все редиректы и проверки доступа должны идти через user_access и drupal_goto.
</rule>
