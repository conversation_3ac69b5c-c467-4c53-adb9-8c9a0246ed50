---
description: 
globs: 
alwaysApply: true
---
# Структура БД для indreamsphuket.com

## Основные таблицы Event Sourcing

### `_s45_events` (Таблица событий)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `id` | bigint | PRIMARY | Уникальный идентификатор события (авто-инкремент) |
| `created` | int | - | Временная метка создания события |
| `siteId` | varchar(64) | - | Идентификатор сайта |
| `arName` | varchar(64) | KEY | Имя типа агрегата (например, PhuketProper<PERSON>) |
| `arId` | varchar(36) | - | UUID агрегата |
| `name` | varchar(36) | - | Название события (например, Saved, Deleted) |
| `authorId` | varchar(36) | - | ID автора изменения |
| `ip` | varchar(36) | - | IP-адрес, с которого произошло изменение |
| `payload` | longtext | - | Сериализованный JSON с данными события |
| `note` | text | - | Примечание к событию |
| `isLast` | int | - | Флаг последнего события для агрегата (1=последнее) |

Эта таблица содержит `42,306` записей и является основой Event Sourcing. Имеет только 2 индекса: PRIMARY по `id` и индекс по `arName`.

### `_s45_aliases` (Таблица URL-алиасов)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `siteId` | varchar(36) | PRIMARY | Идентификатор сайта |
| `langCode` | varchar(8) | PRIMARY | Код языка |
| `sysPath` | varchar(64) | KEY | Системный путь (например, 'property/123') |
| `alias` | varchar(200) | PRIMARY | SEO-дружественный URL |

Эта таблица содержит `18,146` записей и отвечает за маппинг между системными путями и SEO-URL.

## Read Model таблицы

### `_phuket_Property` (Недвижимость)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `id` | varchar(36) | PRIMARY | UUID объекта недвижимости |
| `created` | int | - | Временная метка создания |
| `changed` | int | - | Временная метка последнего изменения |
| `published` | int | - | Флаг публикации (1=опубликовано) |
| `name` | text | - | Сериализованный объект с названиями на разных языках |
| `areaCommon` | int | - | Общая площадь объекта |
| `number` | int | - | Номер объекта |
| `numberStr` | varchar(4) | - | Строковое представление номера |
| `dealType` | varchar(32) | - | Тип сделки (sale, rent) |
| `propertyType` | varchar(32) | - | Тип недвижимости |
| `isRecommended` | int | - | Флаг рекомендованного объекта |
| `isPermium` | int | - | Флаг премиум-объекта |
| `isSaled` | int | - | Флаг проданного объекта |
| `isInvest` | int | - | Флаг инвестиционного объекта |
| `project` | varchar(36) | - | ID связанного проекта |
| `country` | varchar(36) | - | ID страны |
| `locality` | varchar(36) | - | ID местности |
| `subLocality` | varchar(36) | - | ID подместности (района/пляжа) |
| `bedrooms` | int | - | Количество спален |
| `propertyDto` | longtext | - | Сериализованный полный объект с данными |
| `propertyTeaserDto` | longtext | - | Сериализованный сокращенный объект для тизеров |
| `price_sale` | int | - | Цена продажи |
| `price_rent` | int | - | Цена аренды |
| `price_longtime` | int | - | Цена долгосрочной аренды |
| `lat` | double | - | Широта координат |
| `lng` | double | - | Долгота координат |

Эта таблица содержит `3,761` записей и имеет только PRIMARY индекс по `id`.

### `_phuket_Project` (Проекты)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `id` | varchar(36) | PRIMARY | UUID проекта |
| `created` | int | - | Временная метка создания |
| `changed` | int | - | Временная метка последнего изменения |
| `name` | varchar(256) | - | Название проекта на русском |
| `nameEn` | varchar(256) | - | Название проекта на английском |
| `projectDto` | longtext | - | Сериализованный объект с данными проекта |

Эта таблица содержит `532` записи и имеет только PRIMARY индекс по `id`.

### `_phuket_Option` (Опции/справочники)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `id` | varchar(36) | PRIMARY | UUID опции |
| `created` | int | - | Временная метка создания |
| `changed` | int | - | Временная метка последнего изменения |
| `name` | varchar(256) | - | Название опции |
| `optionName` | varchar(256) | - | Техническое имя опции |
| `parentId` | varchar(36) | - | ID родительской опции (для иерархий) |
| `dto` | longtext | - | Сериализованный объект с данными опции |

### `_phuket_Reservation` (Бронирования)
| Поле | Тип | Индекс | Описание |
|------|-----|--------|----------|
| `id` | varchar(36) | PRIMARY | UUID бронирования |
| `created` | int | - | Временная метка создания |
| `changed` | int | - | Временная метка последнего изменения |
| `resType` | varchar(64) | - | Тип бронирования |
| `status` | varchar(64) | - | Статус бронирования |
| `propertyId` | varchar(36) | - | ID связанного объекта недвижимости |
| `propertyNumber` | varchar(36) | - | Номер объекта недвижимости |
| `dateFrom` | int | - | Начальная дата бронирования (timestamp) |
| `dateTo` | int | - | Конечная дата бронирования (timestamp) |
| `dto` | text | - | Сериализованный объект с данными бронирования |

## ER-диаграмма

```
+---------------+       +-------------------+
| _s45_events   |       | _phuket_Property  |
+---------------+       +-------------------+
| id (PK)       |       | id (PK)           |
| arName        |<----->| ...               |
| arId          |       | project           |----+
| payload       |       | country           |--+ |
| ...           |       | locality          |-+ |
+---------------+       | subLocality       |+ |
                        | ...               |  |
                        +-------------------+  |
                                               |
+-----------------+     +-------------------+  |
| _phuket_Option  |     | _phuket_Project   |  |
+-----------------+     +-------------------+  |
| id (PK)         |<----| id (PK)           |<-+
| parentId        |--+  | ...               |
| ...             |  |  +-------------------+
+-----------------+  |
        ^            |
        +------------+

+------------------+    +--------------------+
| _phuket_Property |    | _phuket_Reservation|
+------------------+    +--------------------+
| id (PK)          |<---| propertyId         |
| ...              |    | ...                |
+------------------+    +--------------------+

+---------------+       +-------------------+
| _s45_aliases  |       | users             |
+---------------+       +-------------------+
| sysPath       |       | uid (PK)          |
| alias (PK)    |       | ...               |
| ...           |       +-------------------+
+---------------+            ^
                             |
                        +----+
                        |
+---------------+       |
| _s45_events   |       |
+---------------+       |
| ...           |       |
| authorId      |-------+
| ...           |
+---------------+
```

## Проблемы и рекомендации

1. **Отсутствие индексов в read-модели**:
   - В таблице `_phuket_Property` отсутствуют индексы для полей, используемых для поиска и фильтрации
   - Рекомендуется добавить индексы для `dealType`, `propertyType`, `locality`, `subLocality`, `bedrooms`, `price_sale`, `price_rent`

2. **Слабая связь между таблицами**:
   - Отсутствуют внешние ключи между таблицами
   - Отсутствует индекс для `arId` в таблице `_s45_events`
   - Рекомендуется добавить составной индекс `(arName, arId, isLast)` для оптимизации запросов последних событий

3. **Сериализованные данные**:
   - Использование сериализованных данных в полях `payload`, `propertyDto`, `projectDto` затрудняет запросы и индексирование
   - Рекомендуется денормализовать часто используемые поля и вынести их в отдельные колонки
4. **Высокая кардинальность**:
   - Таблица `_s45_events` содержит 42,306 записей и постоянно растет
   - Рекомендуется внедрить стратегию архивации старых событий для оптимизации производительности

## SQL для оптимизации

```sql
-- Добавление индексов для _s45_events
ALTER TABLE _s45_events ADD INDEX idx_arid_islast (arId, isLast);
ALTER TABLE _s45_events ADD INDEX idx_arname_arid_islast (arName, arId, isLast);

-- Добавление индексов для _phuket_Property
ALTER TABLE _phuket_Property ADD INDEX idx_dealtype_published (dealType, published);
ALTER TABLE _phuket_Property ADD INDEX idx_locality_subloc (locality, subLocality);
ALTER TABLE _phuket_Property ADD INDEX idx_price_sale (price_sale);
ALTER TABLE _phuket_Property ADD INDEX idx_price_rent (price_rent);
ALTER TABLE _phuket_Property ADD INDEX idx_bedrooms (bedrooms);
ALTER TABLE _phuket_Property ADD INDEX idx_project (project);

-- Добавление индексов для _phuket_Reservation
ALTER TABLE _phuket_Reservation ADD INDEX idx_property_dates (propertyId, dateFrom, dateTo);
```

## Типы агрегатов в системе

В системе используются следующие типы агрегатов:
- `PhuketProperty` - Объекты недвижимости
- `PhuketProject` - Проекты недвижимости
- `PhuketOption` - Справочники/опции
- `PhuketReservation` - Бронирования
- `PhuketArticle` - Статьи
- `PhuketNews` - Новости
- `PhuketService` - Услуги
- `PhuketClub` - Клубные предложения
- `PhuketReview` - Отзывы
- `PhuketBoat` - Лодки/яхты
- `PhuketUser` - Пользователи системы
- `Compo` - Компоненты интерфейса

## Рекомендации для разработчиков

1. При работе с таблицей `_s45_events`:
   - Используйте индекс `arName` для фильтрации по типу агрегата
   - Добавьте условие `isLast = 1` для получения только последнего состояния
   - Избегайте операций `ORDER BY` без использования индексов

2. При работе с таблицей `_phuket_Property`:
   - Используйте прямые запросы к ключевым полям вместо поиска в сериализованных данных
   - Добавляйте условие `published = 1` для всех клиентских запросов
   - При создании поиска учитывайте отсутствие индексов

3. При создании новых таблиц:
   - Создавайте индексы для всех полей, используемых в WHERE, JOIN и ORDER BY
   - Избегайте хранения сериализованных данных в полях, по которым будет выполняться поиск
   - Используйте составные индексы для оптимизации частых запросов 
