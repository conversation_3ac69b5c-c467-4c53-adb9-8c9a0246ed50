---
description: 
globs: 
alwaysApply: true
---
# S45 Компоненты: Продвинутые концепции

<rule>
name: localization_system
description: Система локализации в компонентах s45
filters:
  - type: file_path
    pattern: ".*\\.php$"
  - type: file_content
    pattern: "s45_lang"
actions:
  - type: suggest
    message: |
      Локализация в S45 использует объекты LangVO и функцию s45_lang():
      
      - Единый шаблон (.tpl.php) для всех языков
      - Тексты хранятся в LangVO: `['en' => 'Sold', 'ru' => 'Продано']`
      - В шаблоне используется функция: `<?php print s45_lang($this->propertyDto->name); ?>`
      - Функция s45_lang() выбирает текст для текущего языка сайта
      - LangVO передаётся по ссылке: `s45_lang(&$langObject)`
      - Нельзя передавать литералы массивов напрямую в s45_lang()
</rule>

<rule>
name: property_search_query
description: Организация поиска в PhuketPropertyQuery
filters:
  - type: file_path
    pattern: "PhuketPropertyQuery\\.php$|PropertySearch"
actions:
  - type: suggest
    message: |
      Поиск по недвижимости в S45:
      
      - Использует денормализованные таблицы для быстрого чтения (_phuket_Property)
      - Параметры фильтрации из URL преобразуются в условия WHERE
      - Поддерживает фильтры по типу, локации, спальням, площади, цене
      - Результаты возвращаются в SearchResultVO с объектами DTO
      - PhuketRentQuery используется для проверки доступности по датам
      - Оптимизация через индексирование полей фильтрации и денормализацию
      - AJAX-эндпоинт `/s45PhuketSearchJson` для карт возвращает упрощённые данные
</rule>

<rule>
name: composite_components
description: Композитные и вложенные компоненты
filters:
  - type: file_path
    pattern: "\\.tpl\\.php$"
  - type: file_content
    pattern: "s45_render"
actions:
  - type: suggest
    message: |
      Композитные компоненты в S45:
      
      - Родительские компоненты включают дочерние через вызов s45_render() в шаблоне
      - Пример вызова: `<?php echo s45_render('PhuketPropertySlider', $sliderProps); ?>`
      - Компоненты могут передавать данные дочерним через параметры
      - beforeRender() родителя может подготовить данные для дочерних компонентов
      - Система рендеринга обеспечивает включение CSS/JS всех компонентов
      - Вложенность может быть произвольной глубины
</rule>

<rule>
name: conditional_rendering
description: Условный рендеринг в компонентах
filters:
  - type: file_path
    pattern: "\\.tpl\\.php$"
actions:
  - type: suggest
    message: |
      Условный рендеринг в шаблонах S45:
      
      - В методе beforeRender() устанавливаются флаги и данные ($this->isSold, $this->propertyDto)
      - В шаблоне .tpl.php используются стандартные условные конструкции PHP
      - Пример:
        ```php
        <?php if ($this->propertyDto->dealType == 'sale'): ?>
          <!-- HTML для объектов продажи -->
        <?php else: ?>
          <!-- HTML для объектов аренды -->
        <?php endif; ?>
        ```
      - Нет отдельных шаблонов для разных вариантов представления
      - Вся логика ветвления содержится в едином шаблоне
</rule>

<rule>
name: image_styles_system
description: Система стилей изображений
filters:
  - type: file_path
    pattern: "s45_img(Src|SrcR)\\.php$"
  - type: file_content
    pattern: "s45_imgSrc"
actions:
  - type: suggest
    message: |
      Система изображений в S45:
      
      - Использует Drupal Image Styles с кастомными дополнениями
      - Вспомогательные функции s45_imgSrc() и s45_imgSrcR() для получения URL
      - Модуль s45_imagestyles определяет стили изображений
      - Оптимизация через модули imageapi_optimize, resmushit, webp_converter
      - Кэширование обрабатывается стандартным механизмом Drupal
      - Генерированные стили хранятся в sites/default/files/styles/
      - Пример использования: `<img src="<?php echo s45_imgSrc($photo->uri, 'S45_IMST_1900X1000_SCALE'); ?>">`
</rule>

<rule>
name: multilevel_caching
description: Многоуровневое кэширование в s45
filters:
  - type: file_content
    pattern: "cache_get|cache_set|CompoRepo"
actions:
  - type: suggest
    message: |
      Кэширование в S45:
      
      - Стандартное кэширование Drupal (страниц, блоков)
      - Кастомное кэширование с cache_get/cache_set для компонентов
      - Кэш популярных локаций с TTL 24 часа (phuket_popdirs*)
      - Глобальный кэш $GLOBALS['AllCompoData'] для данных компонентов
      - CompoRepo кэширует данные в рамках одного запроса
      - Кэширование переводов через JsonRepo
      - Инвалидация по TTL или через хуки hook_s45_re_property_*
      - Отсутствие единой стратегии инвалидации кэша при изменении контента
</rule>
