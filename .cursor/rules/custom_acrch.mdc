---
description: 
globs: 
alwaysApply: true
---
# IndreamsPhuket Custom Code Cursor Rules

<rule>
name: event_sourcing_basics
description: Основные концепции Event Sourcing и агрегаты
filters:
  - type: file_content
    pattern: "namespace Site45\\\\Event"
actions:
  - type: suggest
    message: |
      Система использует Event Sourcing:
      - Таблица событий: `_s45_events`.
      - `EventStore::addEvent()` (modules/__s45/s45_base/classes/Site45/Event/EventStore.php) сохраняет события и использует `db_transaction()`.
      - `EventQuery::getLastEvent()` (EventQuery.php) возвращает последнее событие.
      - Аггрегаты (`<AR>`) наследуются от `Site45\Event\AR`, методы `load()`, `save()`, `delete()` генерируют события.
      - Read-модели обновляются в `QueryFromEvents` и кастомных обработчиках `handle<ARName><EventName>`.
</rule>

<rule>
name: compo_structure
description: Структура и жизненный цикл компонентов Compo
filters:
  - type: file_path
    pattern: "Site45/Compo/Compo.php"
actions:
  - type: suggest
    message: |
      Компоненты Compo:
      - Наследуются от `Site45\Compo\Compo`.
      - Определяют публичные свойства, доступные в шаблоне `*.tpl.php`.
      - Реализуют `beforeRender($props)` для подготовки данных, используют `s45_toObject()`.
      - `apiGetRendered()` выполняет `beforeRender()`, рендерит HTML, возвращает `RenderedCompoDto`.
      - HTML собирается через `getCompoHtmlFull()`, `getCompoHtml()`, обрабатывается `postProcessor()`, оборачивается `addCompoHtmlWrapper()`.
      - Для вставки в Drupal используйте `s45_render('CompoName', $props)`.
</rule>

<rule>
name: drupal_integration
description: Интеграция Compo и API модулей в Drupal
filters:
  - type: file_path
    pattern: "s45_base\\.module$"
actions:
  - type: suggest
    message: |
      Модуль `s45_base`:
      - `s45_base_menu()` регистрирует маршруты `compo45`, `compomake`, `s45store`, `s45_robots_settings`, `robots.txt`.
      - Доступы: `s45guest`, `s45admin` (функция `s45_base_permission()`).
      - API вызовы обрабатываются `s45_compo_api()`, используется `CompoApi2::exec()` в `Site45/Base/CompoApi2`.
</rule>

<rule>
name: caching_and_performance
description: Кэширование и оптимизация производительности
filters:
  - type: file_extension
    pattern: "\\.php$"
actions:
  - type: suggest
    message: |
      Рекомендации по производительности:
      - Используйте Drupal Cache API (`cache_get`, `cache_set`) для дорогих запросов, например `EventQuery::exec()`.
      - Рассмотрите Queue API для асинхронного обновления read-моделей вместо синхронной обработки.
      - Для frontend: агрегация CSS/JS, lazy loading изображений, image styles (AdvAgg).
</rule>

<rule>
name: tests_and_migrations
description: Тесты и миграции для кастомных модулей
filters:
  - type: file_extension
    pattern: "\\.(install|module)$"
actions:
  - type: suggest
    message: |
      Тестирование и миграции:
      - Добавьте `hook_update_N` для создания таблиц `_s45_events`, `_phuket_Property`, `_phuket_Reservation`.
      - Проверьте наличие тестов в папках `tests` около AR, Query и Compo.
      - Используйте SimpleTest/DrupalTestCase для функционального тестирования API компонентов.
</rule>

<rule>
name: documentation_standards
description: Документация и кодовые стандарты
filters:
  - type: file_extension
    pattern: "\\.(php|inc|module)$"
actions:
  - type: suggest
    message: |
      Документация и стандарты:
      - Документируйте классы и методы PHPDoc (`@param`, `@return`).
      - Следуйте Drupal Coding Standards (2 spaces, camelCase для переменных, underscores для функций).
      - Обеспечьте безопасность: используйте `t()` для переводов, `check_plain()`/`filter_xss()` для вывода, placeholders в `db_query()`.
      - Обновляйте `CHANGELOG.md` и `NewKnowledgeBase.md` после важных изменений без дублирования.
</rule>

<rule>
name: multisite_system
description: Система мультисайтовости
filters:
  - type: file_path
    pattern: "SiteConf\\.php$|SiteConfLoadQuery\\.php$"
actions:
  - type: suggest
    message: |
      ## Система мультисайтовости
      
      - **Ключевой класс:** `SiteConf` предоставляет статические методы `getId()` и `getSets()` для получения информации о текущем сайте
      - **Определение сайта:** Класс `SiteConfLoadQuery` определяет сайт по домену через `$_SERVER['HTTP_HOST']`
      - **Конфигурация:** Настройки хранятся в файле `S45_SITES` (`_repo/Sites.s45.json`)
      - **Структура директорий:** Каждый сайт имеет свой каталог в `S45_SITES_DIR` (`public://FileStore4`):
        ```
        /FileStore4
          /phuket
            /_repo/      - Конфигурации компонентов и объектов
            /files/      - Загруженные файлы
            /pdf/        - Генерируемые PDF
            /tmp/        - Временные файлы
        ```
      - **Кэширование:** Конфигурация кэшируется в глобальной переменной `$GLOBALS['s45']['siteConf']`
      - **Использование:** Большинство компонентов обращаются к ресурсам через `SiteConf::getId()`
      - **Мобильная версия:** Система добавляет поддомен `m.` для мобильных версий сайтов, если в конфигурации установлен флаг `mobile: true`
</rule>

<rule>
name: localization_system
description: Система локализации через LangVO
filters:
  - type: file_path
    pattern: "LangVO\\.php$|s45_lang"
actions:
  - type: suggest
    message: |
      ## Система локализации (LangVO)
      
      - **Класс `LangVO`:** Хранит тексты на разных языках как свойства объекта:
        ```php
        LangVO::create(array(
          'ru' => 'Привет',
          'en' => 'Hello',
          'zh-hans' => '你好',
          'th' => 'สวัสดี'
        ))
        ```
      - **Функция `s45_lang()`:** Извлекает текст на текущем языке с механизмом fallback:
        1. Проверяет текст на запрошенном языке (`$langObject->{$langCode}`)
        2. Проверяет языконезависимый текст (`$langObject->und`)
        3. Для китайского и тайского, если нет перевода, возвращает английский
        4. Возвращает альтернативный текст (`$alter`) или заполнитель `????`
      - **Использование в шаблонах:**
        ```php
        <?php print s45_lang($this->content_title); ?>
        ```
      - **Поддерживаемые языки:** ru, en, zh-hans (китайский), th (тайский)
      - **Глобальный язык:** Определяется через `$GLOBALS['language']->language`
      - **Единый шаблон:** Система использует один шаблон `.tpl.php` для всех языковых версий
</rule>

<rule>
name: url_alias_system
description: Система URL-алиасов
filters:
  - type: file_path
    pattern: "Path\\.php$|url_inbound_alter"
actions:
  - type: suggest
    message: |
      ## Система URL-алиасов и маршрутизации
      
      - **Класс `Path`:** Управляет преобразованием системных путей в дружественные URL
      - **Таблица `_s45_aliases`:** Хранит соответствия между системными путями и URL:
        - `siteId` - идентификатор сайта
        - `langCode` - код языка
        - `sysPath` - системный путь (например, `'property/123'`)
        - `alias` - дружественный URL (например, `'luxury-villa-rawai'`)
      - **Основные методы:**
        - `Path::create()->getSysPath($alias, $langCode)` - получает системный путь по алиасу
        - `Path::create()->getAlias($sysPath, $langCode)` - получает алиас по системному пути
        - `Path::create()->addAlias($sysPath, $alias, $langCode)` - добавляет новый алиас
      - **Интеграция с Drupal:**
        - Хук `s45_path_url_inbound_alter()` преобразует входящие URL
        - Функция `s45_path_url()` заменяет стандартную `url()`
      - **Многоязычные URL:**
        ```php
        Path::create()->addAlias($sysPath, $alias_ru, 'ru');
        Path::create()->addAlias($sysPath, $alias_en, 'en');
        Path::create()->addAlias($sysPath, $alias_en, 'zh-hans');
        Path::create()->addAlias($sysPath, $alias_en, 'th');
        ```
      - **Редиректы:** Классы `Redirect` и `RedirectFromOldSite` обрабатывают перенаправления
      - **Автосоздание алиасов:** При сохранении объектов (недвижимость, новости и т.д.) автоматически создаются или обновляются алиасы
</rule>