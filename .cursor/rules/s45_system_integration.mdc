---
description: 
globs: 
alwaysApply: false
---
# S45 Системная интеграция

<rule>
name: path_management_deep
description: Подробная информация о системе управления URL
filters:
  - type: file_path
    pattern: "s45_path|Path\\.php|Redirect\\.php"
actions:
  - type: suggest
    message: |
      URL-менеджмент в S45:
      
      - Кастомная таблица `_s45_aliases` хранит соответствия системных путей и алиасов
      - Каждый алиас привязан к siteId и langCode
      - Классы Path и Redirect управляют URL-алиасами и перенаправлениями
      - Интеграция с Drupal через hook_url_inbound_alter
      - Path::getAlias() - преобразует системный путь в алиас
      - Path::getSysPath() - преобразует алиас в системный путь
      - Redirect - выполняет 301 редирект с системного пути на алиас
      - RedirectFromOldSite - обрабатывает редиректы со старого сайта
      - Нет автоматического обновления алиасов при изменении контента
</rule>

<rule>
name: multisite_system
description: Система мультисайтовости через SiteConf
filters:
  - type: file_path
    pattern: "SiteConf\\.php|SiteConfLoadQuery\\.php"
actions:
  - type: suggest
    message: |
      Мультисайтовость в S45:
      
      - Единая кодовая база для нескольких логических сайтов
      - Конфигурация хранится в `_sites.s45.json`
      - Каждый сайт имеет:
        - Список доменов (domains)
        - Основной компонент (siteCompo)
        - Наборы функциональности (sets)
      - SiteConf определяет текущий сайт по $_SERVER['HTTP_HOST']
      - SiteConf::getId() - возвращает ID текущего сайта
      - SiteConf::getSets() - возвращает активные наборы функциональности
      - Наборы sets определяют, какие модули и компоненты доступны для сайта
      - CompoScanner использует sets для поиска компонентов в указанных директориях
</rule>

<rule>
name: compo_scanner_loading
description: Регистрация и загрузка компонентов
filters:
  - type: file_path
    pattern: "CompoScanner\\.php|CompoInfo|CompoRepo\\.php"
actions:
  - type: suggest
    message: |
      Регистрация компонентов в S45:
      
      - CompoScanner сканирует директории для поиска компонентов
      - Результат сканирования сохраняется в `_repo/CompoInfo.s45.json`
      - URL `/compomake/scan` запускает сканирование компонентов
      - CompoRepo использует CompoInfo.s45.json для загрузки компонентов
      - При загрузке компонента:
        1. Определяется путь к файлу класса по имени компонента
        2. Файл класса подключается через require_once
        3. Создаётся экземпляр класса через new
        4. Компонент наполняется данными из EventStore или JSON
      - Компонент становится доступен только после регистрации в CompoInfo
</rule>

<rule>
name: seo_implementation
description: Реализация SEO в модулях s45
filters:
  - type: file_path
    pattern: "s45_phuket_seo\\.inc|PhuketSchemaGenerator\\.php|PhuketSearchSeo2\\.php"
actions:
  - type: suggest
    message: |
      SEO в модулях S45:
      
      - Использует стандартный модуль metatag для мета-тегов
      - s45_phuket_seo.inc содержит процедурную логику для SEO
      - PhuketSchemaGenerator генерирует Schema.org JSON-LD разметку
      - PhuketSearchSeo2 создаёт мета-теги для страниц поиска
      - Интеграция через хуки hook_preprocess_html и hook_entity_view_alter
      - Карты сайта (sitemaps) управляются через:
        - Скрипты sitemap_*.php, update_*.php
        - Логику в s45_phuket_sitemap.inc, s45_phuket_sitemapgen
        - Запуск по крону
      - Модуль поддерживает разные типы карт сайта (основной, изображений, языковые)
</rule>

<rule>
name: external_api_integration
description: Взаимодействие с внешними API
filters:
  - type: file_path
    pattern: "s45_phuket_export\\.inc|FazwazConverter\\.php|HipflatConverter\\.php"
actions:
  - type: suggest
    message: |
      Интеграция с внешними API в S45:
      
      - Экспорт данных о недвижимости через фиды:
        - Корневые PHP-скрипты для Facebook, Green-Acres, Nestoria, WorldVillas
        - s45_phuket_export.inc для Hipflat, FazWaz
      - Используются классы-конвертеры (FazwazConverter.php, HipflatConverter.php)
      - PhuketExportQuery управляет процессом экспорта
      - Результаты кэшируются в таблице _phuket_Export
      - Импорт данных из старой системы через:
        - IndreamsParser - подключение к старой БД
        - IndreamsRepo - парсинг HTML через phpQuery
      - Другие интеграции:
        - Cloudflare API
        - Facebook Pixel
        - Telegram Bot
        - OptiPic.io (оптимизация изображений)
</rule>
