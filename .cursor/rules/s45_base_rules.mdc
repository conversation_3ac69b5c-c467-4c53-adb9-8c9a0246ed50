---
description: 
globs: 
alwaysApply: true
---
# s45_base Module and Utilities Cursor Rules

<rule>
name: s45_base_access
description: Access control helper functions
filters:
  - type: file_path
    pattern: "s45_base.access.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_check_access()`, `s45_tmp_accessRules()`, `s45_check_roles()` в `s45_base.access.inc`.
      - Убедитесь, что роли и права реализованы корректно через константы `S45_ACCESS_*`.
      - Проверьте, что пользователи без права не получают доступ.
</rule>

<rule>
name: s45_base_robots
description: Robots.txt settings in s45_base
description: hook implementation for robots.txt
a filters:
  - type: file_path
    pattern: "s45_base.robots.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_base.robots.inc`: функция `s45_robots()` генерирует содержимое robots.txt.
      - Проверьте, что маршрут `robots.txt` зарегистрирован в `s45_base_menu()`.
      - Убедитесь, что `Content-Type` правильно установлен.
</rule>

<rule>
name: s45_api2
description: Alternative Compo API v2 entry in s45_base.2.inc
filters:
  - type: file_path
    pattern: "s45_base.2.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_api2()`: установка `PageId`, вызов `CompoApi452::exec()`.
      - Проверьте `s45_add_phpLib2()` и `s45_add_jsLib2()` для подключений библиотеки 452.
      - Убедитесь, что входные данные конвертируются через `s45_toObject()` и выводятся через `drupal_json_output()`.
</rule>

<rule>
name: s45_base_menu
description: Registration of API and menu items in s45_base.module
filters:
  - type: file_path
    pattern: "s45_base.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_base_menu()`: маршруты `compo45`, `compomake`, `s45store`, `s45_robots_settings`, `robots.txt`.
      - Проверьте `page callback`, `access arguments`, `type` и `menu_name`.
      - Убедитесь, что `MENU_CALLBACK` и `MENU_NORMAL_ITEM` используются корректно.
</rule>

<rule>
name: s45_base_permission
description: Definition of custom permissions in s45_base.module
filters:
  - type: file_path
    pattern: "s45_base.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_base_permission()`: права `s45guest` и `s45admin`.
      - Убедитесь, что роли правильно зарегистрированы и используются в `access arguments`.
</rule>

<rule>
name: s45_compo_api
description: Entry point for Compo API (s45_compo_api)
filters:
  - type: file_path
    pattern: "s45_base.module$"
actions:
  - type: suggest
    message: |
      - Откройте `s45_compo_api()`: обработка аргументов `POST`, `GET`, `ARGS`, `FILES`.
      - Проверьте использование `CompoApi2::exec()`, `PageId::set()`, логированиe через `Logger`.
      - Убедитесь, что `drupal_json_output()` вызывается корректно.
</rule>

<rule>
name: s45_compo_make
description: Entry point for creating new Compo components
filters:
  - type: file_path
    pattern: "s45_base.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_compo_make()`: запуск `CompoScanner` и `CompoMaker`.
      - Проверьте вывод списка компонентов и логику создания новых.
      - Убедитесь, что при повторном запуске не затираются существующие файлы без предупреждения.
</rule>
