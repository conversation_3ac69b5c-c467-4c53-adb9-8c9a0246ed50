---
description: 
globs: 
alwaysApply: true
---
# Compo452 Library Cursor Rules

<rule>
name: compo452_render_class
description: PHP rendering class for Compo452
filters:
  - type: file_path
    pattern: "Render452\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `Render452.php`: класс отвечает за серверный рендеринг компонентов Compo v452.
      - Изучите метод `getRendered()`: проверьте создание DTO и выполнение `beforeRender()` / `afterRender()`.
      - Убедитесь, что метод безопасно обрабатывает входные данные и корректно возвращает HTML.
</rule>

<rule>
name: compo452_api_js
description: JavaScript API for Compo452
a filters:
  - type: file_path
    pattern: "Api\.js$|Api452\.js$"
actions:
  - type: suggest
    message: |
      - Изучите `Api.js` и `Api452.js`: клиентские библиотеки для взаимодействия с Compo v452 API.
      - Проверьте экспорт функций и использование промисов / callback API.
      - Убедитесь, что библиотека вызывает правильный endpoint (s45_api2) и обрабатывает JSON-ответ.
      - Документируйте публичный интерфейс через JSDoc.
</rule>
