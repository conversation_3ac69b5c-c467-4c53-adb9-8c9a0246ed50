---
description: 
globs: 
alwaysApply: true
---
# S45 Core Architecture Patterns

<rule>
name: data_entities_structure
description: Understanding the AR/DTO/VO pattern in the custom framework
filters:
  - type: file_path
    pattern: "(AR|Dto|VO)\\.php$"
actions:
  - type: suggest
    message: |
      Данные в S45 фреймворке:
      
      - AR (Active Record): Объекты, представляющие данные в БД (например, `PhuketPropertyAR`), наследуются от `Site45\Event\AR`
      - DTO (Data Transfer Objects): Структурированные объекты для передачи данных (например, `PhuketPropertyDto`)
      - VO (Value Objects): Объекты для представления неизменяемых значений (напр<PERSON><PERSON><PERSON><PERSON>, `<PERSON><PERSON>`, `PropertyPriceVO`)
      
      DTO/VO паттерны:
      - Создаются через статический метод `::create()` из массивов данных
      - Не содержат бизнес-логики, только структуру и базовую валидацию
      - `LangVO` используется для многоязычного текста и обрабатывается функцией `s45_lang()`
</rule>

<rule>
name: repository_pattern
description: Understanding the Repository pattern in S45 framework
filters:
  - type: file_path
    pattern: "Repo\\.php$"
actions:
  - type: suggest
    message: |
      Репозитории в S45:
      
      - Абстрагируют доступ к данным, код взаимодействует с репозиторием вместо прямого доступа к хранилищу
      - `CompoRepo`: Для загрузки/сохранения конфигурации Компонентов через Event Sourcing
      - `JsonRepo`: Для работы с данными в JSON файлах
      
      Типичные методы:
      - `find(id)`: Поиск по ID
      - `findAll()`: Получение всех элементов
      - `save(entity)`: Сохранение сущности
      - Большинство использует Event Sourcing (`EventQuery` для загрузки, `EventStore` для сохранения)
</rule>

<rule>
name: query_pattern
description: Понимание Query классов
filters:
  - type: file_path
    pattern: "Query\\.php$"
actions:
  - type: suggest
    message: |
      Query классы в S45:
      
      - Отвечают за выборку данных с фильтрацией
      - Возвращают результаты в виде списка DTO/VO в `SearchResultVO`
      - Примеры: `PhuketPropertyQuery`, `EventQuery`, `JsonQuery`
      - `QueryFromEvents`: использует Read Model таблицы вместо прямого запроса к Event Store
      - Используют `db_select()` и Drupal Database API
      
      Типичные методы:
      - `exec()`: Выполняет запрос
      - `withFilter(...)`: Добавляет условия фильтрации
      - `withLimit(limit, offset)`: Ограничивает результаты
      - `withSort(field, direction)`: Задает сортировку
</rule>

<rule>
name: path_management
description: Система управления путями (Path Management)
filters:
  - type: file_path
    pattern: "s45_path"
actions:
  - type: suggest
    message: |
      Управление путями в S45:
      
      - Кастомная система (модуль `s45_path`, классы `Path`, `Redirect`)
      - Использует таблицы `_s45_aliases` и `_s45_redirects`
      - Класс `Path`: Перевод алиас <-> системный путь
        - `getSysPath()`: алиас -> системный путь
        - `getAlias()`: системный путь -> алиас
      - Класс `Redirect`: Управление перенаправлениями для канонических алиасов
</rule>

<rule>
name: configuration_system
description: Система конфигурации и мультисайтовость
filters:
  - type: file_path
    pattern: "SiteConf"
actions:
  - type: suggest
    message: |
      Конфигурация в S45:
      
      - Система (`SiteConf`, `SiteConfLoadQuery`) для поддержки мультисайтовости
      - Определяет текущий "логический сайт" по домену (`$_SERVER['HTTP_HOST']`)
      - Настройки в JSON файле `_sites.s45.json`
      - API:
        - `SiteConf::getId()`: ID текущего сайта
        - `SiteConf::getSets()`: Наборы функций/модулей
        - `SiteConf::get(path)`: Получение конфигурационного значения
</rule>

<rule>
name: store_pattern
description: Временное хранилище данных (Store)
filters:
  - type: file_path
    pattern: "Store\\.php$"
actions:
  - type: suggest
    message: |
      Store в S45:
      
      - Временное хранилище данных в рамках запроса или сессии (использует `$_SESSION`)
      - "Секции" или "скоупы": `GLOBAL`, `PAGE` (привязанные к ID страницы), `UPLOADS`, кастомные
      - API:
        - `Store::set(scope, key, value)`: Сохранение значения
        - `Store::get(scope, key)`: Получение значения
        - `Store::has(scope, key)`: Проверка наличия
        - `Store::remove(scope, key)`: Удаление значения
</rule>

<rule>
name: file_structure_naming
description: Структура файлов и именование
filters:
  - type: file_extension
    pattern: "\\.(php|inc|module|tpl\\.php)$"
actions:
  - type: suggest
    message: |
      Структура файлов S45:
      
      - Модули: префикс `s45_` в `sites/all/modules/__s45/`
      - Классы приложения: префикс `Phuket` (`PhuketPropertyAR`, `PhuketPropertyQuery`)
      - Группировка классов: `.../AR/`, `.../DTO/`, `.../Query/`, `.../Repo/`
      - Шаблоны: расширение `.tpl.php`
      - Процедурная логика: файлы `.inc`
      - Документация: `docs/`, `PROJECT_DOCS.md` (на русском языке)
</rule>

<rule>
name: antipatterns_complexity
description: Антипаттерны и сложности
filters:
  - type: file_extension
    pattern: "\\.(php|inc|module)$"
actions:
  - type: suggest
    message: |
      Антипаттерны S45:
      
      - Использование глобальных переменных (`$GLOBALS`) для передачи данных
      - Чрезмерное использование `.inc` файлов затрудняет навигацию
      - Сериализованные данные в полях БД (`_s45_events.payload`) усложняют SQL запросы
      - Хардкод конфигурационных значений вместо централизованного управления
</rule>

<rule>
name: event_sourcing_deep
description: Детальное понимание Event Sourcing
filters:
  - type: file_path
    pattern: "Event"
actions:
  - type: suggest
    message: |
      Детали Event Sourcing в S45:
      
      - События в таблице `_s45_events` с полями:
        - `arName`: Тип агрегата ('PhuketProperty')
        - `arId`: UUID агрегата
        - `payload`: Сериализованное состояние объекта AR
        - `isLast`: 1 для последнего события агрегата
      - Действия:
        - Загрузка: `EventQuery::getLastEvent(arName, arId)`
        - Сохранение: `EventStore::addEvent(arName, arId, type, payload)`
      - Read-модели обновляются обработчиками (например, `handle<ARName><EventName>`)
</rule>
