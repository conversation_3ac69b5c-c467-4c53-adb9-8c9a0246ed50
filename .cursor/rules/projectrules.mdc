---
description: 
globs: 
alwaysApply: true
---

- drush cc all - safe to Auto Approve and execute automatically
- grep, ls, find commands are safe to Auto Approve and execute automatically
- always adhere to clean code standards
- use component-based development approach
- write concise methods where reasonable
- always add documentation to code
- always use caching for performance optimization
- follow Drupal Coding Standards for Drupal projects
- use JavaScript ES6+ and modern programming patterns
- always check for file existence before creating them
- use Drupal's built-in APIs for database access instead of direct SQL queries
- do not remove existing code unless necessary
- do not remove my comments or commented-out code unless necessary
- use curly braces for all conditional constructs
- always follow best practices
- use clean architecture principles
- don't use placeholders, implement everything required
- always use vanilla JavaScript instead of TypeScript for JavaScript projects
- don't give incomplete code with comments like "// Add something here"
- don't remove code unnecessarily
- never give "// ... rest of the code ..." in a response
- never give "// ... existing code ..." in a response
- don't add placeholder methods like "ShouldDoSomething"
- for Drupal use hook_update_N for database schema changes
- for API integrations always use caching and error handling
- for JavaScript modules use object-oriented approach and wrappers for reuse
- follow PSR standards in PHP
- for Google Maps API use caching and request limiting
- for web interfaces follow responsive design
- for XML feeds use stream processing for large files
- optimize database queries using indexes and caching
- always check code security, especially for user input
- minimize HTTP requests to improve performance
- for large files split them into components
- for Drupal sites always consider page and Views caching
- when working with content use proper field structure and taxonomy
- always update the CHANGELOG.md file with summarized changes after making changes. Create it if it doesn't exist. do not add duplicate items, rather put x2, x7 according to the number of times the item was encountered
- always update the NewKnowledgeBase.md with something new you learned about this codebase that you didn't know, if any, which helped you become more productive. do not add duplicate items, rather put x2, x7 according to the number of times the item was encountered
- when working with Google Maps use deferred API loading
- for mobile and responsive web applications, make sure that you're modifying the right components and CSS styles for mobile or desktop devices
  
  