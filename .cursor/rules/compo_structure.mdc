---
description: 
globs: 
alwaysApply: true
---
# Структура компонентов Compo

- Класс наследуется от `Site45\Compo\Compo`
- Все публичные свойства, доступные в шаблоне, объявлены явно
- Метод `beforeRender()` для подготовки данных
- Корректное использование `s45_toObject()` для входных параметров
- Обязательную документацию для класса и методов

Полный цикл данных:
Данные о недвижимости хранятся как последовательность событий в _s45_events
События содержат поля arName='PhuketProperty', arId=<UUID>, payload=<данные>
Класс PhuketPropertyAR отвечает за генерацию событий
Денормализованная таблица _phuket_Property обновляется на основе событий
PhuketPropertyQuery извлекает данные из read-модели
PhuketPropertyDto передает данные компонентам UI
Особенности реализации Event Sourcing:
Каждое изменение недвижимости сохраняется как новое событие, а не обновление
Поле isLast=1 маркирует последнее событие для каждого агрегата
Полный снимок объекта сериализуется в поле payload каждого события
Восстановление состояния происходит путем десериализации последнего события
Механизм запросов:
Запросы к данным оптимизированы через денормализованную таблицу _phuket_Property
Дополнительная оптимизация через сериализованное поле propertyTeaserDto
Сортировка реализована непосредственно в SQL через sortBy и sortOrder
Фильтры применяются через сложные SQL-запросы с множеством условий
Многоязычность данных:
Тексты хранятся как объекты LangVO с переводами для каждого языка
Объекты LangVO сериализуются в JSON перед сохранением в БД
Функция s45_lang() извлекает текст на текущем языке пользователя
Взаимодействие с DTO:
DTO автоматически генерируется из данных события
DTO включает вложенные структуры (фото, опции, локации)
DTO подготавливается в разных вариантах: полный, для тизера, для карты