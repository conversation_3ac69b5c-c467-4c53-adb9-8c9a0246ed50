---
description: 
globs: 
alwaysApply: true
---
# Form System Rules

<rule>
name: phuket_request_form_component
filters:
  - type: file_path
    pattern: "PhuketRequestForm\\.php$"
actions:
  - type: suggest
    message: |
      ## PhuketRequestForm Component Structure
      
      - Extend `Site45\Compo\Compo`
      - Include methods:
        - `apiSaveForm`: For general request forms
        - `apiSaveBronForm`: For booking/viewing requests
        - `sendEmailNotification`: For email alerts
      - Use `PhuketFormAR::create()` for data storage
      - Always capture request IP via `$_SERVER['REMOTE_ADDR']`
      - Return JSON response with `drupal_json_output` and `drupal_exit()`
</rule>

<rule>
name: form_data_processing
filters:
  - type: file_content
    pattern: "\\$formData\\s*=\\s*\\$_POST"
actions:
  - type: suggest
    message: |
      ## Form Data Processing
      
      - Capture form data directly from `$_POST`
      - Create a new `PhuketFormAR` instance:
        ```php
        $formAR = PhuketFormAR::create();
        $formAR->name = isset($formData['formId']) ? $formData['formId'] : 'DefaultFormName';
        $formAR->data = $formData;
        $formAR->ip = $_SERVER['REMOTE_ADDR'];
        $formAR->save();
        ```
      - Process any special fields if needed
      - Always validate required fields server-side
      - Don't trust client-side validation alone
</rule>

<rule>
name: email_notification_system
filters:
  - type: file_content
    pattern: "sendEmailNotification|prSiteMailing"
actions:
  - type: suggest
    message: |
      ## Email Notification System
      
      - Use `s45_add_mail()` to ensure mail libraries are loaded
      - Configure `prSiteMailing` with standard settings:
        - CharSet: 'UTF-8'
        - SMTPSecure: 'ssl'
        - Host: 'smtp.mail.ru'
        - Port: 465
      - Set sender: '<EMAIL>'
      - Include all form fields in the email body
      - Format HTML emails as tables for readability
      - Add timestamp and IP information automatically
      - Add proper error handling with try/catch and watchdog logging
</rule>

<rule>
name: mobile_form_modals
filters:
  - type: file_content
    pattern: "modal fade.*simpleMobile(Request|Booking)Modal"
actions:
  - type: suggest
    message: |
      ## Mobile Form Modals
      
      - Use Bootstrap modals with dark background (#2B3844)
      - Include data attributes on form:
        - `data-form-id`: Form type identifier
        - `data-form-message`: Success message
      - Structure:
        ```html
        <div class="modal fade" id="simpleMobileFormModal">
          <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">Form Title</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
              </div>
              <div class="modal-body">
                <form id="formId" data-form-id="FormType" data-form-message="Success message">
                  <!-- Form fields -->
                  <button type="submit" class="project-form__btn">Submit</button>
                </form>
              </div>
            </div>
          </div>
        </div>
        ```
      - Always include property context fields (id, number, name)
</rule>

<rule>
name: form_styling_guidelines
filters:
  - type: file_content
    pattern: "project-form__field|project-form__btn"
actions:
  - type: suggest
    message: |
      ## Form Styling Guidelines
      
      - Input fields:
        - Background: rgba(255, 255, 255, 0.1)
        - Border: 1px solid rgba(255, 255, 255, 0.2)
        - Color: white
        - Padding: 12px 15px 12px 45px (for icon)
        - Border-radius: 6px
        - Font-size: 15px
      
      - Submit buttons:
        - Background: #E9B873
        - Color: #2B3844
        - Font-weight: 500
        - Padding: 14px 20px
        - Border-radius: 6px
        - Width: 100%
        - Font-size: 16px
      
      - Icons:
        - Position: absolute, left: 15px
        - Color: rgba(255, 255, 255, 0.6)
        - Font-size: 16px
        - Use Font Awesome 5 standard icons
</rule>

<rule>
name: form_javascript_handlers
filters:
  - type: file_content
    pattern: "function\\s+handleFormSubmit|JQ\\(\\'form"
actions:
  - type: suggest
    message: |
      ## Form JavaScript Handlers
      
      - Initialize with delayed retry pattern to ensure jQuery is loaded:
        ```javascript
        function initializeFormScripts() {
            var JQ = (typeof $ !== 'undefined' && $.fn) ? $ : 
                ((typeof window.jQuery !== 'undefined' && window.jQuery.fn) ? window.jQuery : null);
                
            if (!JQ) {
                setTimeout(initializeFormScripts, 100);
                return;
            }
            // Continue initialization...
        }
        ```
      
      - Form submit handler pattern:
        ```javascript
        JQ(formSelector).off('submit').on('submit', function(e) {
            e.preventDefault();
            var $form = JQ(this);
            
            // Disable button
            $form.find('button[type="submit"]').prop('disabled', true).text('Отправка...');
            
            // Send AJAX request
            JQ.ajax({
                url: apiUrl,
                type: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    // Handle success: show message, reset form
                },
                error: function(xhr, status, error) {
                    // Handle error: show error message
                }
            });
        });
        ```
      
      - Always implement phone input masks using inputmask:
        ```javascript
        JQ('input[type="tel"]').inputmask("+9 (*************");
        ```
</rule>

<rule>
name: property_context_in_forms
filters:
  - type: file_content
    pattern: "propertyId|propertyNumber|propertyName"
actions:
  - type: suggest
    message: |
      ## Property Context in Forms
      
      - Always include these hidden fields in property-related forms:
        ```html
        <input type="hidden" name="propertyId" value="<?php echo $propertyDto->id; ?>">
        <input type="hidden" name="propertyNumber" value="<?php echo $propertyDto->number; ?>">
        <input type="hidden" name="propertyName" value="<?php echo s45_lang($propertyDto->name); ?>">
        ```
      
      - These fields are essential for:
        - Tracking which property the form relates to
        - Including property reference in email notifications
        - Associating form submissions with properties in reports
</rule>

<rule>
name: form_success_notifications
filters:
  - type: file_content
    pattern: "jBox|Notice|alert"
actions:
  - type: suggest
    message: |
      ## Form Success Notifications
      
      - Preferred pattern with jBox fallback to alert:
        ```javascript
        if (typeof jBox !== 'undefined') {
            new jBox('Notice', {
                content: formMessage,
                color: 'green',
                autoClose: 3000
            });
        } else {
            alert(formMessage);
        }
        ```
      
      - Success messages should be:
        - Clear and concise
        - Indicate what happens next
        - Auto-close after 3 seconds
        - Use green color for success state
        
      - Forms should be reset after successful submission:
        ```javascript
        $form[0].reset();
        $form.find('button[type="submit"]').prop('disabled', false).text('Отправить');
        ```
</rule>

<rule>
name: responsive_form_design
filters:
  - type: file_content
    pattern: "media.*max-width"
actions:
  - type: suggest
    message: |
      ## Responsive Form Design
      
      - Mobile viewport (max-width: 767px):
        - Use full-width inputs and buttons
        - Increase touch targets (min-height: 45px)
        - Simplify forms to essential fields only
        - Add custom styles for date inputs
      
      - Form integration:
        - On mobile: Use fixed buttons at top of property detail page
        - On desktop: Place forms in sidebar
      
      - Implement with media queries:
        ```css
        @media (max-width: 767px) {
            .property-action-buttons {
                display: flex;
                justify-content: space-between;
                width: 100%;
            }
            
            .project-form__field {
                min-height: 45px;
            }
        }
        
        @media (min-width: 768px) {
            .property-action-buttons {
                display: none !important;
            }
        }
        ```
</rule>

<rule>
name: email_notification_implementation
filters:
  - type: file_content
    pattern: "s45_add_mail|prSiteMailing"
actions:
  - type: suggest
    message: |
      ## Реализация Email-уведомлений в формах
      
      В системе используется PHPMailer для отправки уведомлений через SMTP:
      
      ```php
      // Подключение библиотек для работы с почтой
      s45_add_mail();
      
      // Создание экземпляра класса для отправки
      $mail = new \prSiteMailing();
      $mail->CharSet = 'UTF-8';
      $mail->isSMTP();
      $mail->SMTPDebug = 0;
      $mail->Host = 'smtp.mail.ru';
      $mail->Port = 465;
      $mail->SMTPSecure = 'ssl';
      $mail->SMTPAuth = true;
      $mail->Username = '<EMAIL>';
      $mail->Password = 'мой_пароль'; // Используйте безопасное хранение
      $mail->setFrom('<EMAIL>', 'Сайт IndreamsPhuket');
      
      // Настройка письма
      $mail->IsHTML(true);
      $mail->Subject = strip_tags($title);
      $mail->Body = $message;
      $mail->AltBody = 'Текстовая версия'; 
      
      // Добавление получателей
      $mail->addAddress('<EMAIL>', 'Получатель');
      
      // Отправка
      $mail->send();
      ```
      
      Зависимости:
      1. Модуль `s45_vendor` должен быть включен
      2. Функция `s45_add_mail()` загружает библиотеки из `sites/all/modules/__s45/s45_vendor/vendor/mail/`
      3. Класс `prSiteMailing` расширяет стандартный `PHPMailer`
      
      Рекомендации:
      - Всегда обрабатывайте исключения при отправке почты
      - Не храните пароли в исходном коде - используйте переменные окружения или конфигурацию
      - Проверяйте результат отправки через `$mail->send()`
</rule>

<rule>
name: compo_api_method_conventions
filters:
  - type: file_content
    pattern: "apiSave|save[A-Z]|handle.*Form"
actions:
  - type: suggest
    message: |
      ## Соответствие методов API компонентов frontend-backend
      
      - **Фронтенд вызывает методы без префикса** `api`, например, `saveForm` или `saveBronForm`.
      - **Бэкенд должен иметь соответствующие публичные методы с точно таким же именем**.
      - Добавьте методы-обертки, если нужны разные имена в интерфейсе:
        ```php
        // Публичный метод, вызываемый с фронтенда
        public function saveForm($params = NULL) {
            return $this->apiSaveForm($params);
        }
        
        // Внутренний метод для реализации логики
        public function apiSaveForm($params = NULL) {
            // основная логика
        }
        ```
      
      - Важно: URL для API-запросов формируется как:
        `/compo45/{pageId}/{componentId}/{methodName}`
      - В JS при вызове метода:
        ```javascript
        var apiUrl = baseUrl + '/compo45/' + pageId + '/PhuketRequestForm/' + formAction;
        ```
      - Где `formAction` - это имя метода, который должен существовать в PHP-классе.
</rule>

<rule>
name: form_event_sourcing_pattern
filters:
  - type: file_content
    pattern: "PhuketFormAR|apiSave.*Form"
actions:
  - type: suggest
    message: |
      ## Event Sourcing для форм
      
      - Формы сохраняются через Event Sourcing с использованием класса `PhuketFormAR`:
        ```php
        $formAR = PhuketFormAR::create();
        $formAR->name = isset($formData['formId']) ? $formData['formId'] : 'ContactForm';
        $formAR->data = $formData;  // Содержит все поля формы
        $formAR->ip = $_SERVER['REMOTE_ADDR'];
        $formAR->save();  // Создает событие в таблице _s45_events
        ```
      
      - События сохраняются в таблице `_s45_events` с полями:
        - `arName`: 'PhuketForm' - тип агрегата
        - `arId`: Уникальный ID (UUID) формы
        - `name`: Тип события ('Saved')
        - `payload`: Сериализованные данные формы
        - `isLast`: Флаг последнего события (1)
      
      - Проверка сохраненных форм:
        ```sql
        SELECT arId, created, FROM_UNIXTIME(created) 
        FROM _s45_events 
        WHERE arName = 'PhuketForm' 
        ORDER BY created DESC
        ```
      
      - В форме всегда указывайте `formId` для идентификации типа формы:
        ```html
        <form id="simpleMobileRequestForm" data-form-id="SimpleRequest">
        ```
</rule>

<rule>
name: form_response_handling
filters:
  - type: file_content
    pattern: "drupal_json_output|drupal_exit"
actions:
  - type: suggest
    message: |
      ## Обработка ответов и статусов форм
      
      - **Всегда используйте** `drupal_json_output()` с `drupal_exit()` для ответа:
        ```php
        drupal_json_output(array(
            'status' => 'success',
            'message' => 'Форма успешно отправлена'
        ));
        drupal_exit();
        ```
      
      - **Для ошибок** обязательно логируйте через `watchdog` и возвращайте информативный ответ:
        ```php
        catch (\Exception $e) {
            watchdog('PhuketRequestForm', 'Error: @error', 
                array('@error' => $e->getMessage()), WATCHDOG_ERROR);
            drupal_json_output(array(
                'status' => 'error',
                'message' => 'Произошла ошибка: ' . $e->getMessage()
            ));
            drupal_exit();
        }
        ```
      
      - **В JavaScript** обрабатывайте ответ правильно:
        ```javascript
        $.ajax({
            url: apiUrl,
            type: 'POST',
            data: $form.serialize(),
            success: function(response) {
                if (response.status === 'success') {
                    // Показать успешное сообщение
                } else {
                    // Показать ошибку
                }
            },
            error: function() {
                // Обработка сетевых ошибок
            }
        });
        ```
</rule>

<rule>
name: debugging_forms
filters:
  - type: file_content
    pattern: "watchdog\\(|FormAR|drupal_json_output"
actions:
  - type: suggest
    message: |
      ## Отладка форм и обнаружение проблем
      
      1. **Проверьте соответствие имен методов**:
         - Метод в PHP компоненте должен точно совпадать с именем в JavaScript.
         - JS: `handleFormSubmit('#form', 'saveForm', '...')` -> PHP: `public function saveForm()`
      
      2. **Проверьте события в базе данных**:
         ```sql
         SELECT arId, created, FROM_UNIXTIME(created) 
         FROM _s45_events WHERE arName = 'PhuketForm' 
         ORDER BY created DESC LIMIT 10;
         ```
      
      3. **Посмотрите логи ошибок**:
         ```
         drush watchdog-show --severity=error --count=20
         ```
      
      4. **Временно добавьте подробное логирование**:
         ```php
         watchdog('FormDebug', 'Received data: @data', 
            array('@data' => print_r($_POST, true)), WATCHDOG_DEBUG);
         ```
      
      5. **Проверьте регистрацию компонента**:
         - Компонент должен быть в файле `files/site4/FileStore4/phuket/_repo/CompoInfo.s45.json`
         - Запустите сканирование компонентов:
         ```php
         $scanner = new Site45\Base\CompoScanner();
         $scanner->getForAllDirs();
         ```
</rule>
