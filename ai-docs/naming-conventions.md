# IndreamsPhuket.com: Naming Conventions

This document outlines the primary naming conventions used throughout the IndreamsPhuket.com project and the S45 platform. Adhering to these conventions is crucial for maintaining code consistency and readability.

## 1. PHP

### 1.1. Files

*   **Module Files**: `modulename.module`
    *   Example: `s45_base.module`, `s45_phuket.module`
*   **Include Files**: `modulename.purpose.inc` or `purpose.inc`
    *   Example: `s45_base.access.inc`, `s45_base.2.inc`
*   **Class Files**: `ClassName.php` (PascalCase)
    *   Example: `Compo.php`, `EventStore.php`, `PhuketPropertyAR.php`
*   **Drupal Template Files**: `template-name.tpl.php` (kebab-case, `.` replaced by `-` from Drupal standard)
    *   Example: `s45-phuket-login.tpl.php`, `phuket-property-teaser.tpl.php`

### 1.2. Classes & Interfaces

*   **Naming**: PascalCase.
*   **S45 Framework Classes**: Often namespaced under `Site45\`.
    *   Example: `Site45\Compo\Compo`, `Site45\Event\EventQuery`
*   **Application-Specific Classes (Phuket)**: Often prefixed with `Phuket`.
    *   Example: `PhuketPropertyAR`, `PhuketPropertyDto`, `PhuketPropertyQuery`
*   **Suffixes**: Common suffixes denote the class type.
    *   Example: `AR` (AggregateRoot), `Dto` (DataTransferObject), `VO` (ValueObject), `Query`, `Repo` (Repository), `Compo` (Component).

### 1.3. Functions & Methods

*   **Drupal Hooks**: `modulename_hookname` (snake_case)
    *   Example: `s45_base_menu()`, `s45_phuket_login_form_alter()`
*   **S45 Helper Functions (Global)**: `s45_function_name` (snake_case)
    *   Example: `s45_url()`, `s45_lang()`, `s45_render()`
*   **Class Methods**: camelCase
    *   Example: `beforeRender()`, `getLastEvent()`, `apiGetRendered()`

### 1.4. Variables

*   **Local Variables**: camelCase
    *   Example: `$formState`, `$siteId`, `$propertyDto`
*   **Object Properties (Public in Compo)**: camelCase
    *   Example: `$this->propertyDto`, `$this->items`
*   **Array Keys (for Drupal Forms, Options etc.)**: snake_case (Drupal common practice)
    *   Example: `$form_state['values']`, `$options['query']`

### 1.5. Constants

*   **Naming**: UPPER_CASE_WITH_UNDERSCORES
    *   Example: `S45_ACCESS_ADMIN`, `DEAL_TYPE_SALE`

## 2. JavaScript (ES6+ encouraged)

*   **Files**: `component-name.js` or `ModuleName.js` (kebab-case or PascalCase for main library files)
    *   Example: `phuket-search-map.js`, `Api452.js`
*   **Variables & Functions**: camelCase
    *   Example: `let propertyId = ...;`, `function updateMapPins() { ... }`
*   **Classes (ES6)**: PascalCase
    *   Example: `class PropertyMap { constructor() { ... } }`
*   **Namespacing/Modules**: For S45 components, often namespaced under `s45` or component name.
    *   Example: `s45.phuketMap.init()`, `IndreamsPhuket.PropertySlider.next()`

## 3. CSS

*   **Files**: `component-name.css` or `module-name.css` (kebab-case)
    *   Example: `phuket-property-card.css`, `s45-admin.css`
*   **Selectors (Classes)**: kebab-case, prefixed to avoid global scope collision. Use BEM-like conventions where appropriate.
    *   Example: `.s45-compo-property-slider`, `.phuket-property-card__title`, `.btn-primary--disabled`

## 4. Database

*   **Table Names**: snake_case, often prefixed with `_s45_` or `_phuket_`.
    *   Example: `_s45_events`, `_phuket_Property`, `_phuket_Reservation`
*   **Column Names**: camelCase (preferred for S45/Phuket specific tables) or snake_case (Drupal core/contrib often use snake_case).
    *   Example: `arName`, `arId`, `dealType`, `propertyDto` (camelCase)
    *   Example: `price_sale`, `price_rent` (snake_case, can exist for historical reasons or specific needs)

## 5. S45 Specific Naming

### 5.1. Compo Components

*   **Class Name & ID**: PascalCase (often reflecting the functionality)
    *   Example: `PhuketPropertySlider`, `AdminUserManagement`
*   **Directory Name**: PascalCase (same as the class name)
    *   Example: `sites/all/modules/__s45/Compo/PhuketPropertySlider/`

### 5.2. Event Sourcing

*   **Aggregate Root Name (`arName`)**: PascalCase (singular noun representing the aggregate)
    *   Example: `PhuketProperty`, `PhuketProject`, `PhuketReservation`
*   **Event Name (`name` field in `_s45_events`)**: PascalCase (past tense verb indicating the change)
    *   Example: `Saved`, `Deleted`, `Published`, `PriceUpdated`

## 6. General File & Directory Names

*   **General Directories**: kebab-case or snake_case for multi-word names when not dictated by specific framework (e.g. PHP Class names).
    *   Example: `ai-docs/`, `backup_before_optimization/`
*   **Configuration Files**: Often follow the tool's standard or kebab-case.
    *   Example: `_sites.s45.json`, `nginx.conf` 