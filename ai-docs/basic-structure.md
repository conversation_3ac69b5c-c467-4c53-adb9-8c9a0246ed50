# IndreamsPhuket.com: Basic Project Structure

This document outlines the basic file and directory structure of the IndreamsPhuket.com project, focusing on key components and their purposes.

## 1. Root Directory

The project root contains standard Drupal 7 files, custom scripts, and configuration.

*   **`index.php`**: Main Drupal entry point.
*   **`cron.php`**: Drupal cron execution script.
*   **`xmlrpc.php`**: XML-RPC interface for Drupal.
*   **`.htaccess`**: Apache configuration file for URL rewriting and access control.
*   **`robots.txt`**: Instructions for web crawlers.
*   **`web.config`**: IIS configuration file (may not be in use if on Nginx/Apache).
*   **Custom PHP Scripts**: Various scripts for specific tasks like sitemap generation (`sitemap_generator.php`), feeds (`FBFeedCsv.php`, `green-acres.php`), image processing (`property_images_dropbox.php`), and utilities.

## 2. Core Drupal Directories

Standard Drupal 7 directories are present:

*   **`includes/`**: Drupal core include files, bootstrap process, and common functions.
*   **`misc/`**: Miscellaneous utility JavaScript libraries, images, and icons used by Drupal core.
*   **`modules/`**: Drupal core and contributed modules provided by Drupal itself.
*   **`profiles/`**: Installation profiles (e.g., `standard`, `minimal`).
*   **`scripts/`**: Core command-line scripts.
*   **`themes/`**: Core themes (e.g., `bartik`, `seven`, `stark`).

## 3. Sites Directory (`sites/`)

This directory handles Drupal's multi-site capabilities and site-specific files.

*   **`sites/all/`**: Contains code shared across all sites if in a multi-site setup.
    *   **`sites/all/modules/`**: Primary location for contributed and custom modules.
        *   **`__s45/`**: **Crucial directory containing the S45 custom platform modules.** This is the heart of the project's unique functionality.
            *   `s45_base/`: Core S45 framework.
                *   `classes/Site45/Compo/`: Base `Compo.php`, `CompoApi2.php`, `CompoRepo.php`, `CompoScanner.php`.
                *   `classes/Site45/Event/`: `AR.php`, `EventDto.php`, `EventQuery.php`, `EventStore.php`, `QueryFromEvents.php`.
                *   `classes/Site45/Path/`: `Path.php`, `Redirect.php`.
                *   `classes/Site45/SiteConf.php`, `PageId.php`.
                *   `s45_base.module`: Main module file, hooks, API entry points.
            *   `s45_page/`: Manages the main "s45" page rendering and site structure.
            *   `s45_path/`: Custom URL alias and redirect system.
            *   `s45_phuket/`: Business logic specific to Phuket properties (ARs, DTOs, Queries for Property, Project, etc.).
            *   `s45_phuket_login/`: Customization for user login.
            *   `s45_imagestyles/`: Defines custom image styles for Drupal.
            *   `Compo/`: Contains various shared S45 Compo components.
        *   `_vendor/`: Likely for older third-party libraries or modules not managed by Composer. Examples: `jquery_update`, `logintoboggan`.
        *   **Contributed Modules**: Other important modules like `advagg` (CSS/JS aggregation), `ctools` (Chaos Tool Suite), `libraries` (API), `memcache` (Memcache integration), `metatag`, `views`, `pathologic`, `token`.
    *   **`sites/all/themes/`**: Location for custom themes.
        *   `site45/`: The main custom theme for IndreamsPhuket.com.

*   **`sites/default/`**: Default site configuration and files.
    *   `settings.php` (not visible in layout, but standard): Main Drupal site configuration (database, caching, etc.).
    *   `files/`: Publicly accessible files uploaded by users, CSS/JS aggregates, generated image styles.
        *   `styles/`: Contains derivative images generated by Drupal's image styles system.

*   **`sites/site4/`**: Appears to be another site or an alias/specific configuration directory, containing its own `files/` structure. This might indicate a multi-site setup or a specific organizational choice.

## 4. Other Key Directories

*   **`admin/`**: Custom scripts and tools for administration, not part of Drupal's core admin interface.
*   **`ai-docs/`**: Documentation generated by or for AI, including this document.
*   **`backup_before_optimization/`**: Backup of files before optimization tasks.
*   **`cache/`**: Custom caching directory, possibly for non-Drupal cache mechanisms or specific data.
*   **`dumper/`**: Seems to contain tools or assets for a database administration tool (MySQLDumper).
*   **`files/` (root level)**: Contains `private/` and `site4/` (which seems to be a symlink or related to `sites/site4/files/`). `FileStore4/` here suggests a custom file storage structure.
*   **`logs/` (within `admin/`)**: Log files for administrative scripts or tools.
*   **`tgbot_docker/`**: Files related to a Telegram bot, likely running in Docker.

## 5. Key File Organization Principles

*   **S45 Framework Core**: Centralized in `sites/all/modules/__s45/s45_base/`, with business-specific logic for "Phuket" in `sites/all/modules/__s45/s45_phuket/`.
*   **Component-Based UI**: UI elements are managed as "Compo" components, with their PHP classes typically in `classes/Site45/Compo/` or module-specific `Compo/` subdirectories, and templates (`.tpl.php`) alongside their respective classes or in a dedicated `templates/` folder.
*   **Event Sourcing**: Core Event Sourcing classes (`AR`, `EventStore`, `EventQuery`) are in `sites/all/modules/__s45/s45_base/classes/Site45/Event/`. Domain-specific Aggregates (e.g., `PhuketPropertyAR`) would reside in relevant business logic modules like `s45_phuket`.
*   **Configuration**: Drupal configuration is primarily in `sites/default/settings.php`. S45 site configuration (`SiteConf`) likely relies on JSON files (e.g., `_sites.s45.json` mentioned in rules, though not visible in layout).

This structure separates Drupal core, contributed modules, S45 custom platform code, and site-specific files, which is typical for a complex Drupal application. 