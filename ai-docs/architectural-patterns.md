# IndreamsPhuket.com: Архитектурные Паттерны

Этот документ описывает ключевые архитектурные паттерны, используемые в проекте IndreamsPhuket.com и кастомном фреймворке S45.

## 1. Фреймворк S45

*   **Описание:** Набор кастомных модулей и библиотек поверх Drupal 7, предоставляющий основу для специфической бизнес-логики сайта. Включает систему компонентов (Compo), Event Sourcing, управление конфигурацией и другие утилиты.
*   **Применение:** Используется для всей основной кастомной функциональности, особенно связанной с недвижимостью, пользователями и отображением контента.
*   **Когда использовать:** При разработке новой или модификации существующей функциональности, которая выходит за рамки стандартных возможностей Drupal и требует интеграции с кастомной логикой проекта.

## 2. Компоненты (Compo)

*   **Описание:** Основной паттерн для построения UI. Компонент — это переиспользуемый блок UI с собственной логикой (PHP класс, наследуемый от `Site45\Compo\Compo`), шаблоном (`.tpl.php`), и опционально CSS/JS файлами. Рендеринг и API-взаимодействие часто идут через `CompoApi2::exec()` или `s45_render()`.
*   **Применение:** Для всех элементов интерфейса – от простых блоков до сложных форм и списков объектов недвижимости. PHP класс готовит данные (часто в виде DTO/VO) в методе `beforeRender()` и передает их в шаблон.
*   **Когда использовать:** Для создания любых UI элементов. Логику – в PHP класс, представление – в `.tpl.php`.

## 3. Event Sourcing (ES)

*   **Описание:** Фундаментальный паттерн для сохранения изменений состояния ключевых сущностей (например, объектов недвижимости, конфигураций компонентов). Все изменения записываются как последовательность неизменяемых событий в таблицу `_s45_events`. Класс `Site45\Event\EventStore` отвечает за запись, `Site45\Event\EventQuery` – за чтение событий.
*   **Применение:** Обеспечивает полный аудит, возможность восстановления состояния на любой момент времени и служит источником данных для построения Read Model.
*   **Когда использовать:** Используется неявно при работе с AR и репозиториями, которые поддерживают ES. Прямое взаимодействие с `EventStore` или `EventQuery` обычно не требуется разработчику бизнес-логики, если он использует вышестоящие абстракции.

## 4. Active Record (AR)

*   **Описание:** Представляет доменные сущности (например, `PhuketPropertyAR`). Класс AR содержит свойства, соответствующие полям сущности, и может содержать базовую логику. В S45 изменения AR сохраняются через Event Sourcing.
*   **Применение:** Для работы с индивидуальными экземплярами сущностей (загрузка, модификация).
*   **Когда использовать:** При необходимости оперировать конкретным объектом домена (например, загрузить объект недвижимости по ID, изменить его данные и сохранить).

## 5. Data Transfer Objects (DTO) / Value Objects (VO)

*   **Описание:** Простые PHP классы для структурированной передачи данных между слоями приложения. DTO (`PhuketPropertyDto`, `FileDto`) обычно представляют сущности или их части. VO (`LangVO`, `PropertyPriceVO`) представляют простые значения. Не содержат бизнес-логики, создаются через статический метод `create()`.
*   **Применение:** Query-классы возвращают данные в виде DTO/VO. Compo-компоненты получают DTO/VO и используют их для отображения. `LangVO` широко используется для мультиязычных полей.
*   **Когда использовать:** Для передачи данных между Query-классами, сервисами и Compo-компонентами, а также для формирования API ответов.

## 6. Repository (Репозиторий)

*   **Описание:** Абстрагирует доступ к коллекциям объектов или конфигурациям. Примеры: `CompoRepo` (для конфигурации компонентов, использует ES и JSON), `JsonRepo` (для работы с JSON файлами).
*   **Применение:** Для загрузки и сохранения данных, управляемых S45, когда не подходит прямое использование AR или Query-объектов (например, для конфигураций).
*   **Когда использовать:** При работе с конфигурациями компонентов или другими наборами данных, для которых предоставлен репозиторий.

## 7. Query (Запрос)

*   **Описание:** Классы, ответственные за выборку данных из БД или других источников по заданным критериям. Инкапсулируют логику построения запросов и возвращают результаты в виде DTO/VO (часто в `SearchResultVO`). Примеры: `PhuketPropertyQuery`, `QueryFromEvents` (базовый класс для запросов к Read Model).
*   **Применение:** Используются Compo-компонентами и сервисами для получения списков данных для отображения или обработки.
*   **Когда использовать:** При необходимости получить набор данных (например, список объектов недвижимости с фильтрами, список новостей).

## 8. Read Model (Модель Чтения)

*   **Описание:** Вследствие использования Event Sourcing, для эффективного чтения данных используются денормализованные таблицы (Read Models), такие как `_phuket_Property`. Эти таблицы содержат актуальное состояние данных в оптимизированном для запросов виде и обновляются на основе событий из `_s45_events`.
*   **Применение:** Query-классы (особенно наследники `QueryFromEvents`) читают данные из этих Read Models, а не пересобирают состояние из лога событий каждый раз.
*   **Когда использовать:** Этот паттерн является частью внутренней архитектуры. При разработке новых Query-классов для сущностей, управляемых через ES, следует ориентироваться на существующие Read Models или планировать создание новых, если текущие не подходят.

## 9. Services (Сервисы)

*   **Описание:** Классы, инкапсулирующие бизнес-логику, не относящуюся напрямую к конкретной сущности AR или UI Compo. Это могут быть расчеты, взаимодействие с внешними API, сложная обработка данных.
*   **Применение:** Примеры: логика обработки форм (`FormApi2`), генерация SEO-данных (`PhuketSchemaGenerator`), конвертеры фидов.
*   **Когда использовать:** Для вынесения переиспользуемой бизнес-логики, которая не является частью конкретного AR или Compo.

## 10. API Endpoints (API Эндпоинты)

*   **Описание:** Кастомные точки входа для AJAX-запросов от frontend или для взаимодействия с внешними системами. Примеры: `/s45PhuketSearchJson` (данные для карты), `/s45PhuketFormAPI2/...` (обработка форм).
*   **Применение:** Обеспечивают программный интерфейс к данным и операциям.
*   **Когда использовать:** Для реализации интерактивности на клиенте или для интеграций, требующих обмена данными в структурированном формате (JSON). 