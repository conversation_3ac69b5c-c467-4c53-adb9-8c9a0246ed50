# IndreamsPhuket.com: Сущности и Связи

Этот документ описывает основные бизнес-сущности в проекте IndreamsPhuket.com, их назначение и связи между ними в рамках кастомного фреймворка S45.

## 1. Общие Принципы Работы с Сущностями

*   **Хранение Состояния:** Изменения состояния ключевых кастомных сущностей сохраняются через механизм **Event Sourcing** в таблицу `_s45_events`. Каждое изменение — это событие.
*   **Модели Чтения (Read Models):** Для эффективных запросов существуют денормализованные таблицы (например, `_phuket_Property`), которые отражают текущее состояние сущностей и обновляются на основе событий.
*   **Представление в Коде:**
    *   **Active Record (AR):** Классы, представляющие сущности (например, `PhuketPropertyAR`). Используются для загрузки и изменения индивидуальных объектов. Сохранение AR инициирует создание событий.
    *   **Data Transfer Objects (DTO):** Простые объекты для передачи данных (например, `PhuketPropertyDto`). Query-классы обычно возвращают DTO.
*   **Идентификаторы:** Каждая сущность в Event Sourcing имеет уникальный идентификатор (`arId`, обычно UUID).

## 2. Ключевые Сущности

### 2.1. PhuketProperty (Объект недвижимости)

*   **Назначение:** Центральная сущность, представляющая объект недвижимости (вилла, квартира, земля и т.д.) на Пхукете, доступный для продажи или аренды.
*   **Таблица Read Model:** `_phuket_Property`
*   **Ключевые Атрибуты:**
    *   `id` (UUID, первичный ключ)
    *   `created`, `changed` (временные метки)
    *   `published` (статус публикации: 1 - опубликован, 0 - нет)
    *   `name` (название, `LangVO`)
    *   `number`, `numberStr` (номер объекта, числовой и строковый)
    *   `dealType` (тип сделки: 'sale', 'rent')
    *   `propertyType` (тип недвижимости: 'villa', 'condo', 'land', etc.)
    *   `price_sale`, `price_rent`, `price_longtime` (цены для разных типов сделок и сроков)
    *   `bedrooms` (количество спален)
    *   `areaCommon` (общая площадь)
    *   `lat`, `lng` (координаты для карты)
    *   `isRecommended`, `isPermium` (вероятно, `isPremium`), `isSaled` (продан/сдан), `isInvest` (инвестиционный) - флаги.
    *   `propertyDto` (сериализованный DTO с полными данными объекта)
    *   `propertyTeaserDto` (сериализованный DTO с сокращенными данными для тизеров/списков)
*   **Связи:**
    *   **`PhuketProject` (Проект):** Может принадлежать проекту. Связь через поле `project` (ID проекта) в `_phuket_Property`.
    *   **`PhuketOption` (Локации, Опции):** Связан с локациями (`country`, `locality`, `subLocality`) и различными характеристиками/удобствами. Связи через ID опций, хранящиеся в `_phuket_Property` или внутри сериализованного `propertyDto`.
    *   **`PhuketReservation` (Бронирование):** Объекты недвижимости могут быть забронированы. Связь через `propertyId` в `_phuket_Reservation`.
    *   **`PhuketRent` (Данные по аренде):** Записи в `_phuket_Rent` связаны с `PhuketProperty` через `propertyId` для предоставления специфичных для аренды данных и периодов.
    *   **`PhuketUser` (Пользователь):** Пользователи (администраторы, агенты) создают и редактируют объекты. Связь отслеживается через `authorId` в событиях (`_s45_events`).
    *   **Изображения/Файлы:** Связан с медиафайлами (ссылки/ID обычно в `propertyDto`).
*   **Использование:** Для отображения в каталогах, на детальных страницах, в результатах поиска. Управляется через админ-интерфейс.

### 2.2. PhuketProject (Проект/Комплекс)

*   **Назначение:** Представляет жилой или коммерческий комплекс, новостройку.
*   **Таблица Read Model:** `_phuket_Project`
*   **Ключевые Атрибуты:**
    *   `id` (UUID, первичный ключ)
    *   `created`, `changed` (временные метки)
    *   `name`, `nameEn` (названия на разных языках)
    *   `projectDto` (сериализованный DTO с данными проекта)
*   **Связи:**
    *   **`PhuketProperty`:** Один проект содержит множество объектов недвижимости (связь от `PhuketProperty.project`).
    *   **`PhuketUser`:** Управляется администраторами/менеджерами.
*   **Использование:** Группировка объектов, информация о застройщиках.

### 2.3. PhuketOption (Опция/Справочник)

*   **Назначение:** Элементы различных справочников: локации, типы недвижимости, удобства, теги и т.д.
*   **Таблица Read Model:** `_phuket_Option`
*   **Ключевые Атрибуты:**
    *   `id` (UUID, первичный ключ)
    *   `created`, `changed` (временные метки)
    *   `name` (название опции, `LangVO`)
    *   `optionName` (системное имя справочника, см. ниже)
    *   `parentId` (для иерархических справочников)
    *   `dto` (сериализованный DTO с данными опции)
*   **Типы справочников (значения `optionName`):** `subLocality` (подрайон/пляж), `furniture` (мебель), `inPrice` (включено в цену продажи), `notInPrice` (не включено в цену продажи), `conciergeService` (консьерж-сервис), `inPriceSale` (включено в цену продажи - дублирует?), `homeImprovement` (улучшения дома), `projectImprovement` (улучшения проекта), `appliances` (бытовая техника), `infrastructure` (инфраструктура), `country` (страна), `locality` (основной район/город), `paymetTerms` (условия оплаты), `view` (вид), `holdDocument` (документы), `tag` (теги), `additionalOptions` (доп. опции).
*   **Связи:**
    *   **`PhuketProperty`:** Объекты недвижимости ссылаются на опции для указания своих характеристик.
*   **Использование:** Фильтрация в поиске, отображение характеристик, построение иерархий.

### 2.4. PhuketReservation (Бронирование)

*   **Назначение:** Бронирование объекта недвижимости (вероятно, для аренды) на даты.
*   **Таблица Read Model:** `_phuket_Reservation`
*   **Ключевые Атрибуты:**
    *   `id` (UUID, первичный ключ)
    *   `created`, `changed` (временные метки)
    *   `resType` (тип бронирования)
    *   `propertyId` (ID связанного `PhuketProperty`)
    *   `propertyNumber` (номер объекта недвижимости)
    *   `dateFrom`, `dateTo` (даты бронирования)
    *   `status` (статус брони: 'pending', 'confirmed', 'cancelled')
    *   `dto` (сериализованный DTO с деталями бронирования)
*   **Связи:**
    *   **`PhuketProperty`:** Связано с конкретным объектом недвижимости.
    *   **`PhuketUser`:** Связано с клиентом и/или менеджером.
*   **Использование:** Управление доступностью объектов аренды.

### 2.5. PhuketUser (Пользователь)

*   **Назначение:** Зарегистрированный пользователь системы (администраторы, агенты, клиенты).
*   **Таблица Read Model:** Соответствует стандартной таблице `users` Drupal, но также является агрегатом в Event Sourcing (`arName='PhuketUser'`).
*   **Ключевые Атрибуты:** Стандартные поля пользователя Drupal (`uid`, `name`, `mail`, `status`, etc.) + кастомные поля/роли S45.
*   **Связи:**
    *   **`_s45_events` (`authorId`):** Действия над сущностями S45 логируются с `authorId`.
    *   **`PhuketProperty` / `PhuketProject`:** Агенты/администраторы как создатели/редакторы.
    *   **`PhuketReservation`:** Клиенты как инициаторы бронирования.
*   **Использование:** Аутентификация, авторизация, персонализация, отслеживание действий.

### 2.6. Контентные Сущности (Article, News, Service, Boat, Club, Excursion, Review)

*   **Назначение:** Представляют различный контент и предложения на сайте:
    *   `PhuketArticle` (статьи)
    *   `PhuketNews` (новости)
    *   `PhuketService` (услуги)
    *   `PhuketBoat` (лодки/яхты)
    *   `PhuketClub` (клубные предложения)
    *   `PhuketExcursion` (экскурсии)
    *   `PhuketReview` (отзывы)
*   **Таблицы Read Model:** `_phuket_Article`, `_phuket_News`, `_phuket_Service`, `_phuket_Boat`, `_phuket_Club`, `_phuket_Excursion`, `_phuket_Review`.
*   **Ключевые Атрибуты (Общие):** `id` (UUID), `created`, `name` (`LangVO`), `xxxDto` (сериализованный DTO с деталями). `PhuketReview` имеет `changed`, `onFront`. `PhuketClub` имеет `sort`.
*   **Связи:**
    *   **`PhuketUser` (Автор/Пользователь):** Могут быть связаны с авторами или пользователями, оставившими отзыв.
    *   **`PhuketOption` (Категории/Теги):** Могут быть классифицированы.
    *   **`PhuketProperty` / `PhuketProject`:** Отзывы могут быть связаны с конкретными объектами или проектами. Услуги/экскурсии могут быть привязаны к локациям или объектам.
*   **Использование:** Информационное наполнение, SEO, маркетинг, пользовательский контент.

### 2.7. PhuketRent (Данные по Аренде)

*   **Назначение:** Специализированная Read Model для данных по аренде, вероятно, для оптимизации поиска и отображения арендных предложений и их доступности.
*   **Таблица Read Model:** `_phuket_Rent`
*   **Ключевые Атрибуты:** Содержит многие поля из `_phuket_Property` (такие как `dealType`, `propertyType`, `bedrooms`, `price_rent`, `lat`, `lng`, `propertyTeaserDto`) и добавляет специфичные для аренды поля:
    *   `id` (INT, auto_increment)
    *   `propertyId` (varchar(36), ID связанного `PhuketProperty`)
    *   `rentFrom`, `rentTo` (временные метки начала и окончания периода доступности или конкретной записи аренды)
    *   `additional` (дополнительная текстовая информация)
*   **Связи:**
    *   **`PhuketProperty`:** Тесно связана через `propertyId`. Каждая запись в `_phuket_Rent` детализирует или агрегирует арендную информацию для конкретного `PhuketProperty`.
*   **Использование:** Для быстрого поиска доступных для аренды объектов на определенные даты, отображения календарей занятости, возможно, для агрегации данных по аренде.

### 2.8. Прочие Вспомогательные Сущности/Таблицы

*   **`_phuket_Form`:** Хранит данные, отправленные через различные формы на сайте. Каждая запись обычно содержит ID формы, сериализованные данные формы и метаинформацию (дата, IP).
*   **`_phuket_PageSeo`:** Хранит кастомные SEO-данные (title, description, keywords) для определенных URL сайта, позволяя переопределять автоматически генерируемые SEO-теги.
*   **`_phuket_Screen`:** Вероятно, связана с системой "Экранов" (специализированных страниц или разделов сайта). Структура: `id` (varchar), `siteId` (varchar), `url` (text), `compo` (varchar), `title` (varchar), `settings` (longtext).
*   **`_phuket_Selection`:** Назначение не до конца ясно без более глубокого анализа DTO. Структура: `id` (varchar), `created` (int), `name` (varchar), `selectionDto` (longtext). Может представлять сохраненные подборки объектов или критерии поиска.


## 3. Взаимодействие с Сущностями

Разработчики взаимодействуют с этими сущностями преимущественно через:
1.  **AR-классы:** Для загрузки, изменения и сохранения отдельных экземпляров (`$propertyAR = PhuketPropertyAR::load($id); $propertyAR->setPrice(1000); $propertyAR->save();`).
2.  **Query-классы:** Для получения списков сущностей в виде DTO (`$properties = PhuketPropertyQuery::create()->setFilter(...)->exec();`).
3.  **DTO/VO:** Для передачи данных между слоями и в компоненты UI.
4.  **Compo-компоненты:** Для отображения данных сущностей, полученных через Query-классы.

Изменение состояния сущностей, управляемых через AR, автоматически приводит к созданию соответствующих событий в `_s45_events`, что затем инициирует обновление связанных Read Models. 