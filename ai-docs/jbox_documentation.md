# jBox Plugin Documentation (основано на анализе кода проекта)

## Оглавление

1.  [Введение](#введение)
2.  [Основные типы jBox](#основные-типы-jbox)
3.  [Инициализация](#инициализация)
4.  [Основные опции](#основные-опции)
    *   [Общие и ID](#общие-и-id)
    *   [Размеры](#размеры)
    *   [Позиционирование](#позиционирование)
    *   [Контент и цель (Target)](#контент-и-цель-target)
    *   [Внешний вид и темы](#внешний-вид-и-темы)
    *   [Анимации](#анимации)
    *   [Оверлей (Overlay)](#оверлей-overlay)
    *   [Поведение при закрытии](#поведение-при-закрытии)
    *   [Задержки (Delays)](#задержки-delays)
    *   [Перетаскивание (Draggable)](#перетаскивание-draggable)
    *   [AJAX](#ajax)
    *   [Аудио](#аудио)
    *   [Специфичные опции для Галереи Изображений](#специфичные-опции-для-галереи-изображений-jboximage)
    *   [Специфичные опции для Подтверждений](#специфичные-опции-для-подтверждений-jboxconfirm)
    *   [Специфичные опции для Уведомлений](#специфичные-опции-для-уведомлений-jboxnotice)
5.  [Методы](#методы)
6.  [События (Callbacks)](#события-callbacks)
7.  [CSS Классы для темизации](#css-классы-для-темизации)
8.  [Примечание](#примечание)

## Введение

**jBox** — это jQuery плагин, который упрощает создание настраиваемых всплывающих подсказок (tooltips), модальных окон, галерей изображений и многого другого.

*   **Автор:** Stephan Wagner (<<EMAIL>>, [https://stephanwagner.me](https://stephanwagner.me))
*   **Предполагаемая лицензия:** MIT
*   **Зависимости (из комментариев в коде):** jQuery (например, 3.4.1)

*Ссылки на официальную документацию и демо (из комментариев в коде, доступ может быть ограничен):*
*   Документация: [https://stephanwagner.me/jBox/documentation](https://stephanwagner.me/jBox/documentation)
*   Демо: [https://stephanwagner.me/jBox/demos](https://stephanwagner.me/jBox/demos)

Эта документация основана на анализе исходного кода jBox, найденного в проекте.

## Основные типы jBox

jBox может быть инициализирован с различными типами, каждый из которых имеет свой набор стандартных опций и поведения:

*   **`Tooltip`**: Стандартные всплывающие подсказки.
*   **`Mouse`**: Всплывающая подсказка, которая следует за курсором мыши.
*   **`Modal`**: Модальные окна.
*   **`Image`**: Для отображения изображений и галерей (предположительно через `jBoxImageWrapper`).
*   **`Confirm`**: Для диалогов подтверждения (предположительно через `jBoxConfirmWrapper`).
*   **`Notice`**: Для уведомлений (предположительно через `jBoxNoticeWrapper`).

## Инициализация

Экземпляр jBox создается с помощью конструктора или через jQuery:

```javascript
// Прямой вызов конструктора
var myModal = new jBox('Modal', {
  content: 'Это мое модальное окно!'
});

// Через jQuery селектор (если плагин расширяет jQuery.fn)
// jQuery('#myButton').jBox('Tooltip', {
//   content: 'Это моя подсказка!'
// });
```
Примечание: Код содержит структуру для оберток `jBoxConfirmWrapper`, `jBoxImageWrapper`, `jBoxNoticeWrapper`, которые расширяют базовый `jBox`.

## Основные опции

Здесь перечислены основные опции, доступные для настройки jBox. Значения по умолчанию указаны в скобках.

### Общие и ID
*   `id` (null): `string` - Уникальный идентификатор для экземпляра jBox. Если не указан, генерируется автоматически (например, 'jBox1').
*   `createOnInit` (true): `boolean` - Если `true`, HTML-структура jBox создается при инициализации.

### Размеры
*   `width` ('auto'): `string|number` - Ширина области контента (например, 'auto', 200, '80%').
*   `height` ('auto'): `string|number` - Высота области контента.
*   `minWidth` (null): `number` - Минимальная ширина.
*   `minHeight` (null): `number` - Минимальная высота.
*   `maxWidth` (null): `number` - Максимальная ширина.
*   `maxHeight` (null): `number` - Максимальная высота.
*   `responsiveWidth` (true): `boolean` - Автоматически подстраивать ширину под размер viewport.
*   `responsiveHeight` (true): `boolean` - Автоматически подстраивать высоту под размер viewport.
*   `responsiveMinWidth` (100): `number` - Минимальная ширина в пикселях при адаптивности.
*   `responsiveMinHeight` (100): `number` - Минимальная высота в пикселях при адаптивности.

### Позиционирование
*   `target` (null): `jQuery selector|jQuery element|string ('mouse')|Window` - Элемент, относительно которого позиционируется jBox. Для типа `Mouse` по умолчанию `'mouse'`, для `Modal` - `jQuery(window)`.
*   `fixed` (false): `boolean` - Использовать `position: fixed`. Для `Modal` по умолчанию `true`.
*   `position`: `object` - Определяет позицию jBox.
    *   `x` ('center'): `string|number` - Горизонтальное выравнивание ('left', 'center', 'right') или числовое смещение.
    *   `y` ('center'): `string|number` - Вертикальное выравнивание ('top', 'center', 'bottom') или числовое смещение.
*   `outside` (null): `string` - Позиционировать jBox снаружи элемента `target` по указанной оси ('x' или 'y').
*   `offset`: `number|object` - Смещение от `target`.
    *   Если число (например, `10`): `{ x: 10, y: 10 }`.
    *   Если объект (например, `{ x: 5, y: 15 }`). По умолчанию: `{ x: 0, y: 0 }`.
*   `attributes`: `object` - Дополнительные атрибуты для позиционирования (редко используется).
    *   `x` (null): `string` - 'left' или 'right'.
    *   `y` (null): `string` - 'top' или 'bottom'.
*   `adjustPosition` (true): `boolean|string` - Автоматически корректировать позицию, если jBox не помещается в viewport. Значения: `true` (эквивалентно 'flipfit'), 'flip', 'flipfit'.
*   `adjustTracker` (false): `boolean` - Перепозиционировать jBox, если `target` элемент меняет свое положение.
*   `adjustDistance`: `number|object` - Минимальное расстояние от краев viewport. По умолчанию: `{ top: 5, right: 5, bottom: 5, left: 5 }`.
*   `reposition` (true): `boolean` - Перепозиционировать jBox при изменении размера окна.
*   `repositionOnOpen` (true): `boolean` - Перепозиционировать при открытии.
*   `repositionOnContent` (true): `boolean` - Перепозиционировать при изменении контента.
*   `pointer` (false): `boolean|string` - Отображать "указатель" (стрелку). Можно задать позицию и смещение, например, 'left:30', 'center:-20'. Для `Tooltip` по умолчанию `true`.
*   `pointTo` ('target'): `string` - На что будет указывать стрелка ('target', 'top', 'right', 'bottom', 'left').

### Контент и цель (Target)
*   `attach` (null): `jQuery selector` - jQuery селектор для элементов, которые будут открывать/закрывать jBox.
*   `trigger` ('click'): `string` - Событие для открытия/закрытия ('click', 'touchclick', 'mouseenter'). Для `Tooltip` и `Mouse` по умолчанию 'mouseenter'.
*   `preventDefault` (false): `boolean` - Предотвращать стандартное действие события (например, переход по ссылке).
*   `content` (null): `string|jQuery element` - HTML-строка или jQuery-элемент для отображения в jBox.
*   `getContent` (null): `string|function` - Атрибут элемента `attach` (например, 'title', 'data-content') или функция, возвращающая контент. Для `Tooltip` по умолчанию 'title'. Функция принимает `source` (прикрепленный элемент) как аргумент.
*   `title` (null): `string` - Заголовок для jBox.
*   `getTitle` (null): `string|function` - Атрибут элемента `attach` или функция для получения заголовка.
*   `footer` (null): `string|jQuery element` - Контент для футера (подвала) jBox.
*   `isolateScroll` (true): `boolean` - Изолировать прокрутку внутри контента jBox (предотвращает прокрутку основной страницы, если мышь над контентом jBox).
*   `blockScroll` (false): `boolean` - Блокировать прокрутку основной страницы, когда jBox открыт. Для `Modal` по умолчанию `true`.
*   `appendTo` (`jQuery('body')`): `jQuery element` - Элемент, в который будет добавлен HTML-код jBox.

### Внешний вид и темы
*   `theme` ('Default'): `string` - CSS-класс темы для jBox (например, 'jBox-Default', 'jBox-MyTheme').
*   `addClass` (null): `string` - Дополнительные CSS-классы для основного элемента-обертки jBox.
*   `zIndex` (10000): `number|string ('auto')` - z-index для jBox. При 'auto' z-index будет автоматически увеличиваться.

### Анимации
*   `fade` (180): `number|boolean` - Длительность анимации появления/исчезновения в мс. `0` или `false` для отключения.
*   `animation` (null): `string|object` - Название стандартной анимации ('pulse', 'zoomIn', 'zoomOut', 'move', 'slide', 'flip', 'tada') или объект с настройками кастомной CSS-анимации. Для `Modal` по умолчанию 'zoomIn'.

### Оверлей (Overlay)
*   `overlay` (false): `boolean` - Отображать полупрозрачный фон (оверлей) под jBox. Для `Modal` по умолчанию `true`.
*   `overlayClass` (null): `string` - Дополнительный CSS-класс для оверлея.

### Поведение при закрытии
*   `closeOnEsc` (false): `boolean` - Закрывать jBox по нажатию клавиши Esc. Для `Modal` по умолчанию `true`.
*   `closeOnClick` (false): `boolean|string` - Закрывать jBox по клику.
    *   `true`: клик в любом месте.
    *   `'box'`: клик по самому jBox.
    *   `'overlay'`: клик по оверлею. Для `Modal` по умолчанию `'overlay'`.
    *   `'body'`: клик в любом месте, кроме самого jBox.
*   `closeOnMouseleave` (false): `boolean` - Закрывать jBox, когда курсор мыши покидает область jBox или прикрепленного элемента.
*   `closeButton` (false): `boolean|string` - Отображать кнопку закрытия.
    *   `true`: стандартная кнопка.
    *   `'box'`: кнопка внутри основного блока.
    *   `'title'`: кнопка в заголовке.
    *   `'overlay'`: кнопка на оверлее.
    *   Для `Modal` по умолчанию `true`.
*   `closeButtonClass` (null): `string` - Дополнительные CSS-классы для кнопки закрытия.
*   `closeButtonHtml` ('&times;'): `string` - HTML-код для кнопки закрытия.
*   `blockClicks` (true): `boolean` - Блокировать клики по прикрепленным элементам во время анимации открытия/закрытия jBox.

### Задержки (Delays)
*   `delayOpen` (0): `number` - Задержка перед открытием в мс.
*   `delayClose` (0): `number` - Задержка перед закрытием в мс. (Минимум 10мс, чтобы избежать закрытия при быстром повторном открытии).

### Перетаскивание (Draggable)
*   `draggable` (false): `boolean|string` - Позволяет перетаскивать jBox.
    *   `true`: можно тащить за любую часть jBox.
    *   `'title'`: можно тащить только за заголовок.
    *   `'box'`: можно тащить за любую часть jBox (аналогично `true`).
*   `dragOver` (true): `boolean` - Разрешить перетаскивание jBox поверх других элементов.
*   `dragSwap` (false): `boolean` - Если `true`, перетаскиваемые jBox могут меняться местами.

### AJAX
*   `ajax`: `object` - Опции для загрузки контента через AJAX (аналогично `jQuery.ajax()`).
    *   `url` (null): `string` - URL для запроса.
    *   `data`: `{}` (`object|string`) - Данные для отправки.
    *   `reload` (false): `boolean|string ('strict')` - Перезагружать контент при каждом открытии. `'strict'` означает, что запрос будет отправлен, даже если URL не изменился.
    *   `getOnce` (false): `boolean` - Загружать контент только один раз.
    *   `setContent` (true): `boolean` - Устанавливать полученный ответ как контент jBox.
    *   `spinner` (true): `boolean|string|jQuery element` - Отображать индикатор загрузки (спиннер). Можно передать HTML-строку или jQuery-элемент для кастомного спиннера.
    *   `spinnerDelay` (300): `number` - Задержка перед отображением спиннера в мс.
    *   `spinnerReposition` (true): `boolean` - Перепозиционировать спиннер, если jBox меняет позицию.

### Аудио
*   `audio` (false): `string|object` - URL аудиофайла или объект `{ open: 'open_sound.mp3', close: 'close_sound.mp3' }` для проигрывания звуков при открытии/закрытии.
*   `volume` (100): `number|object` - Громкость (0-100) или объект `{ open: 75, close: 100 }`.
*   `preloadAudio` (true): `boolean|array` - Предзагружать аудиофайлы. `true` - предзагружать все из опции `audio`. Можно передать массив URL-ов.

### Специфичные опции для Галереи Изображений (jBoxImage)
Эти опции, вероятно, обрабатываются расширением `jBoxImageWrapper`.
*   `showCounter` (false): `boolean` - Отображать счетчик изображений (например, "1 из 5").
*   `imageCounterText` ('{x} of {y}'): `string` - Шаблон текста для счетчика. `{x}` - текущий номер, `{y}` - общее количество.
*   `downloadButton` (false): `boolean` - Отображать кнопку для скачивания изображения.
*   `downloadUrl` (null): `string|function` - URL для скачивания или функция, возвращающая URL. Функция может принимать текущий элемент галереи как аргумент.
*   `imageFullMarkup` (null): `string` - Позволяет задать собственную HTML-разметку для области отображения основного изображения в галерее.
*   `gallery` (предполагается): `string` - Имя галереи, используемое для группировки изображений (например, из `data-jbox-image="galleryName"`).
*   `src` (предполагается): `string` - Атрибут, из которого берется URL большого изображения (например, 'href').

### Специфичные опции для Подтверждений (jBoxConfirm)
Эти опции, вероятно, обрабатываются расширением `jBoxConfirmWrapper`.
*   `targetConfirmation` (false): `boolean` - (Назначение не до конца ясно из кода).
*   `confirmButton` ('Submit'): `string` - Текст для кнопки подтверждения.
*   `cancelButton` ('Cancel'): `string` - Текст для кнопки отмены.
*   `confirm` (null): `function` - Callback-функция, вызываемая при подтверждении.
*   `cancel` (null): `function` - Callback-функция, вызываемая при отмене.

### Специфичные опции для Уведомлений (jBoxNotice)
Эти опции, вероятно, обрабатываются расширением `jBoxNoticeWrapper`.
*   `autoClose` (0): `number` - Автоматически закрывать уведомление через указанное количество миллисекунд. `0` - не закрывать.
*   `color` (null): `string` - Цвет уведомления. Возможные значения из кода: 'black', 'white', 'red', 'green', 'blue', 'yellow'. Задают соответствующий CSS-класс.
*   `stack` (true): `boolean` - Располагать уведомления стопкой (одно под другим).

## Методы

Экземпляр jBox предоставляет следующие публичные методы:

*   `open(options)`: Открывает jBox. Можно передать объект `options` для переопределения некоторых настроек для этого конкретного открытия (например, `content`, `target`, `url` для AJAX, `ignoreDelay`).
*   `close(options)`: Закрывает jBox. Опции: `ignoreDelay` (boolean), `waitForAttach` (boolean), `ignoreOnClose` (boolean).
*   `toggle(options)`: Переключает состояние jBox (открыт/закрыт).
*   `setContent(content, ignore_positioning = false)`: Устанавливает новый контент. Если `ignore_positioning` равно `true`, jBox не будет автоматически перепозиционирован.
*   `getContent()`: Возвращает текущий HTML-контент jBox.
*   `setTitle(title, ignore_positioning = false)`: Устанавливает новый заголовок.
*   `getTitle()`: Возвращает текущий заголовок.
*   `setFooter(footer, ignore_positioning = false)`: Устанавливает новый футер.
*   `getFooter()`: Возвращает текущий HTML футера.
*   `setDimensions(type, value, pos = true)`: Устанавливает ширину (`type = 'width'`) или высоту (`type = 'height'`). `value` - новое значение, `pos` - перепозиционировать ли после изменения (по умолчанию `true`).
*   `setWidth(value, pos = true)`: Алиас для `setDimensions('width', value, pos)`.
*   `setHeight(value, pos = true)`: Алиас для `setDimensions('height', value, pos)`.
*   `position(options)`: Принудительно перепозиционирует jBox. Можно передать опции для коррекции позиционирования.
*   `attach(elements, trigger)`: Прикрепляет jBox к новым DOM-элементам. `elements` - jQuery селектор или объект, `trigger` - событие.
*   `detach(elements)`: Открепляет jBox от указанных DOM-элементов.
*   `disable()`: Временно отключает jBox. Он не будет открываться.
*   `enable()`: Включает ранее отключенный jBox.
*   `destroy()`: Полностью удаляет экземпляр jBox, его HTML из DOM и отвязывает все события.
*   `trigger(event, options)`: Программно вызывает событие (`trigger`) на прикрепленных элементах.
*   `ajax(userOptions)`: Выполняет AJAX-запрос с указанными опциями (переопределяя стандартные AJAX-опции jBox).
*   `cancelAjax()`: Отменяет текущий активный AJAX-запрос.
*   `audio(url, volume)`: Проигрывает аудиофайл. `url` - путь к файлу (без расширения, jBox попытается загрузить .mp3 и .ogg), `volume` (0-100).
*   `animate(animation, options)`: Применяет к jBox одну из встроенных CSS-анимаций. `animation` - имя анимации, `options` - объект с `{ element: jQuery_element_to_animate, complete: callback_function }`.

**Методы, специфичные для `jBoxImage` (предполагаемые, требуют проверки в `jBoxImageWrapper`):**
*   `nextImage()`
*   `prevImage()`
*   `showImage(index)` или `jumpto(index)`

## События (Callbacks)

События задаются как функции обратного вызова в объекте опций при инициализации:

*   `onInit: function() {}`: Вызывается после инициализации экземпляра jBox. `this` ссылается на экземпляр jBox.
*   `onAttach: function(element) {}`: Вызывается, когда jBox прикрепляется к элементу (`element` - прикрепленный jQuery элемент).
*   `onPosition: function() {}`: Вызывается после каждого позиционирования jBox.
*   `onCreated: function() {}`: Вызывается после создания HTML-структуры jBox в DOM.
*   `onOpen: function() {}`: Вызывается при открытии jBox.
*   `onClose: function() {}`: Вызывается при закрытии jBox.
*   `onCloseComplete: function() {}`: Вызывается после полного завершения анимации закрытия.
*   `onDragStart: function() {}`: Вызывается в начале перетаскивания (если `draggable: true`).
*   `onDragEnd: function() {}`: Вызывается по завершении перетаскивания.

**События, специфичные для `jBoxImage` (предполагаемые):**
*   `onImageLoad: function(imageData) {}`: Вызывается после загрузки изображения.
*   `onChangeImage: function(newImageData, oldImageData) {}`: Вызывается при смене изображения в галерее.

## CSS Классы для темизации

jBox использует следующие основные CSS-классы, которые можно использовать для кастомизации внешнего вида:

*   `.jBox-wrapper`: Основной элемент-обертка.
*   `.jBox-[type]`: Класс, зависящий от типа (например, `.jBox-Modal`, `.jBox-Tooltip`). Добавляется к `.jBox-wrapper`.
*   `.jBox-[theme]`: Класс темы (например, `.jBox-Default`, `.jBox-CustomTheme`). Добавляется к `.jBox-wrapper`.
*   `.jBox-container`: Внутренний контейнер для всего содержимого.
*   `.jBox-content`: Область для основного контента.
*   `.jBox-title`: (Если заголовок создан) Контейнер для заголовка.
*   `.jBox-footer`: (Если футер создан) Контейнер для футера.
*   `.jBox-closeButton`: Кнопка закрытия.
*   `.jBox-overlay`: Оверлей (полупрозрачный фон).
*   `.jBox-pointer`: "Указатель" (стрелка) для тултипов.
*   `.jBox-pointer-top`, `.jBox-pointer-bottom`, `.jBox-pointer-left`, `.jBox-pointer-right`, `.jBox-pointer-center`: Классы для позиционирования указателя.
*   Анимационные классы: Например, `.jBox-animated-pulse`, `.jBox-animation-zoomIn-open`. Точные имена могут зависеть от типа анимации и состояния (open/close).

## Примечание

Эта документация была составлена путем анализа исходного кода jBox (`jBox.js`, `jBox.all.js`), найденного в вашем проекте. Она может не полностью соответствовать официальной документации последней версии jBox, если таковая существует. Функциональность расширений (`jBoxImageWrapper`, `jBoxConfirmWrapper`, `jBoxNoticeWrapper`) была частично выведена на основе стандартных опций и общих практик для подобных плагинов, так как их детальный код не был предметом глубокого анализа в рамках данного запроса. Для точной информации по этим расширениям рекомендуется изучить их исходный код в файле `jBox.all.js` или соответствующем файле-обертке. 