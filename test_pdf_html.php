<?php

/**
 * Тестирование HTML версии PDF для проверки исправлений
 */

// Подключаем Drupal
define('DRUPAL_ROOT', getcwd());
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Принудительно устанавливаем английский язык и домен
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REQUEST_URI'] = '/PhuketPdf/1001/guest/debug';
$GLOBALS['language']->language = 'en';

echo "=== ТЕСТИРОВАНИЕ HTML ВЕРСИИ PDF ===\n\n";

// Подключаем функцию генерации PDF
require_once 'sites/all/modules/__s45/s45_phuket/s45_phuket_pdf.inc';

// Имитируем аргументы URL
$_GET['q'] = 'PhuketPdf/1001/guest/debug';

// Устанавливаем аргументы для функции arg()
$GLOBALS['_drupal_path'] = 'PhuketPdf/1001/guest/debug';

echo "Генерируем HTML версию PDF...\n";

try {
    // Вызываем функцию генерации PDF в режиме debug
    $html = s45_phuket_pdf();
    
    echo "✓ HTML успешно сгенерирован\n";
    echo "Длина HTML: " . strlen($html) . " символов\n\n";
    
    // Проверяем наличие изображений в HTML
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches);
    
    if (!empty($matches[1])) {
        echo "Найдено изображений: " . count($matches[1]) . "\n";
        
        foreach ($matches[1] as $i => $src) {
            echo "  Изображение " . ($i + 1) . ": " . $src . "\n";
            
            // Проверяем корректность URL
            if (strpos($src, 'http://.') !== false) {
                echo "    ⚠ ПРОБЛЕМА: Некорректный URL с 'http://.'\n";
            } elseif (strpos($src, 'public://') !== false) {
                echo "    ⚠ ПРОБЛЕМА: URL содержит 'public://'\n";
            } else {
                echo "    ✓ URL выглядит корректно\n";
            }
        }
    } else {
        echo "⚠ Изображения не найдены в HTML\n";
    }
    
    echo "\n";
    
    // Сохраняем HTML в файл для просмотра
    $test_file = 'test_pdf_output.html';
    file_put_contents($test_file, $html);
    echo "HTML сохранен в файл: {$test_file}\n";
    echo "Вы можете открыть его в браузере для проверки\n";
    
} catch (Exception $e) {
    echo "✗ ОШИБКА при генерации HTML: " . $e->getMessage() . "\n";
    echo "Трассировка:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== КОНЕЦ ТЕСТИРОВАНИЯ ===\n";
