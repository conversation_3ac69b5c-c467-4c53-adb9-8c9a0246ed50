---
type: "always_apply"
---

# Оптимизация и безопасность системы

<rule>
name: system_caching_strategy
description: Стратегия кэширования в системе
filters:
  - type: file_content
    pattern: "cache_get|cache_set"
actions:
  - type: suggest
    message: |
      ## Стратегия кэширования системы
      
      - **Множественные уровни кэша:**
        - Для AR/DTO кэширование результатов запросов с TTL 3600 секунд (1 час)
        - Для часто используемых сущностей (например, популярные локации) - кэш с TTL 86400 секунд (24 часа)
        - Для результатов поиска - кэширование на уровне запросов с временными метками изменений
      
      - **Ключи кэша:**
        - Для объектов недвижимости: `phuket_property_<ID>`
        - Для объектов по номеру: `phuket_property_num_<NUMBER>`
        - Для историй цен: `phuket_price_histogram_<DEAL_TYPE>`
        - Для популярных локаций: `phuket_popdirs<DEAL_TYPE>`
      
      - **Инвалидация кэша:**
        - Автоматическая - через механизм TTL (тайм-ту-лив)
        - Через Cron-задачи (например, для гистограмм цен)
        - При сохранении объектов - через хуки `handle<ARName><EventName>()`
      
      - **Использование Drupal Cache API:**
        ```php
        // Чтение из кэша с проверкой существования
        $cache_key = 'phuket_property_' . $id;
        $cache = cache_get($cache_key);
        
        if ($cache && !empty($cache->data)) {
          return $cache->data;
        }
        
        // Запись в кэш с TTL
        cache_set($cache_key, $data, 'cache', time() + 3600);
        ```
</rule>

<rule>
name: async_processing
description: Асинхронная обработка и Cron
filters:
  - type: file_content
    pattern: "hook_cron|_cron\\(\\)"
actions:
  - type: suggest
    message: |
      ## Асинхронная обработка данных
      
      - **Cron система:**
        - Модуль `s45_phuket` реализует `hook_cron()` для периодических задач
        - Каждые 24 часа обновляется кэш гистограмм цен через `PropertyPriceHistogram::getHistogram()`
        - Генерация карты сайта (sitemap) выполняется также раз в сутки
      
      - **Переменные для отслеживания:**
        - `variable_get('s45_phuket_histogram_last_update', 0)` - последнее обновление гистограмм
        - `variable_get('s45_phuket_last_sitemap_update', 0)` - последняя генерация sitemap
      
      - **Read Model обновления:**
        - Обновление read-моделей происходит синхронно через обработчики `handle<ARName><EventName>()`
        - Нет встроенного механизма очередей для асинхронного обновления проекций
        - Для оптимизации можно использовать Drupal Queue API:
          ```php
          // Добавление в очередь
          $queue = DrupalQueue::get('s45_event_handlers');
          $queue->createItem(array('event_id' => $event_id));
          
          // Обработка в hook_cron
          $queue = DrupalQueue::get('s45_event_handlers');
          while($item = $queue->claimItem()) {
            // Обработка события
            $queue->deleteItem($item);
          }
          ```
</rule>

<rule>
name: security_models
description: Модели безопасности и разграничение доступа
filters:
  - type: file_content
    pattern: "s45_check_access|user_access"
actions:
  - type: suggest
    message: |
      ## Система безопасности
      
      - **Двухуровневая модель доступа:**
        - Стандартный Drupal-механизм `user_access()` для базовых прав
        - Кастомный механизм `s45_check_access()` для гранулярных прав к компонентам
      
      - **Определенные константы доступа:**
        ```php
        define('S45_ACCESS_PROPERTY_EDIT_GROUP', 'S45_ACCESS_PROPERTY_EDIT_GROUP');
        define('S45_ACCESS_CONTENT_EDIT_GROUP', 'S45_ACCESS_CONTENT_EDIT_GROUP');
        define('S45_ACCESS_COMPO_EDIT', 'S45_ACCESS_COMPO_EDIT');
        define('S45_ACCESS_COMPO_DELETE', 'S45_ACCESS_COMPO_DELETE');
        define('S45_ACCESS_SYSLOG', 'S45_ACCESS_SYSLOG');
        define('S45_ACCESS_USERS_EDIT', 'S45_ACCESS_USERS_EDIT');
        ```
      
      - **Роли в системе:**
        - `s45guest` - базовый доступ к API (основная страница сайта)
        - `s45admin` - административный доступ к системе
        - `admin` - доступ к управлению всем контентом
        - `agent` - доступ к редактированию недвижимости
        - `content` - доступ к редактированию страниц и статей
      
      - **Проверка прав в коде:**
        ```php
        // Проверка кастомного права
        if (s45_check_access(S45_ACCESS_PROPERTY_EDIT_GROUP)) {
          // разрешить доступ
        }
        
        // Проверка стандартного права Drupal
        if (user_access('s45admin')) {
          // разрешить доступ
        }
        ```
</rule>

<rule>
name: ajax_interaction
description: Модель AJAX-взаимодействия с сервером
filters:
  - type: file_content
    pattern: "jquery.ajax|\\$.ajax|\\$\\.post"
actions:
  - type: suggest
    message: |
      ## AJAX-взаимодействие с сервером
      
      - **Базовый маршрут API:**
        - `/compo45/{pageId}/{componentId}/{methodName}` - основная точка входа для AJAX-вызовов
      
      - **Серверная обработка:**
        - `s45_compo_api()` - центральный метод обработки AJAX запросов
        - Использует `CompoApi2::exec()` для вызова методов компонентов
        - Анализирует данные из `$_POST`, `$_GET`, `$_FILES`
        - Возвращает результат через `drupal_json_output()`
      
      - **Клиентская часть:**
        ```javascript
        // Стандартный шаблон AJAX-запроса
        jQuery.ajax({
            url: '/compo45/' + pageId + '/' + componentId + '/' + methodName,
            type: 'POST',
            data: formData,
            success: function(response) {
                // Обработка успешного ответа
            },
            error: function(xhr, status, error) {
                // Обработка ошибки
            }
        });
        ```
      
      - **Логирование запросов:**
        - Все API-вызовы логируются через `Logger::add()` с данными о запросе и ответе
        - Лог включает информацию о пользователе, HTTP-методе, входных данных и ответе
</rule>

<rule>
name: performance_optimization
description: Оптимизация производительности системы
filters:
  - type: file_content
    pattern: "drupal_add_js|drupal_add_css"
actions:
  - type: suggest
    message: |
      ## Оптимизация производительности
      
      - **Оптимизация CSS/JS:**
        - Использование `drupal_add_js()` с параметрами `'scope' => 'footer'` и `'preprocess' => TRUE`
        - Подключен модуль `advagg` для агрегации и минификации статических ресурсов
        - Обработчик ошибок изображений загружается в конце страницы с высоким весом
      
      - **Оптимизация запросов:**
        - Использование денормализованных таблиц (`_phuket_Property`) для быстрого поиска
        - Сериализованные DTO для хранения полного состояния объектов
        - Кэширование часто используемых данных
      
      - **Оптимизация изображений:**
        - Система стилей изображений для разных сценариев использования
        - Ленивая загрузка изображений через JS
        - Кэширование внешних изображений локально
      
      - **Общие рекомендации:**
        - Добавление индексов для полей фильтрации и сортировки
        - Оптимизация Event Sourcing через кэширование и асинхронную обработку
        - Использование Drupal Queue API для фоновых задач
</rule>
