---
type: "always_apply"
---

# Internal s45 Themes Variant 2 Cursor Rules

<rule>
name: s45_theme2_hook_theme
description: Validate hook_theme2 implementations for theme variant 2
filters:
  - type: file_content
    pattern: "function [a-z0-9_]+_theme2\\s*\\("
actions:
  - type: suggest
    message: |
      - Реализация `hook_theme2()`: ключи массива должны соответствовать названиям тем с суффиксом `2`.
      - Убедитесь в правильном пути к шаблону и совпадении параметров `render element` / `arguments`.
      - Документируйте каждую тему через PHPDoc.
</rule>

<rule>
name: s45_theme2_component_class
description: Structure rules for variant 2 theme component classes
filters:
  - type: file_content
    pattern: "class .+2 extends .+Compo"
actions:
  - type: suggest
    message: |
      - Классы компонентов для темы 2 имеют суффикс `2`.
      - Наследуйте от `Site45\\Compo\\Compo`.
      - Объявите публичные свойства и принимайте их через `beforeRender($props)`.
      - Документируйте отличие от обычных компонентов в PHPDoc.
</rule>

<rule>
name: s45_theme2_templates
description: Template files for variant 2 themes
filters:
  - type: file_path
    pattern: ".*2\\.tpl\\.php$"
actions:
  - type: suggest
    message: |
      - Файлы шаблонов с суффиксом `2`: имя файла соответствует имени темы + `2`.
      - Экранируйте все выводимые данные через `check_plain()` или `filter_xss()`.
      - Используйте `drupal_attributes()` и `l()` для ссылок.
      - Убедитесь, что шаблон зарегистрирован в `hook_theme2()`.
</rule>

<rule>
name: s45_theme2_assets
description: JS/CSS assets for variant 2 themes
filters:
  - type: file_path
    pattern: ".*2\\.(js|css)$"
actions:
  - type: suggest
    message: |
      - Названия файлов с суффиксом `2` соответствуют теме (например `theme2.js`).
      - Для JS используйте ES6+ и модули/IIFE, документируйте через JSDoc.
      - Для CSS используйте неймспейс `.s45-theme2`. Избегайте глобальных сбросов.
      - Подключайте ассеты через `drupal_add_css()` / `drupal_add_js()`.
</rule>
