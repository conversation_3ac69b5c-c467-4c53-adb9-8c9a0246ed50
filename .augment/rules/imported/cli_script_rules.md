---
type: "agent_requested"
---

# Custom CLI Scripts Rules

<rule>
name: cli_script_security
filters:
  - type: file_path
    pattern: "(\.php|\.sh)$"
actions:
  - type: suggest
    message: |
      - CLI-скрипты должны использовать только безопасные методы работы с вводом (filter_var(), escapeshellarg(), check_plain()).
      - Не допускайте прямого доступа к $_GET/$_POST/$_REQUEST — только через безопасные обертки.
      - Всегда проверяйте file_exists перед операциями с файлами.
      - Для PHP используйте Drupal API для работы с БД и файлами.
      - Логируйте все критичные действия (file_put_contents, watchdog, error_log).
      - Документируйте назначение скрипта и параметры запуска.
      - Не храните пароли и ключи в коде — только через переменные окружения или settings.php.
</rule>
