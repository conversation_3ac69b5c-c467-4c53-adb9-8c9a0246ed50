---
type: "agent_requested"
---

# IndreamsPhuket SEO System Rules

## 📊 Архитектура SEO системы

<rule>
name: seo_database_structure
description: Структура базы данных SEO
filters:
  - type: file_content
    pattern: "_phuket_PageSeo|_phuket_Sitemap|_s45_aliases"
actions:
  - type: suggest
    message: |
      **База данных SEO:**
      
      ### _phuket_PageSeo (203 записи)
      - Хранит индивидуальные SEO настройки страниц
      - pageSeoDto содержит сериализованные данные: title, description, h1, keywords, canonical
      
      ### _phuket_Sitemap (430,560 записей)
      - Распределение по языкам: ru(171,908), th(96,837), zh-hans(95,086), en(66,729)
      - ПРОБЛЕМА: превышает лимит 50,000 URL на файл
      - РЕШЕНИЕ: разделить по типам контента
      
      ### _s45_aliases (18,488 записей)
      - SEO алиасы: по 4,622 на каждый язык (ru, en, th, zh-hans)
      - Система преобразования системных путей в красивые URL
</rule>

<rule>
name: seo_meta_tags_system
description: Система мета-тегов и hreflang
filters:
  - type: file_path
    pattern: "s45_phuket_seo.inc$"
actions:
  - type: suggest
    message: |
      **SEO Meta-теги система:**
      
      ### Основные функции:
      - `s45_phuket_html_head_alter()` - модификация HTML head
      - `s45_phuket_generate_hreflang()` - генерация hreflang для 4 языков
      - `s45_phuket_seo_setForPage()` - установка SEO из базы
      
      ### Мультиязычные домены:
      ```php
      'ru' => 'https://indreamsphuket.ru'
      'en' => 'https://indreamsphuket.com' 
      'th' => 'https://th.indreamsphuket.com'
      'zh-hans' => 'https://ch.indreamsphuket.com'
      ```
      
      ### Автоматические теги:
      - Canonical (без пагинации)
      - Hreflang + x-default (английский)
      - Meta description (из PageSeo или автоматически)
</rule>

<rule>
name: seo_url_system
description: Система SEO URL и алиасов
filters:
  - type: file_path
    pattern: "s45_path"
actions:
  - type: suggest
    message: |
      **SEO URL система:**
      
      ### Класс Path.php:
      - `getSysPath($alias)` - алиас → системный путь
      - `getAlias($sysPath)` - системный путь → SEO алиас
      - Мультисайтовая поддержка через siteId
      - Автоматическое разрешение конфликтов
      
      ### Редиректы:
      - `RedirectFromOldSite.php` - 301 редиректы со старого сайта
      - `s45_path_redirect_search()` - специальные редиректы поиска
      - Автоматическое удаление trailing slash
      
      ### Таблица _s45_aliases:
      - Первичный ключ: (siteId, alias, langCode)
      - 18,488 записей равномерно по языкам
</rule>

<rule>
name: sitemap_generation_system
description: Система генерации XML sitemap
filters:
  - type: file_path
    pattern: "sitemap|PhuketSitemap"
actions:
  - type: suggest
    message: |
      **XML Sitemap система:**
      
      ### Проблемы (КРИТИЧНО):
      - 430,560 URL в одном файле (лимит Google: 50,000)
      - Дубли URL с некорректными параметрами: `?amp%3Bpage=`
      - Неравномерное распределение по языкам
      
      ### Типы контента:
      - Properties, Projects, Articles, News, Services
      - Boats, Excursions, Locations, Static pages
      
      ### Особенности:
      - Проверка через robots.txt (`allowInRobots()`)
      - Hreflang теги для каждого URL
      - Автообновление при изменении контента
      
      ### РЕШЕНИЕ:
      ```php
      // Разделить на файлы:
      // sitemap_en_properties.xml (макс 50,000)
      // sitemap_en_projects.xml
      // sitemap_index.xml - главный индекс
      ```
</rule>

<rule>
name: robots_txt_system
description: Система управления robots.txt
filters:
  - type: file_path
    pattern: "robots|s45_base.robots.inc"
actions:
  - type: suggest
    message: |
      **Robots.txt система:**
      
      ### Функция s45_robots():
      ```php
      header('Content-Type: text/plain');
      $langCode = $GLOBALS['language']->language;
      $text = variable_get('s45_robots_'.$langCode);
      ```
      
      ### Текущий статус:
      - ✅ .ru домен: robots.txt работает
      - ✅ Sitemap указан: https://indreamsphuket.ru/sitemap_ru.xml
      - ✅ Либеральная политика индексации
      - ✅ Мониторинг разрешен (UptimeKuma, curl, wget)
      
      ### Переменные Drupal:
      - s45_robots_ru, s45_robots_en, s45_robots_th, s45_robots_zh-hans
</rule>

<rule>
name: seo_components_system
description: SEO компоненты и автогенерация
filters:
  - type: file_content
    pattern: "PhuketPageD2|PhuketLocationSeo|PhuketProjectSeo|addSeo"
actions:
  - type: suggest
    message: |
      **SEO Компоненты:**
      
      ### Базовые классы:
      - `PhuketPageD2` - базовый SEO компонент
      - `PhuketLocationSeo` - SEO для локаций  
      - `PhuketProjectSeo` - SEO для проектов
      - `PhuketSearchSeo` - SEO для поиска
      
      ### Автогенерация:
      - **Title**: название + локация + тип сделки
      - **Description**: описание объекта (160 символов)
      - **H1**: настраиваемый заголовок
      - **Keywords**: автоматически по параметрам
      
      ### Кэширование:
      - `phuket_property_seo_{id}_{lang}` - кэш SEO данных свойств
      - Статический кэш переводов путей
      - Кэш запросов PageSeo (300 записей)
</rule>

<rule>
name: seo_performance_optimization
description: Оптимизация производительности SEO
filters:
  - type: file_content
    pattern: "cache_get|cache_set|PhuketPageSeoQuery"
actions:
  - type: suggest
    message: |
      **Производительность SEO:**
      
      ### Текущие проблемы:
      - PageSeoQuery загружает 300 записей за раз
      - Нет индексов в _phuket_Sitemap по (langCode, created)
      - Статический кэш переводов может расти неограниченно
      
      ### Рекомендации:
      ```sql
      -- Добавить индексы
      ALTER TABLE _phuket_Sitemap ADD INDEX idx_lang_created (langCode, created);
      
      -- Очистить дубли
      DELETE FROM _phuket_Sitemap WHERE url LIKE '%amp%3B%';
      ```
      
      ### Кэширование:
      - Использовать Redis для переводов путей
      - Добавить кэш в PhuketPageSeoQuery::exec()
      - Оптимизировать загрузку SEO данных
</rule>

<rule>
name: multilingual_seo_domains
description: Мультиязычная SEO структура
filters:
  - type: file_content
    pattern: "indreamsphuket\.(ru|com)|th\.indreamsphuket|ch\.indreamsphuket"
actions:
  - type: suggest
    message: |
      **Мультиязычная структура:**
      
      ### Домены и языки:
      - 🇷🇺 **indreamsphuket.ru** → ru (171,908 URL)
      - 🇬🇧 **indreamsphuket.com** → en (66,729 URL) 
      - 🇹🇭 **th.indreamsphuket.com** → th (96,837 URL)
      - 🇨🇳 **ch.indreamsphuket.com** → zh-hans (95,086 URL)
      
      ### Проблема распределения:
      - Русский: 40% (слишком много)
      - Английский: 15.5% (мало для основного)
      
      ### Hreflang реализация:
      - x-default указывает на английский
      - Все языки перекрестно ссылаются
      - Автоматический перевод путей между языками
</rule>

<rule>
name: seo_monitoring_and_debugging
description: Мониторинг и отладка SEO
filters:
  - type: file_content
    pattern: "watchdog|s45_dsm|drupal_set_message"
actions:
  - type: suggest
    message: |
      **Мониторинг SEO:**
      
      ### Логирование:
      - `watchdog('sitemap', $result)` - логи генерации sitemap
      - `watchdog('s45_phuket_seo', 'Error translating path')` - ошибки переводов
      - `watchdog('property_404', 'Объект не найден')` - 404 ошибки
      
      ### Отладка:
      - `s45_dsm($GLOBALS['s45']['PageSeo'])` - отладка SEO данных
      - Проверка robots.txt через `allowInRobots()`
      - Валидация sitemap через `RobotsTxtValidator`
      
      ### Инструменты:
      - `/sitemapgen?key=qqww33` - генерация sitemap
      - `/s45_robots_settings` - настройки robots.txt
      - Google Search Console, Яндекс Вебмастер для мониторинга
</rule>

## 🎯 Быстрые команды для работы с SEO

### Проверка sitemap:
```bash
curl -I https://indreamsphuket.ru/sitemap_ru.xml
curl -s https://indreamsphuket.ru/sitemap.xml | head -20
```

### Проверка robots.txt:
```bash
curl -s https://indreamsphuket.ru/robots.txt | grep -i sitemap
```

### SQL запросы для анализа:
```sql
-- Статистика sitemap по языкам
SELECT langCode, COUNT(*) FROM _phuket_Sitemap GROUP BY langCode;

-- Поиск дублей URL
SELECT url, COUNT(*) FROM _phuket_Sitemap GROUP BY url HAVING COUNT(*) > 1;

-- SEO страницы
SELECT COUNT(*) FROM _phuket_PageSeo;
```

### Drupal команды:
```bash
drush vget s45_robots_ru
drush cc all  # очистка кэша после изменений SEO
```
