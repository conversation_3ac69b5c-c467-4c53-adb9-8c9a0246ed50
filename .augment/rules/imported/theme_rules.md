---
type: "agent_requested"
---

# Theme Hooks and Templates Cursor Rules

<rule>
name: hook_theme_impl
description: Validate hook_theme implementations across modules
filters:
  - type: file_content
    pattern: "function [a-z0-9_]+_theme\s*\("
actions:
  - type: suggest
    message: |
      - Найдите реализацию `hook_theme()`: функция возвращает массив тем для модуля.
      - Убедитесь, что ключи массива совпадают с именами тем и описанием шаблонов.
      - Проверьте наличие шаблонных файлов (`*.tpl.php`) по указанному пути.
      - Убедитесь в корректности параметров `render element` или `arguments`.
      - Документируйте шаблон, перечислив передаваемые переменные.
</rule>

<rule>
name: template_file_existence
description: Ensure template files exist and use safe output
filters:
  - type: file_path
    pattern: "\.tpl\.php$"
actions:
  - type: suggest
    message: |
      - Убедитесь, что каждое шаблонное (`.tpl.php`) лежит в правильной папке модуля.
      - Проверьте, что шаблон соответствует теме, зарегистрированной hook_theme().
      - Экранируйте все выводимые переменные (`check_plain()`, `filter_xss()`).
      - Используйте `drupal_attributes()` для атрибутов и `l()` для ссылок.
</rule>
