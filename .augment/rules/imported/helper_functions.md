---
type: "always_apply"
---

# Helper Functions Cursor Rules

<rule>
name: s45_debug_functions
description: Debugging utilities (s45_dsm, s45_dpq)
filters:
  - type: file_content
    pattern: "function s45_dsm"
actions:
  - type: suggest
    message: |
      - `s45_dsm($obj, $mes)` выводит отладочную информацию только для пользователя uid=1.
      - `s45_dpq($query)` выводит SQL-запросы для отладки.
      - Убедитесь, что вызовы удаляются в продакшене или защищены проверками прав.
</rule>

<rule>
name: s45_url_helper
description: URL alias helper (s45_url)
filters:
  - type: file_content
    pattern: "function s45_url"
actions:
  - type: suggest
    message: |
      - Используйте `s45_url($sysPath, $options)` для генерации URL с алиасами.
      - Возвращает `s45_path_url()` или `url()` по умолчанию.
      - Передавайте массив `$options` с параметрами `query`, `absolute` и др.
</rule>

<rule>
name: s45_setAgent
description: Mobile/Desktop agent detection and redirection
filters:
  - type: file_content
    pattern: "function s45_setAgent"
actions:
  - type: suggest
    message: |
      - `s45_setAgent($siteConfDto)` определяет агент по HTTP_USER_AGENT и устанавливает cookie 's45_agent'.
      - Выполняет перенаправление на m.domain для мобильных пользователей.
      - Добавьте HTTPS и Secure-флаги для cookie.
</rule>

<rule>
name: s45_guid
description: GUID generation utility
filters:
  - type: file_content
    pattern: "function s45_guid"
actions:
  - type: suggest
    message: |
      - `s45_guid($arName)` генерирует pseudo-GUID на базе времени и случайных чисел.
      - Убедитесь в уникальности и соответствии ожидаемому формату.
</rule>

<rule>
name: s45_attr
description: HTML attribute builder
tags: attr
description: Wrapper for drupal_attributes()
filters:
  - type: file_content
    pattern: "function s45_attr"
actions:
  - type: suggest
    message: |
      - `s45_attr($attr)` возвращает строку атрибутов HTML через `drupal_attributes()`.
      - Используйте для безопасного вывода атрибутов `class`, `data-*` и т.д.
</rule>

<rule>
name: s45_img_functions
description: Image URL helpers (s45_imgSrcR, s45_imgSrc, s45_inreams_old_file_name)
filters:
  - type: file_content
    pattern: "function s45_imgSrcR"
actions:
  - type: suggest
    message: |
      - `s45_imgSrcR($fileDto, $styleName)` возвращает URL изображения с поддержкой внешних ссылок.
      - `s45_imgSrc($fileDto, $styleName)` вызывает `s45_imgSrcR`.
      - `s45_inreams_old_file_name($url)` генерирует уникальное имя файла для внешних URL.
      - Проверьте `file_exists()`, используйте fallback или логирование отсутствующих файлов.
</rule>

<rule>
name: s45_conversion_helpers
description: Data conversion utilities (s45_toObject, s45_toArray)
filters:
  - type: file_content
    pattern: "function s45_toObject"
actions:
  - type: suggest
    message: |
      - `s45_toObject($param)` конвертирует массив/объект в `stdClass` через JSON.
      - `s45_toArray($param)` возвращает ассоциативный массив.
      - Используйте для нормализации входных данных перед передачей в компоненты и AR.
</rule>

<rule>
name: s45_addAllRes
description: Dynamic inclusion of component resources
filters:
  - type: file_content
    pattern: "function s45_addAllRes"
actions:
  - type: suggest
    message: |
      - `s45_addAllRes()` автоматически подключает JS/CSS всех активных компонентов.
      - Оптимизируйте производительность: включайте только на страницах с компонентами.
</rule>

<rule>
name: s45_render
description: Component rendering helper
filters:
  - type: file_content
    pattern: "function s45_render"
actions:
  - type: suggest
    message: |
      - `s45_render($compoId, $props)` вызывает `CompoApi2::exec()` для команды `GetRendered`.
      - Возвращает HTML компонента или `null`.
      - Обеспечьте безопасность: экранируйте данные и проверяйте права доступа.
</rule>

<rule>
name: s45_site_helpers
description: Site configuration utilities (s45_getSiteId, s45_getSiteDir)
filters:
  - type: file_content
    pattern: "function s45_getSiteId"
actions:
  - type: suggest
    message: |
      - `s45_getSiteId()` возвращает ID сайта через `SiteConfLoadQuery`.
      - `s45_getSiteDir()` возвращает файловый путь `public://FileStore4/<siteId>`.
      - Используйте для построения путей к файловому хранилищу и репозиториям.
</rule>

<rule>
name: s45_testText_and_s45_lang
description: Text testing and multilingual helpers
tags: i18n
filters:
  - type: file_content
    pattern: "function s45_testText"
actions:
  - type: suggest
    message: |
      - `s45_testText($len, $langCode)` генерирует тестовый текст заданной длины.
      - `s45_lang(&$langObject, $alter, $langCode, $testTextLen)` извлекает перевод с fallback на `und`, `en`.
      - Предотвращайте вывод '????', обеспечьте корректную замену default-значений.
</rule>
