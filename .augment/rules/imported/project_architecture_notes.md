---
type: "agent_requested"
---

# Заметки по Архитектуре Проекта (S45 Framework)

Этот документ суммирует ключевые архитектурные паттерны и концепции, используемые в проекте, а также отмечает области, требующие дальнейшего изучения или прояснения.

## 1. Event Sourcing (ES) и Read Models
*   **Основа:** Изменения состояния основных сущностей (Недвижимость, Проекты и т.д.) хранятся как последовательность событий в таблице `_s45_events`.
*   **Ключевые компоненты:**
    *   `AR` (Active Record, например `PhuketPropertyAR`): Сущности, генерирующие события.
    *   `EventStore`: Класс (`s45_base/classes/Site45/Event/EventStore.php`) для добавления событий в `_s45_events`. Управляет флагом `isLast` (устанавливает `isLast=0` для старых событий и `isLast=1` для нового внутри транзакции `db_transaction()`).
    *   `EventQuery`: Класс (`s45_base/classes/Site45/Event/EventQuery.php`) для извлечения событий. Используется для восстановления состояния AR (часто только последнее событие с `isLast=1`) и для обновления Read Models.
    *   `QueryFromEvents` (базовый класс, например, для `PhuketPropertyQuery` в `s45_base/classes/Site45/Base/QueryFromEvents.php`): Отвечает за обновление денормализованных таблиц (Read Models).
        *   **Жизненный цикл обновления Read Model:**
            *   При создании экземпляра класса, унаследованного от `QueryFromEvents` (например, `PhuketPropertyQuery::create()`), автоматически запускается метод `eventsHandle()`.
            *   `eventsHandle()` считывает ID последнего обработанного события для данного типа Query из переменной Drupal (`variable_get('s45_counter_ИмяКлассаЗапроса')`).
            *   Запрашиваются все *новые* события (с ID больше сохраненного) для ассоциированных `arName` из `_s45_events`.
            *   Для каждого нового события вызывается соответствующий метод-обработчик в классе Query (например, `handlePhuketPropertySaved(EventDto $event)`).
            *   После обработки ID последнего нового события сохраняется обратно в `variable_get()`.
            *   **Важно:** Обновление Read Model происходит синхронно при создании объекта Query, а не по Cron или через прямой триггер от `EventStore`. Частота обновления зависит от частоты вызовов `Query::create()`.
*   **Read Models:** Денормализованные таблицы (например, `_phuket_Property`) для быстрого чтения данных. Обновляются методами-обработчиками в классах `QueryFromEvents`.

## 2. Компоненты (Compo)
*   **Основа:** UI строится из компонентов (PHP класс + `.tpl.php` шаблон).
*   **Класс компонента (`Compo.php`):**
    *   Наследуется от `Site45\Compo\Compo` (`s45_base/classes/Site45/Compo/Compo.php`).
    *   `apiGetRendered($props)`: Основной метод для получения HTML компонента. Вызывает `beforeRender($props)` (если есть), затем `getCompoHtmlFull()` (который вызывает `getCompoHtml()` для рендеринга TPL) и `getCompoRes()` для получения CSS/JS.
    *   `beforeRender($props)`: Метод для подготовки данных перед рендерингом. `$props` преобразуются в объект с помощью `s45_toObject()`.
    *   `getCompoHtml()`: Выполняет `include` `.tpl.php` файла, используя `ob_start()`/`ob_get_clean()`.
    *   `getCompoRes()`: Определяет стандартные (`Compo.js`, `Compo.css`) и специфичные для компонента (`<CompoName>.js`, `<CompoName>.css`) ресурсы.
*   **`CompoRepo.php` (`s45_base/classes/Site45/Base/CompoRepo.php`):**
    *   Отвечает за загрузку и сохранение конфигурации компонентов.
    *   `load($id)`: Загружает данные компонента в следующем порядке приоритета:
        1.  Из кэша `$GLOBALS['AllCompoData']` (если уже загружено в текущем запросе). Этот кэш инициализируется однократно вызовом `loadAllCompoDataFromEvents()`, который загружает **последние события** для всех компонентов (`arName='Compo'`, `isLast=1`). Источник: `confFrom = 'event'`).
        2.  Из JSON-файла конфигурации компонента (например, `S45_SITES_DIR/<site_id>/_repo/Compo.s45.json`). Имя файла может зависеть от `variable_get('s45_phuket_config_file')`. Источник: `confFrom = 'repo'`.
        3.  Если не найдено, создаются данные по умолчанию из класса компонента. Источник: `confFrom = 'class'`.
    *   `save(Compo $compo)`: Сохраняет конфигурацию компонента, создавая новое событие в `EventStore` (`arName='Compo'`, `type='Saved'`). Перед сохранением удаляет из объекта компонента все свойства, не являющиеся частью конфигурации.
*   **Шаблоны (`.tpl.php`):** Используют PHP для вывода. Доступ к данным компонента через `$this`.
*   **Регистрация и сканирование:**
    *   `CompoInfo.s45.json` (в `S45_SITES_DIR/<site_id>/_repo/`): Хранит информацию о компонентах, включая путь к директории (`dir`). Используется `CompoRepo` и `s45_addAllRes()`.
    *   `CompoScanner.php`: Сканирует директории и обновляет `CompoInfo.s45.json`.

## 3. Управление конфигурацией и сайтами
*   **`SiteConf.php` (`s45_base/classes/Site45/Base/SiteConf.php`):**
    *   Определяет текущий сайт на основе `$_SERVER['HTTP_HOST']`.
    *   Загружает конфигурацию из `S45_SITES` (константа, указывающая на `s45_base/_repo/Sites.s45.json`).
    *   `SiteConfDto.php`: DTO для данных конфигурации сайта.
    *   `SiteConfLoadQuery.php`: Query-класс для загрузки `SiteConfDto`.
*   **Константы путей:**
    *   `S45_SITES_DIR`: Определена в `s45_base.module` как `public://FileStore4`. Это корневая директория для файлов конкретных сайтов.
    *   `S45_COMPO_DIR`: Директория с базовыми файлами компонента (`Compo.php`, `Compo.js`, `Compo.css`).
    *   `S45_RES_DIR`: Путь к ресурсам "Темы 1" (CSS).

## 4. Работа с файлами
*   **Загрузка:** Компонент `FormImage` (`s45_base/classes/Site45/Sets/Form/Compo/FormImage/`) управляет загрузкой изображений.
    *   Файлы временно сохраняются в `Store::create('UPLOADS')` с ключом `<arId>__<fieldName>`.
    *   Физически файлы перемещаются в `S45_SITES_DIR/<site_id>/files/tmp/` с помощью `move_uploaded_file()`.
    *   Данные о файлах (включая `dir='tmp'`) передаются в основную форму через скрытые поля в `FormImage.tpl.php`.
*   **Удаление временных файлов:**
    *   В `FormImage::apiDelete()` есть закомментированный `unlink`. **Явного механизма регулярной очистки `S45_SITES_DIR/<site_id>/files/tmp/` или `.../files/old/` в кастомном коде `__s45` не обнаружено.**
    *   Сторонние библиотеки (например, mPDF) могут иметь свою логику очистки своих временных директорий.
    *   Скрипт `download_property_photos.php` удаляет свою временную директорию (`temporary://...`) после использования.
*   **DTO для файлов:** `FileDto.php` (`s45_base/classes/Site45/DtoLib/Base/FileDto.php`).
*   **Функции для URL изображений:**
    *   `s45_imgSrcR(&$fileDto, $styleName)` и `s45_imgSrc()` в `s45_base.lib.inc` генерируют URL для изображений, используя Drupal Image Styles. Поддерживают файлы из `FileStore4` (включая `tmp/` и `old/`), а также внешние URL (которые кэшируются в `old/`).

## 5. JavaScript
*   **Инициализация компонентов:**
    *   `s45_InitAll()` (в `Compo.js`): Инициирует все компоненты на странице.
    *   `s45_InitById(compoId)`: Ищет и вызывает специфичную для компонента JS-функцию инициализации: `window[compoName+'_init'](mdc:compoId)` или, если ее нет, `window[compoName].initById(compoId)`.
*   **API вызовы:**
    *   `s45_api(compoId, command, data)` (глобальная функция в `Compo.js`).
    *   `Compo45.api(command, data, success, error)` (метод прототипа в `Compo.js`).
    *   URL для API: `current_url/compo45/<pageId>/<compoId>/<command>`.
*   **Основные библиотеки (подключаются через `s45_vendor.module` и `s45_add_*` функции):**
    *   jQuery (основная зависимость)
    *   jBox (модальные окна, уведомления)
    *   Owl Carousel (слайдеры)
    *   Fancybox (галереи изображений)
    *   jQuery UI (включая Autocomplete для поиска)
    *   И другие, такие как `jquery.sticky`, `jquery.inputmask`, `datepicker`, `select2`.

## 6. CSS и Темы
*   **Тема 1 (стандартная):**
    *   Базовые CSS: `reset.css`, `vars.css`, `layout.css`, `typo.css` из `S45_RES_DIR` (`s45_base/classes/Site45/Sets/Test1/Res/css/`).
    *   Стили компонентов: `<CompoName>.css` в директории компонента.
    *   Тема Drupal `site45` (`sites/all/themes/site45/`) имеет минимальный `site45.info` и `template.php`, который в основном отключает системные CSS Drupal и может содержать специфичные `preprocess` функции. CSS файлы темы самой по себе не объявляются и не используются активно.
*   **Тема 2 (D2):**
    *   Активируется через `s45_add_theme2()` (в `s45_vendor.module`).
    *   Базовый CSS: `sites/all/modules/__s45/s45_vendor/vendor/s45_theme2/css/` (включает `reset.css`, `vars.css` (с rem и CSS переменными), `bs4_grid_rem.css` (Bootstrap 4 сетка на rem), `bs4_embed.css`).
    *   Компоненты для Темы 2 часто имеют суффикс `D2` или `2` в названии класса/файлов (например, `PhuketPager2`, `PhuketGalleryD2`).
    *   Используют CSS классы Bootstrap 4 для разметки и стилизации, меньше кастомных CSS по сравнению с Темой 1.

## 7. Вспомогательные функции и классы (`s45_base.lib.inc` и др.)
*   `s45_lang(&$langObject, ...)`: Для работы с многоязычными текстами (объекты `LangVO`).
*   `s45_toObject($param)`, `s45_toArray($param)`: Для конвертации типов.
*   `s45_dsm($obj, $mes)`, `s45_dpq($query)`: Отладочные функции.
*   `s45_url($sysPath, $options)`: Генерация URL с учетом алиасов.
*   `Store::create($scope)->set($key, $value)` / `get($key)`: Временное хранилище данных (`$_SESSION`).
*   `Creator::create($object, $props)`: Заполняет свойства объекта из массива/объекта `$props`.
*   `JsonRepo::open($path)` / `save($data, $path)` / `load($id, $path)`: Работа с JSON файлами как с репозиториями.

## 8. Drupal хуки
*   Проект активно использует различные хуки Drupal:
    *   `hook_menu`: (`s45_base_menu`, `s45_page_menu`, `s45_phuket_menu` и т.д.) для определения маршрутов и API эндпоинтов (например, `/compo45/...`, `/s45_api2/...`).
    *   `hook_init`: (`s45_phuket_login_init`, `s45_protocol_fix_init`) для инициализации на ранних этапах загрузки страницы.
    *   `hook_preprocess_html`: (`s45_page_preprocess_html`, `s45_phuket_preprocess_html`, `site45_preprocess_html`) для модификации переменных HTML-шаблона.
    *   `hook_css_alter` (`site45_css_alter`): Для модификации списка CSS файлов.
    *   `hook_form_alter`: Для кастомизации форм (например, `s45_phuket_login_form_alter`).
    *   `hook_url_inbound_alter` (`s45_path_url_inbound_alter`): Для кастомной обработки входящих URL и редиректов.
    *   И многие другие (`hook_cron`, `hook_user_login`, etc.).

## Области для дальнейшего исследования:
*   **Полная цепочка сохранения файлов `FormImage`:** Как именно данные из `Store('UPLOADS')` и временных файлов (`files/tmp/`) попадают в поля сущности при её сохранении (например, в `PhuketPropertyAR`). Предположительно, это происходит в коде обработки основной формы сущности, который читает данные из `$_POST`, включая скрытые поля от `FormImage`.
*   **Очистка `public://FileStore4/<site_id>/files/tmp/` и `public://FileStore4/<site_id>/files/old/`:** Детально исследовать, существует ли механизм очистки этих директорий, или файлы там накапливаются.
*   **Конкретные реализации `QueryFromEvents::handle<ARName><EventName>()`:** Просмотреть несколько ключевых реализаций (например, для `PhuketPropertySaved`) чтобы понять, как именно обновляются Read Models.
*   **Взаимодействие `Compo452` и `Compo`:** Различия и совместное использование, если таковое имеется (кроме общего API эндпоинта `/compo45/`).
*   **Роль `PageId` более детально:** Как именно он используется и влияет на контекст компонентов, особенно в сложных AJAX-сценариях.
*   **Различия в CSS/JS обработке для "Темы 1" и "Темы 2" на уровне `drupal_add_css/js` и агрегации (AdvAgg).**

---
version: 2

