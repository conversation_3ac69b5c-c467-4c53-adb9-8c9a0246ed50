---
type: "always_apply"
---

# s45_phuket_login Module Cursor Rules

<rule>
name: s45_phuket_login_form_alter
description: Custom login form override (hook_form_alter)
filters:
  - type: file_content
    pattern: "function s45_phuket_login_form_alter"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_login_form_alter()`: настройка формы `user_login`.
      - Проверьте placeholder, class и required для полей через `s45_lang()`.
      - Убедитесь, что шаблон `s45-phuket-login.tpl.php` безопасно экранирует данные.
</rule>

<rule>
name: s45_phuket_login_form_submit
description: Form submit handler for redirection after login
filters:
  - type: file_content
    pattern: "function s45_phuket_login_form_submit"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_login_form_submit()`: редирект администраторов на `/s45/admin`.
      - Проверьте `user_access('access administration pages')` и безопасный `drupal_goto()`.
</rule>

<rule>
name: s45_phuket_login_theme
description: Theme implementation for login template
filters:
  - type: file_content
    pattern: "function s45_phuket_login_theme"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_login_theme()`: определение темы `s45_phuket_login_template`.
      - Убедитесь, что шаблон `s45-phuket-login.tpl.php` существует и обрабатывает `$form`.
</rule>

<rule>
name: s45_phuket_login_user_login
description: hook_user_login implementation
filters:
  - type: file_content
    pattern: "function s45_phuket_login_user_login"
actions:
  - type: suggest
    message: |
      - Изучите `s45_phuket_login_user_login()`: установка флага перенаправления в сессии.
      - Проверьте права доступа и корректность записи `$_SESSION['s45_phuket_login_redirect_to_admin']`.
</rule>

<rule>
name: s45_phuket_login_init
description: hook_init for login redirection
filters:
  - type: file_content
    pattern: "function s45_phuket_login_init"
actions:
  - type: suggest
    message: |
      - Откройте `s45_phuket_login_init()`: выполнение редиректа при наличии флага в сессии.
      - Проверьте условия `arg()` и отсутствие вывода перед `drupal_goto()`.
</rule>
