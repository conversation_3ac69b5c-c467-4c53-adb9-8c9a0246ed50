---
type: "always_apply"
---

# Compo Resource Files Rules

<rule>
name: compo_resource_js_css
filters:
  - type: file_extension
    pattern: "\.(js|css)$"
actions:
  - type: suggest
    message: |
      - JS/CSS ресурсы компонентов должны быть неймспейсированы по имени компонента.
      - Для JS используйте ES6+, IIFE или модули, JSDoc для публичных функций.
      - Для CSS используйте уникальные классы/префиксы, избегайте глобальных сбросов.
      - Подключайте ресурсы только через drupal_add_js/drupal_add_css или s45_add_* API.
      - Не допускайте inline-скриптов и стилей.
      - Документируйте назначение файла в начале.
</rule>
