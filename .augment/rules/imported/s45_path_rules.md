---
type: "always_apply"
---

# s45_path Module Cursor Rules

<rule>
name: s45_path_inbound_alter
description: Обработка входящих URL через hook_inbound_alter
filters:
  - type: file_content
    pattern: "function s45_path_url_inbound_alter"
actions:
  - type: suggest
    message: |
      - Изучите `s45_path_url_inbound_alter()`: логику редиректов и изменения system path.
      - Проверьте порядок include_once для зависимостей и используемые классы Path, Redirect, RedirectFromOldSite.
      - Убедитесь в корректной обработке 301 редиректов через `drupal_goto()`.
</rule>

<rule>
name: s45_path_url_helper
description: Хелпер для генерации URL через s45_path_url
filters:
  - type: file_content
    pattern: "function s45_path_url"
actions:
  - type: suggest
    message: |
      - Откройте `s45_path_url()`: генерация алиасов или системных ссылок.
      - Проверьте безопасность входных `$sysPath` и использование `url()`.
      - Убедитесь в удалении префиксов 's45/' и корректной подстановке alias.
</rule>

<rule>
name: s45_path_redirect_search
description: Специальные редиректы для поиска
filters:
  - type: file_content
    pattern: "function s45_path_redirect_search"
actions:
  - type: suggest
    message: |
      - Изучите `s45_path_redirect_search()`: правила редиректа для `/search/form`.
      - Проверьте `arg()` и `$_SERVER['REQUEST_URI']` для точного соответствия.
      - Убедитесь, что после `drupal_goto()` нет дополнительного вывода.
</rule>

<rule>
name: path_class_definitions
description: Базовые классы Path, Redirect и RedirectFromOldSite
filters:
  - type: file_path
    pattern: "classes/Site45/Path/(Path|Redirect|RedirectFromOldSite)\.php$"
actions:
  - type: suggest
    message: |
      - Откройте классы `Path.php`, `Redirect.php`, `RedirectFromOldSite.php`.
      - Проверьте методы `getSysPath()`, `getAlias()`, `exec()` и работу с $_GET/$_SERVER.
      - Убедитесь в безопасной обработке пользовательских URL и отсутствии SQL-инъекций.
</rule>
