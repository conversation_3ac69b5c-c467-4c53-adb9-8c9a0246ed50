---
type: "agent_requested"
---

# 🔍 IndreamsPhuket SEO System Architecture

## 📊 Database Schema

### Core SEO Tables:
- **`_phuket_PageSeo`** (203 records): Individual page SEO settings
  - `id`: varchar(36) - unique page identifier
  - `pageSeoDto`: longtext - serialized SEO data (title, description, keywords)
  
- **`_phuket_Sitemap`** (15,674 records): Dynamic sitemap URLs
  - `url`: varchar(2048) - page URL
  - `langCode`: varchar(10) - language code (ru, en, th, zh-hans)
  - `priority`: float - SEO priority (0.1-1.0)
  - `created`: int(10) - timestamp
  
- **`_s45_aliases`** (28,886 records): SEO-friendly URLs
  - `path`: varchar(255) - Drupal internal path
  - `alias`: varchar(255) - SEO-friendly alias
  - `langCode`: varchar(10) - language code

## 🏗️ Module Architecture

### 1. **s45_phuket_seo.inc** - Core SEO Engine
```php
// Key Functions:
- s45_phuket_seo_pageDto($pageId) // Get page SEO data
- s45_phuket_seo_save($pageId, $data) // Save SEO settings
- s45_phuket_seo_meta_tags() // Generate meta tags
```

### 2. **s45_phuket_sitemap.inc** - Sitemap Generation
```php
// Key Functions:  
- s45_phuket_sitemapgen() // Generate language-specific sitemap
- s45_phuket_sitemapgen_index() // Generate sitemap index
- s45_phuket_sitemapgen_all() // Generate all sitemaps
```

### 3. **s45_base.robots.inc** - Robots.txt Handler
```php
// Function: s45_robots()
// Route: /robots.txt
// Uses: variable_get('s45_robots_'.$langCode)
```

### 4. **s45_path** - SEO URL Management
```php
// Classes:
- Site45\Path\Path.php // URL generation and validation
- Site45\Path\PathAliasManager.php // Alias management
```

## 🌐 Multi-language Configuration

### Language Distribution:
- **Russian (ru)**: 15,674 sitemap URLs, 14,443 aliases
- **English (en)**: 14,443 aliases  
- **Thai (th)**: Unknown count
- **Chinese (zh-hans)**: Unknown count

### Domain Mapping:
- `indreamsphuket.ru` → Russian content
- `indreamsphuket.com` → English content
- `th.indreamsphuket.com` → Thai content
- `ch.indreamsphuket.com` → Chinese content

## 🤖 Robots.txt System

### Current Issues Found:
1. ❌ **Physical robots.txt file override** - Drupal handler bypassed
2. ❌ **Sitemap.xml wrong domain references** - .ru shows .com links
3. ❌ **Missing sitemap route** - No Drupal handler for sitemap.xml

### Variables Used:
- `s45_robots_ru` - Russian robots.txt content
- `s45_robots_en` - English robots.txt content  
- `s45_robots_th` - Thai robots.txt content
- `s45_robots_zh-hans` - Chinese robots.txt content

## 🗺️ Sitemap System

### Current Structure:
```
sitemap.xml (index) → Points to individual language sitemaps
├── sitemap_ru.xml (51MB, 15,674 URLs)
├── sitemap_en.xml (1KB, minimal)  
├── sitemap_th.xml (45MB)
└── sitemap_zh.xml (46MB)
```

### Generation Routes:
- `/sitemapgen?key=qqww33&lang=ru` - Generate specific language
- `/sitemapgen_index?key=qqww33` - Generate sitemap index
- `/sitemapgen_all?key=qqww33` - Generate all sitemaps

### Issues Found:
1. ❌ **Domain mismatch**: .ru sitemap index shows .com URLs
2. ❌ **Missing nginx routing**: Static files served instead of dynamic
3. ❌ **No automatic regeneration**: Manual generation required

## 🔧 SEO Best Practices Implementation

### Meta Tags Generation:
```php
// In page template:
$seoData = s45_phuket_seo_pageDto($pageId);
// Outputs: title, description, keywords, og:tags
```

### URL Structure:
- **Properties**: `/property/{id}`
- **Projects**: `/project/{id}`  
- **Articles**: `/articleabout/{id}`
- **News**: `/newsabout/{id}`
- **Services**: `/service/{id}`

### Priority Settings:
- Homepage: 1.0
- Properties: 0.8
- Projects: 0.7
- Articles: 0.6
- Services: 0.5

## 🚨 Critical Issues to Fix

### 1. Sitemap Domain Resolution
**Problem**: .ru domain shows .com sitemap links
**Solution**: Domain-aware sitemap generation

### 2. Nginx Configuration  
**Problem**: Static files override Drupal handlers
**Solution**: Proper location blocks for SEO files

### 3. Missing Routes
**Problem**: No sitemap.xml route in Drupal
**Solution**: Add menu item for sitemap handler

### 4. Cache Management
**Problem**: Static files cached, changes not reflected
**Solution**: Dynamic generation with proper headers

## 🛠️ Development Guidelines

### When Working with SEO:
1. **Always check language context**: `$GLOBALS['language']->language`
2. **Use proper domain mapping**: Check `$_SERVER['SERVER_NAME']`
3. **Clear caches after changes**: `drush cc all`
4. **Test all language versions**: .ru, .com, th., ch.
5. **Validate XML output**: Use XML validators for sitemaps

### Database Queries:
```sql
-- Check sitemap coverage
SELECT langCode, COUNT(*) FROM _phuket_Sitemap GROUP BY langCode;

-- Check SEO pages
SELECT COUNT(*) FROM _phuket_PageSeo;

-- Check URL aliases  
SELECT langCode, COUNT(*) FROM _s45_aliases GROUP BY langCode;
```

### Key Files to Monitor:
- `robots.txt` (should not exist as physical file)
- `sitemap*.xml` (generated files)
- `/etc/nginx/vhosts-resources/indreamsphuket.com/sitemap.conf`

## 📈 Performance Considerations

### Large Sitemap Files:
- `sitemap_ru.xml`: 51MB (15,674 URLs)
- `sitemap_th.xml`: 45MB  
- `sitemap_zh.xml`: 46MB

### Optimization Needed:
1. **Split large sitemaps** into smaller chunks
2. **Implement caching** for generated sitemaps
3. **Add compression** for XML files
4. **Use CDN** for sitemap delivery

## 🔍 Debugging Commands

```bash
# Check current robots.txt
curl -s https://indreamsphuket.ru/robots.txt | head -10

# Check sitemap index
curl -s https://indreamsphuket.ru/sitemap.xml | head -10

# Generate new sitemap
curl -s "https://indreamsphuket.ru/sitemapgen_index?key=qqww33"

# Check database
drush sql-query "SELECT langCode, COUNT(*) FROM _phuket_Sitemap GROUP BY langCode"

# Clear all caches
drush cc all

# Check nginx config
nginx -t
```
