---
type: "always_apply"
---

# Event Sourcing в S45 Framework

<rule>
name: s45_event_sourcing_optimization
description: Оптимизация таблицы _s45_events с сохранением функциональности
filters:
  - type: file_path
    pattern: "EventStore\\.php$|EventQuery\\.php$"
actions:
  - type: suggest
    message: |
      ## Оптимизация таблицы _s45_events
      
      - **Система использует только события с isLast=1** для всех операций чтения
      - **Механизм чтения данных через `EventQuery::getLastEvent()`** всегда добавляет условие `isLast = 1`
      - **Старые события с isLast=0** не используются в штатной работе системы
      - **Безопасное архивирование** старых событий возможно путем:
         1. Создания архивной таблицы `_s45_events_archive`
         2. Переноса всех записей с `isLast=0` в архивную таблицу
         3. Удаления этих записей из основной таблицы
         4. Оптимизации основной таблицы
      - **Ожидаемый эффект:** уменьшение размера таблицы на ~80-85%
</rule>

<rule>
name: s45_event_sourcing_internals
description: Внутреннее устройство механизма Event Sourcing в системе
filters:
  - type: file_path
    pattern: "EventStore\\.php$|AR\\.php$"
actions:
  - type: suggest
    message: |
      ## Внутреннее устройство Event Sourcing в S45
      
      - **Два ключевых класса:** `EventStore` и `EventQuery`
      - **Запись события** происходит через `EventStore::addEvent()`:
         1. Установка `isLast=0` для всех существующих событий агрегата
         2. Создание нового события с `isLast=1`
         3. Сериализация полного состояния агрегата в поле `payload`
      - **Чтение данных** происходит через `EventQuery::getLastEvent()`:
         1. Поиск события с условиями: `arName`, `arId` и `isLast=1`
         2. Десериализация `payload` в объект агрегата
      - **Восстановление состояния** через `AR::find()`:
         1. Загрузка последнего события с `isLast=1`
         2. Десериализация `payload` и установка свойств агрегата
</rule>

<rule>
name: s45_read_model_denormalization
description: Денормализация данных в read-модели из Event Store
filters:
  - type: file_path
    pattern: "QueryFromEvents\\.php$|PhuketPropertyQuery\\.php$"
actions:
  - type: suggest
    message: |
      ## Денормализация данных из Event Store
      
      - **Класс `QueryFromEvents`** отвечает за обновление read-модели
      - **Обработчики событий** реализуются как методы `handle<ARName><EventName>()`:
         ```php
         // Пример обработчика события сохранения объекта недвижимости
         public function handlePhuketPropertySaved(EventDto $event) {
           // Обновление записи в read-модели (_phuket_Property)
         }
         ```
      - **Метод `exec()`** в `QueryFromEvents` работает с денормализованной таблицей
      - **Обновление read-модели** происходит в момент сохранения агрегата
      - **Для `PhuketProperty`** используется таблица `_phuket_Property`
</rule>

<rule>
name: s45_events_database_structure
description: Структура таблицы _s45_events и её особенности
filters:
  - type: file_content
    pattern: "_s45_events"
actions:
  - type: suggest
    message: |
      ## Структура таблицы _s45_events
      
      - **Таблица `_s45_events`** — центральное хранилище событий
      - **Основные поля:**
         - `id`: Уникальный идентификатор события
         - `arName`: Имя типа агрегата ('PhuketProperty', 'PhuketForm', 'CompoConfig')
         - `arId`: UUID агрегата
         - `name`: Тип события ('Saved', 'Deleted')
         - `payload`: Сериализованное состояние объекта (serialize())
         - `isLast`: Флаг актуальности (1 = последнее событие)
      - **Основные проблемы таблицы:**
         - Большой размер (~1.4 ГБ)
         - 80-85% занимают устаревшие события (isLast=0)
         - Медленные запросы из-за большого объема данных
      - **Использование событий:** в коде используются только события с isLast=1
</rule>

<rule>
name: s45_serialize_methods
description: Методы сериализации в системе Event Sourcing
filters:
  - type: file_content
    pattern: "serialize\\(|json_encode\\("
actions:
  - type: suggest
    message: |
      ## Методы сериализации данных
      
      - **Система использует два подхода:**
         1. `serialize($obj)` — для хранения в Event Store
         2. `json_decode(json_encode($obj))` — для нормализации структур
      - **Важно учитывать различие:**
         - `serialize()` сохраняет PHP-типы и структуру классов
         - `json_decode(json_encode())` преобразует массивы в stdClass
      - **Правильное использование:**
         - В Event Store всегда использовать `serialize()`
         - Для функции `s45_toObject()` использовать `json_decode(json_encode())`
</rule> 