---
type: "always_apply"
---

# Missing Details Cursor Rules

<rule>
name: missing_eventstore
description: Анализ класса EventStore и методов EventStore
filters:
  - type: file_path
    pattern: "sites/all/modules/__s45/s45_base/classes/Site45/Event/EventStore.php"
actions:
  - type: suggest
    message: |
      - Откройте файл `sites/all/modules/__s45/s45_base/classes/Site45/Event/EventStore.php`.
      - Изучите методы `addEvent()` и `getIP()`.
      - Проверьте использование `db_transaction()` для транзакций.
      - Обратите внимание, что нет централизованного триггера обработчиков.
</rule>

<rule>
name: missing_compo_base
description: Анализ базового класса Compo и его методов
filters:
  - type: file_path
    pattern: "sites/all/modules/__s45/s45_base/classes/Site45/Compo/Compo.php"
actions:
  - type: suggest
    message: |
      - Откройте файл `sites/all/modules/__s45/s45_base/classes/Site45/Compo/Compo.php`.
      - Проанализируйте методы: `apiGetRendered()`, `getCompoRes()`, `getCompoHtmlFull()`, `getCompoHtml()`, `setAttributes()`, `addCompoHtmlWrapper()`, `postProcessor()`, `apiLoadEditor()`, `apiSaveEditor()`.
      - Обратите внимание на использование `s45_toObject()` и `s45_render()`.
</rule>

<rule>
name: missing_drupal_integration
description: Анализ маршрутов и прав доступа Compo API
filters:
  - type: file_path
    pattern: "sites/all/modules/__s45/s45_base/s45_base.module"
actions:
  - type: suggest
    message: |
      - Откройте файл `sites/all/modules/__s45/s45_base/s45_base.module`.
      - Проанализируйте функцию `s45_base_menu()` для маршрутов `compo45`, `compomake`, `s45store`, `s45_robots_settings`, `robots.txt`.
      - Изучите `s45_base_permission()` для ролей `s45guest` и `s45admin`.
</rule>

<rule>
name: missing_caching_queues
description: Определить использование кэширования и очередей
filters:
  - type: file_extension
    pattern: "\\.php$"
actions:
  - type: suggest
    message: |
      - В `EventStore::addEvent()` используется транзакция `db_transaction()`.
      - Выполните grep по `cache_get|cache_set` для поиска мест использования Cache API.
      - Обдумайте внедрение Drupal Queue API для асинхронного обновления read-моделей.
      - Кандидаты для кэширования: результаты `EventQuery::exec()` и ресурсы в `getCompoRes()`.
</rule>

<rule>
name: missing_tests_migrations
description: Анализ тестов и миграций в модулях __s45
filters:
  - type: file_extension
    pattern: "\\.install$"
actions:
  - type: suggest
    message: |
      - grep по `hook_update_N` в файлах `.install` — миграции отсутствуют.
      - Проверьте каталоги `tests` в модулях __s45 на наличие тестов.
      - Спланируйте миграции для создания таблицы `_s45_events` и проекционных таблиц `_phuket_Property`, `_phuket_Reservation`.
</rule>
