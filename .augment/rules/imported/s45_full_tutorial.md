---
type: "manual"
---

# Tutorial: __s45 Module

This `__s45` module represents a custom **web framework** and **CMS component** used within a larger Drupal project. It provides foundational features like reusable **Components** (`Compo`/`Compo452`), structured data handling (**DTOs/VOs**), **Event Sourcing**, **Repositories**, **Queries**, **Path Management**, multi-site **Configuration**, and a request **Store**.

These core features are then used to build specific applications, such as the **Phuket Real Estate website** functionality also contained within this module. This application-specific part includes features like detailed property listings, search/filtering, automatic **SEO**, **rental price calculation**, user inquiry forms, an **admin panel**, data export, and parsing of data from legacy systems.

**Source Repository:** [None](None)

## Core Framework Concepts (__s45 - Base/Compo/etc.)

```mermaid
flowchart TD
    A0["Component (`Compo` / `Compo452`)
"]
    A1["Data Transfer Object (DTO) / Value Object (VO)
"]
    A2["Event Sourcing (`AR`, `EventStore`, `EventQuery`)
"]
    A3["Repository (`Repo`)
"]
    A4["Query (`Query`, `QueryFromEvents`, `JsonQuery`)
"]
    A5["Path Management (`Path`, `Redirect`)
"]
    A6["Store (`Store`)
"]
    A7["Component Rendering (`Render452`)
"]
    A8["Configuration (`SiteConf`)
"]
    A7 -- "Renders" --> A0
    A0 -- "Uses data from" --> A1
    A0 -- "Shares data via" --> A6
    A3 -- "Loads/Saves configuration for" --> A0
    A3 -- "Persists data using" --> A2
    A4 -- "Reads data from" --> A2
    A2 -- "Stores/Retrieves state as" --> A1
    A4 -- "Returns results as" --> A1
    A5 -- "Uses site info from" --> A8
    A4 -- "Uses site info from" --> A8
    A3 -- "Uses site info from" --> A8
    A7 -- "Produces structured result ..." --> A1
    A3 -- "Stores/Retrieves simple" --> A1
```

## Application Example Concepts (s45_phuket)

```mermaid
flowchart TD
    B0["Data Entities (AR/DTO)
"]
    B1["Query Classes
"]
    B2["Components (Compo)
"]
    B3["SEO Generation
"]
    B4["Data Converters / Exports
"]
    B5["Admin Interface Components
"]
    B6["Form Handling & API
"]
    B7["Data Parsing (Indreams)
"]
    B8["Component Structure (PHP/TPL/JS/CSS)
"]
    B9["Rental Period Calculation
"]
    B1 -- "Retrieves/Returns" --> B0
    B2 -- "Uses to fetch data" --> B1
    B2 -- "Implements structure" --> B8
    B3 -- "Uses to fetch SEO context" --> B1
    B4 -- "Uses to get export data" --> B1
    B5 -- "Manages (CRUD)" --> B0
    B5 -- "Are specific types of" --> B2
    B6 -- "Saves form data as" --> B0
    B6 -- "Uses for price calculation" --> B9
    B7 -- "Creates/Parses into" --> B0
    B9 -- "Uses Property/Price data" --> B0
```

## Chapters

### Part 1: Phuket Real Estate Application Features (from s45_phuket)

1.  [Components (Compo)
    ](01_components__compo__.md)
2.  [Component Structure (PHP/TPL/JS/CSS)
    ](02_component_structure__php_tpl_js_css__.md)
3.  [Data Entities (AR/DTO)
    ](03_data_entities__ar_dto__.md)
4.  [Query Classes
    ](04_query_classes_.md)
5.  [SEO Generation
    ](05_seo_generation_.md)
6.  [Rental Period Calculation
    ](06_rental_period_calculation_.md)
7.  [Form Handling & API
    ](07_form_handling___api_.md)
8.  [Admin Interface Components
    ](08_admin_interface_components_.md)
9.  [Data Converters / Exports
    ](09_data_converters___exports__.md)
10. [Data Parsing (Indreams)
    ](10_data_parsing__indreams__.md)

### Part 2: Core Framework Features (__s45 Base/Compo/etc.)

11. [Configuration (`SiteConf`)
    ](11_configuration___siteconf___.md)
12. [Path Management (`Path`, `Redirect`)
    ](12_path_management___path____redirect___.md)
13. [Component (`Compo` / `Compo452`)
    ](13_component___compo_____compo452___.md)
14. [Data Transfer Object (DTO) / Value Object (VO)
    ](14_data_transfer_object__dto____value_object__vo__.md)
15. [Component Rendering (`Render452`)
    ](15_component_rendering___render452___.md)
16. [Repository (`Repo`)
    ](16_repository___repo___.md)
17. [Event Sourcing (`AR`, `EventStore`, `EventQuery`)
    ](17_event_sourcing___ar____eventstore____eventquery___.md)
18. [Query (`Query`, `QueryFromEvents`, `JsonQuery`)
    ](18_query___query____queryfromevents____jsonquery___.md)
19. [Store (`Store`)
    ](19_store___store___.md)


---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)
# Chapter 1: Components (Compo)

Welcome to the `s45_phuket` project! This project helps manage a real estate website for Phuket properties. To build the website's pages, both for visitors and administrators, we use a concept called **Components**, often shortened to **Compo**.

Imagine you're building a house. Instead of making every single brick, window frame, and door handle from scratch right there on the construction site, you use pre-fabricated parts: windows, doors, maybe even entire wall sections. This makes building faster, more organized, and easier to maintain. If one window design is used in multiple places, you just use the same pre-fabricated part.

Components (`Compo`) in our project work exactly like that!

## What Problem Do Components Solve?

Think about a typical real estate website page, maybe one showing details about a specific villa. It might have:

*   A header with the site logo and navigation menu.
*   A photo gallery of the villa.
*   A description section with text about the villa.
*   A block showing key features (bedrooms, bathrooms, area).
*   A map showing the location.
*   A form to inquire about the villa.
*   A footer with contact information.

Now, imagine another page listing *all* available villas. Each villa in the list might show a smaller "teaser" – a photo, name, price, and a few key details. Notice that the header and footer are the same as the villa detail page. The "teaser" block for each villa is also a reusable pattern.

Building every page entirely from scratch would be very repetitive and hard to manage. If you wanted to change how the price is displayed in the teasers, you'd have to find and update it on potentially dozens of pages!

This is where Components come in. They solve the problem of repetition and disorganization by breaking the user interface (UI) down into smaller, reusable, self-contained pieces.

## What is a Compo? The Lego Brick Analogy

Think of **Compos** as **Lego bricks** for our website.

*   Each `Compo` class defines a specific piece of the interface (like a property listing block, a search form, a map display, or a page footer).
*   It bundles everything needed for that specific UI element:
    *   **PHP Logic (`*.php`):** To fetch data (like property details) or handle calculations.
    *   **HTML Structure (`*.tpl.php`):** To define how the element looks in the browser.
    *   **Styling (`*.css`):** To control the visual appearance (colors, fonts, layout).
    *   **Client-side Behavior (`*.js`):** To add interactivity (like image sliders or form validation).
*   Pages are then constructed by assembling these reusable Compos, just like building something with Lego bricks.

Some examples of Compos in `s45_phuket`:

*   `PhuketPropertyTeaser`: Shows a small summary of a property (like in a search results list).
*   `PhuketSearchForm`: The main form used to search for properties.
*   `PhuketServiceBlock`: A block listing available services (e.g., airport transfers, tours).
*   `PhuketSite`: Represents the overall site structure, including header and footer elements.
*   `PhuketAdminPage`: The base layout for the administration area pages.

## Using a Compo: Displaying Services

Let's take a simple use case: We want to display a block on the homepage listing some of the services offered, like "Airport Transfer" and "Car Rental".

Instead of writing the HTML and PHP directly onto the homepage code, we use a dedicated `Compo` called `PhuketServiceBlock`.

**1. The Compo Class (PHP Logic):**

Somewhere in our codebase, we have a PHP class that defines this component. It inherits basic functionality from a base `Compo` class.

```php
// File: classes/Site45/Sets/Phuket/Compo/Serv/Services/PhuketServiceBlock/PhuketServiceBlock.php
// (Simplified for clarity)

use Site45\Compo\Compo;
use Site45\DtoLib\Base\LangVO;
// We need Query classes to fetch data
use Site45\Sets\Phuket\Query\ServiceSearch\PhuketServiceQuery;

class PhuketServiceBlock extends Compo {

  // Properties to hold data for the template
  public $content_title;
  public $searchResult; // Will hold the list of services

  // This runs before the HTML is generated
  protected function beforeRender($props) {
    // Create a query to find services for guests
    $query = PhuketServiceQuery::create();
    $query->limit = 5; // Get up to 5 services
    $query->serviceFor = 'guest';

    // Execute the query and store the results
    // This uses Query Classes (covered later)
    // [Query Classes](04_query_classes_.md)
    $this->searchResult = $query->exec();
  }

  // Constructor: Sets up default values
  function __construct() {
    // Default title (supports multiple languages)
    $this->content_title = LangVO::create([
      'en' => 'Our Services',
      'ru' => 'Наши услуги',
    ]);
    // ... other initial setup ...
  }
}
```

**Explanation:**

*   This `PhuketServiceBlock` class is our specialized Lego brick for showing services.
*   The `beforeRender()` method is key: it runs *before* the HTML is created. Here, it uses a `PhuketServiceQuery` (which we'll learn about in the [Query Classes](04_query_classes_.md) chapter) to fetch a list of services from the database.
*   The results are stored in `$this->searchResult`.
*   The `__construct()` method sets up things like the default title.

**2. Assembling the Page:**

We don't call this PHP class directly in the browser. Instead, when defining the structure of our homepage, we simply tell the system: "Put the `PhuketServiceBlock` Compo here."

*(Conceptual - not actual code)*

```
Page Definition for Homepage:
  - Place Header Compo
  - Place Welcome Text Compo
  - Place PhuketServiceBlock Compo // <-- Our component goes here!
  - Place Footer Compo
```

**3. The Result:**

When someone visits the homepage:

*   The system sees it needs to include `PhuketServiceBlock`.
*   It runs the `beforeRender()` method of `PhuketServiceBlock`, which fetches the service data.
*   It then uses an associated HTML template file (`PhuketServiceBlock.tpl.php` - covered in the next chapter) to generate the actual HTML for the service list, using the data stored in `$this->searchResult`.
*   This generated HTML is inserted into the correct place on the page.

The visitor sees a nice block listing the services, generated by our reusable Compo! If we need this block on another page, we just tell the system to place the `PhuketServiceBlock` Compo there too.

## How it Works Under the Hood (Simplified)

When a user requests a page, here's a simplified flow involving Compos:

```mermaid
sequenceDiagram
    participant UB as User Browser
    participant WS as Web Server (Page)
    participant Compo as PhuketServiceBlock
    participant QC as Query Classes
    participant DB as Database
    participant TE as Template Engine

    UB->>WS: Request Homepage
    WS->>Compo: Instantiate Compo (Homepage needs PhuketServiceBlock)
    Compo->>Compo: Run constructor (__construct)
    WS->>Compo: Ask Compo to prepare data (call beforeRender)
    Compo->>QC: Create Service Query
    QC->>DB: Fetch service data
    DB-->>QC: Return service data
    QC-->>Compo: Return searchResult
    Compo->>Compo: Store searchResult
    WS->>TE: Give Compo data and Template file
    TE->>Compo: Access data (e.g., $this->searchResult)
    TE->>TE: Generate HTML fragment
    TE-->>WS: Return HTML for the Compo
    WS->>WS: Assemble full page HTML
    WS-->>UB: Send HTML page
```

**Steps:**

1.  **Request:** The User's Browser asks for a page.
2.  **Page Setup:** The Web Server identifies which Compos are needed for that page (e.g., `PhuketServiceBlock`).
3.  **Compo Initialization:** An instance of the `PhuketServiceBlock` PHP class is created. Its `__construct` method runs.
4.  **Data Fetching:** The system calls the `beforeRender()` method on the Compo instance. This is where the Compo uses [Query Classes](04_query_classes_.md) to talk to the Database and get the necessary data (the list of services).
5.  **Data Storage:** The fetched data is stored in properties of the Compo instance (like `$this->searchResult`).
6.  **Rendering:** The system takes the Compo's data and its associated template file (`*.tpl.php`) and gives them to a Template Engine.
7.  **HTML Generation:** The Template Engine uses the data to fill in the template, producing the final HTML for that specific Compo.
8.  **Assembly:** The Web Server takes the HTML generated by all the Compos on the page and puts them together into the final page HTML.
9.  **Response:** The complete HTML page is sent back to the User's Browser.

## Diving Deeper into the Compo Class

Let's look at the simplified `PhuketServiceBlock.php` again:

```php
// File: classes/Site45/Sets/Phuket/Compo/Serv/Services/PhuketServiceBlock/PhuketServiceBlock.php
// (Simplified)

use Site45\Compo\Compo; // Base class
use Site45\DtoLib\Base\LangVO; // For multi-language text
use Site45\Sets\Phuket\Query\ServiceSearch\PhuketServiceQuery; // For data fetching

// Our component extends the base Compo functionality
class PhuketServiceBlock extends Compo {

  // Public properties are accessible by the template file
  public $content_title;
  public $searchResult; // Will hold the list of Service DTOs

  // Runs before the HTML template is processed
  protected function beforeRender($props) {
    // Create a query using Query Classes
    // [Query Classes](04_query_classes_.md)
    $query = PhuketServiceQuery::create();
    $query->limit = 5;
    $query->serviceFor = 'guest';

    // Execute and store result (likely an array of service objects)
    $this->searchResult = $query->exec();
  }

  // Constructor: Runs when the component is created
  function __construct() {
    // Set default title using LangVO for multiple languages
    $this->content_title = LangVO::create([
      'en' => 'Our Services',
      'ru' => 'Наши услуги',
    ]);
    // Set default setting: this component is visible
    $this->settings_published = 1;
  }
}
```

**Key Parts:**

*   `extends Compo`: This means our `PhuketServiceBlock` gets all the standard features and lifecycle methods of a basic Component.
*   `public $content_title;`, `public $searchResult;`: These are variables (properties) that will hold data. Because they are `public`, the template file (`*.tpl.php`) can easily access them to display the title and loop through the services.
*   `__construct()`: Sets initial default values when the Compo is first created. Here, it sets a default title (using `LangVO` for multi-language support) and makes sure the component is set to be visible (`settings_published = 1`).
*   `beforeRender()`: This is where the main logic for fetching data specific to this instance of the component happens. It prepares everything the template will need. It often involves using [Query Classes](04_query_classes_.md) to get data from the database and storing it in public properties like `$this->searchResult`.

## Conclusion

Components (`Compo`) are the fundamental Lego bricks of the `s45_phuket` user interface. They encapsulate the logic (PHP), structure (HTML Template), style (CSS), and behavior (JS) for a reusable piece of the UI. By building pages from these Compos, we create a system that is more organized, easier to develop, and much simpler to maintain.

In the next chapter, we'll look closer at the specific files that make up a Component and how they work together.

Next: [Component Structure (PHP/TPL/JS/CSS)](02_component_structure__php_tpl_js_css__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 2: Component Structure (PHP/TPL/JS/CSS)

In the [previous chapter](01_components__compo__.md), we learned that **Components (Compo)** are like pre-fabricated parts or Lego bricks for building our website's user interface. We saw how a PHP class like `PhuketServiceBlock.php` handles the logic for a specific UI piece.

Now, let's zoom in and look at the different kinds of files that usually make up a single Component.

## Why Separate Files? The Model Kit Analogy

Imagine you bought a complex model airplane kit. Inside the box, you wouldn't find just one giant sheet with everything mixed together. Instead, you'd likely find:

1.  **Instructions:** Telling you *what* to do (the logic).
2.  **Plastic Parts:** The actual pieces you assemble (the structure).
3.  **Decal Sheet:** Stickers for decoration (the style).
4.  **(Maybe) Moving Parts Guide:** How to make the propeller spin or wheels turn (the interaction).

Keeping these separate makes the complex model much easier to build. You look at the instructions (logic), find the right parts (structure), assemble them, and then apply decals (style) and maybe set up moving parts (interaction).

Our Components use a similar approach with separate files for different jobs:

*   **PHP (`.php`):** The instructions – handles logic and prepares data.
*   **Template (`.tpl.php`):** The plastic parts – defines the HTML structure.
*   **CSS (`.css`):** The decal sheet – controls the visual styling.
*   **JavaScript (`.js`):** The moving parts – manages client-side interactivity (things happening in the user's browser).
*   **(Optional) Editor (`.editor.php`):** A special instruction sheet for configuring the component in the admin area.

This separation makes our code cleaner, easier to understand, and simpler to modify. If you just want to change a color, you only need to look at the CSS file!

## Finding Component Files

Components live in specific folders within the project. The path usually looks like this:

`classes/Site45/Sets/Phuket/Compo/GroupName/ComponentName/`

For example, the `PhuketPropertyTeaser` component (which shows a small summary of a property) has its files inside:

`classes/Site45/Sets/Phuket/Compo/PropertyBlocks/PhuketPropertyTeaser/`

Inside this folder, you'll typically find these files:

*   `PhuketPropertyTeaser.php` (Logic)
*   `PhuketPropertyTeaser.tpl.php` (HTML Structure)
*   `PhuketPropertyTeaser.css` (Styling)
*   `PhuketPropertyTeaser.js` (Interactivity)
*   (Sometimes) `PhuketPropertyTeaser.editor.php` (Admin Config)

Let's look at what each file does, using `PhuketPropertyTeaser` as our example.

## The Core Files Explained

### 1. The PHP File (`ComponentName.php`) - The Brain

This is the main PHP class we talked about in Chapter 1. Its job is to:

*   Prepare data needed by the component.
*   Handle any complex logic *before* the HTML is displayed.
*   Set default values or configurations.

**Example: `PhuketPropertyTeaser.php` (Simplified)**

```php
<?php
// File: classes/.../PhuketPropertyTeaser/PhuketPropertyTeaser.php
use Site45\Compo\Compo;
// We need DTOs (Data Transfer Objects) to hold property data
// Covered in [Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyDto;
// We use Query Classes to fetch data from the database
// Covered in [Query Classes](04_query_classes_.md)
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;

class PhuketPropertyTeaser extends Compo {

  // This will hold all the property details (like name, price, photos)
  // It's 'public' so the TPL file can access it easily.
  /** @var PhuketPropertyDto */
  public $propertyDto;

  // This runs before the HTML is generated
  protected function beforeRender($props) {
    $propertyId = $props['propertyId']; // Get the ID of the property to show

    // Use a Query Class to load the property data from the database
    $this->propertyDto = PhuketPropertyQuery::create()->load($propertyId);

    // Now $this->propertyDto contains all the info for the template
  }

  function __construct() {
    // Set a default title (used in admin area, maybe)
    $this->settings_title = 'Property Teaser';
    // Other defaults can be set here...
  }
}
```

**Explanation:**

*   This PHP class gets the `propertyId` it needs to display.
*   In `beforeRender()`, it uses a `PhuketPropertyQuery` (from [Query Classes](04_query_classes_.md)) to fetch the property's details from the database.
*   It stores these details in the `public $propertyDto` variable. This variable acts like a container holding all the information (name, price, photos, etc.) that the template file will need. We'll learn more about DTOs like `PhuketPropertyDto` in the [Data Entities (AR/DTO)](03_data_entities__ar_dto__.md) chapter.

### 2. The Template File (`ComponentName.tpl.php`) - The Blueprint

This file defines the HTML structure of the component. It's mostly HTML, but it uses special PHP tags (`<?php ... ?>`) to insert the data prepared by the PHP file.

**Example: `PhuketPropertyTeaser.tpl.php` (Simplified)**

```php
<?php /* @var $this PhuketPropertyTeaser */ ?>

<!-- This 'component' tag is often used for structure -->
<component>

  <!-- Link wrapping the image and details -->
  <a class="PhuketPropertyTeaser--galleryWrapper" href="<?php print s45_url('property/'.$this->propertyDto->id); ?>">
      <!-- Display the first photo -->
      <div class="PhuketPropertyTeaser--imageWrapper">
          <img src="<?php print s45_imgSrc($this->propertyDto->photos[0], S45_IMST_500X300_CROP); ?>">
      </div>
  </a>

  <!-- Section for the property name and price -->
  <div class="PhuketPropertyTeaser--header">
      <h3 class="PhuketPropertyTeaser--title">
          <?php // Get the property name from the PHP file's $propertyDto ?>
          <?php print s45_lang($this->propertyDto->name); ?>
      </h3>
      <div class="PhuketPropertyTeaser--price">
          <span class="PhuketPropertyTeaser--priceValue">
              <?php // Get the sale price from the PHP file's $propertyDto ?>
              <?php print number_format((int) $this->propertyDto->priceSale); ?>
          </span>
          <span>THB</span> <?php // Currency code ?>
      </div>
  </div>

  <!-- More HTML for other details like bedrooms, area etc. -->
  
</component>
```

**Explanation:**

*   `<?php /* @var $this PhuketPropertyTeaser */ ?>`: This is a special comment helping code editors understand that `$this` refers to our `PhuketPropertyTeaser` PHP object.
*   `$this->propertyDto`: Inside the template, `$this` refers to the instance of our `PhuketPropertyTeaser` PHP class. We can access its `public` properties like `$propertyDto`.
*   `<?php print ...; ?>`: This PHP command outputs values directly into the HTML.
    *   `$this->propertyDto->id`: Accesses the property's ID.
    *   `s45_imgSrc(...)`: A helper function to get the correct image URL.
    *   `s45_lang($this->propertyDto->name)`: Accesses the property's name. `s45_lang()` is a helper function that handles displaying text in the correct language (e.g., English or Russian).
    *   `number_format(...)`: Formats the price nicely.

This file takes the data prepared by `PhuketPropertyTeaser.php` and arranges it into the final HTML structure the user will see.

### 3. The CSS File (`ComponentName.css`) - The Paint Job

This file contains CSS rules to control how the HTML generated by the `.tpl.php` file looks. It defines colors, fonts, spacing, layout, and more.

**Example: `PhuketPropertyTeaser.css` (Simplified)**

```css
/* File: classes/.../PhuketPropertyTeaser/PhuketPropertyTeaser.css */

/* Style the main container for the image */
.PhuketPropertyTeaser--galleryWrapper {
  display: block; /* Make it a block element */
  position: relative; /* Needed for positioning things inside it */
  height: 223rem; /* Set a specific height */
}

/* Style the property title */
.PhuketPropertyTeaser--title {
  font-size: 16rem; /* Set the font size */
  color: white; /* Set the text color */
  line-height: 125%; /* Adjust line spacing */
  max-width: 65%; /* Prevent it from getting too wide */
  overflow: hidden; /* Hide text that doesn't fit */
}

/* Style the price value */
.PhuketPropertyTeaser--priceValue {
   font-size: 16rem;
   /* Use a variable defined elsewhere for gold color */
   color: var(--color-gold);
}
```

**Explanation:**

*   CSS works by selecting HTML elements (using selectors like `.PhuketPropertyTeaser--title`) and applying style rules (like `color: white;`).
*   The class names used here (e.g., `PhuketPropertyTeaser--title`) match the `class="..."` attributes used in the `PhuketPropertyTeaser.tpl.php` file. This is how the styles are linked to the HTML structure.
*   `var(--color-gold)` uses a CSS variable, which is a way to define reusable values (like brand colors) centrally.

### 4. The JavaScript File (`ComponentName.js`) - The Moving Parts

This file adds interactivity to the component that happens directly in the user's web browser *after* the page has loaded. Examples include:

*   Image sliders/carousels
*   Showing/hiding elements when clicking buttons
*   Validating form inputs before sending them
*   Making requests to the server without reloading the whole page (AJAX)

**Example: `PhuketPropertyTeaser.js` (Simplified)**

```javascript
// File: classes/.../PhuketPropertyTeaser/PhuketPropertyTeaser.js

// Define an object for our component's JavaScript code
var PhuketPropertyTeaser = {

  // Inherit basic functionality from a base Compo JS object
  __proto__: Compo45,

  // This 'init' function runs automatically for each
  // instance of the PhuketPropertyTeaser on the page.
  init() {

    // Find the image gallery *within this specific component*
    // 'this.compoId' uniquely identifies this teaser on the page
    var galleryElement = jQuery('[s45-compo-id=' + this.compoId + '] .owl-carousel');

    // If a gallery element exists, initialize the Owl Carousel library on it
    if (galleryElement.length > 0) {
      galleryElement.owlCarousel({
          loop: true, // Enable looping
          items: 1, // Show one image at a time
          // other carousel settings...
      });
    }

    // You could add other interactions here, like handling clicks
    console.log('Property Teaser initialized: ' + this.compoId);
  },

  // Other functions for handling clicks or events could go here
};
```

**Explanation:**

*   The code is structured within an object `PhuketPropertyTeaser`.
*   `init()`: This function is automatically called by the framework when the component loads on the page. It's the main place to set up event listeners or initialize JavaScript libraries.
*   `jQuery(...)`: This uses the popular jQuery library to easily find (`'[s45-compo-id=...] .owl-carousel'`) and manipulate HTML elements.
*   `this.compoId`: Each component instance on a page gets a unique ID, accessible via `this.compoId`. This ensures the JavaScript targets the correct HTML elements belonging only to *this specific* teaser, especially if there are multiple teasers on the same page.
*   `.owlCarousel({...})`: This calls a third-party JavaScript library (Owl Carousel) to turn a simple list of images into an interactive image slider.

### 5. The Editor File (`ComponentName.editor.php`) - Admin Configuration

Some components have settings that administrators need to change (like a title, or whether the component is shown). The `.editor.php` file defines the form fields for these settings in the admin panel.

**Example: `PhuketServiceBlock.editor.php` (Simplified - for the Service Block component)**

```php
<?php
// File: classes/.../PhuketServiceBlock/PhuketServiceBlock.editor.php
/* @var $this PhuketServiceBlock */ // Tells editor $this is the Service Block
?>

<h2>Translations</h2>

<?php
  // Use a helper function to render a multi-language text input
  print s45_render('FormLangText', array(
    'label' => 'Title', // Label for the input field
    'value' => $this->content_title, // Current value from the PHP object
    'name' => 'formData[content_title]', // Name used when saving the form
  ));
?>

<h2>Settings</h2>

<div class="form-group">
  <?php
    // Use a helper to render a dropdown select box
    print s45_render('FormSelect', array(
      'options' => array( 0 => 'Unpublished', 1 => 'Published' ),
      'defaults' => $this->settings_published, // Current setting
      'attr' => array( 'name' => 'formData[settings_published]' ),
    ));
  ?>
</div>
```

**Explanation:**

*   This file uses helper functions like `s45_render('FormLangText', ...)` and `s45_render('FormSelect', ...)` to quickly build standard form elements (text inputs, dropdowns). These helpers are often part of the [Admin Interface Components](08_admin_interface_components_.md).
*   It accesses properties from the component's PHP object (`$this->content_title`, `$this->settings_published`) to show the current settings.
*   The `name` attributes (`formData[...]`) tell the system how to save the values when the administrator submits the form.

## How They Work Together

Here's a simplified view of how these files interact when a `PhuketPropertyTeaser` needs to be displayed on a page:

```mermaid
sequenceDiagram
    participant Page as Page Request
    participant PHP as PhuketPropertyTeaser.php
    participant DB as Database
    participant TPL as PhuketPropertyTeaser.tpl.php
    participant Browser as Web Browser
    participant CSS as PhuketPropertyTeaser.css
    participant JS as PhuketPropertyTeaser.js

    Page->>PHP: Need Property Teaser (ID: 123)
    PHP->>DB: Fetch data for property 123
    DB-->>PHP: Return property details
    PHP->>PHP: Store details in $this->propertyDto
    PHP->>TPL: Give $propertyDto data to Template
    TPL->>TPL: Generate HTML using data
    TPL-->>Page: Return generated HTML fragment
    Page->>Browser: Send full page HTML (inc. Teaser HTML)
    Browser->>Browser: Render HTML structure
    Browser->>CSS: Apply styles from CSS file
    Browser->>JS: Run init() script for Teaser
    JS->>Browser: Initialize image carousel (if any)
```

1.  **Request:** The system determines a `PhuketPropertyTeaser` is needed for a specific property ID.
2.  **PHP Logic:** The `PhuketPropertyTeaser.php` class runs its `beforeRender` function, fetches data from the Database, and stores it in `$this->propertyDto`.
3.  **HTML Generation:** The `PhuketPropertyTeaser.tpl.php` file uses the data in `$this->propertyDto` to create the specific HTML block for this teaser.
4.  **Browser Rendering:** The browser receives the HTML and displays the basic structure.
5.  **CSS Styling:** The browser applies the rules from `PhuketPropertyTeaser.css` to make the teaser look correct (colors, layout, etc.).
6.  **JS Interaction:** The browser runs the `init()` function in `PhuketPropertyTeaser.js`, which might set up things like the image carousel.

## Conclusion

Components in `s45_phuket` follow a consistent structure, typically involving separate files for PHP logic (`.php`), HTML structure (`.tpl.php`), CSS styling (`.css`), and JavaScript interactions (`.js`). Some also include an `.editor.php` for admin settings.

This "separation of concerns," like having different instruction sheets for a model kit, makes components modular, easier to understand, and much simpler to maintain and update. You know exactly where to look whether you need to change logic, layout, styling, or interactivity.

In the next chapter, we'll dive deeper into how data, like the property details we saw stored in `$this->propertyDto`, is structured and handled using Data Transfer Objects (DTOs) and Active Record (AR).

Next: [Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 3: Data Entities (AR/DTO)

In the [previous chapter](02_component_structure__php_tpl_js_css__.md), we learned how Components like `PhuketPropertyTeaser` are built using separate PHP, TPL, CSS, and JS files. We saw that the PHP file (`.php`) prepares data, often storing it in a variable like `$this->propertyDto`, and the Template file (`.tpl.php`) uses this data to build the HTML.

But what exactly *is* that `$this->propertyDto`? How does the system know what information a "Property" should contain (like price, bedrooms, photos)? And how does it get that information from the database?

This is where **Data Entities** come in.

## What Problem Do Data Entities Solve? The Need for Blueprints

Imagine our real estate website. We deal with several core concepts:

*   **Properties:** Villas, apartments, houses.
*   **Projects:** New developments containing multiple properties.
*   **Users:** Administrators or agents using the system.
*   **Options:** Reusable details like Locations (e.g., "Patong Beach"), Features (e.g., "Swimming Pool"), or Property Types (e.g., "Villa").

We need a consistent and organized way to represent these concepts in our code. If one part of the code needs property details, it needs to know *what* details are available (name, price, area, photos?) and where to find them. If we want to save changes to a property back into the database, we need a structured way to do that too.

Without a plan, different parts of the code might handle property data differently, leading to confusion, errors, and difficulty making changes.

Data Entities act like **detailed blueprints** or **digital records** for each core concept. They ensure everyone uses the same structure and understands what information defines a "Property," a "Project," etc.

In `s45_phuket`, we use two main types of Data Entities:

1.  **Active Record (AR):** Objects that represent data *in the database* and know how to load and save themselves.
2.  **Data Transfer Objects (DTO):** Simple objects whose main job is to *carry data* between different parts of the application in a structured way.

Let's look at each one.

## Active Record (AR): The Database Librarian

Think of the database as a massive library storing information about all our properties, projects, etc. An **Active Record (AR)** object is like a special librarian assigned to one specific item (like one particular villa).

*   **Knows its Item:** An AR object represents a single row in a database table (e.g., one `PhuketPropertyAR` object represents one specific property).
*   **Knows the Library System:** It contains methods to interact with the database – specifically, how to `load` its data from the database and how to `save` changes back to the database.
*   **Holds the Information:** It has properties (variables) that match the columns in the database table, holding the actual data for that item (e.g., `priceSale`, `bedrooms`, `description`).

**Example: `PhuketPropertyAR.php` (Simplified)**

This file defines the blueprint for a Property as it exists in the database.

```php
<?php
// File: classes/Site45/Sets/Phuket/Domain/PhuketPropertyAR.php
// (Simplified for clarity)

namespace Site45\Sets\Phuket\Domain;

// It inherits features from a base AR class
use Site45\Event\AR;

/**
 * Represents a Property record in the database.
 * Knows how to load/save itself.
 */
class PhuketPropertyAR extends AR {

  // --- Properties matching database columns ---

  public $arName = 'PhuketProperty'; // Type identifier

  public $id; // Unique ID (Primary Key)
  public $name; // Property name (multi-language)
  public $dealType; // e.g., 'sale', 'rent'
  public $propertyType; // e.g., 'villa', 'apartment'

  public $priceSale; // Sale price
  public $priceRentLongTime; // Long-term rent price

  public $bedrooms; // Number of bedrooms
  public $bathrooms; // Number of bathrooms
  public $areaLiving; // Living area in sqm
  public $description; // Description text (multi-language)

  public $photos = []; // List of photo details
  public $re_locality; // Link to the location (an Option ID)

  // ... many other properties representing database columns ...

  // --- Methods for database interaction (Simplified Concept) ---

  /**
   * (Conceptual) Loads data from the database for a given ID.
   * The actual loading is handled by Query Classes, but AR defines the structure.
   */
  public static function loadById($id) {
    // ... internal database logic to fetch data for this ID ...
    // ... creates a new PhuketPropertyAR object ...
    // ... fills its properties ($name, $priceSale, etc.) ...
    // return $propertyAR;
  }

  /**
   * (Conceptual) Saves the current object's data back to the database.
   */
  public function save() {
    // ... internal database logic ...
    // If $this->id exists, UPDATE the existing row.
    // If $this->id is new, INSERT a new row.
  }

  // Helper method run after properties are set (e.g., from database)
  protected function afterSetProps($props) {
    // Ensures 'photos' is always an array, even if null in DB
    if (!isset($props->photos)) {
      $this->photos = NULL; // Or maybe [] depending on convention
    }
    // ... other cleanup or default setting logic ...
  }
}
```

**Explanation:**

*   `class PhuketPropertyAR extends AR`: Defines a specific type of Active Record for Properties, inheriting base database functionalities.
*   `public $name;`, `public $priceSale;`, etc.: These properties directly correspond to columns in the `PhuketProperty` database table. They hold the actual data.
*   `loadById()` / `save()` (Conceptual): These represent the AR object's ability to interact with the database. In practice, loading is often handled by [Query Classes](04_query_classes_.md), but the AR class defines *what* data gets loaded into *which* properties. Saving might be triggered directly on the AR object.
*   `afterSetProps()`: A helper function that runs after data is loaded, useful for setting defaults or cleaning up data (like ensuring `$photos` is always an array).

**Key Idea:** The `PhuketPropertyAR` object *is* the property's record, directly linked to the database table.

## Data Transfer Objects (DTO): The Standardized Shipping Box

Now, imagine you need to send property information from one part of the system to another. For example, the part that fetches data from the database ([Query Classes](04_query_classes_.md)) needs to send the property details to the part that displays it ([Component](01_components__compo__.md)).

You *could* send the `PhuketPropertyAR` object directly. However, AR objects carry database logic (`save()`, `load()`) which the Component doesn't need. Sending the AR object is like shipping the whole librarian along with the book – unnecessary baggage!

This is where **Data Transfer Objects (DTOs)** come in. A DTO is like a **standardized shipping box**:

*   **Purpose-Built for Transport:** Its only job is to hold and transport data.
*   **Simple Structure:** It contains only public properties to hold the data, no complex logic or database methods.
*   **Decoupled:** It doesn't know or care about the database. It just holds the information it's given.

**Example: `PhuketPropertyDto.php` (Simplified)**

This file defines a simple container specifically for carrying property data.

```php
<?php
// File: classes/Site45/Sets/Phuket/Query/PropertySearch/PhuketPropertyDto.php
// (Simplified for clarity)

namespace Site45\Sets\Phuket\Query\PropertySearch;

// It inherits from a base DTO class (usually very simple)
use Site45\Base\Dto;

/**
 * A simple container (box) to hold and transfer Property data.
 * Does NOT talk to the database.
 */
class PhuketPropertyDto extends Dto {

  public $type = 'PhuketProperty'; // Identifier

  // --- Properties to hold data ---
  // These often mirror AR properties, but can be simplified or adjusted

  public $id;
  public $name; // Property name (multi-language)
  public $dealType;
  public $propertyType;

  public $priceSale;
  public $priceRentLongTime;

  public $bedrooms;
  public $bathrooms;
  public $areaLiving;
  public $description;

  public $photos = []; // List of photo details
  public $re_locality; // Location ID

  // DTOs might include extra processed info not in the DB table directly
  public $priceTeaser; // e.g., A formatted price for display
  public $inFavorites; // e.g., boolean indicating if user favorited it

  // ... other properties needed for transfer ...

  // NO database methods like save() or load() here!
}

```

**Explanation:**

*   `class PhuketPropertyDto extends Dto`: Defines a simple data container for Properties.
*   `public $id;`, `public $name;`, etc.: Public properties to hold the data. Notice they often match the AR properties, but they don't *have* to be identical. Sometimes DTOs include extra calculated or formatted data (`$priceTeaser`, `$inFavorites`) that isn't directly in the database table.
*   **Crucially:** There are no `save()` or `load()` methods. This object doesn't know how to talk to the database; it just holds data.

**Key Idea:** The `PhuketPropertyDto` is just a structured bag for carrying property information around.

## How AR and DTO Work Together

Let's revisit our use case: showing a property teaser using the `PhuketPropertyTeaser` component.

1.  **Request:** The system needs to display a teaser for Property ID `123`.
2.  **Query:** A [Query Class](04_query_classes_.md) (e.g., `PhuketPropertyQuery`) is used to fetch the data.
3.  **AR Loading (Internal):** Inside the Query Class, it likely uses the `PhuketPropertyAR` definition to understand the database structure and loads the data for ID `123` from the database into a (temporary) `PhuketPropertyAR` object.
4.  **DTO Creation:** The Query Class then creates a new, empty `PhuketPropertyDto` object.
5.  **Data Transfer:** The Query Class copies the relevant data from the loaded `PhuketPropertyAR` object into the properties of the `PhuketPropertyDto` object (e.g., `AR->name` goes into `DTO->name`, `AR->priceSale` goes into `DTO->priceSale`). It might also calculate extra fields like `DTO->priceTeaser`.
6.  **Return DTO:** The Query Class returns the filled `PhuketPropertyDto` object.
7.  **Component Usage:** The `PhuketPropertyTeaser.php` component receives this `PhuketPropertyDto` and stores it in its `$this->propertyDto` variable.
8.  **Template Display:** The `PhuketPropertyTeaser.tpl.php` template accesses the data directly from the DTO: `<?php print $this->propertyDto->name; ?>`, `<?php print $this->propertyDto->priceSale; ?>`.

**Why the extra step? Why not just pass the AR?**

*   **Separation of Concerns:** The Component doesn't need database logic. Using a DTO keeps the Component simple and focused only on displaying data.
*   **Clear Contract:** The DTO defines exactly what data the Component can expect, acting as a clear "contract" between the data-fetching layer and the presentation layer.
*   **Flexibility:** The DTO can be tailored. It might combine data from multiple AR objects or add calculated fields, providing exactly what the Component needs in one neat package.

## Under the Hood: The Data Flow

Here's a simplified diagram showing how AR and DTO work together when a component needs property data:

```mermaid
sequenceDiagram
    participant Compo as Component (e.g., PhuketPropertyTeaser.php)
    participant Query as Query Class (e.g., PhuketPropertyQuery)
    participant AR as Active Record (PhuketPropertyAR)
    participant DB as Database
    participant DTO as Data Transfer Object (PhuketPropertyDto)
    participant TPL as Template (PhuketPropertyTeaser.tpl.php)

    Compo->>Query: Get data for Property ID 123
    Query->>AR: Use AR definition to know what to load
    Query->>DB: Fetch raw data for Property 123
    DB-->>Query: Return raw data (columns/values)
    Query->>AR: Create AR object, populate with raw data
    Note over Query,AR: Data now in PhuketPropertyAR object
    Query->>DTO: Create empty PhuketPropertyDto
    Query->>DTO: Copy data from AR object to DTO object
    Query->>DTO: Calculate extra fields (e.g., priceTeaser)
    Query-->>Compo: Return the filled PhuketPropertyDto
    Compo->>Compo: Store DTO in $this->propertyDto
    Compo->>TPL: Pass data (via $this) to Template for rendering
    TPL->>DTO: Access data like $this->propertyDto->name
    TPL-->>Compo: Generate HTML fragment
```

## Code Examples Revisited

Let's quickly look at the key pieces again:

**1. The AR (`PhuketPropertyAR.php` - defines the DB record)**

```php
// Simplified AR Definition
class PhuketPropertyAR extends AR {
  public $id;
  public $name;
  public $priceSale;
  public $bedrooms;
  // ... other DB columns ...

  // Knows (conceptually) how to load/save
}
```

**2. The DTO (`PhuketPropertyDto.php` - defines the data carrier)**

```php
// Simplified DTO Definition
class PhuketPropertyDto extends Dto {
  public $id;
  public $name;
  public $priceSale;
  public $bedrooms;
  public $priceTeaser; // Maybe calculated
  // ... other needed properties ...

  // NO database logic
}
```

**3. Component PHP (`PhuketPropertyTeaser.php` - receives the DTO)**

```php
// Simplified Component PHP
class PhuketPropertyTeaser extends Compo {
  /** @var PhuketPropertyDto */ // Type hint helps!
  public $propertyDto; // Will hold the DTO

  protected function beforeRender($props) {
    $propertyId = $props['propertyId'];
    // Query class returns the DTO
    $this->propertyDto = PhuketPropertyQuery::create()->load($propertyId);
  }
}
```

**4. Component Template (`PhuketPropertyTeaser.tpl.php` - uses the DTO)**

```php
// Simplified Component Template
/* @var $this PhuketPropertyTeaser */ ?>
<div>
  <h3><?php print s45_lang($this->propertyDto->name); ?></h3>
  <div>Price: <?php print $this->propertyDto->priceSale; ?> THB</div>
  <div>Bedrooms: <?php print $this->propertyDto->bedrooms; ?></div>
  <!-- Access data directly from the DTO -->
</div>
```

## Conclusion

Data Entities provide the essential blueprints for the information our application manages, like Properties, Projects, and Users.

*   **Active Record (AR) (`PhuketPropertyAR`)** objects act like live records linked to the database. They define the structure based on database tables and conceptually handle loading and saving.
*   **Data Transfer Objects (DTO) (`PhuketPropertyDto`)** act like simple, standardized containers used purely to carry structured data between different parts of the application (like from a query result to a component).

Using both AR and DTO helps keep our code organized, separates database logic from presentation logic, and makes the flow of data clear and predictable. When a Component needs data, it usually receives it neatly packaged inside a DTO.

In the next chapter, we'll look at the [Query Classes](04_query_classes_.md) that actually perform the work of fetching data from the database (using AR definitions) and packaging it into DTOs.

Next: [Query Classes](04_query_classes_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 4: Query Classes

In the [previous chapter](03_data_entities__ar_dto__.md), we learned about Data Entities like Active Record (AR) and Data Transfer Objects (DTOs). DTOs are like neat boxes carrying structured data, for example, the details of a `PhuketPropertyDto`. But how does our application *get* the right boxes filled with the right data from the database library? If we need "all 3-bedroom villas for sale", who goes and finds them?

That's the job of **Query Classes**!

## What Problem Do Query Classes Solve? Finding the Right Information

Imagine you walk into a huge library (our database) full of records about properties, services, projects, etc. You need very specific information: "Show me all available villas with 3 bedrooms, located near Bangtao Beach, that cost less than $500,000."

You wouldn't just wander around hoping to stumble upon the right records. You'd go to a specialized librarian who knows exactly how to search the archives based on your specific request.

**Query Classes** are these specialized librarians for our application's data.

*   **They know what you're looking for:** You tell them the criteria (filters) like `dealType = 'sale'`, `bedrooms = 3`, `locality = 'Bangtao'`, `price <= 500000`.
*   **They know how to search:** They contain the specific instructions (database query logic, often SQL) needed to find matching records in the database tables.
*   **They bring back organized results:** They fetch the raw data and usually package it neatly into those DTOs we learned about ([Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)), ready for other parts of the application (like [Components](01_components__compo__.md)) to use.

Without Query Classes, the logic for searching and filtering data might be scattered all over the application, making it messy, repetitive, and hard to change. Query Classes centralize this data-fetching logic.

## What is a Query Class? The Specialized Librarian

A Query Class is a PHP class specifically designed to perform a certain *type* of data search. For instance:

*   `PhuketPropertyQuery`: Finds properties (villas, apartments).
*   `PhuketProjectQuery`: Finds development projects.
*   `PhuketServiceQuery`: Finds services (like airport transfers).
*   `PhuketOptionQuery`: Finds options (like locations, features).

Each Query Class typically has:

1.  **Properties for Filters:** Public variables you can set to specify your search criteria (e.g., `$dealType`, `$bedrooms`, `$limit`).
2.  **Execution Method (`exec()`):** A function you call to start the search. It uses the filter properties you set, builds and runs the database query, and processes the results.
3.  **Loading Method (`load()`):** Often, a function to fetch a *single* specific item by its ID (e.g., load one specific property).

## How to Use a Query Class: Fetching Properties

Let's simplify our earlier example. Suppose a [Component (Compo)](01_components__compo__.md) needs to display the first 10 published villas available for sale. Here's how it would use `PhuketPropertyQuery`:

**1. Inside the Component's PHP File (e.g., `SomeVillaListComponent.php`)**

```php
<?php
// Simplified Component PHP File

use Site45\Compo\Compo;
// Import the Query Class we need
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;
// Import the DTO it will return
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyDto;

class SomeVillaListComponent extends Compo {

  // This will hold the list of villas found
  public $villas; // Expecting an array of PhuketPropertyDto

  protected function beforeRender($props) {
    // 1. Create the query object
    $query = PhuketPropertyQuery::create();

    // 2. Set the search criteria (filters)
    $query->dealType = 'sale';       // We want properties for sale
    $query->propertyType = 'villa';  // Only villas
    $query->published = 1;           // Only published ones
    $query->limit = 10;              // Get maximum 10 results
    $query->sortBy = 'created';      // Sort by creation date
    $query->sortOrder = 'DESC';      // Newest first

    // 3. Execute the query!
    // The exec() method returns a SearchResultVO object
    $searchResult = $query->exec();

    // 4. Get the list of DTOs from the result
    // SearchResultVO has a ->rows property containing the DTOs
    $this->villas = $searchResult->rows;

    // Now $this->villas holds an array of PhuketPropertyDto objects
    // ready for the template (.tpl.php) file to display.
  }

  // ... constructor, etc. ...
}
```

**Explanation:**

1.  **`PhuketPropertyQuery::create()`**: We create an instance of our specialized "property librarian".
2.  **`$query->dealType = 'sale';` etc.**: We give the librarian our specific instructions by setting its public properties.
3.  **`$query->exec()`**: We tell the librarian: "Go find the information now!" This is where the magic happens – the Query Class talks to the database.
4.  **`$searchResult->rows`**: The `exec()` method usually returns a standard object (often `SearchResultVO`) which contains metadata (like the total number of found items) and the actual results in a property called `rows`. The `rows` property holds an array of `PhuketPropertyDto` objects, each representing one villa that matched our criteria.
5.  **`$this->villas = ...`**: Our component stores this list of DTOs in its own public property, making it available to its template file ([Component Structure (PHP/TPL/JS/CSS)](02_component_structure__php_tpl_js_css__.md)) for display.

That's it! The component doesn't need to know *how* the database search works; it just uses the Query Class like a tool.

## Under the Hood: How `exec()` Works (Simplified)

What happens inside the Query Class when you call `$query->exec()`?

```mermaid
sequenceDiagram
    participant Compo as Component
    participant QC as Query Class (e.g., PhuketPropertyQuery)
    participant DBAL as DB Abstraction Layer
    participant DB as Database
    participant DTO as Data Transfer Object (e.g., PhuketPropertyDto)

    Compo->>QC: Create Query & Set Filters (dealType='sale', limit=10)
    Compo->>QC: Execute (call exec())
    QC->>DBAL: Build DB Query based on filters (e.g., "SELECT * FROM properties WHERE dealType='sale' ... LIMIT 10")
    DBAL->>DB: Send SQL Query
    DB-->>DBAL: Return Raw Data Rows (database results)
    DBAL-->>QC: Give Raw Data Rows to Query Class
    QC->>QC: Prepare SearchResultVO object
    loop For each Raw Data Row
        QC->>DTO: Create DTO (PhuketPropertyDto)
        QC->>DTO: Populate DTO with Row Data (copy values)
        QC->>QC: Add populated DTO to SearchResultVO.rows
    end
    QC-->>Compo: Return SearchResultVO (filled with DTOs)
```

**Steps:**

1.  **Filters Set:** The Component creates the Query Class and sets filter properties like `$dealType`, `$limit`.
2.  **`exec()` Called:** The Component calls the `exec()` method.
3.  **Query Building:** Inside `exec()`, the Query Class looks at the filter properties that were set. It translates these into a database query (usually SQL). It often uses a helper layer (DB Abstraction Layer) to build this query safely and consistently.
4.  **Database Interaction:** The Query Class (via the abstraction layer) sends the constructed SQL query to the Database.
5.  **Raw Data Return:** The Database finds the matching rows and sends the raw data back.
6.  **DTO Creation:** The Query Class receives the raw data. It usually loops through each row returned by the database.
7.  **DTO Population:** For each row, it creates a new DTO object (e.g., `PhuketPropertyDto`) and copies the data from the raw row into the DTO's properties.
8.  **Result Packaging:** It collects all these created DTOs into the `rows` array of a result object (like `SearchResultVO`). This object might also contain other info like the total number of matching records found (`total`).
9.  **Return Result:** The `exec()` method finishes and returns the `SearchResultVO` object to the Component.

## A Peek Inside a Query Class (`PhuketServiceQuery.php`)

Let's look at a simplified version of `PhuketServiceQuery`, the "librarian" for finding services.

```php
<?php
// File: classes/Site45/Sets/Phuket/Query/ServiceSearch/PhuketServiceQuery.php
// (Highly Simplified)

namespace Site45\Sets\Phuket\Query\ServiceSearch;

use Site45\Base\QueryFromEvents; // Base class for queries using event system
use Site45\Sets\Phuket\Query\ServiceSearch\PhuketServiceDto;
use Site45\DtoLib\Base\SearchResultVO; // Standard result object

class PhuketServiceQuery extends QueryFromEvents {

  // --- Filter Properties ---
  public $serviceFor; // e.g., 'guest', 'owner'
  public $limit = 100; // Default limit
  public $start = 0;   // Default starting offset
  public $sortBy = 'created'; // Default sort field
  public $sortOrder = 'DESC'; // Default sort direction

  // --- The Execution Method ---
  public function exec() {
    // 1. Create the standard result object
    $searchResult = new SearchResultVO();
    $searchResult->filter = $this; // Store the filters used

    // 2. Get the database table name (defined elsewhere)
    $tableName = $this->table->getName(); // e.g., 'phuket_service_index'

    // 3. Start building the database query (using DB Abstraction Layer)
    $query = db_select($tableName); // SELECT * FROM phuket_service_index

    // 4. Add conditions based on filters set by the user
    if ($this->serviceFor) {
      // Add a WHERE clause: WHERE serviceFor = 'guest'
      $query->condition('serviceFor', $this->serviceFor);
    }
    // ... add more conditions for other filters ...

    // 5. Get total count (for pagination) - clone query before limits
    $queryTotal = clone $query;
    $queryTotal->addExpression('COUNT(*)', 'total');
    $searchResult->total = $queryTotal->execute()->fetchColumn(0);

    // 6. Add sorting and limits to the main query
    $query->fields($tableName); // Select all fields from the table
    $query->orderBy($this->sortBy, $this->sortOrder);
    $query->range($this->start, $this->limit);

    // 7. Execute the main query to get the actual rows
    $queryResult = $query->execute()->fetchAll();

    // 8. Process the raw results into DTOs
    if ($queryResult) {
      foreach ($queryResult as $tableRow) {
        // Data for each service is often stored serialized in one column
        $serviceData = @unserialize($tableRow->serviceDto);
        // Create a DTO from the unserialized data
        $serviceDto = PhuketServiceDto::create($serviceData);
        // Add the DTO to our result list
        $searchResult->rows[] = $serviceDto;
      }
    }

    // 9. Return the final result object
    return $searchResult;
  }

  // --- The Loading Method ---
  public function load($id) {
    // Similar logic to exec(), but specifically fetches one row by ID
    // and returns a single PhuketServiceDto or NULL.
    // ... implementation details ...
    $query = db_select($this->table->getName());
    $query->condition('id', $id);
    $query->fields($this->table->getName());
    $queryResult = $query->execute()->fetch();

    if($queryResult){
        $serviceData = @unserialize($queryResult->serviceDto);
        return PhuketServiceDto::create($serviceData);
    }
    return NULL;
  }

  // Other methods related to handling data updates (from QueryFromEvents)
  // ... like handlePhuketServiceSaved(), handlePhuketServiceDeleted() ...
  // These update the search index table when services are changed.
}
```

**Key Simplifications and Notes:**

*   **`extends QueryFromEvents`**: This query class inherits features related to automatically updating a dedicated search table (`phuket_service_index`) whenever a `PhuketService` is saved or deleted. This keeps the search table fast and up-to-date. Not all query classes do this.
*   **`db_select()`, `->condition()`, `->orderBy()`, `->execute()`**: These are functions from the database abstraction layer, providing a safe way to build and run SQL queries without writing raw SQL directly.
*   **`@unserialize($tableRow->serviceDto)`**: In this specific example, the pre-processed data for the DTO is stored in a single database column (`serviceDto`) in a serialized format. The query unserializes it to create the DTO object. This is an optimization technique used in this project. Other query classes might build DTOs directly from multiple columns.
*   **`load($id)`**: Shows the common pattern for fetching a single item.

This example illustrates the core job of a Query Class: take filters, build a query, execute it, and package the results (often as DTOs) for use elsewhere.

## Conclusion

Query Classes are the workhorses for fetching data in `s45_phuket`. They act like specialized librarians, taking search criteria (filters, sorting, limits) and efficiently retrieving the requested information from the database.

*   They encapsulate complex database query logic.
*   They provide a clean interface for other parts of the system (like [Components](01_components__compo__.md)) to request data.
*   They typically return data neatly packaged in [Data Entities (AR/DTO)](03_data_entities__ar_dto__.md), usually DTOs within a `SearchResultVO` object.

By using Query Classes, we keep our data fetching logic organized, reusable, and separate from the UI presentation logic, making the application easier to understand and maintain.

In the next chapter, we'll explore how the system generates SEO-friendly metadata (like page titles and descriptions) for different pages, often using the data fetched by these Query Classes.

Next: [SEO Generation](05_seo_generation_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 5: SEO Generation

In the [previous chapter](04_query_classes_.md), we learned how **Query Classes** act like specialized librarians, fetching exactly the data we need from the database, often packaging it into handy DTO boxes. For example, we saw how `PhuketPropertyQuery` could find specific properties based on filters like price or location.

Now, imagine we have a page showing the results of a property search, maybe "Villas for sale near Bangtao Beach". How do we tell search engines like Google what this specific page is about? And how do we make the title shown in the browser tab relevant to the search? Doing this well helps people find our pages through search engines. This is where **SEO Generation** comes in.

## What Problem Does SEO Generation Solve? Telling Search Engines About Dynamic Pages

Search Engine Optimization (SEO) involves making web pages easy for search engines to understand and rank. Key pieces of information include:

1.  **Page Title (`<title>`):** The text shown in the browser tab and as the main blue link in search results.
2.  **Meta Description (`<meta name="description">`):** A short summary shown below the title in search results. It encourages users to click.
3.  **H1 Heading (`<h1>`):** The main heading displayed *on* the page itself, telling visitors what the page is about.

Think of these like the **cover title**, **back cover blurb**, and **main chapter title** of a book. They give a quick, accurate summary of the content.

Now, consider our real estate site. We have:

*   Search results pages (potentially thousands of combinations of filters!).
*   Individual property pages.
*   Project pages (for new developments).
*   Location pages (showing properties in a specific area).

Manually writing unique, optimized titles, descriptions, and H1 headings for every single possible page is impossible! A search for "3-bedroom villas for sale in Patong" needs different SEO text than "2-bedroom apartments for rent in Karon".

This is the problem **SEO Generation** solves. It provides a way to **automatically create relevant SEO metadata** based on the specific content or filters being used for a page.

## What are SEO Generator Classes? The Automated Copywriters

In `s45_phuket`, we have a set of helper classes located in `classes/Site45/Sets/Phuket/Funcs/` that act like **automated copywriters**. Their job is to take information about the current page (like search filters or details about a specific property/project) and generate optimized Title, Description, and H1 text.

Key examples include:

*   `PhuketSearchSeo2`: Seems specifically designed to generate SEO text for **property search result pages**, particularly for the **English** language version of the site. It takes the user's search filters as input.
*   `PhuketProjectSeo`: Generates SEO text for **individual project detail pages**. It takes the project's data ([DTO](03_data_entities__ar_dto__.md)) as input.
*   `PhuketLocationSeo`: Generates SEO text for pages dedicated to **specific locations** (like Patong Beach). It takes the location's data ([DTO](03_data_entities__ar_dto__.md)) as input.

These classes contain the logic and templates to assemble meaningful and relevant SEO text dynamically.

## Use Case: Generating SEO for Search Results (English)

Let's say a user performs a search on the English version of the site for: "2 bedroom apartments for rent near Karon Beach with a price up to 50,000 THB/month".

**1. Input:** The system has a filter object representing this search. It might look something like this (simplified concept):

```php
// This object holds the user's search criteria
$filter = (object) [
  'dealType' => 'rent',
  'propertyType' => ['apartment'],
  'bedrooms' => [2], // Looking for 2 bedrooms
  'subLocality' => [35], // Assume 35 is the ID for Karon Beach
  'priceTo' => 50000,
  // ... other potential filters ...
];
$lang = 'en'; // Current language is English
```

**2. Using the Generator:** A [Component (Compo)](01_components__compo__.md) responsible for displaying the search results page would use `PhuketSearchSeo2` to generate the SEO metadata:

```php
// Import the necessary class
use Site45\Sets\Phuket\Funcs\PhuketSearchSeo2;

// Create an instance of the SEO generator
$seoGenerator = PhuketSearchSeo2::create();

// Generate the parts for H1, Title, and Description
$h1Array = $seoGenerator->makeH1En($filter);
$titleArray = $seoGenerator->makeTitleEn($filter);
$descrArray = $seoGenerator->makeDescrEn($filter);

// Combine the parts into final strings
$h1 = implode(' ', $h1Array);
$title = implode(' ', $titleArray);
$description = implode(' ', $descrArray);

// Now, these variables can be used to set the page's H1, title tag,
// and meta description tag in the HTML output.
```

**Explanation:**

*   We create an instance of `PhuketSearchSeo2`.
*   We call methods like `makeH1En()`, `makeTitleEn()`, and `makeDescrEn()`, passing in the `$filter` object containing the user's search criteria.
*   These methods return arrays of text pieces.
*   `implode(' ', ...)` joins these pieces together with spaces to form the final strings.

**3. Output:** Based on the input filters, the generated strings might look something like this:

*   **`$h1`:** `2 bedrooms apartments for Rent in Phuket (Karon)`
*   **`$title`:** `Holiday 2 bedrooms apartments for Rent in Karon | Price to 50 000 bat | Phuket - IndreamsPhuket.com`
*   **`$description`:** `The best offers for renting holliday apartments in Phuket (Karon) | Price to 50 000 bat | 2 bedrooms | We have been working for over 12 years, clients trust us and stay with us!`

Notice how the generated text directly reflects the user's search ("2 bedrooms", "apartments", "rent", "Karon", price). This is much better for SEO than a generic title like "Search Results".

## Use Case: Generating SEO for a Project Page

Imagine we're displaying the details page for a project called "Paradise Villas".

**1. Input:** The page component would first load the project's data using a [Query Class](04_query_classes_.md), resulting in a `PhuketProjectDto` object.

```php
// Assume $projectDto holds the loaded data for "Paradise Villas"
// $projectDto->name->en = 'Paradise Villas';
// $projectDto->subLocality->name->en = 'Rawai';
// $projectDto->locality->name->en = 'Phuket';
// ... other project details ...

$lang = 'en'; // Current language
```

**2. Using the Generator:** The component would use `PhuketProjectSeo` to generate the title and description:

```php
// Import the necessary class
use Site45\Sets\Phuket\Funcs\PhuketProjectSeo;

// Assume $h1 is already set for the page based on project name
$h1 = s45_lang($projectDto->name); // e.g., "Paradise Villas"

// Generate Title and Description using the project DTO
$title = PhuketProjectSeo::getTitle($projectDto, $lang, $h1);
$description = PhuketProjectSeo::getDescr($projectDto, $lang);

// These strings are now ready to be used in the page's HTML.
```

**Explanation:**

*   We use the static methods `getTitle()` and `getDescr()` from `PhuketProjectSeo`.
*   We pass the `$projectDto` containing all the project details and the current `$lang`.
*   The methods return the generated strings.

**3. Output:** The generated strings would incorporate details from the `$projectDto`:

*   **`$title`:** `Paradise Villas, Rawai Phuket - 5 apartments for Sale & 3 for Rent ✔ Just actual real estate properties - IndreamsPhuket.com` (Note: Property counts are often calculated separately and made available, e.g., via `$GLOBALS['SEO']['projectPropsCount']` as seen in the code).
*   **`$description`:** `Find 5 apartments, 3 villas at Paradise Villas, Rawai Phuket - 2022 year of construction ✔ Pool ✔ Gym ⭐ Guaranteed income 7% ☎ +66 123 4567` (Includes details like property types, location, amenities, contact info).

## How It Works Under the Hood (Simplified)

How do these generator classes turn filters or DTOs into nice sentences?

```mermaid
sequenceDiagram
    participant Page as Page Component
    participant SEO as SEO Generator (e.g., PhuketSearchSeo2)
    participant Input as Filter Object / DTO
    participant OptQ as Option Query
    participant DB as Database

    Page->>SEO: Request SEO for Input (Filter/DTO)
    SEO->>Input: Read properties (e.g., dealType, bedrooms, location IDs)
    opt Input contains Location IDs
        SEO->>OptQ: Request location names for IDs
        OptQ->>DB: Fetch Option data from DB
        DB-->>OptQ: Return Option names (e.g., 'Karon')
        OptQ-->>SEO: Return location names
    end
    SEO->>SEO: Apply internal rules/templates based on Input data
    Note right of SEO: e.g., if dealType='rent', add 'for Rent'
    SEO->>SEO: Assemble text pieces into an array (e.g., ["2 bedrooms", "apartments", "for Rent", "in Karon"])
    SEO->>SEO: Join array pieces into final string
    SEO-->>Page: Return generated Title, Description, H1 strings
```

**Steps:**

1.  **Receive Input:** The Page Component calls a method on the SEO Generator class (e.g., `makeTitleEn()`) and passes the filter object or DTO.
2.  **Read Data:** The generator method reads the relevant properties from the input object (e.g., `dealType`, `bedrooms`, `subLocality` list).
3.  **Fetch Additional Info (if needed):** If the input contains IDs (like location IDs), the generator might use a [Query Class](04_query_classes_.md) (like `PhuketOptionQuery`) to fetch the corresponding names (e.g., turn location ID `35` into the name "Karon").
4.  **Apply Rules & Templates:** The core logic involves checking the input data and applying predefined rules or templates. For example:
    *   If `bedrooms` contains `[2]`, add the piece "2 bedrooms".
    *   If `dealType` is `'rent'`, add the piece "for Rent".
    *   If `subLocality` names are fetched, add them like "in Karon".
5.  **Assemble Pieces:** The relevant text pieces are gathered into an array in a specific order.
6.  **Join and Return:** The array pieces are joined together (usually with spaces) to form the final SEO string, which is then returned to the Page Component.

## Diving Deeper: A Look Inside the Code

Let's peek at highly simplified snippets to see the pattern.

**Example: Inside `PhuketSearchSeo2::makeH1En()`** (Generates H1 for English search)

```php
// Simplified snippet from classes/.../Funcs/PhuketSearchSeo2.php
protected function makeH1En($filter) {
    $h1Array = array();

    // Check if 'bedrooms' filter is set
    if($bedrooms = $this->getBedrooms($filter)){ // Calls helper method
      $h1Array[] = implode(', ', $bedrooms) .' bedrooms'; // Add piece
    }

    // Check property types (logic simplified)
    $propTypes = $this->getPropertyTypesCorrected($filter); // Helper
    $h1Array[] = implode(', ', $propTypes); // Add piece (e.g., 'apartments')

    // Check deal type
    $h1Array[] = $this->getDealType($filter, 'H1'); // Helper (e.g., 'for Rent')

    $h1Array[] = 'in Phuket'; // Add static piece

    // Check for locations
    if($beaches = $this->getBeaches($filter)){ // Helper fetches names
      $h1Array[] = '(' . implode(', ', $beaches) . ')'; // Add piece (e.g., '(Karon)')
    }

    // ... make first letter uppercase ...

    return $h1Array; // Return the array of pieces
}
```

**Explanation:**

*   The method initializes an empty array `$h1Array`.
*   It calls several helper methods (`getBedrooms`, `getPropertyTypesCorrected`, `getDealType`, `getBeaches`) which look at the `$filter` object and return specific text pieces or arrays of pieces.
*   Each relevant piece is added to the `$h1Array`.
*   Finally, the array containing all the H1 parts is returned.

**Example: Inside `PhuketSearchSeo2::getBeaches()`** (Fetches location names)

```php
// Simplified snippet from classes/.../Funcs/PhuketSearchSeo2.php
protected function getBeaches($filter){
    $beaches = array();
    // Check if subLocality filter is set and is an array
    if(isset($filter->subLocality) AND is_array($filter->subLocality)){
      foreach ($filter->subLocality as $locId) {
        // Use Option Query to load location data by ID
        if ($locDto = PhuketOptionQuery::create()->load($locId)) {
          // Get the English name from the loaded DTO
          $beaches[] = s45_lang($locDto->name, 'en'); // s45_lang gets text
        }
      }
    }
    return $beaches; // Return array of beach names
}
```

**Explanation:**

*   It checks if the `$filter` has `subLocality` IDs.
*   It loops through each ID.
*   For each ID, it uses `PhuketOptionQuery` ([Query Classes](04_query_classes_.md)) to load the corresponding location DTO from the database.
*   It extracts the English name using the `s45_lang()` helper function and adds it to the `$beaches` array.
*   It returns the array of names (e.g., `['Karon']`).

## What about Static Pages or Manual Overrides?

While these dynamic generators (`PhuketSearchSeo2`, `PhuketProjectSeo`, etc.) are great for pages with changing content, what about more static pages like the "About Us" or "Contact" page? Or what if we want to manually override the automatically generated SEO for a specific search result URL?

For this, the project also seems to have:

*   `PhuketPageSeoAR`: An [Active Record](03_data_entities__ar_dto__.md) entity representing manually defined SEO metadata for a specific page path (URL).
*   `PhuketPageSeoQuery`: A [Query Class](04_query_classes_.md) to search for and load these manual SEO definitions based on the current page's URL.

This suggests a system where the application first checks if manual SEO data exists for the current URL using `PhuketPageSeoQuery`. If found, it uses that. If not found, it falls back to using the dynamic generators (like `PhuketSearchSeo2`) based on the page's content or filters.

## Conclusion

SEO Generation is crucial for making a content-rich site like a real estate portal discoverable by search engines. Instead of trying to manually write SEO text for every possible page, `s45_phuket` uses dedicated **SEO Generator Classes** (like `PhuketSearchSeo2`, `PhuketProjectSeo`, `PhuketLocationSeo`).

These classes act as **automated copywriters**. They take context (like search filters or data DTOs) as input and use internal rules and templates to dynamically construct relevant and optimized Page Titles, Meta Descriptions, and H1 Headings. This ensures that even pages generated on-the-fly (like search results) present clear and specific information to both users and search engines.

In the next chapter, we'll shift gears and look at a different kind of calculation: how the system determines rental prices for different periods (e.g., daily, weekly, monthly).

Next: [Rental Period Calculation](06_rental_period_calculation_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 6: Rental Period Calculation

In the [previous chapter](05_seo_generation_.md), we saw how the system automatically generates SEO-friendly text like titles and descriptions for dynamic pages. Now, let's switch gears and look at another important calculation: figuring out the total rental price for a property over a specific period.

## What Problem Does Rental Period Calculation Solve? Complex Rental Pricing

Imagine a customer wants to rent a beautiful villa in Phuket from July 1st to July 10th. How much should they pay? It's not always as simple as multiplying a single nightly rate by the number of nights. Consider these factors:

1.  **Tiered Pricing:** Like hotels, properties often have different rates depending on the length of stay. A daily rate might be higher than the *effective* daily rate if someone books for a whole week or month (think bulk discounts). There might be specific rates for 1 day, 3 days, 1 week, 2 weeks, or a month.
2.  **Seasonal Rates:** The price per day/week/month can change drastically depending on the time of year. High season (like December/January) is typically much more expensive than the low season (like September/October). A rental period might even cross over two different seasons!

Calculating the final price accurately requires checking the duration to determine the correct pricing *tier* (daily, weekly, etc.) and then looking up the specific rate for *each day* within the requested period based on the property's seasonal price table.

This is the problem the **`PhuketPeriod`** class solves. It acts like a specialized **rental price calculator** that handles these complexities.

## The Key Players: The Calculator and the Price List

To calculate the rental price, we primarily use two things:

1.  **`PhuketPeriod` Class:** This is the main calculator. You give it the start date, end date, and the property ID, and it figures out the total price.
2.  **`PhuketPeriodPriceDto`:** This is a small blueprint, a [Data Transfer Object (DTO)](03_data_entities__ar_dto__.md), that defines the structure for *one row* in a property's seasonal price table. Think of it like a single entry in the hotel's price list for a specific season (e.g., "High Season: Dec 15 - Jan 15"). It holds the start/end dates for that season (`from`, `to`) and the prices for different durations within that season (`day`, `day3`, `week`, `week2`, `month`).

**Where does the actual price list come from?** Each property in the system has its own rental price table stored with its data. When `PhuketPeriod` needs to calculate a price, it first fetches this table (which is essentially an array of data matching the `PhuketPeriodPriceDto` structure) from the property's record, likely via its [Active Record (AR)](03_data_entities__ar_dto__.md) representation (`PhuketPropertyAR`).

## How to Use `PhuketPeriod`: Calculating a Rental Price

Let's say we want to find the price for renting property with ID `123` from `2024-07-01` to `2024-07-10`.

**1. Inside a Component or API Handler**

```php
<?php
// Import the calculator class
use Site45\Sets\Phuket\Funcs\Period\PhuketPeriod;

// Define the rental details
$propertyId = 123;
$fromDate = '2024-07-01';
$toDate = '2024-07-10';

// 1. Create an instance of the calculator
$periodCalculator = PhuketPeriod::create();

// 2. Call the getPrice method
$totalPrice = $periodCalculator->getPrice($fromDate, $toDate, $propertyId);

// 3. Use the result
echo "The total rental price is: " . round($totalPrice) . " THB";

// (Optional) Get debug information
// $debugInfo = $periodCalculator->getPrice($fromDate, $toDate, $propertyId, TRUE);
// print_r($debugInfo);

?>
```

**Explanation:**

1.  **`PhuketPeriod::create()`:** We get a new instance of our price calculator.
2.  **`$periodCalculator->getPrice(...)`:** We call the main calculation method, passing:
    *   `$fromDate`: The check-in date string.
    *   `$toDate`: The check-out date string.
    *   `$propertyId`: The unique ID of the property we want to rent.
    *   (Optional `TRUE`): If you pass `TRUE` as the last argument, it returns detailed debugging information instead of just the price.
3.  **`$totalPrice`:** The method returns the calculated total rental price in the base currency (likely THB - Thai Baht).

That's it! The component doesn't need to worry about the seasons or pricing tiers; it just asks `PhuketPeriod` for the final price.

## How It Works Under the Hood: The Calculation Steps

What magic happens inside `$periodCalculator->getPrice()`?

**1. High-Level Walkthrough:**

Imagine the calculator gets the request: Property `123`, from `2024-07-01` to `2024-07-10`.

1.  **Load Price Table:** It first asks the system (using `PhuketPropertyAR::load()`) to get the rental price table stored for property `123`. This table might look like:
    *   Row 1 (`PhuketPeriodPriceDto` data): Low Season (May 1 - Oct 31) - Day: 3000, Week: 18000, Month: 60000...
    *   Row 2 (`PhuketPeriodPriceDto` data): High Season (Nov 1 - Apr 30) - Day: 5000, Week: 30000, Month: 90000...
2.  **Calculate Duration:** It calculates the total number of days: July 1st to July 10th is 10 days.
3.  **Determine Pricing Tier:** Based on the duration (10 days), it determines the *primary* pricing tier to use. The logic is roughly:
    *   Less than 3 days? Use the `day` rate column.
    *   Less than 7 days? Use the `day3` rate column (price is for 3 days).
    *   Less than 14 days? Use the `week` rate column (price is for 7 days).
    *   Less than 30 days? Use the `week2` rate column (price is for 14 days).
    *   30 days or more? Use the `month` rate column (price is for 30 days).
    *   In our 10-day example, it falls under "less than 14 days", so the `week` rate column will be used. This means the weekly price will form the basis of the daily calculation.
4.  **Iterate Through Each Day:** It then loops through each day of the rental period (July 1st, July 2nd, ... July 10th).
5.  **Find Seasonal Rate for the Day:** For each specific day (e.g., July 1st):
    *   It looks through the loaded price table (the list of `PhuketPeriodPriceDto` data) to find which season row applies to that date. (July 1st falls into the "Low Season" row).
    *   It gets the price from the *determined tier column* (`week`) for that season. (Low Season `week` price is 18000 THB).
6.  **Calculate Effective Daily Rate:** It calculates the effective cost for *that specific day* based on the tier. Since we're using the `week` tier (which represents 7 days), the effective daily rate for July 1st is `18000 / 7 = ~2571.43` THB.
7.  **Sum Daily Rates:** It adds this effective daily rate to a running total.
8.  **Repeat:** It repeats steps 5-7 for July 2nd, July 3rd, ..., July 10th. Note that if the period crossed into a different season (e.g., renting from Oct 25 to Nov 5), the seasonal rate lookup in step 5 would fetch a different price for the November days.
9.  **Return Total:** After looping through all days, it returns the final summed-up total price.

**2. Sequence Diagram:**

This diagram shows the interaction when a component asks for the price:

```mermaid
sequenceDiagram
    participant Comp as Component/API
    participant Period as PhuketPeriod
    participant PropAR as PhuketPropertyAR
    participant PriceTable as Property Price Table (Data)

    Comp->>Period: getPrice('2024-07-01', '2024-07-10', 123)
    Period->>PropAR: load(123)
    PropAR-->>Period: Return Property Data (inc. PriceTable)
    Period->>Period: Calculate duration (10 days)
    Period->>Period: Determine Tier Col ('week') based on duration
    Period->>Period: Start Loop (Day 1: July 1)
    Period->>PriceTable: Find Season Row for July 1 (Low Season)
    Period->>PriceTable: Get 'week' price for Low Season (18000)
    Period->>Period: Calculate daily rate (18000 / 7)
    Period->>Period: Add to total
    Period->>Period: Loop (Day 2: July 2) ... (Day 10: July 10)
    Period-->>Comp: Return Final Total Price
```

**3. Code Dive:**

Let's look at simplified versions of the key methods inside `PhuketPeriod`.

**`PhuketPeriod::getPrice()` (Simplified)**

```php
// File: classes/Site45/Sets/Phuket/Funcs/Period/PhuketPeriod.php

protected $priceTable; // Will hold the property's price data

public function getPrice($from, $to, $propertyId, $debug = FALSE){

    // Convert dates to timestamps
    $fromTs = strtotime($from);
    $toTs = strtotime($to);

    // 1. Load property data & price table
    $propAR = PhuketPropertyAR::load($propertyId);
    $this->priceTable = $propAR->priceRent; // Get the array of price rules

    // 2. Calculate total days
    $daysCount = (int) (($toTs - $fromTs)/60/60/24 + 1.1);

    // 3. Determine which price column (tier) to use based on duration
    $colInfo = $this->getPriceCol($fromTs, $toTs, $daysCount);
    // $colInfo['colName'] is e.g., 'week'
    // $colInfo['days'] is e.g., 7 (days represented by the 'week' price)

    $totalPrice = 0;
    // 4. Loop through each day in the period
    for($i = 0; $i < $daysCount; $i++){
        $currentDayTs = $fromTs + $i*60*60*24; // Timestamp for the current day

        // 5. Find the seasonal price for this day using the chosen tier column
        $priceForTierPeriod = $this->getPriceColForDate($currentDayTs, $colInfo['colName']);

        // 6. Calculate the effective daily rate for THIS day
        $priceForDay = ($priceForTierPeriod / $colInfo['days']) + 0.0001; // Add small amount for precision

        // 7. Add to total
        $totalPrice += $priceForDay;
    }

    // Handle debug output if requested...

    // 9. Return final price
    return $totalPrice;
}
```

**`PhuketPeriod::getPriceCol()` (Simplified - Determines Tier)**

```php
// File: classes/Site45/Sets/Phuket/Funcs/Period/PhuketPeriod.php

protected function getPriceCol($from, $to, $daysCount) {
    // Default to monthly rate
    $colName = 'month';
    $daysInTier = 30;

    // Check shorter durations
    if($daysCount < 30 ){
        $colName = 'week2'; // Bi-weekly rate
        $daysInTier = 14;
    }
    if($daysCount < 14){
        $colName = 'week'; // Weekly rate
        $daysInTier = 7;
    }
    if($daysCount < 7){
        $colName = 'day3'; // 3-day rate
        $daysInTier = 3;
    }
    if($daysCount < 3){
        $colName = 'day'; // Daily rate
        $daysInTier = 1;
    }

    // Return the column name and the number of days it represents
    return array(
        'colName' => $colName, // e.g., 'week'
        'days' => $daysInTier, // e.g., 7
    );
}
```

**`PhuketPeriod::getPriceColForDate()` (Simplified - Finds Seasonal Price)**

```php
// File: classes/Site45/Sets/Phuket/Funcs/Period/PhuketPeriod.php

protected function getPriceColForDate($dateTs, $colName) {
    $year = date('Y', $dateTs);

    // Loop through each row (season) in the property's price table
    foreach ($this->priceTable as $periodData) {
        // Create a DTO for easier access to this season's data
        $periodDto = PhuketPeriodPriceDto::create($periodData);

        // Construct full start/end dates for the season in the relevant year
        // (Handles seasons crossing New Year, e.g., Dec 15 - Jan 15)
        $seasonStartStr = $periodDto->from . '.' . $year; // e.g., "01.11.2024"
        $seasonEndStr = $periodDto->to . '.' . $year;     // e.g., "30.04.2024"
        // ... (logic here to adjust years if season spans Dec/Jan) ...
        $seasonStartTs = strtotime( /* adjusted start date */ );
        $seasonEndTs = strtotime( /* adjusted end date */ );


        // Check if the current day falls within this season's date range
        if (($dateTs >= $seasonStartTs) && ($dateTs <= $seasonEndTs)) {
            // YES! Return the price from the correct column for this season
            return $periodDto->{$colName}; // e.g., $periodDto->week
        }
    }

    // If no price rule found for this date (shouldn't happen ideally)
    return 0;
}
```

**`PhuketPeriodPriceDto.php` (The Price List Row Blueprint)**

This simple DTO just defines the expected structure for each row in the `priceRent` table of a property.

```php
<?php
// File: classes/Site45/Sets/Phuket/Funcs/Period/PhuketPeriodPriceDto.php

namespace Site45\Sets\Phuket\Funcs\Period;

use Site45\Base\Dto; // Basic DTO functionality

/**
 * Represents one seasonal pricing rule in a property's rental price table.
 */
class PhuketPeriodPriceDto extends Dto{

  public $type = 'PhuketPeriodPrice'; // Identifier

  // Season date range (e.g., "15.12", "15.01")
  public $from;
  public $to;

  // Prices for different tiers *during this season*
  public $day;   // Price per day (if booking 1-2 days)
  public $day3;  // Price for 3 days (if booking 3-6 days)
  public $week;  // Price for 7 days (if booking 7-13 days)
  public $week2; // Price for 14 days (if booking 14-29 days)
  public $month; // Price for 30 days (if booking 30+ days)
}
```

## Conclusion

Calculating rental prices isn't always straightforward due to tiered pricing based on duration and seasonal rate changes. The `s45_phuket` project uses the `PhuketPeriod` class as a dedicated calculator to handle this complexity.

By taking the desired dates and property ID, `PhuketPeriod` automatically:

1.  Loads the property's specific seasonal price table (structured using `PhuketPeriodPriceDto`).
2.  Determines the correct pricing tier (daily, weekly, etc.) based on the stay duration.
3.  Looks up the appropriate seasonal rate for *each day* of the stay.
4.  Calculates the effective daily cost based on the tier rate.
5.  Sums these daily costs to provide the final, accurate rental price.

This keeps the pricing logic centralized and makes it easy for other parts of the application, like booking forms, to get the correct price without needing to know all the underlying rules.

In the next chapter, we'll look at how forms, like the rental request form that might use this price calculation, are handled, and how components expose functionality via APIs.

Next: [Form Handling & API](07_form_handling___api__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 7: Form Handling & API

In the [previous chapter](06_rental_period_calculation_.md), we explored how the system calculates rental prices based on dates and seasons using the `PhuketPeriod` class. But how does a customer actually *submit* a rental request with their chosen dates? Or how do they send a simple contact message? This is where **Form Handling & API** comes into play.

## What Problem Does This Solve? The Website's Reception Desk

Think of your website as a hotel. Visitors browse the rooms (properties), check prices, and eventually, they might want to ask a question, make a booking request, or provide their contact details. They do this by filling out forms on the website.

But just having a form isn't enough. We need a system to:

1.  **Present the form:** Display the correct fields (name, email, dates, message, etc.).
2.  **Collect the information:** Get the data the user typed in.
3.  **Check for completeness (Validation):** Make sure required fields aren't empty and the email looks like an email. This often happens *instantly* in the browser.
4.  **Send the information:** Transmit the collected data securely to the website's backend for processing.
5.  **Process the information:** Save the request, perhaps send an email notification to a manager, and maybe send a confirmation back to the user.
6.  **Give feedback:** Let the user know their request was sent successfully.

This entire process is **Form Handling**. The **API** (Application Programming Interface) part refers to the specific backend "address" (URL) and rules that the website's frontend (JavaScript in the browser) uses to send the form data to the backend for processing.

Think of the whole system as the **website's reception desk**:

*   **Forms (Compos like `PhuketRentRequest`)**: The blank registration cards or inquiry sheets given to visitors.
*   **JavaScript Validation**: The quick check by the receptionist to see if the main fields on the card are filled out.
*   **JavaScript Submission (AJAX)**: Handing the filled card over the counter without needing to walk away and come back (no full page reload).
*   **Backend API (`FormApi2`)**: The main receptionist who takes the card, understands what type of request it is, and knows what to do next.
*   **Saving (`PhuketFormAR`)**: Filing the card away in the records cabinet.
*   **Notifications (Emails)**: Informing the relevant hotel staff (manager) about the new request and maybe sending a receipt (confirmation email) to the guest.

## Key Parts of the System

Let's break down the components involved:

1.  **Form Components:** These are [Components (Compo)](01_components__compo__.md) specifically designed to display HTML forms. Examples include:
    *   `PhuketRentRequest`: Shows the form for requesting a property rental.
    *   `PhuketSaleSelection`: A form for users interested in buying, allowing them to specify criteria.
    *   `PhuketMoreInfo`: A simpler form often used to ask for more details about a property.
    These components use PHP, TPL, CSS, and JS files as described in [Component Structure (PHP/TPL/JS/CSS)](02_component_structure__php_tpl_js_css__.md).

2.  **Client-Side JavaScript (`form_api.js`, `form_api2.js`)**: These JavaScript files run in the user's browser. They handle:
    *   **Validation:** Using libraries like jQuery Validate to check if required fields are filled correctly *before* sending the data. This provides instant feedback to the user.
    *   **AJAX Submission:** Using functions like `phuket_from_api2()` to send the form data to the backend API *without* reloading the entire webpage. This makes the user experience smoother.

3.  **Backend API Class (`FormApi2.php`)**: This PHP class acts as the central processor for incoming form submissions sent by the JavaScript. It receives the data, identifies the form type, and orchestrates the saving and notification steps.

4.  **Form Configuration (`PhuketFormConfAR`)**: This [Active Record](03_data_entities__ar_dto__.md) entity stores settings for different forms, such as email templates for notifications, MailChimp integration details, or subject lines. `FormApi2` loads this configuration to know how to handle a specific form submission.

5.  **Form Data Storage (`PhuketFormAR`)**: Another [Active Record](03_data_entities__ar_dto__.md) entity used to save the actual submitted data (name, email, message, etc.) into the database, creating a record of each submission.

6.  **Email Sending:** The system uses email libraries (potentially integrated with services like MailChimp via `MailChimp.php`) to send notifications to administrators and confirmation emails to users.

## Use Case: Submitting a "More Info" Request

Let's walk through what happens when a user fills out the `PhuketMoreInfo` form on a property page:

1.  **Display:** The `PhuketMoreInfo` [Component (Compo)](01_components__compo__.md) renders the HTML form (defined in its `.tpl.php` file), including fields for Name, Email, Phone, and Comment. The form tag might have attributes like `data-form-id="MoreInfoForm"`.
2.  **Fill & Submit:** The user fills in their details and clicks the "Send Request" button.
3.  **JS Intercept & Validate:** The `phuket_form_send2()` function (from `form_api2.js`, linked in the component's JS or template) intercepts the default form submission. It uses jQuery Validate (`phuket_form_validate2()`) to check if required fields (like Name, Email) are filled. If not, error messages appear next to the fields.
4.  **JS AJAX Send:** If validation passes, the `phuket_form_send2()` function calls `phuket_from_api2()`. This function gathers all the data from the form fields and sends it via an AJAX POST request to a specific backend URL, like `/s45PhuketFormAPI2/MoreInfoForm`. The `MoreInfoForm` part comes from the `data-form-id` attribute.
5.  **Backend API Receives:** The web server routes the request `/s45PhuketFormAPI2/MoreInfoForm` to the `FormApi2` class. An instance is created using `FormApi2::create('MoreInfoForm', $_POST)`.
6.  **API Processing (`FormApi2::exec`)**:
    *   It loads the configuration for `MoreInfoForm` using `PhuketFormConfAR::load('MoreInfoForm')`. This tells it things like the email subject line for manager notifications.
    *   It saves the submitted data (`name`, `email`, `phone`, `comment`, `objectId`) into the database by creating and saving a `PhuketFormAR` object.
    *   It might check the `PhuketFormConfAR` to see if a client confirmation email is configured. If so, it formats and sends one (perhaps using MailChimp).
    *   It formats and sends a notification email to the relevant manager(s) containing the submitted details (using `FormApi2::getHuman()` to make it readable).
7.  **API Response:** `FormApi2` sends a JSON response back to the browser's JavaScript, typically indicating success (`{ "status": 1 }`).
8.  **JS Feedback:** The `.success()` part of the `phuket_from_api2()` call in JavaScript receives the success response. It then:
    *   Calls `phuket_form_ok_message2()` to display a friendly "Thank you" popup message (defined in the component's PHP).
    *   Calls `phuket_form_clear2()` to clear the form fields.

The user sees the "Thank You" message, and the form is ready for another submission, all without the page having to reload!

## Under the Hood: The Form Submission Flow

Here’s a simplified sequence diagram showing the journey of the form data:

```mermaid
sequenceDiagram
    participant Browser as User's Browser (JS)
    participant Validator as JS Validation (jQuery Validate)
    participant API_JS as JS API Call (form_api2.js)
    participant Server as Web Server (API Endpoint)
    participant API_PHP as FormApi2 Class
    participant ConfAR as PhuketFormConfAR
    participant FormAR as PhuketFormAR (DB)
    participant Email as Email Service

    Browser->>Browser: User fills form & clicks Submit
    Browser->>Validator: Trigger validation
    alt Validation Fails
        Validator-->>Browser: Show error messages
    else Validation Passes
        Browser->>API_JS: Call phuket_form_send2()
        API_JS->>API_JS: Gather form data
        API_JS->>Server: AJAX POST to /s45PhuketFormAPI2/{formId} with data
        Server->>API_PHP: Instantiate FormApi2::create(formId, data)
        API_PHP->>API_PHP: Call exec()
        API_PHP->>ConfAR: Load configuration for formId
        ConfAR-->>API_PHP: Return form settings
        API_PHP->>FormAR: Create & Save submitted data
        FormAR-->>API_PHP: Confirm save
        API_PHP->>Email: Send Manager Notification
        opt Client Confirmation Configured
            API_PHP->>Email: Send Client Confirmation
        end
        Email-->>API_PHP: Confirm emails sent (or queued)
        API_PHP-->>Server: Return success JSON ({status: 1})
        Server-->>API_JS: Send JSON response back
        API_JS->>Browser: Trigger success actions
        Browser->>Browser: Show "Thank You" message
        Browser->>Browser: Clear form fields
    end

```

## Code Dive: Key Pieces Simplified

Let's look at simplified code snippets involved in submitting the `PhuketMoreInfo` form.

**1. Form HTML (in `PhuketMoreInfo.tpl.php`)**

```html
<!-- Simplified form structure -->
<form data-form-id="MoreInfoForm" data-form-message="<?php print s45_lang($this->content_message); ?>" >
  
  <!-- Hidden field to know which property the request is about -->
  <input type="hidden" name="objectId" value="<?php print $this->objectId; ?>">

  <div class="form-group">
    <label><?php print s45_lang($this->content_name); ?></label>
    <input type="text" name="name" required data-msg="Please enter your name">
  </div>

  <div class="form-group">
    <label>Email</label>
    <input type="email" name="email" required data-msg="Please enter a valid email">
  </div>
  
  <div class="form-group">
    <label>Phone</label>
    <input type="tel" name="phone" required data-msg="Please enter your phone number">
  </div>

  <div class="form-group">
    <label><?php print s45_lang($this->content_note); ?></label>
    <textarea name="note"></textarea>
  </div>

  <input type="submit" value="<?php print s45_lang($this->content_btn); ?>">

</form>

<!-- JavaScript initialization often happens here or in the component's .js file -->
<script>
  jQuery(document).ready(function() {
    // Find *this specific* form instance and set up validation/submission
    var form = jQuery('[s45-compo-id="<?php print $this->id; ?>"] form');
    if (form.length > 0) {
      phuket_form_send2("<?php print $this->id; ?>", form); 
    }
  });
</script>
```

*   `data-form-id="MoreInfoForm"`: Tells the JavaScript which form configuration to use on the backend.
*   `data-form-message="..."`: Provides the success message text.
*   `required data-msg="..."`: Attributes used by jQuery Validate.
*   `phuket_form_send2(...)`: Attaches the validation and AJAX submission logic from `form_api2.js` to this form.

**2. JavaScript (`form_api2.js`)**

```javascript
// Simplified function to set up validation and submit handler
function phuket_form_send2(compoId, myForm) {
    // 1. Setup validation rules (simplified)
    phuket_form_validate2(compoId, myForm);

    // 2. Define what happens on submit
    jQuery(myForm).on('submit', (function (e) {
        e.preventDefault(); // Stop normal page reload

        if (myForm.valid()) { // Check if form passes validation
            var formId = jQuery(myForm).attr('data-form-id');
            var successMessage = jQuery(myForm).attr('data-form-message');
            
            // Disable button to prevent double clicks
            jQuery(myForm).find('input[type=submit]').attr('disabled', true);

            // 3. Send data via AJAX using helper function
            phuket_from_api2(formId, this) // 'this' refers to the form element
              .success(function (result) {
                // Re-enable button
                jQuery(myForm).find('input[type=submit]').attr('disabled', false);
                
                // 4. Handle success response
                if (result.status) {
                    phuket_form_ok_message2(successMessage); // Show popup
                    phuket_form_clear2('[data-form-id="' + formId + '"]'); // Clear fields
                } else {
                    // Handle potential errors from backend
                }
              })
              .fail(function() {
                 // Handle AJAX call failure (network error, etc.)
                 jQuery(myForm).find('input[type=submit]').attr('disabled', false);
              });
        }
    }));
}

// Simplified AJAX helper function
function phuket_from_api2(FormId, formDataElement){
  // Use FormData to easily collect all fields, including files if any
  var formData = new FormData(formDataElement); 

  return jQuery.ajax({
	url: '/s45PhuketFormAPI2/' + FormId, // The backend API endpoint
	type: "POST",
	data: formData,
	processData: false, // Needed for FormData
	contentType: false, // Needed for FormData
	cache: false,
  });
}
```

*   This JS connects the form's submit event to the validation and AJAX process.

**3. Backend API (`FormApi2.php`)**

```php
<?php
namespace Site45\Sets\Phuket\Form;

use Site45\Sets\Phuket\Domain\PhuketFormConfAR; // Form configuration
use Site45\Sets\Phuket\Domain\PhuketFormAR;     // Form data storage
// ... other necessary imports

class FormApi2 {

    protected $formId;    // e.g., "MoreInfoForm"
    protected $formData;  // Object containing submitted data (name, email...)
    protected $formConf;  // Loaded PhuketFormConfAR object

    // Factory method to create instance
    public static function create($formId, $formData) {
        $api = new static();
        $api->formId = $formId;
        $api->formData = s45_toObject($formData); // Convert array to object
        // ... maybe load current manager info ...
        return $api;
    }

    // Main execution logic
    public function exec() {
        // 1. Load form configuration from database
        if ($this->formConf = PhuketFormConfAR::load($this->formId)) {

            // Log the submission (useful for debugging)
            watchdog('form_api2', 'Form filled: ' . $this->formConf->id . ' Data: <pre>' . print_r($this->formData, true) . '</pre>');

            // 2. Send email to client (if configured)
            $this->mailClient(); 

            // 3. Save the submitted data
            $formAR = $this->saveForm();

            // 4. Send notification email to manager
            $this->mailManager($formAR);

            // 5. Return success status
            return ['status' => 1];

        } else {
            // Handle case where form config doesn't exist
            watchdog('form_api2', 'Error: Config not found for form ' . $this->formId, WATCHDOG_ERROR);
            return ['status' => 0, 'error' => 'Configuration not found'];
        }
    }

    // Simplified: Saves data using PhuketFormAR
    protected function saveForm() {
        $formAR = PhuketFormAR::create();
        $formAR->name = $this->formConf->name; // Use configured name
        $formAR->data = $this->formData; // Store the actual submitted data
        $formAR->ip = $_SERVER['REMOTE_ADDR']; // Record user's IP
        $formAR->save();
        return $formAR; // Return the saved AR object
    }
    
    // Simplified: Sends email to client (uses MailChimp)
    protected function mailClient() {
        if (empty($this->formConf->mailClient) || empty($this->formData->email)) {
            return; // Don't send if not configured or no email provided
        }
        // ... logic to prepare email content using $this->formConf->mailText ...
        // ... logic to integrate with MailChimp::create()->...->exec() ...
    }

    // Simplified: Sends notification to manager
    protected function mailManager($formAR) {
        $title = 'Form filled: ' . $this->formConf->name;
        $messageBody = self::getHuman($formAR); // Format data nicely
        
        // ... use PHPMailer or similar library to configure and send email ...
        // ... send to configured manager emails (e.g., <EMAIL>) ...
    }

    // Helper to format submitted data for email
    public static function getHuman($formAR) {
        $humanReadable = '';
        $data = $formAR->data;
        $labels = ['name' => 'Name', 'email' => 'Email', 'phone' => 'Phone', 'note' => 'Comment', /*...*/];
        
        foreach ($data as $key => $value) {
            if (/* skip internal keys */) continue;
            $label = isset($labels[$key]) ? $labels[$key] : ucfirst($key);
            // ... format value nicely (handle arrays, object IDs, etc.) ...
            $humanReadable .= '<br><b>' . $label . ':</b> ' . htmlspecialchars($value);
        }
        $humanReadable .= '<br><br>IP: ' . $formAR->ip;
        return $humanReadable;
    }
}
```

*   This class acts as the central controller on the backend, handling the entire process after the data arrives from the browser.

## Conclusion

Handling forms is a fundamental part of most websites. In `s45_phuket`, this involves a combination of:

*   **Frontend Components (`PhuketRentRequest`, etc.)** to display the forms.
*   **Client-Side JavaScript (`form_api2.js`)** for instant validation and smooth AJAX submission.
*   A dedicated **Backend API (`FormApi2`)** to receive the submitted data.
*   **Configuration (`PhuketFormConfAR`)** to define how each form should be processed (e.g., email templates).
*   **Data Storage (`PhuketFormAR`)** to keep a record of submissions.
*   **Email Services** to send notifications.

This setup separates concerns: the frontend handles presentation and immediate user feedback, while the backend API manages the core logic of processing, saving, and notifying, acting like an efficient digital reception desk.

In the next chapter, we'll shift our focus to the administrative side of the website and look at the components used to build the admin interface.

Next: [Admin Interface Components](08_admin_interface_components_.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 8: Admin Interface Components

Welcome to Chapter 8! In the [previous chapter](07_form_handling___api__.md), we explored how visitors interact with the website by submitting forms and how the backend API processes these requests. Now, we'll switch perspectives and look behind the scenes at the tools used by website administrators to manage all the content. This is the website's "control room," and it's built using **Admin Interface Components**.

## What Problem Do Admin Components Solve? The Website Control Room

Imagine our real estate website is like a busy hotel. We've seen how guests (visitors) can browse rooms (properties) and make inquiries ([Form Handling & API](07_form_handling___api__.md)). But someone needs to manage the hotel itself! The hotel manager needs a way to:

*   Add new rooms (properties) or update details of existing ones.
*   See a list of all rooms and their current status.
*   Manage staff accounts (users).
*   Configure hotel options (like room types or amenities).
*   View guest inquiries (form submissions).

Doing this directly in the database is difficult and risky. Instead, the manager needs a user-friendly interface – a **control room** or **dashboard** – specifically designed for these tasks.

This is the problem **Admin Interface Components** solve. They are the building blocks used to create this private backend interface, making it easy and safe for administrators to manage the website's content and settings.

## What are Admin Components? Special Compos for the Backend

Admin Interface Components are simply [Components (Compo)](01_components__compo__.md) that are specifically designed for the administrative area of the website. They follow the same fundamental structure we saw in [Component Structure (PHP/TPL/JS/CSS)](02_component_structure__php_tpl_js_css__.md), using PHP for logic, TPL for HTML structure, CSS for styling, and JS for interactivity.

However, their *purpose* is different from the components visitors see. They provide the tools needed for management tasks. Think of them as specialized tools in the control room:

*   **`PhuketAdminPropertyList`**: A component that displays a list or table of all properties, often with filters and search options.
*   **`PhuketAdminPropertyEditor`**: A component that shows a form for adding a *new* property or editing the details of an *existing* one.
*   **`PhuketAdminProjectEditor`**: Similar to the property editor, but for managing development projects.
*   **`PhuketAdminUserList`**: Displays a list of website administrators or agents.
*   **`PhuketAdminOptionList`**: Used to manage reusable options like locations (e.g., "Patong Beach"), property features (e.g., "Swimming Pool"), or property types (e.g., "Villa"). These options often populate dropdown menus in other editor forms.
*   **`PhuketAdminFormList`**: Shows a list of all the inquiries or requests submitted through website forms.

These components work together to create a comprehensive backend system.

## Use Case: Managing Properties in the Admin Area

Let's look at a very common task: viewing and editing properties. This typically involves two main admin components working together.

### 1. Listing Properties (`PhuketAdminPropertyList`)

First, the administrator needs to see a list of all properties. This is handled by `PhuketAdminPropertyList`.

**The PHP File (`PhuketAdminPropertyList.php` - Simplified)**

This file fetches the list of properties to display.

```php
<?php
// File: classes/.../PhuketAdminPropertyList/PhuketAdminPropertyList.php
// (Simplified)
use Site45\Compo\Compo;
// We use Query Classes to find properties
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;

class PhuketAdminPropertyList extends Compo {

  // Will hold the list of properties found
  public $searchResult;
  // Holds any filters applied by the admin (e.g., searching by name)
  public $state_filter;

  protected function beforeRender($props) {
    // Create a property query
    $query = PhuketPropertyQuery::create($this->state_filter, FALSE);
    $query->limit = 20; // Show 20 properties per page
    $query->sortBy = 'number'; // Sort by property number
    $query->sortOrder = 'DESC';
    $query->showSaled = 1; // Include sold/rented properties

    // Execute the query to get the list of properties
    // The result usually contains an array of DTOs or ARs.
    $this->searchResult = $query->exec();
  }

  // API method to handle deleting a property (called via JS)
  public function apiDeleteProperty($propertyId) {
    // Load the property's Active Record object and delete it
    if($propertyAR = PhuketPropertyAR::load($propertyId)){
      $propertyAR->delete();
    }
    // Return success status & re-rendered list (simplified)
    return ['status' => 1, 'editedCompo' => $this->apiGetRendered()];
  }

  // ... Constructor, translations, etc. ...
}
```

**Explanation:**

*   It uses `PhuketPropertyQuery`, our "property librarian" from [Query Classes](04_query_classes_.md), to find the properties.
*   It applies filters (`$this->state_filter`), sets limits, and defines sorting relevant for the admin view.
*   The results are stored in `$this->searchResult`, ready for the template.
*   It includes an `apiDeleteProperty` method. This is a backend function that can be called by JavaScript in the admin interface (like clicking a "Delete" button) to remove a property. This is similar to the API handling we saw in [Form Handling & API](07_form_handling___api__.md).

**The Template File (`PhuketAdminPropertyList.tpl.php` - Conceptual)**

This file creates the HTML table to display the property list.

```html
<!-- Simplified concept of the template -->
<component>
  <h2>Property List (<?php print $this->searchResult->total; ?> found)</h2>
  
  <!-- Filters might go here -->

  <table>
    <thead>
      <tr>
        <th>ID</th>
        <th>Name</th>
        <th>Type</th>
        <th>Price</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <?php foreach ($this->searchResult->rows as $property): ?>
      <tr>
        <td><?php print $property->id; ?></td>
        <td><?php print s45_lang($property->name); ?></td>
        <td><?php /* Display type, etc. */ ?></td>
        <td><?php /* Display price */ ?></td>
        <td>
          <!-- Link/button to edit this specific property -->
          <a href="/admin/property/edit?propertyId=<?php print $property->id; ?>">Edit</a>
          <!-- Button that calls the apiDeleteProperty JS function -->
          <button onclick="deleteProperty('<?php print $property->id; ?>')">Delete</button>
        </td>
      </tr>
      <?php endforeach; ?>
    </tbody>
  </table>
  
  <!-- Pagination controls might go here -->
</component>
```

**Explanation:**

*   It displays the total number of properties found.
*   It loops through the `$this->searchResult->rows` (which contains the property data fetched by the PHP file).
*   For each property, it displays key details in a table row.
*   Crucially, it includes "Actions" like an "Edit" link (which goes to the editor page) and a "Delete" button (which would trigger JavaScript to call the `apiDeleteProperty` method in the PHP file).

### 2. Editing a Property (`PhuketAdminPropertyEditor`)

When the administrator clicks the "Edit" link for a specific property, they are taken to a page showing the `PhuketAdminPropertyEditor` component.

**The PHP File (`PhuketAdminPropertyEditor.php` - Simplified)**

This file loads the data for the *single* property being edited.

```php
<?php
// File: classes/.../PhuketAdminPropertyEditor/PhuketAdminPropertyEditor.php
// (Simplified)
use Site45\Compo\Compo;
// We need the Active Record class to load/save the property
use Site45\Sets\Phuket\Domain\PhuketPropertyAR;

class PhuketAdminPropertyEditor extends Compo {

  // Will hold the property data being edited (usually an AR object)
  /** @var PhuketPropertyAR */
  public $editedPropertyAR;
  public $title = 'Edit Property'; // Page title

  protected function beforeRender($props) {
    // Get the ID of the property to edit (from URL or props)
    $propertyId = isset($_GET['propertyId']) ? $_GET['propertyId'] : $props;

    // Load the property's data using its Active Record class
    // Note: Admin components often work directly with AR objects for editing
    if ($propertyAR = PhuketPropertyAR::load($propertyId)) {
      $this->editedPropertyAR = $propertyAR;
      $this->title = 'Editing: ' . s45_lang($propertyAR->name);
    } else {
      // Or create a new empty AR object if adding a new property
      $this->editedPropertyAR = PhuketPropertyAR::create($propertyId);
      $this->title = 'Add New Property';
    }
  }

  // API method to save changes (called via JS on form submit)
  public function apiSaveProperty($formData) {
    // Load existing or create new AR object
    if (!$propertyAR = PhuketPropertyAR::load($formData->id)) {
      $propertyAR = PhuketPropertyAR::create($formData->id);
    }

    // Update the AR object's properties with submitted data
    $propertyAR->setProps($formData);
    // ... potentially clean up or format some data ...

    // Save the changes back to the database
    $propertyAR->save();

    // Return success status (JS might redirect or show message)
    return ['status' => 1, 'savedId' => $propertyAR->id];
  }

  // ... Constructor, etc. ...
}
```

**Explanation:**

*   It gets the `propertyId` of the property to edit.
*   It uses `PhuketPropertyAR::load()` ([Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)) to fetch the specific property's data directly into an Active Record object. This object knows how to save itself back to the database.
*   The loaded `$this->editedPropertyAR` object is made available to the template.
*   It includes an `apiSaveProperty` method. When the admin submits the editor form, JavaScript calls this method, sending the form data. This method updates the `PhuketPropertyAR` object and calls `$propertyAR->save()` to persist the changes to the database.

**The Template File (`PhuketAdminPropertyEditor.tpl.php` - Simplified)**

This file creates the HTML form with fields for editing property details.

```php
<?php /* @var $this PhuketAdminPropertyEditor */ ?>
<component>
  <h3><?php print $this->title; ?></h3>

  <!-- The form submits data via JS to apiSaveProperty -->
  <form id="property-editor-form">
    <!-- Hidden field for the ID -->
    <input type="hidden" name="id" value="<?php print $this->editedPropertyAR->id; ?>">

    <div class="form-group">
      <label>Property Name (EN)</label>
      <input type="text" name="name[en]" value="<?php print s45_htmlescape($this->editedPropertyAR->name->en ?? ''); ?>">
    </div>
    
    <div class="form-group">
      <label>Property Name (RU)</label>
      <input type="text" name="name[ru]" value="<?php print s45_htmlescape($this->editedPropertyAR->name->ru ?? ''); ?>">
    </div>

    <div class="form-group">
      <label>Sale Price (THB)</label>
      <input type="number" name="priceSale" value="<?php print $this->editedPropertyAR->priceSale; ?>">
    </div>

    <div class="form-group">
      <label>Bedrooms</label>
      <input type="number" name="bedrooms" value="<?php print $this->editedPropertyAR->bedrooms; ?>">
    </div>

    <div class="form-group">
      <label>Description (EN)</label>
      <textarea name="description[en]"><?php print s45_htmlescape($this->editedPropertyAR->description->en ?? ''); ?></textarea>
    </div>

    <!-- Many more fields for location, features, photos, etc. -->
    <!-- Often organized into tabs (see full tpl file) -->

    <button type="submit">Save Changes</button>
  </form>
</component>
```

**Explanation:**

*   It displays a form.
*   Each form field corresponds to a property of the `$this->editedPropertyAR` object (e.g., `name`, `priceSale`, `bedrooms`).
*   The `value="..."` attribute of each input is pre-filled with the current data from the loaded property.
*   Multi-language fields (like `name`, `description`) often use array notation (e.g., `name[en]`, `name[ru]`).
*   When the "Save Changes" button is clicked, JavaScript intercepts the submission, gathers the data, and sends it to the `apiSaveProperty` method in the PHP file via an AJAX call.

## How Admin Pages are Built: The Master Layout

Individual admin components like lists and editors don't exist in isolation. They are placed within a master layout component, typically `PhuketAdminPage`.

**`PhuketAdminPage`** provides the consistent structure for all backend pages:

*   The top navigation bar.
*   The left-hand sidebar menu (listing all management sections like "Properties", "Projects", "Users", "Options", etc.).
*   The main content area where the specific admin component (like `PhuketAdminPropertyList` or `PhuketAdminPropertyEditor`) is displayed.

Think of `PhuketAdminPage` as the dashboard frame, and components like `PhuketAdminPropertyList` as the specific screen or widget plugged into the main area.

**Conceptual Page Definition (How the Property List page might be built):**

```
Admin Page Definition for '/admin/properties':
  - Use PhuketAdminPage as the main layout.
  - In the main content area of PhuketAdminPage:
    - Place the PhuketAdminPropertyList component.
```

When the admin navigates to `/admin/properties`, the system loads `PhuketAdminPage`, which in turn loads and displays `PhuketAdminPropertyList` in its designated content slot.

## Under the Hood: Loading an Admin Page

Let's trace the request for the property list page (`/admin/properties`):

```mermaid
sequenceDiagram
    participant Admin as Admin User (Browser)
    participant Server as Web Server/Router
    participant AdminPage as PhuketAdminPage
    participant PropList as PhuketAdminPropertyList
    participant PropQuery as PhuketPropertyQuery
    participant DB as Database

    Admin->>Server: Request /admin/properties
    Server->>AdminPage: Route request to PhuketAdminPage (as main layout)
    AdminPage->>AdminPage: Initialize layout (sidebar, header)
    AdminPage->>PropList: Instantiate PhuketAdminPropertyList (for content area)
    PropList->>PropList: Run beforeRender()
    PropList->>PropQuery: Create query, set filters (limit=20, etc.)
    PropQuery->>DB: Execute database query (SELECT * FROM properties...)
    DB-->>PropQuery: Return property data rows
    PropQuery->>PropQuery: Package results (e.g., in SearchResultVO)
    PropQuery-->>PropList: Return searchResult
    PropList->>PropList: Store searchResult in $this->searchResult
    PropList->>Server: Render PropList HTML (table) using its TPL
    AdminPage->>Server: Render AdminPage HTML (layout) using its TPL, inserting PropList HTML
    Server-->>Admin: Send complete HTML page
```

**Steps:**

1.  **Request:** The administrator clicks the "Properties" link in the sidebar.
2.  **Routing:** The server recognizes the `/admin/properties` path and knows it should use `PhuketAdminPage` as the main layout and `PhuketAdminPropertyList` as the content.
3.  **Layout Init:** `PhuketAdminPage` sets up the sidebar, header, etc.
4.  **Content Init:** `PhuketAdminPage` tells `PhuketAdminPropertyList` to get ready.
5.  **Data Fetching:** `PhuketAdminPropertyList` runs its `beforeRender`, uses `PhuketPropertyQuery` to talk to the database, and gets the list of properties.
6.  **Rendering:** `PhuketAdminPropertyList` uses its `.tpl.php` file to generate the HTML table for the properties. `PhuketAdminPage` uses its `.tpl.php` file to generate the overall page layout, embedding the property table HTML inside the main content area.
7.  **Response:** The final combined HTML is sent to the admin's browser.

## Conclusion

Admin Interface Components are the specialized `Compo` classes used to build the backend "control room" of the `s45_phuket` website. They provide administrators with the tools they need to manage content like properties, projects, users, and options.

*   They follow the standard PHP/TPL/JS/CSS structure of [Components (Compo)](01_components__compo__.md).
*   Examples include lists (`PhuketAdminPropertyList`, `PhuketAdminUserList`) and editors (`PhuketAdminPropertyEditor`, `PhuketAdminProjectEditor`).
*   They often interact directly with [Data Entities (AR/DTO)](03_data_entities__ar_dto__.md), particularly Active Record objects for editing and saving data.
*   They use [Query Classes](04_query_classes_.md) to fetch data for display in lists.
*   They leverage API methods and JavaScript for dynamic actions like saving and deleting, similar to [Form Handling & API](07_form_handling___api__.md).
*   The overall admin interface structure is typically provided by a master layout component like `PhuketAdminPage`.

These components create a functional and organized environment for website management. In the next chapter, we'll look at how data managed in this admin area can be exported or converted into different formats, such as generating feeds for other real estate portals.

Next: [Data Converters / Exports](09_data_converters___exports__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 9: Data Converters / Exports

Welcome to Chapter 9! In the [previous chapter](08_admin_interface_components_.md), we saw how administrators manage the website's content using the specialized **Admin Interface Components**. We learned how they can add, edit, and list properties, projects, and other data.

Now, what if we need to share this property information with *other* websites? Many real estate businesses list their properties not only on their own site but also on larger property portals like FazWaz, Hipflat, or Mercury. These external portals often require property data to be submitted in a very specific format, usually XML, which might be different from how our website stores it internally.

This is where **Data Converters / Exports** come in.

## What Problem Do Converters Solve? Speaking Different Languages

Imagine our website has detailed information about a villa: its name, price, number of bedrooms, description, photos, location, etc. Internally, we might represent this using our standard `PhuketPropertyDto` ([Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)).

Now, suppose we want to list this villa on:

1.  **Portal A (e.g., FazWaz):** They require the data in XML format, where the number of bedrooms must be in a tag called `<beds>` and the price in a tag called `<salePrice>`.
2.  **Portal B (e.g., Mercury):** They also want XML, but they call the bedroom tag `<rooms>` and require the price to be in Euros inside a `<price>` tag, even if we store it in Thai Baht (THB).
3.  **Internal App C:** Maybe we have a mobile app that needs a simplified version of the data, perhaps also in XML but with different tags again.

Each external system speaks a slightly different "language" or follows a different "blueprint" for property data. We can't just send them our internal `PhuketPropertyDto` directly.

**Data Converters** act like **translators**. They take our standard internal property data (`PhuketPropertyDto`) and convert it into the specific XML format required by each external portal or system.

**`PhuketExportQuery`** acts like the **shipping manager**. It figures out *which* properties need to be sent to *which* portals (based on settings chosen in the [Admin Interface Components](08_admin_interface_components_.md)) and orchestrates the translation and preparation process.

This ensures that our property data is correctly formatted and displayed on third-party platforms, increasing visibility.

## Key Parts: The Translators and the Manager

1.  **Converter Classes:** These PHP classes perform the actual translation. Each class is tailored for a specific target format:
    *   `MercuryConverter.php`: Translates property data into the XML format required by the Mercury portal.
    *   `FazwazConverter.php`: Translates data for the FazWaz portal's XML feed.
    *   `HipflatConverter.php`: Translates data for the Hipflat portal's XML feed.
    *   `AppConverter.php`: Translates data into a format suitable for a specific internal application (perhaps a mobile app).
    These converters typically have an `getItem()` method that takes a `PhuketPropertyDto` and returns an XML string snippet for that single property, formatted according to the target portal's rules. They also often have `getHeader()` and `getFooter()` methods to create the surrounding XML structure needed for a complete feed file.

2.  **`PhuketExportQuery.php`**: This [Query Class](04_query_classes_.md) manages the overall export process. It doesn't do the translation itself, but it:
    *   **Listens for changes:** Because it extends `QueryFromEvents`, it automatically reacts when a property is saved or deleted.
    *   **Checks export flags:** When a property is saved, it checks if flags like `export_Fazwaz` or `export_Hipflat` are enabled for that property (these flags are likely set via the [Admin Interface Components](08_admin_interface_components_.md)).
    *   **Triggers the correct converter:** If a flag is enabled, it calls the appropriate Converter class (e.g., `FazwazConverter::getItem()`) to generate the XML snippet for that property.
    *   **Stores the result:** It saves the generated XML snippet into a dedicated database table (`_phuket_Export`, defined by `PhuketExportTable.php`). This table acts as a cache, holding the ready-to-use XML pieces for each exported property.
    *   **Handles deletions:** If a property is deleted or its export flag is turned off, `PhuketExportQuery` removes the corresponding XML snippet from the `_phuket_Export` table.
    *   **Provides the data:** It allows other parts of the system (like a script that generates the final XML feed file) to query and retrieve all the stored XML snippets for a specific portal (e.g., "give me all rows where `name` = 'Fazwaz'").

## Use Case: Generating the FazWaz XML Feed

Let's imagine the process for creating the XML feed for the FazWaz portal:

**Step 1: Preparing Individual Property Snippets (Event-Driven)**

1.  **Admin Action:** An administrator edits a property using `PhuketAdminPropertyEditor` ([Admin Interface Components](08_admin_interface_components_.md)). They check a box labeled "Export to FazWaz" and save the property.
2.  **Event Triggered:** Saving the `PhuketPropertyAR` object triggers a 'Saved' event.
3.  **`PhuketExportQuery` Reacts:** The `handlePhuketPropertySaved` method inside `PhuketExportQuery` is automatically called because it's listening for these events.
4.  **Check Flag:** The method receives the saved property's data (likely as a `PhuketPropertyDto`) and checks if the `export_Fazwaz` flag is true.
5.  **Call Converter:** Since the flag is true, it calls `FazwazConverter::getItem($propertyDto)`.
6.  **Translation:** `FazwazConverter::getItem()` takes the property DTO and generates the FazWaz-specific XML snippet for this single property. It maps internal fields (`propertyType`, `priceSale`, `bedrooms`) to FazWaz tags (`<propertyType>`, `<salePrice>`, `<beds>`).
    ```php
    // Inside FazwazConverter::getItem($propertyDto) - Simplified Concept
    $item = '<item>';
    // Map propertyType ID to FazWaz type string
    $item .= '<propertyType>' . $this->getPropertyType($propertyDto) . '</propertyType>';
    $item .= '<title>' . s45_lang($propertyDto->name) . '</title>';
    $item .= '<refId>' . $propertyDto->number . '</refId>';
    // ... map location, description, etc. ...
    $item .= '<beds>' . $propertyDto->bedrooms . '</beds>';
    $item .= '<baths>' . $propertyDto->bathrooms . '</baths>';
    // ... map prices (handling sale/rent) ...
    // ... map photos ...
    $item .= '</item>';
    return $item;
    ```
7.  **Store Snippet:** `PhuketExportQuery` takes the returned XML string and saves it into the `_phuket_Export` database table. The record in the table might look conceptually like:
    *   `id`: `Fazwaz__PROPERTY_ID`
    *   `name`: `Fazwaz`
    *   `propId`: `PROPERTY_ID`
    *   `number`: `PROPERTY_NUMBER`
    *   `item`: `<item><propertyType>condo</propertyType>...</item>` (The generated XML snippet)

This process happens automatically every time a property marked for export is saved, ensuring the `_phuket_Export` table always holds the latest XML snippets.

**Step 2: Assembling the Full Feed File (On Demand)**

When FazWaz (or a scheduled task) requests the full feed file (e.g., by accessing a specific URL like `/s45-indreams-export/fazwaz.xml`):

1.  **Request:** A request comes in for the FazWaz feed.
2.  **Query for Snippets:** The code handling this request uses `PhuketExportQuery` to fetch all records from the `_phuket_Export` table where the `name` is 'Fazwaz'.
    ```php
    // Simplified concept of fetching export data
    $exportQuery = PhuketExportQuery::create();
    $exportQuery->name = 'Fazwaz'; // Filter by portal name
    $exportQuery->limit = 10000;  // Get all relevant items
    $exportResult = $exportQuery->exec(); // Fetches rows from _phuket_Export table
    ```
3.  **Get Header:** It calls `FazwazConverter::getHeader()` to get the standard opening XML tags required by FazWaz.
    ```php
    // Inside FazwazConverter::getHeader() - Simplified
    $xml = '<?xml version="1.0" encoding="UTF-8" ?>';
    $xml .= '<rss version="2.0">'; // FazWaz uses an RSS-like format
    $xml .= '<channel>';
    $xml .= '<title>Our Agency Name</title>';
    // ... other channel info ...
    return $xml;
    ```
4.  **Combine Items:** It loops through the `rows` fetched by `PhuketExportQuery`. Each row contains the pre-generated XML snippet (`item`) for one property. It concatenates all these snippets together.
    ```php
    $fullXml = FazwazConverter::getHeader(); // Start with the header
    if ($exportResult->rows) {
        foreach ($exportResult->rows as $exportRow) {
            $fullXml .= $exportRow->item; // Add the XML snippet for each property
        }
    }
    ```
5.  **Get Footer:** It calls `FazwazConverter::getFooter()` to get the standard closing XML tags.
    ```php
    // Inside FazwazConverter::getFooter() - Simplified
    $xml = '</channel>';
    $xml .= '</rss>';
    return $xml;
    ```
6.  **Append Footer & Serve:** It appends the footer to the combined string and sends the complete XML content back as the response.
    ```php
    $fullXml .= FazwazConverter::getFooter(); // Add the footer
    // Set the content type header to XML
    header('Content-Type: application/xml; charset=utf-8');
    // Output the final XML
    echo $fullXml;
    ```

FazWaz receives a complete, correctly formatted XML file containing all the properties marked for export.

## Under the Hood: The Export Flow

**1. Event Handling by `PhuketExportQuery`:**

```mermaid
sequenceDiagram
    participant Admin as Admin User
    participant AdminEditor as PhuketAdminPropertyEditor
    participant PropAR as PhuketPropertyAR
    participant EventSys as Event System
    participant ExportQ as PhuketExportQuery
    participant Converter as Specific Converter (e.g., FazwazConverter)
    participant ExportDB as _phuket_Export Table

    Admin->>AdminEditor: Check "Export to FazWaz", Click Save
    AdminEditor->>PropAR: Set properties (inc. export_Fazwaz=true), call save()
    PropAR->>PropAR: Save data to main property table
    PropAR->>EventSys: Trigger "PhuketPropertySaved" event (with Property ID)
    EventSys-->>ExportQ: Notify handler: handlePhuketPropertySaved(eventDto)
    ExportQ->>ExportQ: Load Property DTO using eventDto->arId
    ExportQ->>ExportQ: Check if propertyDto->export_Fazwaz is TRUE
    alt Export Flag is TRUE
        ExportQ->>Converter: Call Converter::getItem(propertyDto)
        Converter-->>ExportQ: Return XML snippet string
        ExportQ->>ExportDB: Save/Update row (name='Fazwaz', propId=..., item=XML snippet)
    else Export Flag is FALSE or Property Sold
        ExportQ->>ExportDB: Delete row WHERE name='Fazwaz' AND propId=...
    end
```

**2. Code Snippets:**

**`PhuketExportQuery::handlePhuketPropertySaved()` (Simplified)**

```php
// File: classes/.../ExportSearch/PhuketExportQuery.php
protected function handlePhuketPropertySaved($eventDto)
{
  // Load the full DTO for the saved property
  $propertyDto = PhuketPropertyQuery::create()->load($eventDto->arId);

  // *** Logic for FazWaz ***
  $exportToFazWaz = isset($propertyDto->export_Fazwaz) && $propertyDto->export_Fazwaz;
  $isSuitableForFazWaz = !$propertyDto->isSaled && ($propertyDto->propertyType->id != 'hotel');

  if ($exportToFazWaz && $isSuitableForFazWaz) {
    // Call the converter to get the XML snippet
    $xml = FazwazConverter::getItem($propertyDto);
    // Save the snippet to the export table
    $this->saveExportData('Fazwaz', $propertyDto, $xml);
  } else {
    // Remove any existing snippet if export is disabled or property not suitable
    $this->deleteExportData($propertyDto->id, 'Fazwaz');
  }

  // *** Similar logic blocks for Hipflat, App, Mercury etc. ***
  // if (isset($propertyDto->export_Hipflat) && ... ) { ... }
  // if (isset($propertyDto->export_App) && ... ) { ... }
}

// Helper to save data to the _phuket_Export table
protected function saveExportData($exportName, $propertyDto, $xml) {
    // Use the DB Table object ($this->table is PhuketExportTable)
    $this->table->id = $exportName . '__' . $propertyDto->id; // Unique key
    $this->table->name = $exportName;       // Portal identifier
    $this->table->propId = $propertyDto->id;  // Property FK
    $this->table->number = (int) $propertyDto->number; // Property number for sorting
    $this->table->item = $xml;              // The generated XML snippet
    $this->table->save(); // Performs INSERT or UPDATE
}

// Helper to delete data from the _phuket_Export table
protected function deleteExportData($propertyId, $exportName) {
    db_delete($this->table->getName()) // Use DB abstraction layer
      ->condition('propId', $propertyId)
      ->condition('name', $exportName)
      ->execute();
}
```

*   This method acts as the central dispatcher, checking flags and calling the appropriate converters based on admin settings.
*   It uses helper methods (`saveExportData`, `deleteExportData`) to manage the cached XML snippets in the `_phuket_Export` database table.

**`FazwazConverter::getItem()` (Conceptual Reminder)**

```php
// File: classes/.../Fazwaz/FazwazConverter.php
public static function getItem($propertyDto) {
    $conv = new static(); // Create instance for helper methods if needed
    $item = '<item>';
    // --- Translate DTO fields to FazWaz XML tags ---
    $item .= '<status>available</status>'; // Example static value
    $item .= '<propertyType>' . $conv->getPropertyType($propertyDto) . '</propertyType>'; // Calls helper
    $item .= '<title>' . s45_lang($propertyDto->name) . '</title>'; // Uses LangVO
    $item .= '<link>' . s45_url('property/' . $propertyDto->id) . '</link>'; // Generate URL
    $item .= '<refId>' . $propertyDto->number . '</refId>'; // Internal reference number
    // ... many more fields ...
    $item .= '<beds>' . $propertyDto->bedrooms . '</beds>';
    $item .= '<baths>' . $propertyDto->bathrooms . '</baths>';
    $item .= '<area>' . $propertyDto->areaCommon . '</area>';
    // ... photos loop ...
    // ... agent info ...
    // ... features loop ...
    $item .= '</item>';
    return $item; // Return the translated XML snippet
}
```

*   This class focuses purely on the translation task: mapping fields from the input `PhuketPropertyDto` to the specific XML structure required by FazWaz.

## Conclusion

Data Converters / Exports provide the mechanism for sharing the website's property data with external systems like real estate portals.

*   **Converter Classes** (`FazwazConverter`, `MercuryConverter`, etc.) act as translators, converting internal `PhuketPropertyDto` data into specific external XML formats.
*   **`PhuketExportQuery`** acts as the export manager. It listens for property changes, checks export flags set by admins, triggers the correct converters, and caches the resulting XML snippets in a dedicated database table (`_phuket_Export`).
*   Full XML feed files are generated on demand by retrieving the cached snippets using `PhuketExportQuery` and wrapping them with appropriate headers and footers provided by the converter classes.

This system allows `s45_phuket` to syndicate its listings effectively across multiple platforms while keeping the translation logic organized and separate for each target format.

In the final chapter, we'll look at the reverse process: importing data *into* the system, specifically focusing on parsing data from an external source called Indreams.

Next: [Data Parsing (Indreams)](10_data_parsing__indreams__.md)

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 10: Data Parsing (Indreams)

Welcome to the final chapter! In the [previous chapter](09_data_converters___exports__.md), we learned how the system can act as a translator, converting our property data into specific formats (like XML) needed by external portals using **Data Converters / Exports**.

Now, we'll look at the opposite situation: getting data *into* our system from an older, external source. Specifically, we'll explore how the `s45_phuket` project can fetch and understand property data stored in a separate, possibly older system referred to as 'indreams'.

## What Problem Does This Solve? Importing from an Old System

Imagine you're upgrading from an old, maybe slightly messy spreadsheet to a brand new, powerful database application. You have lots of valuable information in the old spreadsheet, but it's not structured quite the same way as your new system expects. You need a way to:

1.  **Access the old spreadsheet:** Get the file or connect to where it's stored.
2.  **Read the data:** Understand the rows and columns in the old spreadsheet.
3.  **Extract specific information:** Pull out just the pieces you need (like the property price, status, or features) from the old format.
4.  **(Optional) Populate the new system:** Use the extracted information to create or update records in your new database application.

This is essentially what the "Data Parsing (Indreams)" components do. They are designed to connect to an external database (associated with the 'indreams' system), retrieve data stored as HTML content, and then parse that HTML to extract specific property details. It's like importing data from an old system into our shiny new `s45_phuket` application.

## The Key Players: The Retriever and The Reader

Two main classes work together to achieve this:

1.  **`IndreamsParser` (The Retriever):** This class is responsible for connecting to the separate 'indreams' database (`u3131_1arcade`) and fetching the raw HTML content associated with a specific property ID from that old system. Think of it as the person who knows where the old spreadsheet is stored and how to retrieve the correct file based on an old ID number.

2.  **`IndreamsRepo` (The Reader/Extractor):** This class takes the raw HTML fetched by `IndreamsParser`. It then uses a tool called **`phpQuery`** (which is like a specialized magnifying glass for reading HTML structure) to search through the HTML and pull out specific pieces of information, such as whether the property is published, what's included in the price, or other details. It knows how to find the "Price" cell or the "Status" checkbox within the old HTML structure.

## Use Case: Getting Publish Status from the Old System

Let's say we have a property in our *new* system that originated from the *old* 'indreams' system. We know its old ID was `12345`. We want to check if this property was marked as "published" or "active" in the old system.

**1. Using the Classes:**

```php
<?php
// Import the 'Reader' class
use Site45\Sets\Phuket\Parser\IndreamsRepo;

// The ID of the property in the OLD 'indreams' system
$oldIndreamsId = '12345'; 

// 1. Create the Repo/Reader, passing the old ID.
//    This internally uses IndreamsParser to fetch the HTML.
$indreamsRepo = IndreamsRepo::create($oldIndreamsId);

// 2. Check if the Repo was successfully created (HTML was found)
if ($indreamsRepo) {
    
    // 3. Call the method to get the specific piece of data
    $isPublishedInOldSystem = $indreamsRepo->getPublished();
    
    // 4. Use the result
    if ($isPublishedInOldSystem === 1) {
        echo "Property {$oldIndreamsId} was published in the Indreams system.";
    } elseif ($isPublishedInOldSystem === 0) {
        echo "Property {$oldIndreamsId} was NOT published in the Indreams system.";
    } else {
        echo "Could not determine publish status for {$oldIndreamsId}.";
    }
    
} else {
    echo "Could not find or parse data for Indreams ID: {$oldIndreamsId}.";
}

?>
```

**Explanation:**

1.  **`IndreamsRepo::create($oldIndreamsId)`:** We create an instance of the `IndreamsRepo`. Crucially, *inside* this `create` method, it automatically calls `IndreamsParser` to connect to the old database and fetch the HTML for ID `12345`. It also initializes the `phpQuery` tool with this HTML.
2.  **`if ($indreamsRepo)`:** We check if the HTML was successfully retrieved and parsed. If the ID didn't exist in the old system, `create()` would return `NULL`.
3.  **`$indreamsRepo->getPublished()`:** We call a specific method on the `IndreamsRepo` object. This method uses `phpQuery` to look for a specific HTML element (in this case, a checkbox with the ID `#realtytranslation-2-active`) within the fetched HTML and checks if it's marked as "checked".
4.  **Result:** The method returns `1` if the checkbox was checked (published), `0` if it wasn't, or potentially `NULL` if the HTML structure was unexpected.

This allows us to query specific details from the old system based on its ID, without needing to know the raw database structure of the old system ourselves.

## How It Works Under the Hood: Fetching and Reading

Let's trace the steps involved when we call `IndreamsRepo::create('12345')` and then `$indreamsRepo->getPublished()`:

```mermaid
sequenceDiagram
    participant Caller as Calling Code
    participant Repo as IndreamsRepo
    participant Parser as IndreamsParser
    participant OldDB as Indreams DB (u3131_1arcade)
    participant pQuery as phpQuery Library

    Caller->>Repo: IndreamsRepo::create('12345')
    Repo->>Parser: IndreamsParser::create()
    Repo->>Parser: getParsedHtml('12345')
    Parser->>Parser: connectToArcada() [Switch DB connection]
    Parser->>OldDB: SELECT nid FROM node WHERE title='12345'
    OldDB-->>Parser: Return node ID (e.g., 500)
    Parser->>OldDB: SELECT body_value FROM field_data_body WHERE entity_id=500
    OldDB-->>Parser: Return HTML content string
    Parser->>Parser: connectToDefault() [Switch DB back]
    Parser-->>Repo: Return HTML content string
    Repo->>pQuery: Load HTML into phpQuery DOM object
    Repo->>Repo: Store phpQuery object in $this->pq
    Repo-->>Caller: Return IndreamsRepo instance
    
    Caller->>Repo: getPublished()
    Repo->>pQuery: Use $this->pq->find('#realtytranslation-2-active')->attr('checked')
    pQuery-->>Repo: Return 'checked' or NULL
    Repo->>Repo: Convert result to 1 or 0
    Repo-->>Caller: Return 1 (published) or 0 (not published)
```

**Steps:**

1.  **Create Repo:** The calling code initiates `IndreamsRepo::create()` with the old ID.
2.  **Call Parser:** The Repo asks `IndreamsParser` to get the HTML.
3.  **Connect to Old DB:** `IndreamsParser` temporarily switches the active database connection to point to the `u3131_1arcade` database.
4.  **Fetch Node ID:** It queries the `node` table in the old DB to find the internal node ID (`nid`) associated with the title (which is our `indreamsId`).
5.  **Fetch HTML:** It uses the `nid` to query the `field_data_body` table and retrieve the actual HTML content stored in the `body_value` column.
6.  **Switch Back DB:** `IndreamsParser` switches the active database connection back to the default one for the main `s45_phuket` application.
7.  **Return HTML:** The fetched HTML string is returned to `IndreamsRepo`.
8.  **Load HTML:** `IndreamsRepo` uses the `phpQuery` library to parse this HTML string into a structure it can easily search.
9.  **Repo Ready:** The `IndreamsRepo` instance, now containing the parsed HTML via `phpQuery`, is returned.
10. **Call `getPublished()`:** The caller asks the Repo for the publish status.
11. **`phpQuery` Find:** The Repo uses `phpQuery`'s `find()` method with a CSS selector (`#realtytranslation-2-active`) to locate the specific checkbox element within the parsed HTML. It then uses `attr('checked')` to see if the 'checked' attribute exists.
12. **Interpret Result:** `phpQuery` returns the attribute value (or indicates if it's present). The Repo converts this into a `1` or `0`.
13. **Return Status:** The final status (`1` or `0`) is returned to the caller.

## Code Dive: A Glimpse Inside

Let's look at simplified snippets of the key classes.

**`IndreamsParser::getParsedHtml()` (Simplified)**

```php
<?php
namespace Site45\Sets\Phuket\Parser;

class IndreamsParser {

  public function getParsedHtml($indreamsId) {
    $html = NULL;
    
    // 1. Switch to the old 'arcada' database connection
    $this->connectToArcada(); 
    
    // 2. Find the internal ID (nid) based on the old title/ID
    $queryNode = db_select('node'); // Use DB abstraction layer
    $queryNode->fields('node', ['nid']);
    $queryNode->condition('title', $indreamsId);
    if ($nodeResult = $queryNode->execute()->fetch()) {
      $nid = $nodeResult->nid;

      // 3. Fetch the HTML body using the internal ID (nid)
      $queryBody = db_select('field_data_body');
      $queryBody->fields('field_data_body', ['body_value']);
      $queryBody->condition('entity_id', $nid);
      if ($bodyResult = $queryBody->execute()->fetch()) {
        $html = $bodyResult->body_value; // Get the raw HTML
      }
    }
    
    // 4. Switch back to the default database connection
    $this->connectToDefault(); 

    return $html; // Return the fetched HTML string (or NULL)
  }

  // Helper method to switch DB connection context
  protected function connectToArcada(){ /* ... DB connection switch logic ... */ }
  
  // Helper method to switch back DB connection context
  protected function connectToDefault(){ db_set_active(); /* Switch back */ }
  
  // ... create() method ...
}
```

*   This class's main job is database interaction: switch connection, query old tables based on the ID, get the HTML, and switch back.

**`IndreamsRepo::create()` and `getPublished()` (Simplified)**

```php
<?php
namespace Site45\Sets\Phuket\Parser;

// ... other necessary imports ...

class IndreamsRepo {
  
  public $indreamsId;
  public $html; // Raw HTML stored here
  public $pq;   // phpQuery object stored here

  // Factory method to create instance AND fetch/parse HTML
  public static function create($indreamsId) {
    $repo = new static();
    $repo->indreamsId = $indreamsId;
    
    // 1. Call the Parser to get the HTML
    $repo->html = IndreamsParser::create()->getParsedHtml($indreamsId);
    
    // 2. If HTML was fetched successfully...
    if ($repo->html) {
      // 3. Load phpQuery library (if not already loaded)
      s45_add_phpQuery(); 
      // 4. Parse the HTML using phpQuery
      $dom = \phpQuery::newDocument($repo->html); 
      $repo->pq = pq($dom); // Store the phpQuery object
      return $repo; // Return the ready-to-use Repo object
    }
    
    return NULL; // Return NULL if HTML couldn't be fetched
  }
  
  // Method to extract the 'published' status
  public function getPublished() {
    $published = NULL; // Default if not found
    if ($this->pq) { // Check if phpQuery object exists
      // Use phpQuery to find the element and check its 'checked' attribute
      // `#realtytranslation-2-active` is a CSS selector targeting an element
      // with the ID 'realtytranslation-2-active' (likely a checkbox)
      $isChecked = ($this->pq->find('#realtytranslation-2-active')->attr('checked') == 'checked');
      $published = (int) $isChecked; // Convert boolean true/false to 1/0
    }
    return $published;
  }

  // Example: Method to extract furniture price from HTML
  public function getFurniturePrice() {
      if(!$this->pq) return NULL;
      // Find a <select> dropdown with a specific ID, get the text of the selected <option>
      $valueText = $this->getSelectedTextById('realty-pr_mebelnyj_paket'); 
      if ($valueText && ($valueText <> 'Нет')) { // Check if a value exists and isn't 'No'
          // Remove any non-numeric characters to get just the price
          return preg_replace('/[^0-9]/', '', $valueText); 
      }
      return NULL;
  }

  // Helper method using phpQuery to get selected option's text
  protected function getSelectedTextById($id) {
    if(!$this->pq) return NULL;
    // Find element by ID, then find selected option within it, then get text
    return $this->pq->find('select[id='.$id.'] option[selected]')->text();
  }

  // ... other methods like getNotInPriceSale(), load() etc. using $this->pq->find(...) ...
}
```

*   `IndreamsRepo` orchestrates the process: it uses `IndreamsParser` to get the HTML, then uses `phpQuery` (via `$this->pq`) to target specific elements within that HTML using CSS-like selectors (e.g., `find('#element-id')`, `find('select[id=...] option[selected]')`) and extract attributes (`attr('checked')`) or text (`text()`).
*   The `load()` method (partially shown in the provided code) likely uses many other similar extraction methods to gather various details (price, features, descriptions) from the parsed HTML, potentially to populate a `PhuketPropertyAR` object ([Data Entities (AR/DTO)](03_data_entities__ar_dto__.md)) for the new system.

## Conclusion

The `IndreamsParser` and `IndreamsRepo` classes provide a specialized mechanism for importing data from an older, related system ('indreams').

*   `IndreamsParser` handles the connection to the external database and retrieves raw HTML content based on an old property ID.
*   `IndreamsRepo` takes this HTML, parses it using the `phpQuery` library, and provides methods to extract specific data points (like publish status or pricing details) by querying the HTML structure.

This pattern is useful when migrating data from legacy systems or integrating with external sources where direct database access is complex, but a structured HTML representation of the data is available. It effectively allows the new system to "read" data from the old one, even if they are structured differently.

This concludes our tour through the core concepts of the `s45_phuket` project! We hope these chapters have given you a solid foundation for understanding how the different parts work together.

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 1: Configuration (`SiteConf`)

Welcome to the `__s45` project! This is the very first step in understanding how `__s45` works. We'll start with something fundamental: how the system knows which website it's currently running and what settings to use for that specific site.

Imagine you have a magic remote control that automatically adjusts your TV, lights, and speakers based on *which room* you are in. `SiteConf` is like that magic remote for your website project.

## What Problem Does `SiteConf` Solve?

Let's say you want to build two different websites:
1.  `www.my-cool-blog.com` - A personal blog.
2.  `www.awesome-shop.com` - An online store.

Instead of writing completely separate code for each, wouldn't it be great if you could use the *same* core `__s45` codebase for both? But how would the code know whether to show blog posts or product listings? How would it know which design theme to use?

This is where `SiteConf` comes in! It acts like the website's **settings panel** or **control room**. Its main job is to figure out *which website* the visitor is currently looking at (based on the domain name like `www.my-cool-blog.com`) and load the correct settings for *that specific site*.

## Meet `SiteConf`: Your Project's Control Room

`SiteConf` is a helper tool within the `__s45` system. It provides easy access to site-specific configurations. Think of it as the gatekeeper that holds the keys and instructions for the current website.

These settings are typically stored in a central configuration file, usually named `_sites.s45.json`. This file contains information about all the different websites your `__s45` project manages.

## How Does It Know Which Website You're On?

It's simple! `SiteConf` looks at the address you type into your web browser's address bar. In technical terms, it checks the `$_SERVER['HTTP_HOST']` variable, which holds the domain name (e.g., `www.my-cool-blog.com`).

It then looks inside the `_sites.s45.json` file and searches for an entry that lists this domain name. Once it finds a match, it knows which website configuration to load.

## What Kind of Settings Does It Manage?

Once `SiteConf` identifies the current website, it can provide specific details about it, such as:

1.  **`siteId`**: A unique internal name or identifier for the website (e.g., `my_blog`, `awesome_shop`). This is useful for organizing files and data related to that specific site.
2.  **`sets`**: A list of "feature sets" or groups of components that should be enabled for this particular site. For example, `my_blog` might have the `blogging` set enabled, while `awesome_shop` might have the `ecommerce` set enabled. This allows different sites to have different functionalities using the same underlying codebase. We'll learn more about components later in [Chapter 3: Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md).

These settings are often packaged into a neat data container called `SiteConfDto`, which is a type of [Data Transfer Object (DTO)](04_data_transfer_object__dto____value_object__vo__.md).

## Using `SiteConf` in Your Code

Let's see how you can actually use `SiteConf` to get information about the current site.

**Example 1: Getting the Current Site's Unique ID**

Imagine you need to find the folder where the images for the *current* website are stored, and the folder name uses the site's unique ID. You can use `SiteConf::getId()`:

```php
<?php
// Make sure the SiteConf class can be found
use Site45\Base\SiteConf;

// Get the unique ID for the website being viewed right now
$currentSiteId = SiteConf::getId();

echo "The internal ID for this website is: " . $currentSiteId;

// --- Example Output ---
// If you visited www.my-cool-blog.com, and its ID is 'my_blog':
// The internal ID for this website is: my_blog
//
// If you visited www.awesome-shop.com, and its ID is 'awesome_shop':
// The internal ID for this website is: awesome_shop
?>
```

This simple line `SiteConf::getId()` does all the work of checking the domain name and looking up the corresponding `siteId` in the configuration file.

**Example 2: Finding Out Which Feature Sets Are Enabled**

Let's say you want to check if the "blogging" features are active for the current site before showing a "Latest Posts" section. You can use `SiteConf::getSets()`:

```php
<?php
// Make sure the SiteConf class can be found
use Site45\Base\SiteConf;

// Get the list of enabled feature sets for this site
$enabledSets = SiteConf::getSets();

echo "Enabled feature sets for this site: ";
print_r($enabledSets); // print_r is a simple way to display array contents

// --- Example Output ---
// For www.my-cool-blog.com (which might use 'core' and 'blogging' sets):
// Enabled feature sets for this site: Array ( [0] => s45_basic:core [1] => my_blog_features:blogging )
//
// For www.awesome-shop.com (which might use 'core' and 'ecommerce' sets):
// Enabled feature sets for this site: Array ( [0] => s45_basic:core [1] => shop_features:ecommerce )
?>
```

Again, `SiteConf::getSets()` handles looking up the configuration for the current domain and returning the list of `sets` defined for it.

## How It Works Under the Hood

You don't usually need to worry about the internal details, but understanding them can be helpful. Here’s a simplified step-by-step look:

1.  **You Ask:** Your code calls a method like `SiteConf::getId()`.
2.  **Query Time:** `SiteConf` doesn't do the lookup itself. It asks another specialized helper, `SiteConfLoadQuery`, to find the configuration.
3.  **Domain Check:** `SiteConfLoadQuery` gets the current domain name (e.g., `www.my-cool-blog.com`) from the server (`$_SERVER['HTTP_HOST']`).
4.  **Open Config:** It opens the main configuration file (`_sites.s45.json`). This file contains details for *all* the sites managed by this `__s45` instance.
5.  **Search:** It loops through the site entries in the file, checking the `domains` list in each entry.
6.  **Match Found!** When it finds an entry where the `domains` list includes `www.my-cool-blog.com`, it knows it has found the correct configuration.
7.  **Data Packaging:** It takes the configuration data for that site (like its `id`, `sets`, etc.) and puts it into a `SiteConfDto` object.
8.  **Return:** `SiteConfLoadQuery` returns this `SiteConfDto` object back to the `SiteConf` method.
9.  **Final Answer:** `SiteConf::getId()` extracts just the `id` part from the `SiteConfDto` and gives it back to your code.

Here's a diagram showing the flow:

```mermaid
sequenceDiagram
    participant YourCode as Your Code
    participant SiteConf as SiteConf Class
    participant SCQuery as SiteConfLoadQuery
    participant ConfigFile as _sites.s45.json

    YourCode->>SiteConf: Call getId()
    SiteConf->>SCQuery: Ask to load config for current domain
    Note over SCQuery, ConfigFile: Gets domain (e.g., www.my-cool-blog.com)
    SCQuery->>ConfigFile: Open and read _sites.s45.json
    ConfigFile-->>SCQuery: Return all site configurations
    SCQuery->>SCQuery: Search for entry matching domain
    Note over SCQuery: Finds entry for 'my_blog' site
    SCQuery-->>SiteConf: Return SiteConfDto (with id='my_blog', sets=[...])
    SiteConf->>SiteConf: Extract 'id' from Dto
    SiteConf-->>YourCode: Return 'my_blog'
```

**A Glimpse at the Code:**

The core logic for finding the site configuration lives in the `SiteConfLoadQuery` class. Here's a *very simplified* look at its `exec` (execute) method:

```php
// Simplified view of s45_base/classes/Site45/QueryLib/Site/SiteConfLoadQuery.php

public function exec() {
    // If we already found the config during this request, reuse it!
    if (isset($GLOBALS['s45']['siteConf'])) {
        return $GLOBALS['s45']['siteConf'];
    }

    // 1. Get the path to the main configuration file
    $configFile = S45_SITES; // This constant holds the path to _sites.s45.json

    // 2. Load all configurations from the JSON file
    $allSiteConfigs = JsonRepo::open($configFile); // JsonRepo helps read JSON files

    // 3. Loop through each site defined in the file
    foreach ($allSiteConfigs as $siteId => $siteData) {

        // 4. Turn the raw data into a structured SiteConfDto object
        // We'll learn more about DTOs in Chapter 4!
        $siteConfDto = SiteConfDto::create($siteData);
        $siteConfDto->id = $siteId; // Make sure the ID is stored

        // 5. Does the current domain ($this->domain) match this site's domains?
        if ($this->domain && isset($siteConfDto->domains)) {
            $allValidDomains = $siteConfDto->domains;
            // Also check mobile version (e.g., m.domain.com) if configured
            if (isset($siteConfDto->mobile) && $siteConfDto->mobile) {
                foreach ($siteConfDto->domains as $d) {
                    $allValidDomains[] = 'm.' . $d;
                }
            }

            if (in_array($this->domain, $allValidDomains)) {
                // MATCH! Store globally for next time & return.
                $GLOBALS['s45']['siteConf'] = $siteConfDto;
                return $siteConfDto;
            }
        }
        // (Also handles finding by explicit siteId if domain wasn't used)
        if($this->siteId && ($this->siteId == $siteId)){
             $GLOBALS['s45']['siteConf'] = $siteConfDto;
             return $siteConfDto;
        }

    }

    // No site found matching the domain or ID
    $GLOBALS['s45']['siteConf'] = null;
    return null;
}
```

And the `SiteConf` class itself just uses this query:

```php
// Simplified view of s45_base/classes/Site45/Base/SiteConf.php
namespace Site45\Base;
use Site45\QueryLib\Site\SiteConfLoadQuery; // Use the query class

class SiteConf {
    public static function getId() {
        // Create query, set domain from server, execute, get 'id'
        $config = SiteConfLoadQuery::create()
                        ->setDomain($_SERVER['HTTP_HOST']) // Get current domain
                        ->exec(); // Run the query
        // Return the id property if config was found, otherwise null
        return $config ? $config->id : null;
    }

    public static function getSets() {
        // Create query, set domain from server, execute, get 'sets'
        $config = SiteConfLoadQuery::create()
                        ->setDomain($_SERVER['HTTP_HOST'])
                        ->exec();
        // Return the sets property if config was found, otherwise null
        return $config ? $config->sets : null;
    }
}
```

See? `SiteConf` provides a simple interface (`getId()`, `getSets()`) while the `SiteConfLoadQuery` does the actual work of reading the file and finding the right configuration.

## Connecting to Other Parts

The information provided by `SiteConf` is crucial for other parts of the `__s45` system. For example:

*   When the system needs to load a specific [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md), it might use `SiteConf::getId()` to find the correct site-specific storage directory (`_repo`) where component data is saved (as seen in `CompoRepo`).
*   Tools like `CompoScanner`, which automatically find available components, use `SiteConf::getSets()` to know which directories (belonging to the enabled sets) they should scan.

## Conclusion

You've just learned about `SiteConf`, the control room for your `__s45` project!

*   It solves the problem of running **multiple websites** from a **single codebase**.
*   It identifies the current site based on the **domain name** (`$_SERVER['HTTP_HOST']`).
*   It reads configuration from a central file (`_sites.s45.json`).
*   It provides key settings like the unique **`siteId`** and enabled feature **`sets`**.
*   You can easily get these settings using methods like `SiteConf::getId()` and `SiteConf::getSets()`.

Understanding `SiteConf` is the first step because many other parts of `__s45` rely on knowing *which* site they are currently operating on.

Now that we know how `__s45` identifies the current site, the next logical step is to understand how it manages URLs and file paths within that site. Let's move on to [Chapter 2: Path Management (`Path`, `Redirect`)](02_path_management___path____redirect___.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 2: Path Management (`Path`, `Redirect`)

Welcome back! In [Chapter 1: Configuration (`SiteConf`)](01_configuration___siteconf___.md), we learned how `__s45` knows *which* website it's running for. Now, let's dive into how it handles the addresses or URLs *within* that website.

Imagine you're building a website. You want nice, easy-to-remember URLs for your visitors, like `www.my-cool-blog.com/about-us`. But behind the scenes, your web application might understand that page using a different, more technical name, maybe something like `s45/page/about`. How do you connect the user-friendly URL to the system's internal path? And what happens if someone accidentally types the internal path?

This is where **Path Management** comes in, using two main helpers: `Path` and `Redirect`. Think of them as your website's GPS and traffic controller.

## What Problem Does Path Management Solve?

Websites need two kinds of addresses:

1.  **User-Friendly URLs (Aliases):** Short, descriptive paths that people see in their browser's address bar (e.g., `/contact`, `/products/cool-widget`). These are good for users and search engines.
2.  **System Paths:** Internal identifiers that the `__s45` system uses to find the right code or content (e.g., `s45/page/contact`, `s45/product/view/123`). These are often more structured and useful for developers.

The core problem is translating between these two types of addresses and ensuring users always see the friendly version.

**Use Case:** Let's say you have an "About Us" page.
*   You want visitors to go to `www.yoursite.com/about-us`.
*   The system internally knows this page as `s45/page/about`.
*   Path Management helps translate `/about-us` to `s45/page/about` when a user visits the page.
*   It also helps generate the correct `/about-us` link when you need to link to that page from somewhere else.
*   Finally, if someone somehow finds and types `www.yoursite.com/s45/page/about` into their browser, Path Management should automatically redirect them to the nicer `/about-us` URL.

## Meet `Path` and `Redirect`: Your Website's GPS and Traffic Cop

1.  **`Path` Class:** This is the translator.
    *   It looks up user-friendly URLs (aliases) to find the corresponding system paths.
    *   It looks up system paths to find their user-friendly aliases.
    *   It uses a dedicated database table (`_s45_aliases`) to store these translations, keeping track of which `siteId` (from [Chapter 1: Configuration (`SiteConf`)](01_configuration___siteconf___.md)) and language the translation belongs to.

2.  **`Redirect` Class:** This is the traffic controller.
    *   It automatically sends users from one URL to another.
    *   Its main job is to ensure users always end up on the "official" alias URL, even if they initially requested the system path.
    *   It can also be used to redirect old, outdated URLs to new ones (though we'll focus on the system path redirection here).

## Using `Path`: Translating Addresses

Let's see how to use the `Path` class for our "About Us" page example.

**Example 1: Finding the System Path from an Alias**

Imagine the user types `www.yoursite.com/about-us`. The `__s45` system needs to figure out which internal page this corresponds to.

```php
<?php
// Make sure the Path class can be found
use Site45\Path\Path;

// Create a Path helper object
$pathHelper = Path::create();

// The alias the user requested (without the domain name)
$userAlias = 'about-us';
// Let's assume the current language is English ('en')
$currentLanguage = 'en';

// Ask the Path helper to find the system path for this alias
$systemPath = $pathHelper->getSysPath($userAlias, $currentLanguage);

if ($systemPath) {
  echo "The alias '/{$userAlias}' corresponds to the system path: " . $systemPath;
} else {
  echo "Could not find a system path for the alias '/{$userAlias}'.";
}

// --- Example Output ---
// Assuming 'about-us' is mapped to 'page/about' in the database:
// The alias '/about-us' corresponds to the system path: s45/page/about
?>
```

**Explanation:**
*   `Path::create()` gives us a `Path` object ready to work.
*   `$pathHelper->getSysPath('about-us', 'en')` looks into the `_s45_aliases` table. It searches for an entry where `alias` is 'about-us', `langCode` is 'en', and the `siteId` matches the current site (remember `SiteConf`?).
*   If it finds a match, it returns the `sysPath` value from that table row, automatically adding `s45/` at the beginning.

**Example 2: Finding the Alias from a System Path**

Now, imagine you're writing code to create a link *to* the "About Us" page. You know its system path (`s45/page/about`), but you need the user-friendly alias (`/about-us`) to put in the link's `href` attribute.

```php
<?php
// Make sure the Path class can be found
use Site45\Path\Path;

// Create a Path helper object
$pathHelper = Path::create();

// The system path we want to find the alias for
$systemPath = 's45/page/about';
// Let's assume the current language is English ('en')
$currentLanguage = 'en';

// Ask the Path helper for the alias
// Note: getAlias expects the path *without* the 's45/' prefix
$internalPart = str_replace('s45/', '', $systemPath);
$alias = $pathHelper->getAlias($internalPart, $currentLanguage);

if ($alias) {
  echo "The system path '{$systemPath}' has the alias: /" . $alias;
} else {
  // If no alias is found, it might return NULL or the original system path part
  echo "Could not find a specific alias for '{$systemPath}'.";
}

// --- Example Output ---
// Assuming 'page/about' is mapped to 'about-us' in the database:
// The system path 's45/page/about' has the alias: /about-us
?>
```

**Explanation:**
*   `$pathHelper->getAlias('page/about', 'en')` does the reverse lookup in the `_s45_aliases` table. It searches for an entry where `sysPath` is 'page/about', `langCode` is 'en', and the `siteId` matches the current site.
*   If found, it returns the corresponding `alias`.

**Managing Aliases:**
The `Path` class also has methods like `addAlias($sysPath, $alias, $langCode)` and `deleteAlias($sysPath, $langCode)` which allow you or other parts of the system to create, update, or remove these translations in the `_s45_aliases` database table.

## Using `Redirect`: Enforcing Friendly URLs

What happens if a user (or maybe a search engine robot) tries to access the "About Us" page using its system path, like `www.yoursite.com/s45/page/about`? We don't want that! It looks messy, and having the same content accessible at two different URLs can be bad for search engine rankings (duplicate content).

This is where the `Redirect` class comes in. It checks the incoming request path. If it looks like a system path (starts with `s45/`) and has a known alias, it automatically tells the user's browser to go to the alias URL instead.

You don't usually call the `Redirect` class directly in your page-building code. It's often hooked into the very early stages of how `__s45` handles an incoming web request.

**Example: How `Redirect` Works (Conceptual)**

Imagine the `__s45` system receives a request for `/s45/page/about`.

```php
<?php
// Make sure the Redirect and Path classes can be found
use Site45\Path\Redirect;
use Site45\Path\Path; // Redirect uses Path internally

// Get the requested path from the web server
$requestedPath = $_GET['q']; // Simplified: e.g., 's45/page/about'

// Create a Redirect helper
$redirectHelper = Redirect::create();

// Ask the helper to check and perform any necessary redirect
// This function might stop everything and send a redirect header if needed!
$redirectHelper->exec($requestedPath);

// If exec() didn't redirect, the code continues to load the page...
echo "Loading content for path: " . $requestedPath;

// --- Example Outcome ---
// 1. User requests 's45/page/about'
// 2. Redirect::create()->exec('s45/page/about') is called.
// 3. Inside exec(), it sees 's45/' at the start.
// 4. It uses Path::create()->getAlias('page/about') to find the alias ('about-us').
// 5. It finds the alias!
// 6. It tells the browser: "Hey, permanently move (301 Redirect) to '/about-us' instead."
// 7. The browser automatically makes a NEW request to '/about-us'.
// 8. The script execution for 's45/page/about' stops.
// 9. The system handles the new '/about-us' request normally.
?>
```

**Explanation:**
*   `Redirect::create()` gets the redirect helper ready.
*   `$redirectHelper->exec($requestedPath)` is the key step. It checks if `$requestedPath` starts with `s45/`.
*   If it does, it uses the `Path` class (specifically `Path::getAlias`) to see if there's a user-friendly alias defined for the part after `s45/` (e.g., `page/about`).
*   If an alias is found (e.g., `about-us`), `exec` performs a "301 Permanent Redirect", telling the browser the correct address is `/about-us`. The browser then automatically goes to `/about-us`.

## How It Works Under the Hood (`Path`)

Let's peek inside `Path::getSysPath()`:

1.  **Input:** It receives the alias (e.g., `about-us`) and language code (e.g., `en`).
2.  **Site ID:** It implicitly knows the current site's ID (e.g., `my_blog`) from `SiteConf::getId()`.
3.  **Database Query:** It runs a query on the `_s45_aliases` database table, looking something like this (simplified SQL):
    ```sql
    SELECT sysPath
    FROM _s45_aliases
    WHERE siteId = 'my_blog'
      AND alias = 'about-us'
      AND langCode = 'en'
    LIMIT 1;
    ```
4.  **Result:** If the query finds a row, it takes the value from the `sysPath` column (e.g., `page/about`).
5.  **Prefix & Return:** It adds `s45/` to the beginning and returns the full system path (`s45/page/about`). If no row was found, it returns `NULL`.

Here's a diagram:

```mermaid
sequenceDiagram
    participant YourCode as Your Code
    participant PathClass as Path Class
    participant Database as _s45_aliases Table
    participant SiteConf as SiteConf (Implicit)

    YourCode->>PathClass: Call getSysPath('about-us', 'en')
    Note over PathClass, Database: Needs current site ID
    PathClass->>SiteConf: getId()
    SiteConf-->>PathClass: Return siteId (e.g., 'my_blog')
    PathClass->>Database: Query: Find sysPath WHERE siteId='my_blog', alias='about-us', langCode='en'
    Database-->>PathClass: Return row with sysPath='page/about'
    PathClass->>PathClass: Prepend 's45/' to 'page/about'
    PathClass-->>YourCode: Return 's45/page/about'
```

**A Glimpse at the Code (`Path::getSysPath`)**

This is a simplified look at the code in `s45_path/classes/Site45/Path/Path.php`:

```php
// Simplified from s45_path/classes/Site45/Path/Path.php
namespace Site45\Path;

class Path {
  protected $siteId; // Stores the current site ID

  public static function create() {
    $path = new static();
    // $path->checkTableAliases(); // Checks if DB table exists (simplified away)
    // s45_getSiteId() is a helper that likely uses SiteConf::getId()
    $path->siteId = s45_getSiteId();
    return $path;
  }

  public function getSysPath($alias, $langCode = NULL) {
    // If language not given, get it from global settings
    $langCode = $langCode ? $langCode : $GLOBALS['language']->language;

    // --- Build Database Query ---
    // 'db_select' is likely a helper function for database access
    $query = db_select('_s45_aliases');
    $query->condition('siteId', $this->siteId); // Use current site ID
    $query->condition('alias', $alias);         // Filter by the alias given
    $query->condition('langCode', $langCode);   // Filter by language
    $query->fields('_s45_aliases', array('sysPath')); // We only want the sysPath column
    // --- Execute Query ---
    // fetchCol(0) gets the first column of results as an array
    $queryResult = $query->execute()->fetchCol(0);

    // --- Process Result ---
    if (isset($queryResult[0])) {
      // Found it! Prepend 's45/' and return
      return 's45/' . trim($queryResult[0]);
    }

    // Not found
    return NULL;
  }

  // ... other methods like getAlias, addAlias, deleteAlias ...
}
```

## How It Works Under the Hood (`Redirect`)

Now let's look inside `Redirect::exec()` when it handles a system path like `s45/page/about`:

1.  **Input:** It receives the requested path (e.g., `s45/page/about`).
2.  **System Path Check:** It checks if the path starts with `s45/`. Yes, it does.
3.  **Find Alias:** It takes the part *after* `s45/` (`page/about`) and uses `Path::create()->getAlias('page/about')` to look up the corresponding user-friendly alias.
4.  **Alias Found?** Let's assume `getAlias` returns `about-us`.
5.  **Redirect!** Because an alias was found, the `Redirect` class tells the web server (using a function like `drupal_goto` or `header()`) to send a "301 Moved Permanently" response back to the browser, telling it to go to `/about-us` instead. It also includes any query parameters (like `?id=123`) from the original request.
6.  **Execution Stops:** The script handling the original `s45/page/about` request usually stops immediately after sending the redirect instruction.

Here's a diagram:

```mermaid
sequenceDiagram
    participant Browser as Browser
    participant S45System as __s45 System
    participant RedirectClass as Redirect Class
    participant PathClass as Path Class

    Browser->>S45System: Request URL: /s45/page/about?source=email
    S45System->>RedirectClass: Call exec('s45/page/about')
    Note over RedirectClass: Path starts with 's45/'? Yes.
    RedirectClass->>PathClass: Call getAlias('page/about')
    PathClass-->>RedirectClass: Return alias 'about-us'
    Note over RedirectClass: Alias found! Prepare redirect.
    RedirectClass->>S45System: Initiate 301 Redirect to '/about-us?source=email'
    S45System-->>Browser: Send HTTP 301 Redirect Header
    Browser->>S45System: Automatically makes NEW request for URL: /about-us?source=email
    S45System->>S45System: Handle the '/about-us' request normally (no redirect needed this time)
```

**A Glimpse at the Code (`Redirect::exec`)**

This is a simplified look at `s45_path/classes/Site45/Path/Redirect.php`:

```php
// Simplified from s45_path/classes/Site45/Path/Redirect.php
namespace Site45\Path;
use Site45\Path\Path; // Needs Path class to find aliases

class Redirect {
  protected $siteId;

  public static function create() {
    $path = new static();
    // $path->siteId = s45_getSiteId(); // Knows the current site
    return $path;
  }

  public function exec($requestedPath) {
    // Check if the path starts with 's45/'
    if (substr($requestedPath, 0, 4) == 's45/') {

      // Use Path class to find the alias for the part *after* 's45/'
      // Note: Path::getAlias needs the path *without* 's45/'
      $internalPart = str_replace('s45/', '', $requestedPath);
      $alias = Path::create()->getAlias($internalPart); // Language assumed from global context

      if ($alias) {
        // Found an alias! Time to redirect.
        // Get original query parameters (?x=1&y=2) if any
        $query_params = drupal_get_query_parameters(); // Helper to get $_GET params

        // Build the new, full URL with the alias and params
        // 'url()' is likely a helper to build URLs correctly
        $newUrl = url($alias, array('query' => $query_params, 'absolute' => TRUE));

        // Perform the redirect (301 = Permanent)
        // 'drupal_goto' stops script execution and sends the redirect header
        drupal_goto($newUrl, array(), 301);
      }
    }

    // Other redirect logic (e.g., for old aliases) might go here...
    // ... (Simplified away for this example) ...
  }
}
```

## Connecting to Other Parts

*   **Templates & Links:** When generating links within your website's pages (HTML templates), you'll often use `Path::getAlias()` to ensure you're linking to the user-friendly URL, not the system path.
*   **Menu System:** The website's menu system likely uses `Path` to store and retrieve the correct URLs for menu items.
*   **Component Loading:** When the system receives a request (after any redirects), it uses the resulting path (either the original alias or the translated system path) to figure out which [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md) should handle the request.

## Conclusion

You've now learned about `Path` and `Redirect`, the core tools `__s45` uses for managing website URLs!

*   They solve the problem of translating between **user-friendly aliases** (like `/about-us`) and **internal system paths** (like `s45/page/about`).
*   The `Path` class handles the **translation** using the `_s45_aliases` database table, considering the specific site and language. You use `getSysPath()` to find the system path and `getAlias()` to find the alias.
*   The `Redirect` class acts as a **traffic controller**, automatically sending users from system paths to their corresponding aliases using 301 redirects, ensuring URL consistency.

Understanding how `__s45` handles paths is crucial because URLs are the fundamental way users and the system navigate the website's content and features.

Now that we know how the site is identified (`SiteConf`) and how its URLs are managed (`Path`, `Redirect`), let's look at the building blocks that actually create the content and functionality on those paths. Next up is [Chapter 3: Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 3: Component (`Compo` / `Compo452`)

Welcome back! In [Chapter 2: Path Management (`Path`, `Redirect`)](02_path_management___path____redirect___.md), we saw how `__s45` handles website addresses (URLs) and translates them. Now that we know *how* to get to a specific "page" or location within our site, let's explore *what* actually builds the content we see on that page.

## What Problem Do Components Solve?

Imagine building a website like assembling a car. You don't start by melting metal; you start with pre-built parts: an engine, wheels, seats, a steering wheel. Each part has a specific job and fits together with others.

Web pages are similar. They are often made up of distinct sections: a header with the logo and menu, a sidebar with links, a main content area, maybe a footer with copyright info. Writing the entire HTML, CSS, and JavaScript for every single page from scratch would be incredibly repetitive and hard to manage.

**Use Case:** Let's think about a real estate website.
*   It needs a **Search Form** to find properties.
*   It needs to display **Property Teasers** (small summaries) in a list.
*   It needs a **Main Menu** to navigate the site.
*   It needs a **Detailed View** for a single property.

Instead of coding these elements over and over again, wouldn't it be great if we could build each one *once* as a reusable "block" and then just place these blocks where needed?

This is exactly what **Components** (`Compo` or `Compo452` in `__s45`) are designed for!

## Meet Components: Your Website's Lego Blocks

Think of Components as specialized Lego blocks for building web pages. Each component type is a self-contained unit with its own appearance and function. It encapsulates everything needed for that specific piece of the user interface.

A typical component in `__s45` consists of several files, usually grouped together in a folder named after the component:

1.  **PHP Class (`*.php`):** The "brain" of the component.
    *   Defines the component's logic, data properties, and methods.
    *   Often inherits from a base class like `Site45\Compo\Compo` or `Site45\Compo452\Compo452`.
    *   Might fetch data it needs to display (e.g., getting property details from a database).
    *   Example: `PhuketPropertyTeaser.php` contains the logic for displaying a property summary.

2.  **Template (`*.tpl.php`):** The "skeleton" or HTML structure.
    *   Defines how the component looks in HTML.
    *   Uses PHP code snippets (`<?php ... ?>`) to display data provided by the PHP class (e.g., showing the property name or price).
    *   Example: `PhuketPropertyTeaser.tpl.php` defines the HTML layout for the teaser block.

3.  **CSS (`*.css`):** The "skin" or styling rules.
    *   Makes the component look good (colors, fonts, spacing, layout).
    *   Targets the HTML elements defined in the template.
    *   Example: `PhuketPropertyTeaser.css` styles the property teaser.

4.  **JavaScript (`*.js`):** The "interactivity" or behavior.
    *   Adds dynamic features like image sliders, pop-ups, or handling button clicks within the component.
    *   Often runs after the component's HTML is loaded in the browser.
    *   Example: `PhuketPropertyTeaser.js` might initialize an image carousel for the property photos.

5.  **Editor Template (`*.editor.php`) (Optional):** A special template used in the admin area.
    *   Provides a form for administrators to change the component's settings or content easily.
    *   Example: `BaseParagraph.editor.php` (if it existed) would allow an admin to edit the paragraph's text.

**`Compo` vs `Compo452`:** You'll notice references to both `Compo` and `Compo452` in the code. `Compo452` appears to be a newer or refactored version of the original `Compo` concept. While there might be some technical differences in how they are implemented or rendered, the core idea remains the same: they are both self-contained, reusable building blocks for your website. For now, think of them as fulfilling the same fundamental purpose.

## How Components Are Used

Web pages in `__s45` are typically assembled by combining multiple components. The system figures out, based on the URL requested ([Chapter 2: Path Management (`Path`, `Redirect`)](02_path_management___path____redirect___.md)) and the site configuration ([Chapter 1: Configuration (`SiteConf`)](01_configuration___siteconf___.md)), which components need to be displayed on the current page.

For our real estate example page showing a list of properties:
*   The page might load a `BaseMainMenu` component for navigation.
*   It might load a `PropertySearchForm` component.
*   It would then load multiple instances of the `PhuketPropertyTeaser` component, one for each property found.
*   Finally, it might load a `Footer` component.

The process of taking these components, running their logic, generating their HTML, and combining them into a final page is called **Rendering**. We'll learn more about this in [Chapter 5: Component Rendering (`Render452`)](05_component_rendering___render452___.md).

## Inside a Component: The `PhuketPropertyTeaser` Example

Let's look at the `PhuketPropertyTeaser` component, which displays a small summary of a property, often used in search results.

**1. The PHP Class (`PhuketPropertyTeaser.php`)**

This file defines the component's logic and data.

```php
// Simplified from Compo/PropertyBlocks/PhuketPropertyTeaser/PhuketPropertyTeaser.php
<?php
use Site45\Compo\Compo; // Inherits from the base Compo class
use Site45\DtoLib\Base\LangVO;
use Site45\Base\Store; // Uses the Store to get data
use Site45\Sets\Phuket\Query\PhuketPropertyQuery; // Uses a Query to load data
use Site45\Sets\Phuket\Query\PhuketPropertyDto; // Uses a DTO to hold data

class PhuketPropertyTeaser extends Compo {

  // Data this component needs or holds
  public $propertyId; // The ID of the property to show
  public $propertyDto; // Holds the property details (as a DTO)
  public $currentCurrency; // The currency to display prices in

  // Default text (often multi-language using LangVO)
  public $content_bedrooms;
  public $content_isSaled;

  // Method called BEFORE the template is rendered
  protected function beforeRender($props) {
    // Get property details if only ID was given
    if (isset($props['propertyId'])) {
      // Use a Query to load property data (more on Queries later)
      $this->propertyDto = PhuketPropertyQuery::create()->load($props['propertyId']);
    } else {
      // Or use data passed directly
      $this->propertyDto = PhuketPropertyDto::create($props['propertyDto']);
    }
    // Get the current currency from the Store (shared application state)
    $this->currentCurrency = Store::create()->get('currency', 'USD');
  }

  // Constructor: Sets default values when the component is created
  function __construct() {
    // Set up default text values (using LangVO for multi-language)
    $this->content_bedrooms = LangVO::create(['ru' => 'спален', 'en' => 'rooms']);
    $this->content_isSaled = LangVO::create(['ru' => 'Продано', 'en' => 'Sold']);
    // ... other default settings ...
  }
}
```

*   **Inheritance:** It `extends Compo`, gaining base component functionality.
*   **Properties:** It declares variables like `$propertyId` and `$propertyDto` to hold data specific to this teaser instance. We'll see `$propertyDto` is a [Data Transfer Object (DTO)](04_data_transfer_object__dto____value_object__vo__.md), a structured way to hold data.
*   **`beforeRender()`:** This important method runs *before* the HTML template is processed. It's the place to fetch or prepare any data the template will need. Here, it loads the property details using a `PhuketPropertyQuery` (more on Queries in [Chapter 8: Query](08_query___query____queryfromevents____jsonquery___.md)) or uses data passed in. It also gets the current currency from the `Store` (more in [Chapter 9: Store (`Store`)](09_store___store___.md)).
*   **`__construct()`:** The constructor sets up initial default values, especially language strings using `LangVO` (a type of [Value Object](04_data_transfer_object__dto____value_object__vo__.md)).

**2. The Template (`PhuketPropertyTeaser.tpl.php`)**

This file defines the HTML structure and displays the data prepared by the PHP class.

```php
// Simplified from Compo/PropertyBlocks/PhuketPropertyTeaser/PhuketPropertyTeaser.tpl.php
<?php /* @var $this PhuketPropertyTeaser */ // Hint for IDEs about $this ?>

<component> <? // Placeholder tags, often removed during rendering ?>

  <? // Link to the full property details page ?>
  <a class="PhuketPropertyTeaser--galleryWrapper" href="<?php print s45_url('property/'.$this->propertyDto->id); ?>">
      <div class="PhuketPropertyTeaser--gallery">
          <div class="PhuketPropertyTeaser--imageWrapper">
              <? // Display the first photo. s45_imgSrc is likely a helper function ?>
              <img class="s45-img-cover" src="<?php print s45_imgSrc($this->propertyDto->photos[0]); ?>">
              <? // Show "Sold" overlay if needed ?>
              <?php if($this->propertyDto->isSaled): ?>
                <div class="PhuketPropertyTeaser-Saled">
                  <?php print s45_lang($this->content_isSaled); // Use helper for language ?>
                </div>
              <?php endif; ?>
          </div>
      </div>
  </a>

  <div class="PhuketPropertyTeaser--header">
      <? // Display the property name (using the language helper) ?>
      <h3 class="PhuketPropertyTeaser--title">
        <?php print s45_lang($this->propertyDto->name); ?>
      </h3>
      <? // Display the price (simplified) ?>
      <div class="PhuketPropertyTeaser--price">
          <?php print number_format((int) $this->propertyDto->priceSale); ?>
          <span><?php print $this->currentCurrency; ?></span>
      </div>
  </div>

  <? // Display characteristics like bedrooms ?>
  <div class="PhuketPropertyTeaser--chars">
    <div class="PhuketPropertyTeaser--char">
      <img src="/path/to/bedroom-icon.svg"> <? // Icon ?>
      <?php print $this->propertyDto->bedrooms; ?> <?php print s45_lang($this->content_bedrooms); ?>
    </div>
    <? // ... other characteristics ... ?>
  </div>

</component>
```

*   **HTML + PHP:** It's mostly HTML, but uses `<?php ... ?>` tags to insert dynamic data.
*   **`$this`:** Inside the template, `$this` refers to the PHP class instance (`PhuketPropertyTeaser`). You access its properties like `$this->propertyDto->name` or `$this->content_isSaled`.
*   **Helpers:** It uses functions like `s45_url()` to create correct links and `s45_lang()` to display text in the current user's language.

**3. CSS (`PhuketPropertyTeaser.css`)**

This file adds styles to the HTML elements.

```css
/* Simplified from Compo/PropertyBlocks/PhuketPropertyTeaser/PhuketPropertyTeaser.css */

/* Style the main container */
.PhuketPropertyTeaser {
  background-color: #f8f8f8;
  border: 1px solid #eee;
  margin-bottom: 20px;
}

/* Style the image wrapper */
.PhuketPropertyTeaser--galleryWrapper {
  display: block;
  position: relative;
  height: 223rem; /* Using custom 'rem' units */
}

/* Style the title */
.PhuketPropertyTeaser--title {
  font-size: 16rem;
  color: #333;
  margin: 5px 0;
}

/* Style the 'Sold' overlay */
.PhuketPropertyTeaser-Saled {
  position: absolute;
  color: white;
  font-size: 36rem;
  /* ... more styling ... */
}
```

*   **Selectors:** It uses CSS class names defined in the template (like `.PhuketPropertyTeaser`, `.PhuketPropertyTeaser--title`) to apply styles.
*   **Encapsulation:** Styles defined here are generally intended to affect only *this* component, preventing conflicts with other parts of the page.

**4. JavaScript (`PhuketPropertyTeaser.js`)**

This file adds interactive behavior.

```javascript
// Simplified from Compo/PropertyBlocks/PhuketPropertyTeaser/PhuketPropertyTeaser.js

var PhuketPropertyTeaser = {
  // Inherits basic component JS behavior (conceptual)
  __proto__: Compo45, // Assumes a base JS object 'Compo45' exists

  // Initialization function, runs when the component loads
  init: function() {
    // Find the image carousel within this specific component instance
    // 'this.compoId' holds the unique ID of this component on the page
    var carouselElement = jQuery('[s45-compo-id=' + this.compoId + '] .owl-carousel');

    // If a carousel exists, initialize the Owl Carousel library on it
    if (carouselElement.length > 0) {
      carouselElement.owlCarousel({
        items: 1, // Show one image at a time
        loop: true, // Loop back to the start
        nav: false, // Hide next/prev arrows
        dots: true // Show navigation dots
        // ... other carousel options ...
      });
    }

    // Other JS logic for this component could go here...
    // E.g., handling clicks on a 'favorite' button
  }
};
```

*   **`init()`:** A common pattern is to have an `init` function that runs when the component is ready in the browser.
*   **Targeting:** It often uses jQuery or plain JavaScript to find HTML elements *within* the specific instance of the component, frequently using a unique ID attribute like `s45-compo-id`.
*   **Libraries:** It can use third-party JavaScript libraries (like Owl Carousel here) to add complex features.

These four files work together to create a complete, reusable `PhuketPropertyTeaser` block.

## How It Fits Together (Simplified Flow)

Here's a simplified view of how components come together to build a page:

```mermaid
sequenceDiagram
    participant Browser
    participant S45System as __s45 System
    participant PathMgmt as Path Management
    participant CompoLoader as Component Loader/Renderer
    participant PropertyTeaserPHP as PhuketPropertyTeaser.php
    participant PropertyTeaserTPL as PhuketPropertyTeaser.tpl.php

    Browser->>S45System: Request URL (e.g., /properties?type=sale)
    S45System->>PathMgmt: Determine route/page info from URL
    PathMgmt-->>S45System: Page needs: Header, SearchForm, PropertyList, Footer
    S45System->>CompoLoader: Render components for PropertyList page
    Note over CompoLoader: Finds it needs PhuketPropertyTeaser (multiple times)
    CompoLoader->>PropertyTeaserPHP: Create instance & call beforeRender()
    Note over PropertyTeaserPHP: Fetches property data (DB, Store, etc.)
    PropertyTeaserPHP-->>CompoLoader: Data is ready (in $this->propertyDto)
    CompoLoader->>PropertyTeaserTPL: Process template with data
    PropertyTeaserTPL-->>CompoLoader: Generated HTML for one teaser
    Note over CompoLoader: Repeats for other teasers... Collects CSS/JS links too.
    CompoLoader-->>S45System: Assembled HTML, CSS links, JS links for all components
    S45System-->>Browser: Send final Page (HTML, CSS, JS)
```

This diagram shows that the system identifies the necessary components, runs their PHP logic (`beforeRender`), combines the logic's data with the HTML template (`.tpl.php`), and gathers all the pieces (HTML, CSS, JS) to send back to the browser.

## Conclusion

You've learned about Components (`Compo` / `Compo452`), the fundamental building blocks of pages in `__s45`!

*   They solve the problem of creating **reusable UI elements**.
*   Think of them like **Lego blocks**, each with a specific purpose.
*   A component typically consists of a **PHP class** (logic), a **Template (`.tpl.php`)** (HTML structure), **CSS** (styling), and **JavaScript** (interactivity).
*   Pages are **assembled** by combining different components.
*   `Compo` and `Compo452` represent potentially different versions, but the core concept is the same.

We saw how components often need data to display (like the property details in `PhuketPropertyTeaser`). How is this data typically structured and passed around within the application, especially when feeding it *into* components? That brings us to our next topic: Data Transfer Objects and Value Objects.

Let's move on to [Chapter 4: Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 4: Data Transfer Object (DTO) / Value Object (VO)

In [Chapter 3: Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md), we learned how websites in `__s45` are built using reusable blocks called Components. We saw that components often need data to display – like our `PhuketPropertyTeaser` needing property details. But how should we organize and pass this data around our application?

## What Problem Do DTOs and VOs Solve?

Imagine you're packing boxes to move house. You wouldn't just throw random items into any box, right? You'd probably use specific boxes for specific things: a box for books, a box for fragile glassware, a box for kitchen utensils. This keeps things organized, prevents damage, and makes unpacking easier.

In software, data is like those household items. We need to move it between different parts of our application. For example:
*   Get property details from the database.
*   Pass those details to a component like `PhuketPropertyTeaser`.
*   Maybe pass some user settings to another part of the system.

If we just pass data around loosely, maybe using simple PHP arrays (`['name' => 'Beach Villa', 'price' => 500000, 'bedrooms' => 3]`), it can get messy:
*   Did we spell the keys correctly (`'price'` or `'Price'`)?
*   Is the price a number or a string?
*   Did we forget to include the number of bathrooms?
*   How does the receiving code know *what* data to expect?

This is where **Data Transfer Objects (DTOs)** and **Value Objects (VOs)** come in! They are like custom-designed containers or blueprints for our data.

**Use Case:** We need to fetch details about a property (ID: 123) from the database and give it to our `PhuketPropertyTeaser` component so it can display the property's name, price, and number of bedrooms. How can we ensure this data is packaged neatly and consistently?

## Meet DTOs and VOs: Your Data Containers

DTOs and VOs are special PHP classes designed specifically to hold data in a structured way. They act as contracts, defining exactly *what* pieces of information belong together.

1.  **Data Transfer Object (DTO - `Dto.php`):**
    *   Think of this as a container for a more complex "thing" or entity.
    *   Often represents a whole object you might find in your database, like a `Property`, a `User`, or a `WebsiteConfiguration`.
    *   Usually has an `id` property to uniquely identify the specific entity it represents.
    *   Examples in `__s45`:
        *   `PropertyDto`: Holds all details about a single property listing.
        *   `ComplexDto`: Holds details about a property development complex.
        *   `SiteConfDto` (from [Chapter 1: Configuration (`SiteConf`)](01_configuration___siteconf___.md)): Holds configuration for a specific website.
        *   `FileDto`: Represents information about an uploaded file.

2.  **Value Object (VO - `VO.php`):**
    *   Think of this as a container for a simpler, often specific *value* or concept.
    *   Usually doesn't have its own unique `id`. Its identity *is* its value(s).
    *   Often used for things like prices, coordinates, or multilingual text snippets.
    *   Examples in `__s45`:
        *   `PropertyPriceVO`: Might hold the price amount, currency, and perhaps price per square meter.
        *   `LangVO`: **Very important!** Used to hold text in multiple languages.
        *   `SearchResultVO`: Holds the results of a search query, including the list of found items and total count.

**The Key Idea:** Both DTOs and VOs provide *structure*. When you receive a `PropertyDto` object, you know it should have properties like `name`, `price`, `bedrooms`, etc., because the `PropertyDto` class defines them.

## Using DTOs and VOs

Let's see how we can use a `PropertyDto` and a `LangVO`.

**Example 1: Creating and Using a `PropertyDto`**

Imagine we've fetched property data from the database as an array. We can package it into a `PropertyDto`.

```php
<?php
// Make sure the DTO class can be found
use Site45\DtoLib\Property\PropertyDto;
use Site45\DtoLib\Base\LangVO; // We'll need this too

// 1. Sample data (maybe from a database query)
$propertyDataFromDb = [
  'id' => 123,
  'name' => ['en' => 'Beach Villa', 'ru' => 'Вилла на Пляже'], // Name in multiple languages
  'bedrooms' => 3,
  'bathrooms' => 2,
  'price' => ['amount' => 500000, 'currency' => 'USD'] // Price data
];

// 2. Create a PropertyDto instance using the data
// The static 'create()' method is the standard way to make DTOs/VOs
$property = PropertyDto::create($propertyDataFromDb);

// 3. Now we have a structured object! Access properties easily:
echo "Property ID: " . $property->id . "\n";

// Accessing the LangVO for the name:
// We need a helper function (like s45_lang) to get the right language text
echo "Property Name (EN): " . s45_lang($property->name) . "\n"; // Assumes s45_lang() picks 'en'

echo "Bedrooms: " . $property->bedrooms . "\n";

// Accessing the nested PropertyPriceVO for the price:
echo "Price: " . $property->price->amount . " " . $property->price->currency . "\n";

// --- Example Output (assuming current language is 'en') ---
// Property ID: 123
// Property Name (EN): Beach Villa
// Bedrooms: 3
// Price: 500000 USD
?>
```

**Explanation:**
*   We include the necessary `use` statements.
*   `PropertyDto::create($propertyDataFromDb)` takes our raw array and automatically populates a new `PropertyDto` object. It's smart enough to see the `name` data is an array of languages and creates a `LangVO` for it. It likely does the same for `price`, creating a `PropertyPriceVO`.
*   We can then access the data using standard object property syntax (`$property->id`, `$property->bedrooms`).
*   Accessing nested objects (like the price VO) is straightforward (`$property->price->amount`).
*   For multilingual text stored in `LangVO` objects (like `$property->name`), we usually use a helper function like `s45_lang()` which knows the current user's language and extracts the correct translation.

**Example 2: Using `LangVO` for Interface Text**

In our `PhuketPropertyTeaser` component (from Chapter 3), we needed text like "Sold" or "rooms". These need to change based on the website's language. `LangVO` is perfect for this.

```php
<?php
// Inside a Component class, like PhuketPropertyTeaser.php
use Site45\DtoLib\Base\LangVO;

class PhuketPropertyTeaser extends Compo {
  // ... other properties ...
  public $content_bedrooms;
  public $content_isSaled;

  function __construct() {
    // Create LangVO instances for text labels
    $this->content_bedrooms = LangVO::create(['ru' => 'спален', 'en' => 'bedrooms']);
    $this->content_isSaled = LangVO::create(['ru' => 'Продано', 'en' => 'Sold']);
  }

  // ... other methods ...
}

// --- Later, in the template (PhuketPropertyTeaser.tpl.php) ---

// Display the number of bedrooms label in the current language
<span class="label">
  <?php print s45_lang($this->content_bedrooms); ?>
</span>

// Display the "Sold" overlay text in the current language
<div class="overlay">
  <?php print s45_lang($this->content_isSaled); ?>
</div>
?>
```

**Explanation:**
*   In the component's constructor (`__construct`), we create `LangVO` instances for each piece of text that needs translation. We provide an array where keys are language codes (`en`, `ru`) and values are the translations.
*   In the component's template (`.tpl.php`), we use the `s45_lang()` helper function, passing it the `LangVO` object (e.g., `$this->content_bedrooms`). This function automatically looks up the translation for the currently active language.

## How It Works Under the Hood

DTOs and VOs are quite simple internally. They are mostly about defining public properties.

**1. Base Classes (`Dto.php`, `VO.php`)**

There are base classes that provide the common `create()` method.

```php
// Simplified view of s45_base/classes/Site45/Base/Dto.php
namespace Site45\Base;
use Site45\Base\Creator; // Uses a helper to populate data

class Dto {
  public $type = 'NotSet'; // Often identifies the DTO type
  public $id;            // Common property for unique ID
  // Other common properties like created, changed, name might be here

  public static function create($data = NULL){
    $dto = new static(); // Creates an instance of the *specific* DTO class (e.g., PropertyDto)
    Creator::create($dto, $data); // Uses a helper to copy data into the object
    return $dto;
  }
}

// Simplified view of s45_base/classes/Site45/Base/VO.php
namespace Site45\Base;
use Site45\Base\Creator;

class VO {
  public static function create($data = NULL){
    $vo = new static(); // Creates an instance of the *specific* VO class (e.g., LangVO)
    Creator::create($vo, $data); // Uses the same helper
    return $vo;
  }
}
```

*   The `create()` method is static, meaning you call it on the class itself (`PropertyDto::create()`), not on an object instance.
*   `new static()` is a clever PHP trick that creates an instance of the class you actually called `create()` on (e.g., `PropertyDto` or `LangVO`).
*   `Creator::create($object, $data)` is a helper utility (we don't need its details now) that intelligently copies the values from the input `$data` (like our array from the database) onto the public properties of the newly created `$dto` or `$vo` object. It might even recursively create nested DTOs/VOs if needed (like creating the `LangVO` for the `name` property).

**2. Specific DTO (`PropertyDto.php`)**

A specific DTO class defines its own set of public properties.

```php
// Simplified view of s45_base/classes/Site45/DtoLib/Property/PropertyDto.php
namespace Site45\DtoLib\Property;
use Site45\Base\Dto; // Inherits from the base Dto class
use Site45\DtoLib\Base\LangVO;
use Site45\DtoLib\Property\PropertyPriceVO;
// ... other use statements ...

class PropertyDto extends Dto {
  public $type = 'Property'; // Specific type identifier

  // Declare public properties for all expected data fields
  public $dealType;
  public $propertyType;

  /** @var PropertyPriceVO */ // This comment helps tools know the expected type
  public $price;

  public $areaCommon;
  public $bedrooms; // Expects an integer
  public $bathrooms;

  /** @var LangVO */ // Expects a LangVO object for the description
  public $description;

  public $photos = []; // Default to an empty array

  // ... many other properties ...

  // Constructor can set default values for complex properties
  function __construct() {
    $this->name = new LangVO(); // Default name is an empty LangVO
    $this->description = new LangVO(); // Default description
    $this->price = new PropertyPriceVO(); // Default price
    // ... maybe other defaults ...
  }
}
```

*   It `extends Dto` to inherit the `create()` method and common properties like `id`.
*   It declares many `public` properties (`$bedrooms`, `$bathrooms`, `$price`, etc.). These define the structure.
*   Comments like `/** @var PropertyPriceVO */` are called "annotations". They don't change how the code runs, but they help developers (and some tools) understand that the `$price` property is expected to hold a `PropertyPriceVO` object.
*   The `__construct` method often initializes properties that are themselves objects (like `LangVO` or `PropertyPriceVO`) to ensure they exist even if no data is provided for them.

**3. The `LangVO.php`**

This VO is slightly special because its `create()` method handles simple strings and language arrays directly.

```php
// Simplified view of s45_base/classes/Site45/DtoLib/Base/LangVO.php
namespace Site45\DtoLib\Base;
use Site45\Base\VO; // Inherits from base VO

class LangVO extends VO {
  public $type = 'Lang';

  // Override the default create method for specific LangVO logic
  public static function create($data = NULL){
    $vo = new static(); // Create a new LangVO instance

    if(is_string($data)){
      // If input is just a string, assume it's for the current language
      global $language; // Access global language settings
      $langCode = $language->language; // Get current language code (e.g., 'en')
      $vo->{$langCode} = $data; // Dynamically set property (e.g., $vo->en = "Hello")
    }
    elseif (is_array($data) || is_object($data)) {
      // If input is an array or object, loop through it
      foreach ($data as $key => $value){
        // Assume keys are language codes (en, ru, th...)
        if(is_string($value)){
          $vo->{$key} = $value; // Set property dynamically (e.g., $vo->ru = "Привет")
        }
      }
    }
    return $vo;
  }
}
```

*   It overrides `create()` to handle the common cases:
    *   If you pass a simple string (e.g., `LangVO::create('Hello')`), it assumes this string is for the *current* language and sets the corresponding property (e.g., `$vo->en = 'Hello'`).
    *   If you pass an array (e.g., `LangVO::create(['en' => 'Hello', 'ru' => 'Привет'])`), it loops through the array and sets properties named after the keys (e.g., `$vo->en = 'Hello'`, `$vo->ru = 'Привет'`).

**Flow Diagram:**

Here's how data might flow from a data source (like a Query) into a DTO and then to a Component:

```mermaid
sequenceDiagram
    participant DataSource as Data Source (e.g., Query/Repo)
    participant PropertyDto as PropertyDto Class
    participant CreatorUtil as Creator Utility
    participant Component as Your Component (e.g., Teaser)

    DataSource->>DataSource: Fetch raw data (e.g., array from DB)
    DataSource->>PropertyDto: Call PropertyDto::create(rawData)
    PropertyDto->>PropertyDto: Create empty PropertyDto object ($dto)
    PropertyDto->>CreatorUtil: Call Creator::create($dto, rawData)
    CreatorUtil->>CreatorUtil: Populate $dto properties from rawData
    Note over CreatorUtil: May create nested VOs like LangVO/PriceVO
    CreatorUtil-->>PropertyDto: Return populated $dto
    PropertyDto-->>DataSource: Return fully populated $dto
    DataSource-->>Component: Pass $dto object to Component
    Component->>Component: Use data: $dto->id, s45_lang($dto->name), $dto->price->amount
```

This shows the `create()` method orchestrating the process, using the `Creator` utility to handle the mapping, resulting in a structured `PropertyDto` object ready for the component to use.

## Connecting to Other Parts

DTOs and VOs are used extensively throughout `__s45`:

*   [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md): Components receive data packaged in DTOs/VOs as input (like `$this->propertyDto` in our example).
*   [Repository (`Repo`)](06_repository___repo__.md): Repositories, which handle saving and loading data, often work directly with DTOs. They might take a DTO to save it or return a DTO when loading data.
*   [Query (`Query`, `QueryFromEvents`, `JsonQuery`)](08_query___query____queryfromevents____jsonquery___.md): Query objects, responsible for fetching data based on specific criteria, typically return their results packaged within DTOs or VOs (often inside a `SearchResultVO`).
*   [Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery___.md): When reconstructing the current state of an object from its history of events, the final state is often represented as a DTO.

## Conclusion

You've now learned about Data Transfer Objects (DTOs) and Value Objects (VOs)!

*   They solve the problem of passing data around your application in a **structured and consistent** way.
*   Think of them as **custom data containers** or **blueprints**.
*   **DTOs (`Dto.php`)** typically represent complex entities (like `PropertyDto`) and often have an `id`.
*   **VOs (`VO.php`)** represent simpler values (like `PropertyPriceVO` or `LangVO`).
*   The **`LangVO`** is crucial for handling multilingual text.
*   You create them using the static `::create()` method (e.g., `PropertyDto::create($data)`).
*   They make code easier to understand and maintain because you know exactly what data structure to expect.

Now that we understand how components (`Compo`) are built and how they receive structured data (DTOs/VOs), how does the system actually take these components and turn them into the final HTML page that the user sees? That's the job of the renderer.

Let's move on to [Chapter 5: Component Rendering (`Render452`)](05_component_rendering___render452___.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 5: Component Rendering (`Render452`)

Welcome back! In [Chapter 4: Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md), we learned how `__s45` uses DTOs and VOs to neatly package data. We saw how a component like `PhuketPropertyTeaser` might receive its data in a `PropertyDto`.

Now, we have our component block ([Chapter 3: Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md)) and its structured data (DTOs/VOs). But how does the system actually combine these pieces to create the final HTML code that gets sent to the user's web browser?

## What Problem Does Component Rendering Solve?

Imagine you're a movie director. You have the actors (your component logic), the script (the data in DTOs), and the set design instructions (the `.tpl.php` template). But someone needs to actually *film the scene* – put the actors on set, have them say their lines according to the script, record it, and also make notes about which costumes (CSS) and special effects (JavaScript) were used.

Similarly, in `__s45`, we need a process that takes a component object (like our `PhuketPropertyTeaser` instance), runs its logic, uses its data, processes its HTML template, and figures out which CSS and JavaScript files are needed for it to look and work correctly on the webpage.

This process is called **Component Rendering**.

**Use Case:** We have created an instance of our `PhuketPropertyTeaser` component and given it the `PropertyDto` for a specific beach villa. How do we generate the actual HTML snippet for this teaser, including its image, title, price, and the "Sold" badge if necessary? And how do we know we need to include `PhuketPropertyTeaser.css` and `PhuketPropertyTeaser.js` on the final page?

## Meet `Render452`: Your Component's Stage Manager

The `Render452` class (and similar logic within the base `Compo` class's `apiGetRendered` method) acts like that stage manager or director. Its main job is to take a component object and transform it into a piece of the final webpage.

Here's what it typically does:

1.  **Gets the Component:** It receives a specific instance of a component class (e.g., an object of type `PhuketPropertyTeaser`).
2.  **Prepares Data:** It often calls a special method on the component, like `beforeRender()`, allowing the component to fetch any last-minute data it needs (maybe checking if the property was *just* sold).
3.  **Finds the Template:** It locates the component's HTML structure file, the `.tpl.php` file (e.g., `PhuketPropertyTeaser.tpl.php`).
4.  **Executes the Template:** It runs the PHP code inside the `.tpl.php` file. Crucially, inside this template, the special variable `$this` refers back to the component object, so the template can access the component's data (like `$this->propertyDto->name`). The output generated by this template (which is mostly HTML) is captured.
5.  **Wraps the HTML:** It takes the HTML generated by the template and wraps it in standard `<div>` tags. These wrapper divs get important attributes like a unique ID (`s45-compo-id`) and the component's name (`s45-compo-name`), which are often used by CSS and JavaScript.
6.  **Collects Resources:** It figures out the paths to the CSS file (e.g., `PhuketPropertyTeaser.css`) and JavaScript file (e.g., `PhuketPropertyTeaser.js`) associated with this component.
7.  **Packages the Result:** It bundles the final wrapped HTML, the list of required CSS files, and the list of required JS files into a neat package, often using a special DTO called `RenderedCompoDto`.

## Using the Renderer

You usually don't call `Render452` directly yourself. The `__s45` system typically handles rendering components when it's building a page. However, components themselves often have a method like `apiGetRendered()` that encapsulates this process.

Let's imagine the system needs to render our `$propertyTeaserComponent` object:

```php
<?php
// Assume $propertyTeaserComponent is an instance of PhuketPropertyTeaser
// It already has its $propertyDto loaded (maybe via beforeRender or earlier)

use Site45\DtoLib\Base\RenderedCompoDto; // We expect this kind of result

// --- This is how the system might trigger rendering ---
// Option 1: Using the component's own method (Common)
/** @var RenderedCompoDto $renderedOutput */
$renderedOutput = $propertyTeaserComponent->apiGetRendered();

// Option 2: Using a dedicated renderer (Less common for direct use)
// use Site45\Compo452\Render452;
// $renderedOutput = Render452::getRendered($propertyTeaserComponent);

// --- Now let's look at the result ---
echo "--- Rendered HTML Snippet --- \n";
echo $renderedOutput->html; // The final HTML code for the teaser
echo "\n\n";

echo "--- Required CSS Files --- \n";
print_r($renderedOutput->res->css); // Array of CSS file paths
echo "\n";

echo "--- Required JS Files --- \n";
print_r($renderedOutput->res->js); // Array of JS file paths
?>
```

**Explanation:**

*   We call the component's `apiGetRendered()` method (or potentially `Render452::getRendered()`).
*   This method does all the steps described above (calls `beforeRender`, processes template, wraps HTML, finds resources).
*   It returns a `RenderedCompoDto` object. This DTO acts as a container for the rendering results.
*   `$renderedOutput->html` contains the generated HTML string, ready to be inserted into the page.
*   `$renderedOutput->res` is another small object (a `CompoResVO`) holding arrays of file paths:
    *   `$renderedOutput->res->css` lists the CSS files needed by this component.
    *   `$renderedOutput->res->js` lists the JavaScript files needed.

**Example Output (Conceptual):**

```text
--- Rendered HTML Snippet ---
<div s45-compo-id="comp_123abc" s45-compo-name="PhuketPropertyTeaser" class="s45-Wrapper not-inited PhuketPropertyTeaserWrapper">
  <div class="s45-Compo PhuketPropertyTeaser">
    <a class="PhuketPropertyTeaser--galleryWrapper" href="/path/to/property/123">
      <div class="PhuketPropertyTeaser--gallery">
        <div class="PhuketPropertyTeaser--imageWrapper">
          <img class="s45-img-cover" src="/path/to/image.jpg">
          </div>
      </div>
    </a>
    <div class="PhuketPropertyTeaser--header">
      <h3 class="PhuketPropertyTeaser--title">Beach Villa</h3>
      <div class="PhuketPropertyTeaser--price">500,000 <span>USD</span></div>
    </div>
    <div class="PhuketPropertyTeaser--chars">
      <div class="PhuketPropertyTeaser--char">
        <img src="/path/to/bedroom-icon.svg"> 3 bedrooms
      </div>
    </div>
  </div>
</div>


--- Required CSS Files ---
Array
(
    [0] => /system/path/to/s45_base/Compo/Compo.css
    [1] => /system/path/to/PhuketPropertyTeaser/PhuketPropertyTeaser.css
)


--- Required JS Files ---
Array
(
    [0] => /system/path/to/s45_base/Compo/Compo.js
    [1] => /system/path/to/PhuketPropertyTeaser/PhuketPropertyTeaser.js
)

```

The system would then take this HTML and eventually place it on the page. It would also collect all the CSS and JS file paths from *all* rendered components and make sure they are included in the final page's `<head>` section or before the closing `</body>` tag.

## How It Works Under the Hood

Let's trace the steps when `$component->apiGetRendered()` is called (focusing on the logic inside the `Compo` base class, which is similar to `Render452`).

1.  **Check Permission:** Verify if the component should be displayed (e.g., is it `settings_published` or is the current user an editor?). If not, return an empty result.
2.  **`beforeRender()`:** Call the component's `$this->beforeRender($props)` method if it exists. This lets the component load or prepare its data just before the template is processed.
3.  **Get HTML:** Call an internal method like `getCompoHtmlFull()` to generate the HTML.
    *   **Set Attributes:** Prepare arrays of HTML attributes for the outer wrapper (`<div>`) and the inner component (`<div>`), including classes like `s45-Wrapper`, `s45-Compo`, the component's name, and its unique ID.
    *   **Execute Template (`getCompoHtml()`):**
        *   Find the path to the `.tpl.php` file.
        *   Check if the file exists. If not, show an error.
        *   Use **Output Buffering**: Start capturing anything that would normally be printed (`ob_start()`).
        *   `include $templateFilePath;`: This executes the PHP code within the template. The template code uses `$this` to access the component's properties (like `$this->propertyDto`) and prints HTML.
        *   Stop capturing and get the captured content (`$html = ob_get_clean();`).
        *   Remove placeholder `<component>` tags if present.
        *   Wrap the captured `$html` with the inner `div` using the prepared attributes (`<div class="s45-Compo <?php print $this->name; ?>">...</div>`).
    *   **Wrap Again:** Wrap the result of the previous step with the outer `div` using its prepared attributes (`<div s45-compo-id="..." s45-compo-name="..." class="s45-Wrapper ...">...</div>`).
4.  **Get Resources:** Call an internal method like `getCompoRes()` to determine the CSS and JS files needed. This usually involves checking if `ComponentName.css` and `ComponentName.js` exist in the component's directory and adding base component CSS/JS files (`Compo.css`, `Compo.js`).
5.  **Create Result DTO:** Create a `RenderedCompoDto` object.
    *   Set `$renderedCompo->id` and `$renderedCompo->name`.
    *   Set `$renderedCompo->html` to the fully wrapped HTML generated in step 3.
    *   Create a `CompoResVO` object containing the CSS and JS file arrays from step 4 and assign it to `$renderedCompo->res`.
6.  **Return:** Return the populated `RenderedCompoDto`.

**Sequence Diagram:**

```mermaid
sequenceDiagram
    participant System as Page Builder
    participant Component as Component Object (e.g., PhuketPropertyTeaser)
    participant Template as Component.tpl.php
    participant ResultDTO as RenderedCompoDto

    System->>Component: Call apiGetRendered()
    Component->>Component: Run beforeRender() (load data)
    Component->>Component: Find template path (Component.tpl.php)
    Component->>Template: Start Buffer & Include Template File
    Note over Template: Uses $this->propertyDto to print HTML
    Template-->>Component: Return captured HTML output
    Component->>Component: Wrap HTML with inner/outer divs & attributes
    Component->>Component: Find associated CSS & JS file paths
    Component->>ResultDTO: Create RenderedCompoDto
    Component->>ResultDTO: Set html, css list, js list
    ResultDTO-->>Component: Return populated DTO
    Component-->>System: Return RenderedCompoDto
```

## A Glimpse at the Code

Let's look at simplified snippets from the `Compo` base class which handles rendering via `apiGetRendered`.

**1. `apiGetRendered()` - The Main Entry Point**

```php
// Simplified from s45_base/classes/Site45/Compo/Compo.php
use Site45\DtoLib\Base\RenderedCompoDto;
use Site45\DtoLib\Base\CompoResVO;

class Compo {
  // ... properties like $id, $name, $dir, $settings_published ...

  public function apiGetRendered($props = NULL){
    // Check if published or if user has edit rights
    if($this->settings_published /* OR check user permission */){

      // Call beforeRender if it exists
      if(method_exists($this, 'beforeRender')){
        $this->beforeRender($props); // Prepare data
      }

      // Create the result container
      $renderedCompo = new RenderedCompoDto();
      $renderedCompo->id = $this->id;
      $renderedCompo->name = $this->name;

      // Get the final HTML (includes template execution and wrapping)
      $renderedCompo->html = $this->getCompoHtmlFull();

      // Get the CSS/JS resource list
      $renderedCompo->res = $this->getCompoRes();

      return $renderedCompo; // Return the packaged result
    }
    // Return empty DTO if not published/allowed
    return new RenderedCompoDto();
  }
  // ... other methods ...
}
```

*   This method orchestrates the process: check permissions, call `beforeRender`, get HTML, get resources, and package the result in a `RenderedCompoDto`.

**2. `getCompoHtml()` - Executing the Template**

```php
// Simplified from s45_base/classes/Site45/Compo/Compo.php
class Compo {
  // ... properties ...

  protected function getCompoHtml() {
    // Construct the template file path
    $templateFilePath = $this->dir.'/'.$this->name.'.tpl.php';

    // Check if template file exists (simplified check)
    if(!file_exists($templateFilePath)){
      return '<div style="color:red">Template not found: '.$templateFilePath.'</div>';
    }

    // --- The core template execution ---
    ob_start(); // Start capturing output
    include $templateFilePath; // Execute the template's PHP code
                               // Inside template, $this refers to the Compo object
    $html = ob_get_clean(); // Get the captured HTML
    // --- End of core execution ---

    // Remove potential <component> tags (legacy?)
    $html = str_replace('<component>', '', $html);
    $html = str_replace('</component>', '', $html);

    // Wrap the template's output in the *inner* component div
    // $this->attr_Compo is prepared earlier by setAttributes()
    $compoHtml = '<div '.drupal_attributes($this->attr_Compo).'>'; // drupal_attributes formats array as HTML attributes
    $compoHtml .= $html;
    $compoHtml .= '</div>';

    return $compoHtml;
  }
  // ... other methods like getCompoHtmlFull, setAttributes ...
}
```

*   This shows the crucial part: using `ob_start()`, `include`, and `ob_get_clean()` to run the `.tpl.php` file and capture its output. It then wraps this output in an inner `div`. The full HTML (with the outer wrapper) is assembled in `getCompoHtmlFull()`.

**3. `getCompoRes()` - Finding CSS/JS Files**

```php
// Simplified from s45_base/classes/Site45/Compo/Compo.php
use Site45\DtoLib\Base\CompoResVO;

class Compo {
  // ... properties ...

  protected function getCompoRes(){
    // Create the resource container VO
    $compoRes = new CompoResVO();

    // Add base component resources (always needed)
    $compoRes->js[] = S45_COMPO_DIR.'/Compo.js'; // Path constant
    $compoRes->css[] = S45_COMPO_DIR.'/Compo.css';

    // Check if component-specific JS file exists
    $compoJSPath = $this->dir.'/'.$this->name.'.js';
    if(file_exists($compoJSPath)){
      $compoRes->js[] = $compoJSPath;
    }

    // Check if component-specific CSS file exists
    $compoCssPath = $this->dir.'/'.$this->name.'.css';
    if(file_exists($compoCssPath)){
      $compoRes->css[] = $compoCssPath;
    }

    return $compoRes; // Return the VO with lists of files
  }
  // ... other methods ...
}
```

*   This method simply checks for the existence of the component's `.css` and `.js` files and adds their paths (along with base paths) to a `CompoResVO` object.

## Connecting to Other Parts

*   **Page Assembly:** The main part of the `__s45` system that builds the final HTML page uses the renderer (`apiGetRendered`) for each component placed on that page. It collects the HTML snippets and concatenates them, and it aggregates all the unique CSS and JS file paths to include them correctly in the `<head>` or at the end of the `<body>`.
*   [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md): The renderer takes a component object as its primary input.
*   [Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md): The rendering process produces a `RenderedCompoDto` containing the results, which itself uses a `CompoResVO` for the resource lists. The component being rendered often uses DTOs/VOs (like `PropertyDto`, `LangVO`) internally to access its data within the `beforeRender` method or the `.tpl.php` template.

## Conclusion

You've now learned about Component Rendering in `__s45`, primarily handled by the `Compo::apiGetRendered` method (or the similar `Render452` class)!

*   It solves the problem of transforming a **Component object** (with its logic and data) into the final **HTML code** seen by the user.
*   Think of it as a **stage manager** or **director** assembling the scene.
*   It executes the component's **`.tpl.php` template**, making the component's data available via `$this`.
*   It **wraps** the resulting HTML with standard `div` tags containing important attributes (`s45-compo-id`, etc.).
*   It identifies the necessary **CSS and JavaScript files** for the component.
*   It returns all this information packaged in a **`RenderedCompoDto`**.

We've seen how components get their data (via DTOs) and how they are turned into HTML (via rendering). But where does the data originally come from, especially persistent data like property listings or user accounts? How is it saved and loaded? That leads us to the concept of Repositories.

Let's move on to [Chapter 6: Repository (`Repo`)](06_repository___repo__.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 6: Repository (`Repo`)

Welcome back! In [Chapter 5: Component Rendering (`Render452`)](05_component_rendering___render452___.md), we saw how the system takes a [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md) object and its data (often in [DTOs/VOs](04_data_transfer_object__dto____value_object__vo__.md)) to generate the final HTML. But where does the persistent data *for* those components actually come from? If you customize a component's settings on a page, where are those settings saved so they appear the same way next time?

## What Problem Do Repositories Solve?

Imagine you have a library full of books (your application's data: user settings, component configurations, product details, blog posts, etc.). You need a system to:

1.  **Find** a specific book when you need it (load data).
2.  **Store** new books or updated versions of existing books (save data).
3.  **Organize** the books so they are easy to find and manage.

Without a system, you might end up with books scattered everywhere, making it hard to find what you need or put things back in the right place. In software, this translates to having database queries, file reading/writing code, or calls to external services sprinkled throughout your application. This makes the code messy, hard to change (what if you switch from JSON files to a database?), and prone to errors.

**Use Case:** Let's say an administrator uses the website's backend to change the title and display settings of a specific "Featured Properties" component on the homepage. How does the system save these new settings? And later, when a visitor views the homepage, how does the system load these specific settings for that component?

This is where **Repositories** (`Repo` in `__s45`) come in.

## Meet Repositories: Your Data Librarians

Think of Repositories as specialized librarians. Each type of repository is responsible for managing a specific kind of "book" (data object). Their main job is to **abstract away the details of *how* and *where* the data is stored**.

When your code needs data (e.g., the configuration for the "Featured Properties" component), it asks the appropriate Repository (the librarian). The Repository knows whether to look for that data in a database, a JSON file, an [Event Store](07_event_sourcing___ar____eventstore____eventquery__.md), or somewhere else entirely. Similarly, when your code wants to save data, it gives the data to the Repository, and the Repository handles the actual saving process.

The code *using* the Repository doesn't need to know the storage details – it just interacts with the librarian!

In `__s45`, you'll mainly encounter two types:

1.  **`CompoRepo`:** The librarian for [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md) data.
    *   Its primary job is to load the configuration (settings, content) for a specific component instance.
    *   It mainly interacts with the [Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md) system. It loads the latest state of a component by reading its history of changes (events) from the `EventStore`.
    *   It saves changes to a component by adding a new event (like "ComponentSaved") to the `EventStore`.
    *   It *might* use a JSON file (`_repo/Compo.s45.json`) as a fallback or cache, but the Event Store is the primary source of truth for component state in the provided code.

2.  **`JsonRepo`:** A general-purpose librarian for reading and writing data directly to JSON files.
    *   It's a simple helper for managing data stored in `.json` format.
    *   Often used for simpler data structures like:
        *   Cached translations (as seen in `YandexTranslator`).
        *   Site settings or configurations not managed elsewhere.
        *   Internal lookup lists (like the `CompoInfo.s45.json` file used by `CompoRepo` itself to find component code locations).

## Using `CompoRepo`: Managing Component Data

Let's revisit our use case: loading and saving the settings for the "Featured Properties" component (let's say its ID is `featured_properties_hp`).

**Example 1: Loading Component Configuration**

When the homepage needs to display the "Featured Properties" component, the system uses `CompoRepo` to load its specific configuration.

```php
<?php
// Make sure CompoRepo and the Component class are available
use Site45\Base\CompoRepo;
use Site45\Compo\Compo; // Base class for components

// The unique ID of the component instance we want to load
$componentId = 'featured_properties_hp';

// Ask the CompoRepo librarian to load the component
/** @var Compo $featuredProperties */ // Type hint: we expect a Compo object
$featuredProperties = CompoRepo::load($componentId);

// Check if loading was successful
if ($featuredProperties) {
  echo "Loaded component: " . $featuredProperties->name . "\n"; // Should be 'FeaturedProperties' (class name)
  echo "Component Instance ID: " . $featuredProperties->id . "\n"; // Should be 'featured_properties_hp'
  echo "Config source: " . $featuredProperties->confFrom . "\n"; // Tells us where data came from ('event', 'repo', 'class')

  // Now the component object $featuredProperties contains all its
  // loaded settings (e.g., $featuredProperties->settings_title)
  // and is ready to be rendered.
} else {
  echo "Could not load component with ID: " . $componentId;
}

// --- Example Output (if loaded from Event Store) ---
// Loaded component: FeaturedProperties
// Component Instance ID: featured_properties_hp
// Config source: event
?>
```

**Explanation:**
*   `CompoRepo::load('featured_properties_hp')` tells the repository to find and reconstruct the component instance with the ID `featured_properties_hp`.
*   `CompoRepo` looks for this component's data, primarily by querying the [Event Store](07_event_sourcing___ar____eventstore____eventquery__.md). It might check a cache or a fallback JSON file too.
*   If found, it creates an instance of the correct component class (e.g., `FeaturedProperties`) and fills it with the loaded configuration data.
*   It returns the fully populated component object, ready for rendering. The `$featuredProperties->confFrom` property tells us where the data was ultimately loaded from.

**Example 2: Saving Component Configuration**

After the administrator changes the component's title and saves the form, the system uses `CompoRepo` to persist these changes.

```php
<?php
// Make sure CompoRepo and EventStore are available
use Site45\Base\CompoRepo;
use Site45\Event\EventStore; // CompoRepo uses EventStore for saving

// Assume $componentToSave is our 'FeaturedProperties' component object
// after the administrator updated its properties (e.g., $componentToSave->settings_title = "Top Villas")

// Ask the CompoRepo librarian to save the component's state
CompoRepo::save($componentToSave);

echo "Component configuration for '{$componentToSave->id}' has been saved.";

// --- What happens ---
// CompoRepo::save() takes the $componentToSave object.
// It cleans the data, removing temporary runtime properties.
// It then calls EventStore::create()->addEvent(...) to record a 'Saved' event.
// This event contains the cleaned configuration data.
// The Event Store handles permanently storing this event.
?>
```

**Explanation:**
*   `CompoRepo::save($componentToSave)` takes the component object containing the new settings.
*   Crucially, in the provided `CompoRepo.php` code, `save` **does not** directly write to a JSON file. Instead, it prepares the relevant configuration data and tells the [Event Store (`EventStore`)](07_event_sourcing___ar____eventstore____eventquery__.md) to record a `Saved` event for this component.
*   This event-based approach means we store the *history* of changes rather than just overwriting the previous state. The `CompoRepo::load` method then reconstructs the latest state from this history.

## Using `JsonRepo`: Managing Simple JSON Data

`JsonRepo` is much simpler. It reads and writes directly to specified JSON files.

**Example 3: Loading Cached Data (e.g., Translations)**

Imagine a simple cache for translated strings stored in `_repo/Translations.s45.json`.

```php
<?php
use Site45\Base\JsonRepo;
use Site45\Base\SiteConf; // To get the site path

// Define the path to the JSON file for the current site
$siteId = SiteConf::getId();
$filePath = S45_SITES_DIR.'/'.$siteId.'/_repo/Translations.s45.json'; // S45_SITES_DIR is a constant path

// The unique key (ID) for the data we want within the JSON file
$translationKey = 'welcome_message_hash'; // e.g., an md5 hash of the original text

// Ask JsonRepo to load the data associated with this key
$translationData = JsonRepo::load($translationKey, $filePath);

if ($translationData) {
  echo "Loaded translation data for key '{$translationKey}':\n";
  print_r($translationData); // Shows the object/array stored under that key
} else {
  echo "No translation data found for key '{$translationKey}' in {$filePath}.";
}

// --- Example Output (if data exists in Translations.s45.json) ---
// Loaded translation data for key 'welcome_message_hash':
// stdClass Object
// (
//     [id] => welcome_message_hash
//     [ru] => Добро пожаловать
//     [en] => Welcome
//     [th] => ยินดีต้อนรับ
// )
?>
```

**Explanation:**
*   `JsonRepo::load($key, $filePath)` attempts to read the JSON file at `$filePath`.
*   It decodes the JSON into a PHP data structure (usually an object).
*   It looks for a top-level property named after `$key` (e.g., `welcome_message_hash`).
*   If found, it returns the value associated with that key (which could be a string, number, array, or another object). If not found, or the file doesn't exist, it returns `NULL`.

**Example 4: Saving Data to JSON**

After translating a new phrase, we can save it to the cache using `JsonRepo`.

```php
<?php
use Site45\Base\JsonRepo;
use Site45\Base\SiteConf;

// Path to the JSON file
$siteId = SiteConf::getId();
$filePath = S45_SITES_DIR.'/'.$siteId.'/_repo/Translations.s45.json';

// Prepare the data object to save
// IMPORTANT: The object MUST have an 'id' property matching the key!
$newTranslation = new \stdClass();
$newTranslation->id = 'thank_you_hash'; // The key it will be saved under
$newTranslation->ru = 'Спасибо';
$newTranslation->en = 'Thank You';
$newTranslation->th = 'ขอบคุณ';

// Ask JsonRepo to save the object to the file
$success = JsonRepo::save($newTranslation, $filePath);

if ($success) {
  echo "Saved translation data for key '{$newTranslation->id}' to {$filePath}.";
} else {
  echo "Failed to save translation data.";
}

// --- What happens ---
// JsonRepo::save() reads the entire Translations.s45.json file (if it exists).
// It adds or updates the entry with the key 'thank_you_hash'.
// It then writes the entire updated data structure back to Translations.s45.json.
?>
```

**Explanation:**
*   `JsonRepo::save($object, $filePath)` takes a data object (which *must* have an `id` property) and the file path.
*   It reads the existing JSON file (or starts with an empty structure).
*   It adds or replaces the entry whose key matches `$object->id` with the `$object` itself.
*   It then overwrites the JSON file with the updated data structure, nicely formatted.

## How `CompoRepo` Works Under the Hood

Let's look closer at `CompoRepo::load($id)`. It prioritizes efficiency and uses the Event Store as the primary source.

**Step-by-Step:**

1.  **Get Site Context:** Determines the current `siteId` using [SiteConf](01_configuration___siteconf___.md).
2.  **Check Global Cache:** Looks for `$GLOBALS['AllCompoData']`. `CompoRepo` tries to load *all* component data from the Event Store *once* per page request for efficiency. If this cache exists and contains the requested `$id`, it uses the cached data (Step 7).
3.  **Load All from Events (if not cached):** If the global cache isn't populated yet, it performs a query using `EventQuery` on the [Event Store (`EventStore`)](07_event_sourcing___ar____eventstore____eventquery__.md). This query asks for the *latest event payload* for *all* components (`arName`='Compo').
4.  **Populate Global Cache:** The results from the Event Store query are stored in `$GLOBALS['AllCompoData']`, keyed by component ID. Each entry is marked with `confFrom = 'event'`.
5.  **Check Cache Again:** Now that the cache is populated (or was already), it checks again for the specific `$id`. If found, uses this data (Step 7).
6.  **Fallback to JSON Repo:** If the component data wasn't found via events (maybe it's an older component never saved via events, or its data only exists in the JSON file), it tries `JsonRepo::load($id, $repoPath)`, where `$repoPath` points to `_repo/Compo.s45.json`. If found, it uses this data, marks it `confFrom = 'repo'`, and adds it to the cache.
7.  **Default (Class):** If data wasn't found in events or the JSON repo, it assumes the component should be created with its default settings defined in its class. It prepares minimal data (`id`, `name`) and marks it `confFrom = 'class'`.
8.  **Find Component Class:** It looks up the component's file location using another JSON file (`_repo/CompoInfo.s45.json`), which is loaded via `JsonRepo::open`. This file maps component names (like `FeaturedProperties`) to their directory paths.
9.  **Instantiate & Populate:** It includes the component's PHP file (`require_once`) and creates a new instance (`new $compoName()`). It then uses the `Creator::create($compo, $compoData)` utility (similar to how DTOs are created) to populate the component object with the data found in step 5, 6, or 7.
10. **Return:** Returns the populated component object.

**Sequence Diagram (`CompoRepo::load`)**

```mermaid
sequenceDiagram
    participant YourCode
    participant CompoRepo
    participant GlobalCache as $GLOBALS['AllCompoData']
    participant EventQuery
    participant JsonRepo
    participant CompoClass as Component PHP Class
    participant Creator

    YourCode->>CompoRepo: load('comp_id')
    CompoRepo->>GlobalCache: Check if 'comp_id' exists?
    alt Cache Miss (First Load)
        GlobalCache-->>CompoRepo: No
        CompoRepo->>EventQuery: Get latest state for ALL 'Compo'
        EventQuery-->>CompoRepo: Return list of component states
        CompoRepo->>GlobalCache: Populate cache with states (confFrom='event')
    end
    CompoRepo->>GlobalCache: Check again for 'comp_id'
    alt Found in Cache (from Events)
      GlobalCache-->>CompoRepo: Yes, return stateData (confFrom='event')
    else Not Found in Events
      GlobalCache-->>CompoRepo: No
      CompoRepo->>JsonRepo: load('comp_id', 'Compo.s45.json')
      alt Found in JSON Repo
        JsonRepo-->>CompoRepo: Yes, return stateData
        Note over CompoRepo: Set confFrom='repo', update cache
      else Not Found in JSON (Use Defaults)
        JsonRepo-->>CompoRepo: No (NULL)
        Note over CompoRepo: Create default data (confFrom='class')
      end
    end
    Note over CompoRepo: Find component class file path (using CompoInfo.s45.json)
    CompoRepo->>CompoClass: Instantiate new Component()
    CompoRepo->>Creator: Populate component instance with stateData
    Creator-->>CompoRepo: Populated component object
    CompoRepo-->>YourCode: Return component object
```

**Code Snippet (`CompoRepo::load` - Simplified)**

```php
// Simplified from s45_base/classes/Site45/Base/CompoRepo.php
use Site45\Base\JsonRepo;
use Site45\Event\EventQuery;
use Site45\Base\Creator;

class CompoRepo implements RepoInterface {

  public static function load($id) {
    $repo = new static(); // Gets siteId, repoPath

    // Load from Event Store ONCE per request into cache
    if (!isset($GLOBALS['AllCompoData'])) {
      $GLOBALS['AllCompoData'] = $repo->loadAllCompoDataFromEvents(); // Uses EventQuery
    }

    // 1. Check Cache (populated by events)
    if (isset($GLOBALS['AllCompoData'][$id])) {
      $compoData = $GLOBALS['AllCompoData'][$id];
      $compoName = $compoData->name;
    // 2. Fallback: Check JSON Repo file
    } elseif ($compoData = JsonRepo::load($id, $repo->repoPath)) {
      $compoName = $compoData->name;
      $compoData->confFrom = 'repo';
      $GLOBALS['AllCompoData'][$id] = $compoData; // Cache it
    // 3. Default: Use class defaults
    } else {
      $compoData = new \stdClass();
      $compoData->confFrom = 'class';
      $compoData->id = $id;
      $compoData->name = $id; // Assume ID is class name if not found
      $compoName = $id;
      $GLOBALS['AllCompoData'][$id] = $compoData; // Cache minimal data
    }

    // Create the component instance and populate it
    $compo = $repo->compoCreate($compoName, $compoData); // Finds class, requires, instantiates

    return $compo;
  }

  protected function loadAllCompoDataFromEvents() {
    // Uses EventQuery::create([...])->exec() to get latest state for all Components
    // Returns array keyed by component ID, marks confFrom = 'event'
    // ... (Implementation details skipped) ...
    $allCompoData = [];
    $res = EventQuery::create(['arName' => 'Compo', 'isLast' => 1, /*...*/])->exec();
    if($res->rows){ /* loop and fill $allCompoData */ }
    return $allCompoData;
  }

  protected function compoCreate($compoName, $compoData) {
    // 1. Load CompoInfo.s45.json using JsonRepo::open to find component file path
    // 2. require_once $compoClassPath;
    // 3. $compo = new $compoName();
    // 4. Creator::create($compo, $compoData); // Populate with loaded data
    // 5. Set runtime variables like $compo->dir
    // ... (Implementation details skipped) ...
    $compoInfoList = JsonRepo::open(/* ... path to CompoInfo.s45.json ... */);
    $compoClassPath = $compoInfoList->{$compoName}->dir.'/'.$compoName.'.php';
    require_once $compoClassPath;
    $compo = new $compoName();
    Creator::create($compo, $compoData);
    $compo->dir = $compoInfoList->{$compoName}->dir;
    // ... more setup ...
    return $compo;
  }
  // ... save(), delete() methods ...
}
```

**How `CompoRepo::save` Works**

1.  **Get Input:** Receives the `$compo` object.
2.  **Clean Data:** Creates a plain data copy (`json_decode(json_encode($compo))`). It then iterates through the properties, keeping only core fields (`id`, `name`, `type`, `children`, etc.) and configuration fields (`settings_*`, `content_*`). Runtime variables (`dir`, etc.) are removed.
3.  **Timestamps:** Updates `changed` timestamp, sets `created` if not already set.
4.  **Send to Event Store:** Calls `EventStore::create()->addEvent('Compo', $compoData->id, 'Saved', $compoData)`. It passes the Aggregate Root type ('Compo'), the component's ID, the event name ('Saved'), and the cleaned `$compoData` as the payload.
5.  **Event Store Handles Persistence:** The `EventStore` takes care of actually saving this event record to the database (or wherever events are stored).

**Code Snippet (`CompoRepo::save` - Simplified)**

```php
// Simplified from s45_base/classes/Site45/Base/CompoRepo.php
use Site45\Event\EventStore;

class CompoRepo implements RepoInterface {
  // ... load() ...

  public static function save($compo) {
    // Make a plain data copy to avoid modifying the original object
    $compoData = json_decode(json_encode($compo));

    // Update timestamps
    $compoData->changed = time();
    $compoData->created = isset($compoData->created) ? $compoData->created : time();

    // Define fields to keep (core + config prefixes)
    $fieldsForSave = ['type', 'id', /*...*/ 'children', 'created', 'changed'];

    // Clean the data: Remove runtime variables
    foreach ($compoData as $paramName => $paramValue) {
      if (in_array($paramName, $fieldsForSave)) continue;
      if (strpos($paramName, 'settings_') === 0) continue;
      if (strpos($paramName, 'content_') === 0) continue;
      if (strpos($paramName, 'attr_') === 0) continue; // Keep attributes too

      // Remove anything else
      unset($compoData->{$paramName});
    }

    // *** Send 'Saved' event to the Event Store ***
    EventStore::create()->addEvent('Compo', $compoData->id, 'Saved', $compoData);

    // Note: No direct call to JsonRepo::save() here!
  }
  // ... delete() ...
}
```

## How `JsonRepo` Works Under the Hood

`JsonRepo` is straightforward file manipulation.

*   **`load($objectId, $filePath)`:** Calls `open($filePath)`. If successful and the returned data has a property named `$objectId`, it returns that property's value.
*   **`open($filePath)`:** Checks if the file exists. If yes, reads the entire file content (`file_get_contents`), decodes it from JSON (`json_decode`), and returns the result. Handles errors gracefully.
*   **`save($object, $filePath)`:** Ensures `$object->id` exists. Calls `open($filePath)` to get the current data (or an empty object). Sets `$repo->{$object->id} = $object;`. Calls `saveRepo($repo, $filePath)` to write the updated structure back.
*   **`saveRepo($repo, $filePath)`:** Encodes the `$repo` data structure back into a JSON string (`json_encode` with pretty printing). Opens the `$filePath` for writing (`fopen 'w'`), writes the JSON string (`fwrite`), and closes the file (`fclose`).

**Code Snippets (`JsonRepo` - Simplified)**

```php
// Simplified from s45_base/classes/Site45/Base/JsonRepo.php

class JsonRepo {

  public static function load($objectId, $filePath) {
    // Open reads the whole file and decodes JSON
    if (($repo = self::open($filePath)) && isset($repo->{$objectId})) {
      // Return the specific object requested
      $repo->{$objectId}->id = $objectId; // Ensure ID is set
      return $repo->{$objectId};
    }
    return NULL; // Not found or file error
  }

  public static function open($filePath) {
    if (file_exists($filePath)) {
      $json = file_get_contents($filePath);
      return json_decode($json); // Return PHP object/array
    }
    // Handle file not found (e.g., log error for admin)
    return FALSE;
  }

  public static function save($object, $filePath) {
    // Ensure object has an ID to use as the key
    if (!isset($object->id)) {
      // Log error: Object needs an ID to be saved
      return FALSE;
    }

    // Load existing repo data or start fresh
    $repo = self::open($filePath);
    if (!$repo) {
      $repo = new \stdClass(); // Start with empty object if file didn't exist
    }

    // Add or update the object in the repo using its ID as the key
    $repo->{$object->id} = $object;

    // Write the entire updated repo structure back to the file
    self::saveRepo($repo, $filePath);
    return TRUE;
  }

  public static function saveRepo($repo, $filePath) {
    // Encode back to JSON, nicely formatted
    $json = json_encode($repo, JSON_PRETTY_PRINT|JSON_UNESCAPED_UNICODE|JSON_UNESCAPED_SLASHES);

    // Write the JSON string to the file, overwriting previous content
    $fp = fopen($filePath, 'w');
    fwrite($fp, $json);
    fclose($fp);
  }
}
```

## Connecting to Other Parts

Repositories are central hubs for data access:

*   [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md): `CompoRepo` is specifically designed to load and save the persistent state (configuration) of components.
*   [Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md): `CompoRepo` relies heavily on `EventQuery` to load component state and `EventStore` to save changes by recording events. This is a fundamental part of how component data is managed.
*   [Configuration (`SiteConf`)](01_configuration___siteconf___.md): Repositories often need the current `siteId` from `SiteConf` to construct the correct path to site-specific storage directories (e.g., `S45_SITES_DIR.'/'.$siteId.'/_repo/'`).
*   [Query (`Query`, `QueryFromEvents`, `JsonQuery`)](08_query___query____queryfromevents____jsonquery__.md): While Repositories typically load/save single objects by ID, Queries are used for fetching lists of objects based on criteria. Sometimes, a Query might use a Repository internally, or vice versa. `CompoRepo` uses `EventQuery` internally.
*   [Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md): Although `CompoRepo` works directly with `Compo` objects, other repositories (or `JsonRepo`) might load/save data directly as DTOs or VOs.

## Conclusion

You've now learned about Repositories (`Repo`), your application's data librarians!

*   They solve the problem of **centralizing data access** and **abstracting storage details**.
*   Your code interacts with the Repository (the librarian) instead of directly talking to databases or files.
*   **`CompoRepo`** manages [Component](03_component___compo_____compo452___.md) configuration, primarily using the [Event Store](07_event_sourcing___ar____eventstore____eventquery__.md) for loading (`EventQuery`) and saving (`EventStore`).
*   **`JsonRepo`** is a simple helper for reading/writing data directly to **JSON files**.

We saw that `CompoRepo` doesn't just save the latest state; it saves *events* representing changes. This leads us directly to our next topic: understanding the powerful pattern of Event Sourcing that `CompoRepo` relies upon.

Let's dive into [Chapter 7: Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 7: Event Sourcing (`AR`, `EventStore`, `EventQuery`)

Welcome back! In [Chapter 6: Repository (`Repo`)](06_repository___repo__.md), we saw how `CompoRepo` is responsible for loading and saving component configurations. We noticed something interesting: `CompoRepo::save` doesn't just overwrite the old data. Instead, it uses something called the `EventStore` to record a `Saved` event. This hints at a powerful pattern called **Event Sourcing**.

## What Problem Does Event Sourcing Solve?

Imagine you're keeping track of inventory in a small shop. You could just keep a single sheet of paper showing the *current* number of items for each product. But what happens if you discover a mistake? How do you know *when* you received new stock or *when* items were sold?

A better approach is to keep a **logbook** (or a ledger in accounting). Instead of just the current number, you record *every single change*:
*   "Received 10 apples"
*   "Sold 2 apples"
*   "Found 1 extra apple during stock check"
*   "Sold 3 apples"

With this logbook, you not only know the current count (by replaying all the entries), but you also have a complete history of *how* you got there. You can audit the changes, understand trends, and even fix mistakes by adding correcting entries.

Event Sourcing applies this logbook idea to your application's data. Instead of storing just the current state of an important entity (like a Component or a Property), we store a **sequence of events** that describe every change made to that entity over time.

**Use Case:** Remember our "Featured Properties" component from Chapter 6? Let's say an admin changed its title, then later changed which properties it displays. Using traditional methods, we'd only know the *final* title and list. With Event Sourcing, we want to know:
*   When was the component first created?
*   When was the title changed, and what was the old title?
*   When was the list of properties updated?
*   Who made these changes?

This allows us to build a full audit trail and even potentially revert the component back to a previous state.

## Meet the Key Players: `AR`, `EventStore`, `EventQuery`

Event Sourcing in `__s45` involves three main concepts:

1.  **Aggregate Root (`AR`)**:
    *   Think of this as the **important entity** itself, the main character whose life story we are tracking. In our examples, a `Component` instance is an Aggregate Root. Other examples could be a `Property`, a `User`, or an `Order`.
    *   The `AR` class (`Site45\Event\AR`) is a base class that these entities can inherit from.
    *   An `AR` object knows how to determine its *current state* by looking at its history of events. It can also trigger new events when changes occur (like saving or deleting).

2.  **`EventStore`**:
    *   This is the **logbook writer**. Its sole responsibility is to **add new events** to the history (the "event stream") of a specific Aggregate Root.
    *   When `CompoRepo::save` is called, it ultimately tells the `EventStore` to add a `Saved` event for that component.
    *   The `EventStore` ensures events are stored reliably and in order, usually in a dedicated database table (`_s45_events`). Think of it as appending entries to the log.

3.  **`EventQuery`**:
    *   This is the **logbook reader**. Its job is to **retrieve events** from the `EventStore`.
    *   You can use `EventQuery` to ask for:
        *   All events for a specific Aggregate Root (`AR`).
        *   Only the *latest* event for an `AR`.
        *   Events of a specific type (e.g., only `Deleted` events).
        *   Events within a certain time range.
    *   `CompoRepo::load` uses `EventQuery` to find the latest `Saved` event for a component to reconstruct its current state.

**What is an "Event"?**
An event is simply a data record representing something significant that **has happened** in the past.
*   It's named in the past tense (e.g., `ComponentSaved`, `PropertyListed`, `UserRegistered`, `OrderShipped`).
*   It contains data relevant to that specific occurrence (e.g., the `ComponentSaved` event contains the component's configuration *at the time it was saved*).
*   Events are **immutable**: once recorded in the `EventStore`, they are never changed or deleted (though you can add *new* events to correct mistakes or supersede old ones).

## How Event Sourcing Solves the Use Case

Let's trace how saving and loading our "Featured Properties" component (`featured_properties_hp`) works with Event Sourcing:

**Saving Changes:**

1.  Admin changes the component's title and clicks "Save".
2.  The system updates the `$componentToSave` object in memory.
3.  Code calls `CompoRepo::save($componentToSave)`.
4.  `CompoRepo::save` prepares the component's data (removing temporary properties).
5.  `CompoRepo::save` calls `EventStore::create()->addEvent('Compo', 'featured_properties_hp', 'Saved', $componentData, 'Admin updated title')`.
6.  `EventStore` takes this information and writes a new row to the `_s45_events` database table. This row contains the component's ID, the event name ('Saved'), the timestamp, the user who made the change, the full component data (`$componentData`) at that moment, and the optional note.

Now, the `_s45_events` table contains a new entry representing this specific save action.

**Loading the Component:**

1.  A visitor requests the homepage.
2.  The system needs the 'Featured Properties' component. It calls `CompoRepo::load('featured_properties_hp')`.
3.  `CompoRepo::load` needs the latest state. It uses `EventQuery` (often via its internal `loadAllCompoDataFromEvents` method which queries for the latest events for all components).
4.  `EventQuery` looks into the `_s45_events` table and finds the most recent event for `arName='Compo'` and `arId='featured_properties_hp'`. Let's say it finds the `Saved` event we just added.
5.  `EventQuery` returns this event's data (specifically the `$componentData` stored in the `payload` field) back to `CompoRepo`.
6.  `CompoRepo` takes this data (which represents the latest saved state) and uses it to create and populate the `FeaturedProperties` component object.
7.  The fully loaded component object is returned, ready for rendering.

**Benefits:**

*   **Full Audit Trail:** The `_s45_events` table contains the entire history of changes for every component (and potentially other ARs). We can see who changed what, and when.
*   **Time Travel:** We could (with slightly more complex queries using `EventQuery`) reconstruct the state of the component as it was at *any point in the past* by replaying events up to that point.
*   **Debugging:** If a component is in a weird state, we can examine its event history to understand how it got there.

## Using the Abstractions

Let's look at how these pieces interact.

**Saving an Event (`EventStore`)**

You typically don't call `EventStore` directly for standard operations like saving a component. High-level classes like `CompoRepo` or the `AR` base class handle this. But understanding what they do is key.

```php
<?php
// Inside CompoRepo::save($componentToSave) or AR::save()

use Site45\Event\EventStore;

// $arName = 'Compo'; // The type of Aggregate Root
// $arId = $componentToSave->id; // The specific instance ID
// $eventName = 'Saved'; // The event that occurred
// $payload = $cleanedComponentData; // The state at the time of the event
// $note = 'User description of change'; // Optional context

// Tell the EventStore to record this event
EventStore::create()->addEvent($arName, $arId, $eventName, $payload, $note);

// --- What happens ---
// EventStore writes a new record to the _s45_events table containing
// all this information. It also ensures the 'isLast' flag is set correctly.
?>
```

**Querying Events (`EventQuery`)**

`CompoRepo` uses `EventQuery` internally, but you can also use it directly to explore the event history.

**Example 1: Getting the Latest Event for an AR**

This is what `CompoRepo::load` effectively does to get the current state.

```php
<?php
use Site45\Event\EventQuery;
use Site45\Event\EventDto; // The result type

$arName = 'Compo';
$arId = 'featured_properties_hp';

// Ask EventQuery for the single most recent event for this component
/** @var EventDto $latestEvent */
$latestEvent = EventQuery::create()->getLastEvent($arName, $arId);

if ($latestEvent && $latestEvent->name !== 'Deleted') {
  echo "Latest event ID: " . $latestEvent->id . "\n";
  echo "Event Name: " . $latestEvent->name . "\n"; // e.g., 'Saved'
  echo "Saved State (Payload): \n";
  print_r($latestEvent->payload); // This is the data CompoRepo uses
} elseif ($latestEvent && $latestEvent->name === 'Deleted') {
  echo "Component '{$arId}' was deleted.";
} else {
  echo "No events found for component '{$arId}'.";
}

// --- Example Output ---
// Latest event ID: 54321
// Event Name: Saved
// Saved State (Payload):
// stdClass Object
// (
//     [id] => featured_properties_hp
//     [name] => FeaturedProperties
//     [settings_title] => Top Villas
//     [settings_propertyIds] => Array (...)
//     ... more properties ...
// )
?>
```

**Example 2: Getting All Events for an AR**

Let's see the full history.

```php
<?php
use Site45\Event\EventQuery;
use Site45\DtoLib\Base\SearchResultVO;

$arName = 'Compo';
$arId = 'featured_properties_hp';

// Configure the query
$query = EventQuery::create([
  'arName' => $arName,
  'arId' => $arId,
  'sortBy' => 'id',     // Sort by event ID (chronological)
  'sortOrder' => 'ASC',
  'limit' => 100       // Get up to 100 events
]);

// Execute the query
/** @var SearchResultVO $result */
$result = $query->exec();

echo "Found {$result->total} events for component '{$arId}':\n";

/** @var \Site45\Event\EventDto $event */
foreach ($result->rows as $event) {
  echo "- Event ID: {$event->id}, Name: {$event->name}, Time: " . date('Y-m-d H:i', $event->created) . "\n";
}

// --- Example Output ---
// Found 3 events for component 'featured_properties_hp':
// - Event ID: 54301, Name: Saved, Time: 2023-10-26 10:00
// - Event ID: 54315, Name: Saved, Time: 2023-10-26 11:30
// - Event ID: 54321, Name: Saved, Time: 2023-10-27 09:15
?>
```

**Aggregate Root (`AR` Base Class)**

The `AR` class provides standard methods for interacting with the Event Store. Entities managed via Event Sourcing often inherit from this.

```php
<?php
namespace Site45\Event;

use Site45\Event\EventQuery;
use Site45\Event\EventStore;

class AR {
  public $arName; // Must be set by child class (e.g., 'Compo')
  public $id;
  public $created;
  public $changed;
  // ... other common properties ...

  // Factory method (simplified)
  public static function create($id = NULL){ /* ... */ }

  // Loads the LATEST state using EventQuery
  public static function load($id) {
    $ar = new static(); // Creates instance of child class
    // Get the very last event recorded for this AR instance
    if (($lastEvent = EventQuery::create()->getLastEvent($ar->arName, $id))
        && ($lastEvent->name <> 'Deleted')) {
      // Apply the data from the event's payload
      $ar->setProps($lastEvent->payload);
      $ar->changed = $lastEvent->created; // Set changed time from event
      return $ar;
    }
    return NULL; // Not found or was deleted
  }

  // Saves the current state by adding a 'Saved' event via EventStore
  public function save() {
    // ... checks for id and arName ...
    // Records a 'Saved' event with the current object state as payload
    EventStore::create()->addEvent($this->arName, $this->id, 'Saved', $this /*, $note */);
  }

  // Records a 'Deleted' event via EventStore
  public function delete($eventNote = NULL){
    // ... checks for id and arName ...
    EventStore::create()->addEvent($this->arName, $this->id, 'Deleted', $this, $eventNote);
  }

  // Helper to apply properties from an event payload (or other source)
  public function setProps($props){
    if (is_array($props) || is_object($props)) {
      $props = json_decode(json_encode($props)); // Ensure it's an object
      // ... (logic to copy properties onto $this) ...
    }
    return $this;
  }
}
?>
```

*   The `AR` base class conveniently wraps the common uses of `EventStore` (for `save`, `delete`) and `EventQuery` (for `load`).
*   Specific AR types (like a potential `PropertyAR` or the `CompoAR` mentioned in the files) would extend this base class and define their specific properties and `arName`.

## How It Works Under the Hood

**`EventStore::addEvent`**

1.  **Input:** Receives `$arName`, `$arId`, `$name`, `$payload`, `$note`.
2.  **Transaction:** Starts a database transaction to ensure atomicity.
3.  **Update `isLast` Flag:** Sets the `isLast` flag to `0` for all *existing* events belonging to this specific `$arId`. This marks the previous "latest" event as no longer being the latest.
4.  **Prepare Event Data:** Creates a new record for the `_s45_events` table, populating fields like `created` (timestamp), `authorId` (current user), `ip`, `siteId`, the input parameters (`arName`, `arId`, `name`), the serialized `$payload`, and the `$note`. Sets `isLast` to `1` for this new event.
5.  **Save to DB:** Inserts the new record into the `_s45_events` table.
6.  **Commit:** Commits the database transaction. If any step failed, it rolls back.

```mermaid
sequenceDiagram
    participant Caller as Caller (e.g., CompoRepo)
    participant EventStore
    participant DbTable as _s45_events Table

    Caller->>EventStore: addEvent('Compo', 'id123', 'Saved', {data...}, 'note')
    Note over EventStore, DbTable: Start DB Transaction
    EventStore->>DbTable: UPDATE _s45_events SET isLast = 0 WHERE arId = 'id123'
    DbTable-->>EventStore: OK
    EventStore->>EventStore: Prepare new event record (isLast=1, timestamp, payload, etc.)
    EventStore->>DbTable: INSERT INTO _s45_events VALUES (...)
    DbTable-->>EventStore: OK
    Note over EventStore, DbTable: Commit DB Transaction
    EventStore-->>Caller: Return TRUE (success)
```

**`EventQuery::exec` / `getLastEvent`**

1.  **Input:** Receives filter parameters (like `arName`, `arId`, `isLast`, `limit`, `sortBy`, etc.).
2.  **Build Query:** Constructs a SQL `SELECT` query for the `_s45_events` table based on the provided filters.
    *   Adds `WHERE` clauses for `arName`, `arId`, `isLast`, etc.
    *   Adds `ORDER BY` clause for sorting.
    *   Adds `LIMIT` and `OFFSET` for pagination.
3.  **Execute Query:** Runs the SQL query against the database.
4.  **(Optional) Count Total:** If requested (`countTotal=true`), runs a separate `COUNT(*)` query with the same filters to get the total number of matching events.
5.  **Format Results:** Iterates through the raw database rows returned by the `SELECT` query.
    *   For each row, creates an `EventDto` object.
    *   Populates the `EventDto` with data from the row (unserializing the `payload`).
    *   Adds the `EventDto` to the `rows` array of a `SearchResultVO`.
6.  **Return:** Returns the `SearchResultVO` containing the list of `EventDto` objects (`rows`), the total count (`total`), and the original filter (`filter`).
7.  **`getLastEvent`:** This is a helper method that simply calls `exec` with specific filters: `limit=1`, `sortOrder='DESC'`, and the provided `$arName` and `$arId`. It then returns the first (and only) event from the `rows` array, or `NULL` if none was found.

```mermaid
sequenceDiagram
    participant Caller as Caller (e.g., CompoRepo)
    participant EventQuery
    participant DbTable as _s45_events Table
    participant SearchResultVO as SearchResultVO

    Caller->>EventQuery: create({...filters...})->exec()
    Note over EventQuery: Build SQL SELECT query based on filters
    EventQuery->>DbTable: Execute SELECT ... FROM _s45_events WHERE ... ORDER BY ... LIMIT ...
    DbTable-->>EventQuery: Return matching DB rows
    EventQuery->>SearchResultVO: Create SearchResultVO
    loop For Each DB Row
        EventQuery->>EventQuery: Create EventDto, unserialize payload
        EventQuery->>SearchResultVO: Add EventDto to results.rows
    end
    EventQuery->>SearchResultVO: Set total count (if calculated)
    SearchResultVO-->>EventQuery: Populated SearchResultVO
    EventQuery-->>Caller: Return SearchResultVO
```

## Connecting to Other Parts

Event Sourcing is a core pattern, connecting strongly to:

*   [Repository (`Repo`)](06_repository___repo__.md): As we saw, `CompoRepo` is a prime example of a repository using Event Sourcing (`EventStore` for saving, `EventQuery` for loading). Other repositories for different Aggregate Roots might follow the same pattern.
*   **Database:** The `EventStore` relies on a database table (like `_s45_events` defined in `EventTable.php`) to persist the events.
*   [Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md): Events themselves are often represented as DTOs (`EventDto`). The `payload` within an event often contains a DTO representing the state of the AR at that time. `EventQuery` returns results packaged in a `SearchResultVO`.
*   [Query (`Query`, `QueryFromEvents`, `JsonQuery`)](08_query___query____queryfromevents____jsonquery___.md): The next chapter introduces `QueryFromEvents`, a specialized query type that *builds its results* by processing event streams, demonstrating a powerful application of Event Sourcing.

## Conclusion

You've learned about Event Sourcing, a powerful pattern for managing the history of your application's data!

*   It solves the problem of needing a **full audit trail** and understanding *how* data changed over time, not just its current state.
*   Think of it like a **detailed logbook** instead of just a final snapshot.
*   Key components are:
    *   **Aggregate Root (`AR`):** The entity whose history is tracked (e.g., `Component`).
    *   **`EventStore`:** The "logbook writer" - reliably adds new events.
    *   **`EventQuery`:** The "logbook reader" - retrieves events from the store.
*   Events are **immutable records** of things that happened (`ComponentSaved`, `ItemDeleted`).
*   This pattern allows for auditing, debugging, and potentially reconstructing past states.
*   In `__s45`, `CompoRepo` uses this pattern heavily, relying on `EventStore` and `EventQuery` behind the scenes.

Now that we understand how changes are recorded as events, how can we build complex views or summaries *from* these event histories? This leads us to different kinds of queries, including those specifically designed to work with event streams.

Let's move on to [Chapter 8: Query (`Query`, `QueryFromEvents`, `JsonQuery`)](08_query___query____queryfromevents____jsonquery___.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 8: Query (`Query`, `QueryFromEvents`, `JsonQuery`)

Welcome back! In [Chapter 7: Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md), we learned how `__s45` records the history of changes to things like Components using events. We saw how `EventQuery` can read this history, especially to find the *latest* state of a single item.

But what if we need more? What if we want to find a *list* of items that match certain criteria? For example, how do we find all properties currently listed "For Sale" in a specific district? Or how do we find all blog posts tagged with "Technology"?

## What Problem Do Queries Solve?

Imagine you're back in the library from [Chapter 6: Repository (`Repo`)](06_repository___repo__.md). The Repository librarian was great for finding *one specific book* if you knew its exact title or ID. But now, you have a different task: you need a list of *all* mystery novels published in the last year, or *all* books written by a particular author. The librarian who fetches individual books isn't quite equipped for this broader search.

You need a specialized **detective** or **researcher**. You give this researcher your specific clues (like "genre: mystery", "published date: after last year"), and they go through the relevant records (the library catalog, maybe even looking through specific shelves) to bring you back a list of all the books that match.

In `__s45`, **Queries** are these specialized detectives. Their job is to find and return *collections* of data based on specific search criteria (filters).

**Use Case:** Let's say on our real estate website, we need to display a list of all 3-bedroom properties currently available for sale in the "Laguna" area, sorted by price from lowest to highest. How do we find exactly those properties?

## Meet the Query Detectives: `Query`, `QueryFromEvents`, `JsonQuery`

Queries in `__s45` are classes designed to fetch data based on filters you provide. They handle the complexity of searching through the correct data source (like event histories or JSON files) and return the results in a structured way, usually as a list of [DTOs/VOs](04_data_transfer_object__dto____value_object__vo__.md) packaged inside a `SearchResultVO`.

There are a few main types:

1.  **`Query` (Base Concept):** This represents the general idea of a query. Specific query classes often inherit from a base `Query` class which provides common functionalities like handling pagination (`start`, `limit`).

2.  **`QueryFromEvents`:** This is a very powerful type of query detective! It specializes in building its results by reading the **event logbook** (the `EventStore` from [Chapter 7: Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md)).
    *   Instead of storing the final list of properties somewhere, it processes the stream of events (`PropertyListed`, `PropertySold`, `PriceChanged`, etc.) to build an up-to-date picture of which properties match the criteria.
    *   It's often used for complex data where the current state depends on a history of changes.
    *   Example: A `PropertySearchQuery` that finds available properties by processing `PropertyListed`, `PropertySold`, `PropertyDetailsUpdated` events.

3.  **`JsonQuery`:** This detective specializes in searching through data stored in **JSON files**.
    *   It reads a specific JSON file (often managed by [JsonRepo](06_repository___repo__.md)), filters the records within that file based on your criteria, and returns the matching ones.
    *   It's useful for simpler datasets or configurations stored directly in JSON.
    *   Example: A query to find all defined "Property Types" (like 'Villa', 'Condo', 'Land') if they are stored in a `PropertyTypes.json` file.

All these queries typically take filter parameters (like `propertyType`, `location`, `minPrice`), apply them to their respective data sources, and return a `SearchResultVO`.

**What is `SearchResultVO`?**
It's a standard [Value Object](04_data_transfer_object__dto____value_object__vo__.md) used to package the results of a query. It usually contains:
*   `rows`: An array containing the actual data items found (often as DTOs, e.g., an array of `PropertyDto` objects).
*   `total`: The total number of items that matched the query criteria (useful for showing "Displaying 1-10 of 55 properties").
*   `filter`: Often includes the original query parameters used for the search.

## Using Queries: Finding Our Properties

Let's solve our use case: finding all 3-bedroom properties for sale in "Laguna", sorted by price. We'll assume there's a `PropertySearchQuery` (likely extending `QueryFromEvents`) designed for this.

```php
<?php
// Make sure the necessary classes are available
use Site45\Sets\Phuket\Query\PhuketPropertyQuery; // Example Query class name
use Site45\DtoLib\Base\SearchResultVO;
use Site45\DtoLib\Property\PropertyDto; // We expect Property DTOs in the result

// 1. Define our search criteria (filters)
$filters = [
  'bedrooms' => 3,
  'dealType' => 'sale', // Looking for properties 'for sale'
  'district' => 'Laguna',
  'sortBy' => 'priceSale', // Field to sort by
  'sortOrder' => 'ASC',   // Sort direction (Ascending - lowest first)
  'start' => 0,           // Start from the first result (for pagination)
  'limit' => 10           // Get maximum 10 results per page
];

// 2. Create the Query object with our filters
// The ::create() method is common for setting up queries
/** @var PhuketPropertyQuery $propertyQuery */
$propertyQuery = PhuketPropertyQuery::create($filters);

// 3. Execute the query to get the results
/** @var SearchResultVO $searchResult */
$searchResult = $propertyQuery->exec();

// 4. Use the results
echo "Found a total of: " . $searchResult->total . " properties matching your criteria.\n";
echo "Displaying properties " . ($filters['start'] + 1) . " to " . ($filters['start'] + count($searchResult->rows)) . "\n\n";

if ($searchResult->rows) {
  echo "Matching Properties:\n";
  /** @var PropertyDto $property */
  foreach ($searchResult->rows as $property) {
    // Access data using the PropertyDto structure
    // s45_lang is a helper to get text in the current language
    echo "- ID: " . $property->id . ", Name: " . s45_lang($property->name) . ", Price: " . $property->priceSale->amount . "\n";
  }
} else {
  echo "No properties found matching your criteria.";
}

// --- Example Output ---
// Found a total of: 55 properties matching your criteria.
// Displaying properties 1 to 10
//
// Matching Properties:
// - ID: prop_456, Name: Laguna Garden Villa, Price: 450000
// - ID: prop_123, Name: Cozy Laguna Condo, Price: 480000
// - ID: prop_789, Name: Modern Laguna Pool House, Price: 520000
// ... (up to 10 properties) ...
?>
```

**Explanation:**

1.  **Define Filters:** We create an array `$filters` holding all our search clues (bedrooms, location, sorting, pagination).
2.  **Create Query:** We instantiate the appropriate query class (`PhuketPropertyQuery::create($filters)`), passing in our filters. The query object now knows what we're looking for.
3.  **Execute:** We call the `exec()` method on the query object. This is where the magic happens – the query detective goes to work! It processes its data source (events, JSON, etc.) based on the filters.
4.  **Process Results:** The `exec()` method returns a `SearchResultVO`. We can access the `total` count and loop through the `rows` array, which contains the actual `PropertyDto` objects matching our search.

This simple interface (`create()`, `exec()`) hides the complexity of how the data is actually found and processed.

## How `QueryFromEvents` Works Under the Hood

`QueryFromEvents` is sophisticated. It doesn't query the *entire* event log every single time you search. That would be too slow! Instead, it typically works like this:

1.  **Read Model Table:** There's usually a dedicated database table (let's call it `_query_property_search`) specifically designed to hold the *current, searchable state* of properties. This table is optimized for fast querying (unlike the event log, which is optimized for writing). Think of it as the detective's frequently updated case file or index cards.
2.  **Event Processing:** The `QueryFromEvents` system runs periodically (or sometimes triggered directly). It checks the main `_s45_events` table for *new* events that have occurred since the last time it checked (using a counter stored somewhere, like `variable_get('s45_counter_PropertySearchQuery')`).
3.  **Handlers:** For each new event (like `PropertySaved`, `PropertyDeleted`, `PriceChanged`), it calls a corresponding handler method within the query class (e.g., `handlePropertySaved(EventDto $event)`).
4.  **Update Read Model:** These handler methods contain the logic to update the `_query_property_search` table based on the event.
    *   `handlePropertySaved`: Might insert a new row or update an existing row in the search table with the latest property details from the event's payload.
    *   `handlePropertyDeleted`: Might remove the corresponding row from the search table.
5.  **`exec()` Method:** When you call `$query->exec()`, it **queries the read model table** (`_query_property_search`), *not* the event log. It applies your filters (bedrooms, location, sorting, limit) directly to this optimized table.

**Sequence Diagram (`QueryFromEvents` Creation & Execution):**

```mermaid
sequenceDiagram
    participant UserCode
    participant QueryFE as QueryFromEvents Class
    participant EventStore as _s45_events Table
    participant ReadModelDB as _query_property_search Table
    participant ResultVO as SearchResultVO

    UserCode->>QueryFE: QueryFromEvents::create(filters)
    Note over QueryFE: Get last processed event ID (e.g., from variable_get)
    QueryFE->>EventStore: Get events WHERE id > lastEventId AND arName='Property' LIMIT N
    EventStore-->>QueryFE: Return new event DTOs
    loop For Each New Event
        alt Event is 'PropertySaved'
            QueryFE->>QueryFE: call handlePropertySaved(event)
            QueryFE->>ReadModelDB: INSERT or UPDATE row based on event payload
            ReadModelDB-->>QueryFE: OK
        else Event is 'PropertyDeleted'
            QueryFE->>QueryFE: call handlePropertyDeleted(event)
            QueryFE->>ReadModelDB: DELETE row WHERE id = event.arId
            ReadModelDB-->>QueryFE: OK
        else ... other event types ...
        end
    end
    Note over QueryFE: Update last processed event ID (variable_set)
    QueryFE-->>UserCode: Return Query Object (ready to execute)

    UserCode->>QueryFE: query->exec()
    Note over QueryFE: Build SQL query for Read Model Table based on filters
    QueryFE->>ReadModelDB: SELECT * FROM _query_property_search WHERE bedrooms=3 AND district='Laguna' ORDER BY priceSale ASC LIMIT 10
    ReadModelDB-->>QueryFE: Return matching rows
    QueryFE->>ResultVO: Create SearchResultVO
    QueryFE->>ResultVO: Populate rows with DTOs from DB results
    QueryFE->>ResultVO: Set total count
    ResultVO-->>QueryFE: Return populated SearchResultVO
    QueryFE-->>UserCode: Return SearchResultVO
```

This shows that the potentially slow event processing happens during the `create()` phase (or periodically in the background), while the `exec()` method is fast because it queries an optimized table.

**Code Snippets (`QueryFromEvents` - Simplified)**

```php
// Simplified concept from s45_base/classes/Site45/Base/QueryFromEvents.php

abstract class QueryFromEvents {
  // ... properties like sortBy, limit, start ...
  protected $eventLastId; // Tracks the last event processed
  protected $table; // Object representing the read model DB table

  // Called when the query object is created
  public static function create($filter = NULL, $debug = FALSE, $eventMaxCount = 300) {
    $query = new static();
    // ... apply filters ...
    $query->setTable(); // Point to the read model table (e.g., _query_property_search)

    // Get ID of last processed event from storage (e.g., Drupal variable)
    $query->eventLastId = variable_get($query->getCounterName(), 0);

    // *** Process new events ***
    $query->eventsHandle($eventMaxCount);

    return $query;
  }

  // Process new events since last run
  protected function eventsHandle($eventMaxCount) {
    // 1. Query EventStore for new events using EventQuery
    $newEvents = EventQuery::create([
      'arName' => $this->getArNames(), // e.g., ['Property']
      'fromId' => ($this->eventLastId + 1),
      'limit' => $eventMaxCount,
    ])->exec()->rows;

    // 2. Loop through new events and call handlers
    if ($newEvents) {
      foreach ($newEvents as $eventDto) {
        // Dynamically call handler: e.g., 'handle' + 'Property' + 'Saved'
        $methodName = 'handle' . $eventDto->arName . $eventDto->name;
        if (method_exists($this, $methodName)) {
          $this->$methodName($eventDto); // Calls handlePropertySaved, handlePropertyDeleted etc.
        }
        $this->eventLastId = $eventDto->id; // Update last processed ID
      }
    }

    // 3. Save the last processed event ID for next time
    variable_set($this->getCounterName(), $this->eventLastId);
  }

  // Example Handler (must be implemented in specific query class like PropertySearchQuery)
  // abstract protected function handlePropertySaved(EventDto $event);
  // abstract protected function handlePropertyDeleted(EventDto $event);

  // Executes the search against the READ MODEL table
  abstract public function exec();

  // Returns the name of the Aggregate Roots this query cares about
  abstract protected function getArNames();

  // Sets up the $this->table property (pointing to the read model table)
  abstract protected function setTable();

  protected function getCounterName() { /* generates unique name for variable_get/set */ }
}
```

*   `create()` initializes the query and triggers `eventsHandle()`.
*   `eventsHandle()` fetches new events from the `EventStore` using `EventQuery`.
*   It loops through events, calling specific `handle...` methods (which must be implemented in the child query class) to update the read model table.
*   It saves the ID of the last event processed.
*   The `exec()` method (implemented in the child class) is responsible for querying the read model table (using `$this->table`) based on the filters provided during `create()`.

## How `JsonQuery` Works Under the Hood

`JsonQuery` is simpler. It works directly with a JSON file.

1.  **Load Data:** When a `JsonQuery` object is created (`::create()`), its constructor uses `JsonRepo::open()` to read the entire specified JSON file (e.g., `_repo/PropertyTypes.json`) into memory (`$this->records`).
2.  **`exec()` Method:** When you call `exec()`, it typically calls an internal method like `makeRows()`.
3.  **`makeRows()`:** This method (which must be implemented in the specific `JsonQuery` child class) iterates through the `$this->records` loaded in the constructor. It applies any filters defined in the query object (e.g., if you were filtering property types by a 'category' field). It also handles sorting. The filtered and sorted results are put into the `$this->searchResult->rows` array.
4.  **Pagination:** The `exec()` method then takes the results from `makeRows()` and applies the `limit` and `start` parameters (pagination) using `array_slice`.
5.  **Return:** It returns the final `SearchResultVO`.

**Sequence Diagram (`JsonQuery` Execution):**

```mermaid
sequenceDiagram
    participant UserCode
    participant JsonQueryClass as JsonQuery Class
    participant JsonRepo
    participant JsonFile as _repo/Data.json
    participant ResultVO as SearchResultVO

    UserCode->>JsonQueryClass: JsonQuery::create(filters)
    JsonQueryClass->>JsonRepo: JsonRepo::open('.../Data.json')
    JsonRepo->>JsonFile: Read file content
    JsonFile-->>JsonRepo: Return JSON string
    JsonRepo->>JsonRepo: Decode JSON
    JsonRepo-->>JsonQueryClass: Return data object/array ($this->records)
    JsonQueryClass-->>UserCode: Return Query Object (ready to execute)

    UserCode->>JsonQueryClass: query->exec()
    JsonQueryClass->>JsonQueryClass: call makeRows()
    Note over JsonQueryClass: Loop through $this->records, apply filters, sort. Populate internal result rows.
    JsonQueryClass->>JsonQueryClass: Apply limit/start pagination (array_slice)
    JsonQueryClass->>ResultVO: Create SearchResultVO
    JsonQueryClass->>ResultVO: Set final rows, total count, filter info
    ResultVO-->>JsonQueryClass: Return populated SearchResultVO
    JsonQueryClass-->>UserCode: Return SearchResultVO
```

**Code Snippets (`JsonQuery` - Simplified)**

```php
// Simplified concept from s45_base/classes/Site45/Base/JsonQuery.php

abstract class JsonQuery {
  // ... properties like start, limit ...
  protected $dtoName; // Base name for the JSON file (e.g., 'PropertyType')
  protected $records; // Holds all data loaded from the JSON file
  protected $searchResult; // Holds the final SearchResultVO

  // Constructor - called by ::create()
  function __construct() {
    // Determine the JSON file path based on site and dtoName
    $siteId = SiteConf::getId(); // Assumes SiteConf is available
    $repoPath = S45_SITES_DIR.'/'.$siteId.'/_repo/'.$this->dtoName.'.json'; // Path constant

    // Load ALL records from the JSON file into memory
    $this->records = JsonRepo::open($repoPath);

    // Basic processing (e.g., ensure 'id' and 'type' are set)
    if($this->records){
      foreach ($this->records as $id => $record) {
        if(is_object($record)){
           $record->type = $this->dtoName;
           $record->id = $id;
        }
      }
    }

    $this->searchResult = new SearchResultVO();
  }

  // Static factory method
  public static function create($filter = NULL){
    $query = new static(); // Calls the constructor above
    if($filter){
      Creator::create($query, $filter); // Apply filter properties to the query object
    }
    return $query;
  }

  // Main execution method
  public function exec() {
    $this->searchResult->filter = json_decode(json_encode($this)); // Store filters used

    if($this->records){
      // *** Apply filtering and sorting (implemented in child class) ***
      $this->makeRows();

      // Get total count BEFORE pagination
      $this->searchResult->total = count($this->searchResult->rows);

      // Apply pagination (limit/start)
      $this->searchResult->rows = array_slice($this->searchResult->rows, $this->start, $this->limit);
    } else {
       $this->searchResult->total = 0;
       $this->searchResult->rows = [];
    }

    return $this->searchResult;
  }

  // Abstract method: Child class must implement filtering/sorting logic
  // It should process $this->records and populate $this->searchResult->rows
  abstract protected function makeRows();
}
```

*   The constructor loads all data from the JSON file via `JsonRepo::open`.
*   `create()` applies any filter criteria to the query object itself (e.g., setting `$query->categoryFilter = 'Villas'`).
*   `exec()` calls the abstract `makeRows()` method.
*   `makeRows()` (implemented in the specific query class, e.g., `PropertyTypeQuery`) must contain the logic to iterate `$this->records`, apply filters (checking `$this->categoryFilter` etc.), sort the results, and put them into `$this->searchResult->rows`.
*   `exec()` then handles the final pagination.

## Connecting to Other Parts

Queries are central to retrieving collections of data and connect to many other concepts:

*   [Data Transfer Object (DTO) / Value Object (VO)](04_data_transfer_object__dto____value_object__vo__.md): Queries almost always return their results (`rows`) as an array of DTOs or VOs, packaged within a `SearchResultVO`.
*   [Event Sourcing (`AR`, `EventStore`, `EventQuery`)](07_event_sourcing___ar____eventstore____eventquery__.md): `QueryFromEvents` is fundamentally based on processing event streams from the `EventStore` using `EventQuery`.
*   [Repository (`Repo`)](06_repository___repo__.md): While Repositories typically fetch single items by ID (`Repo::load`), Queries fetch collections based on criteria. `JsonQuery` often relies on `JsonRepo` to read the underlying data file.
*   [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md): Components often use Queries in their `beforeRender` method (or similar logic) to fetch the list of data they need to display (e.g., a list component fetching items via a query).
*   **Database:** `QueryFromEvents` relies on a read-model database table for efficient querying. Other query types might interact with the database directly (though less common in the provided `__s45` structure which favors events or JSON).

## Conclusion

You've now learned about Queries, the data detectives of `__s45`!

*   They solve the problem of finding **collections of data** based on specific **search criteria (filters)**.
*   Think of them as specialized researchers you give clues to.
*   **`QueryFromEvents`** builds results by processing the event log ([Event Store](07_event_sourcing___ar____eventstore____eventquery__.md)), often using an optimized read-model table.
*   **`JsonQuery`** retrieves results by filtering data stored in JSON files ([JsonRepo](06_repository___repo__.md)).
*   You typically use them by `::create($filters)` and then calling `->exec()`.
*   Results are usually returned in a `SearchResultVO` containing a list of [DTOs/VOs](04_data_transfer_object__dto____value_object__vo__.md) in the `rows` property and a `total` count.

We've seen how data is configured (`SiteConf`), how paths are managed (`Path`), how pages are built (`Compo`), how data is structured (`DTO/VO`), how components become HTML (`Render452`), how individual items are saved/loaded (`Repo`), how history is tracked (`Event Sourcing`), and now how lists of data are found (`Query`).

What about managing shared application state that isn't tied to a specific component or data entity? Things like the currently selected currency, the logged-in user's preferences, or temporary messages? This is where the `Store` comes in.

Let's move on to the final chapter: [Chapter 9: Store (`Store`)](09_store___store___.md).

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)# Chapter 9: Store (`Store`)

Welcome to the final chapter! In [Chapter 8: Query (`Query`, `QueryFromEvents`, `JsonQuery`)](08_query___query____queryfromevents____jsonquery__.md), we learned how to use Queries to find and retrieve collections of data based on specific criteria, like searching for properties. But what about data that isn't part of a large collection or a saved entity? What if you just need a temporary place to jot down some notes that different parts of your code can read during a single web page visit?

## What Problem Does `Store` Solve?

Imagine you have a webpage with two parts:
1.  A **Search Form** where the user enters criteria (like "show me houses for rent").
2.  A **Results List** that displays the properties matching the criteria entered in the form.

How does the Results List component know *what* the user entered into the Search Form component?

One way is to pass the search criteria directly from the form to the results component. But sometimes, these components might be loaded independently or live in different parts of the page structure, making direct passing complicated.

Wouldn't it be nice if the Search Form could just write the user's search criteria onto a **temporary, shared notepad**, and the Results List could simply read from that same notepad whenever it needs the criteria?

This is exactly what the `Store` class helps us do!

## Meet `Store`: Your Shared Notepad

Think of the `Store` class as providing a simple, temporary notepad that different parts of your code can access during a single web request, or even across multiple requests within a user's session (like a shopping cart).

It works like a dictionary or a simple key-value system:
*   You can **save** a piece of data using a specific name (the "key"). This is like writing "Search Filters: {dealType: 'rent', propertyType: 'house'}" on the notepad.
*   You can **retrieve** that data later by asking for it using the same name (the "key"). This is like looking up "Search Filters" on the notepad.

`Store` is particularly useful for sharing temporary information or "state" between different components or functions without needing complex connections between them.

**Different Notepads (Scopes/Sections):**

`Store` can manage several different notepads, called "sections" or "scopes":

1.  **`GLOBAL` (Default):** A general notepad accessible from anywhere. Good for very simple, widely shared data.
2.  **`PAGE`:** A special notepad unique to the *current page request*. This is perfect for our search filter example, as the filters are only relevant while the user is interacting with that specific search results page. It automatically uses a unique ID for the current page view generated by `PageId`.
3.  **`UPLOADS`:** A dedicated notepad specifically used by components like `FormImage` to keep track of files being uploaded within a form before the main entity (like a property) is saved.
4.  **Custom Sections:** You can create your own named sections if needed.

## Using `Store`: Reading and Writing Notes

Using the `Store` is very straightforward.

**Example 1: Saving Search Filters (in the Search Form Component)**

Let's say our Search Form component (`PropertySearchPage` in the example code) determines the initial filters. It can save them to the `PAGE` store.

```php
<?php
// Inside a component like PropertySearchPage.php
use Site45\Base\Store;

// Define the search filters
$searchFilterData = [
  'dealType' => 'rent',
  'propertyType' => 'house',
];

// Get the notepad for the current PAGE request
$pageStore = Store::create('PAGE');

// Write the filters onto the notepad under the key 'propertySearchFilter'
$pageStore->set('propertySearchFilter', $searchFilterData);

echo "Search filters saved to the page store!";

// --- What happens ---
// 1. Store::create('PAGE') gets a Store object linked to the current page's unique ID.
// 2. ->set('propertySearchFilter', ...) saves the $searchFilterData array
//    into the PHP session under a key associated with this page ID
//    and the name 'propertySearchFilter'.
?>
```

**Explanation:**
*   `Store::create('PAGE')` gives us access to the specific notepad for the current page request. `PAGE` is a special keyword here.
*   `$pageStore->set('propertySearchFilter', $searchFilterData)` writes our `$searchFilterData` array onto that notepad, labelling it with the key `'propertySearchFilter'`.

**Example 2: Retrieving Search Filters (in the Results List Component)**

Now, another component on the same page (like `PropertySearchForm` in the example code) needs to know the filters. It can read them from the `PAGE` store.

```php
<?php
// Inside another component like PropertySearchForm.php
use Site45\Base\Store;

// Get the notepad for the current PAGE request
$pageStore = Store::create('PAGE');

// Read the data labelled 'propertySearchFilter' from the notepad.
// If it's not found, use an empty array [] as a default.
$filters = $pageStore->get('propertySearchFilter', []); // Provide a default value!

// Now we can use the filters
if ($filters) {
  echo "Retrieved filters from page store: \n";
  print_r($filters);
} else {
  echo "No filters found in the page store.";
}

// --- Example Output (if filters were saved previously on this page view) ---
// Retrieved filters from page store:
// Array
// (
//     [dealType] => rent
//     [propertyType] => house
// )

// --- What happens ---
// 1. Store::create('PAGE') gets the same Store object for the current page.
// 2. ->get('propertySearchFilter', []) looks in the session for the data
//    associated with this page ID and the key 'propertySearchFilter'.
// 3. If found, it returns the saved data (our array).
// 4. If not found, it returns the default value we provided ([]).
?>
```

**Explanation:**
*   `Store::create('PAGE')` accesses the *same* notepad used in Example 1, because it's within the same page request.
*   `$pageStore->get('propertySearchFilter', [])` reads the data associated with the key `'propertySearchFilter'`.
*   The second argument `[]` is important: it's the **default value** to return if the key isn't found on the notepad. This prevents errors if the data hasn't been set yet.

**Other Scopes:**

*   **Global:** `Store::create()` or `Store::create('GLOBAL')` accesses the global notepad.
*   **Uploads:** `Store::create('UPLOADS')` accesses the notepad used for file uploads, often combined with an Aggregate Root ID and field name as the key (e.g., `Store::create('UPLOADS')->get('property123__photos')`).

## How It Works Under the Hood

The `Store` class is actually quite simple! It uses PHP's built-in **session handling (`$_SESSION`)** as its underlying storage mechanism. Sessions allow you to store user-specific information across multiple page requests.

**Step-by-Step (`Store::create` and `set/get`):**

1.  **`Store::create($section)`:**
    *   Determines the actual section name. If `$section` is `'PAGE'`, it calls `PageId::get()` to get the unique ID for the current page view. Otherwise, it uses the provided `$section` name (like `'GLOBAL'` or `'UPLOADS'`).
    *   Checks if the section already exists within the session data structure: `$_SESSION['s45']['store'][$sectionName]`.
    *   If the section doesn't exist in the session, it initializes it as an empty array: `$_SESSION['s45']['store'][$sectionName] = [];`.
    *   Creates a `Store` object instance and stores the `$sectionName` inside it.
    *   Returns the `Store` object.

2.  **`$store->set($key, $value)`:**
    *   Takes the `$key` and `$value`.
    *   (Optional: Converts the value to a standard PHP object using `s45_toObject` for consistency).
    *   Directly assigns the `$value` to the session array using the stored section name and the provided key: `$_SESSION['s45']['store'][$this->section][$key] = $value;`.

3.  **`$store->get($key, $default)`:**
    *   Takes the `$key` and the `$default` value.
    *   Checks if the key exists within the session array for the stored section: `isset($_SESSION['s45']['store'][$this->section][$key])`.
    *   If it exists, it returns the stored value: `$_SESSION['s45']['store'][$this->section][$key]`.
    *   If it *doesn't* exist, it returns the provided `$default` value.

**Session Structure:**

The data ends up organized within the `$_SESSION` superglobal like this:

```php
$_SESSION = [
  's45' => [
    'store' => [
      'GLOBAL' => [
        'some_global_setting' => 'value1',
        // ... other global items ...
      ],
      'page_123456_abcdef' => [ // Unique ID from PageId::get() for a page view
        'propertySearchFilter' => ['dealType' => 'rent', 'propertyType' => 'house'],
        // ... other items specific to this page view ...
      ],
      'UPLOADS' => [
        'propertyEditForm__mainImage' => [ /* FileDto objects */ ],
        // ... other upload tracking items ...
      ],
      // ... other custom sections ...
    ]
    // ... other s45 session data ...
  ]
  // ... other non-s45 session data ...
];
```

**Sequence Diagram:**

```mermaid
sequenceDiagram
    participant UserCode as Your Component
    participant StoreClass as Store Class
    participant PageIdClass as PageId Class
    participant Session as $_SESSION Data

    UserCode->>StoreClass: Store::create('PAGE')
    StoreClass->>PageIdClass: PageId::get()
    PageIdClass-->>StoreClass: Return uniquePageId (e.g., 'page_123456_abcdef')
    StoreClass->>Session: Check if $_SESSION['s45']['store']['page_123456_abcdef'] exists?
    alt Section Not Initialized
        Session-->>StoreClass: No
        StoreClass->>Session: Initialize: $_SESSION['s45']['store']['page_123456_abcdef'] = []
        Session-->>StoreClass: OK
    else Section Exists
        Session-->>StoreClass: Yes
    end
    StoreClass-->>UserCode: Return Store object (section='page_123456_abcdef')

    UserCode->>StoreClass: store->set('filters', {data})
    StoreClass->>Session: Set $_SESSION['s45']['store']['page_123456_abcdef']['filters'] = {data}
    Session-->>StoreClass: OK
    StoreClass-->>UserCode: Return TRUE

    UserCode->>StoreClass: store->get('filters', [])
    StoreClass->>Session: Check if $_SESSION['s45']['store']['page_123456_abcdef']['filters'] is set?
    Session-->>StoreClass: Yes, return {data}
    StoreClass-->>UserCode: Return {data}
```

## A Glimpse at the Code

Let's look at the simplified code for the `Store` class.

**`Store::create()`**

```php
// Simplified from s45_base/classes/Site45/Base/Store.php
namespace Site45\Base;
use Site45\Base\PageId; // Needs PageId for the 'PAGE' scope

class Store {
  protected $section; // Holds the final section name (e.g., 'GLOBAL' or 'page_123_abc')

  public static function create($section = 'GLOBAL') {
    $store = new static(); // Create an instance of this Store class

    // Check if the special 'PAGE' section is requested
    // If yes, get the unique page ID to use as the section name
    $sectionName = ($section == 'PAGE') ? PageId::get() : $section;

    // Store the final section name in the object
    $store->section = $sectionName;

    // --- Initialize the section in the PHP Session if it doesn't exist ---
    // This ensures we always have an array to write to later
    if (!isset($_SESSION['s45']['store'][$store->section])) {
      $_SESSION['s45']['store'][$store->section] = array();
    }
    // --- End Initialization ---

    return $store; // Return the configured Store object
  }
  // ... get() and set() methods below ...
}
```

*   This method handles the logic for the `PAGE` scope using `PageId::get()`.
*   It makes sure the necessary array structure exists within `$_SESSION` before returning the `Store` instance.

**`Store::get()`**

```php
// Simplified from s45_base/classes/Site45/Base/Store.php
class Store {
  protected $section;
  // ... create() method above ...

  /**
   * Gets a value from the store for the current section.
   *
   * @param string $name The key of the data to retrieve.
   * @param mixed $default The value to return if the key is not found.
   * @return mixed The stored value or the default value.
   */
  public function get($name, $default = NULL) {
    // Check if the key exists within this store's section in the session
    if (isset($_SESSION['s45']['store'][$this->section][$name])) {
      // Key exists, return the stored value
      return $_SESSION['s45']['store'][$this->section][$name];
    } else {
      // Key doesn't exist, return the provided default value
      return $default;
    }
  }
  // ... set() method below ...
}
```

*   This is a standard lookup: check if the key exists in the session array for the current section; return the value if yes, otherwise return the default.

**`Store::set()`**

```php
// Simplified from s45_base/classes/Site45/Base/Store.php
class Store {
  protected $section;
  // ... create() and get() methods above ...

  /**
   * Sets a value in the store for the current section.
   *
   * @param string $name The key to store the data under.
   * @param mixed $value The data to store.
   * @param string|null $mode Optional mode (e.g., 'INIT' to set only if not already set).
   * @return boolean TRUE if the value was set.
   */
  public function set($name, $value, $mode = null) {
    // Optional: Convert arrays to standard objects for consistency
    if (is_object($value)) {
      $value = s45_toObject($value); // s45_toObject likely converts array-like objects
    }

    // Handle special modes (like 'INIT' - set only if not present)
    if ($mode) {
      if ($mode == 'INIT') {
        if (!isset($_SESSION['s45']['store'][$this->section][$name])) {
          $_SESSION['s45']['store'][$this->section][$name] = $value;
          return TRUE; // Value was set because it didn't exist
        }
        // If mode is 'INIT' and value already exists, do nothing
        return FALSE;
      }
      // Handle other potential modes...
    } else {
      // Default behavior: unconditionally set the value
      $_SESSION['s45']['store'][$this->section][$name] = $value;
      return TRUE; // Value was set (or overwritten)
    }

    return FALSE; // Should only happen if an unknown mode was used
  }
}
```

*   This method directly writes the `$value` into the `$_SESSION` array at the location determined by the `$this->section` and the provided `$name` (key).
*   It includes an optional `INIT` mode to only set the value if it doesn't already exist.

## Connecting to Other Parts

*   [Component (`Compo` / `Compo452`)](03_component___compo_____compo452___.md): Components are the primary users of the `Store`. They use it to share temporary data (like search filters between `PropertySearchPage` and `PropertySearchForm`) or manage intermediate state (like `FormImage` using the `UPLOADS` scope).
*   **`PageId` Class:** Used internally by `Store::create('PAGE')` to generate and retrieve the unique ID for the current page request, ensuring the `PAGE` scope is isolated to that specific view.
*   **PHP Sessions (`$_SESSION`):** The `Store` class is essentially a user-friendly wrapper around PHP's session mechanism, providing organization through sections.

## Conclusion

You've reached the end of the `__s45` tutorial series and learned about the `Store`!

*   It solves the problem of sharing **temporary data** between different parts of your code within a single request or user session.
*   Think of it as a shared **notepad** using a **key-value** system (`set`/`get`).
*   It uses PHP's `$_SESSION` for storage.
*   It provides different **scopes (sections)** like `GLOBAL`, `PAGE` (unique per request), and `UPLOADS` to organize the stored data.
*   It's useful for passing information like search filters, user preferences, or temporary messages without complex parameter passing.

Congratulations! You've journeyed through the core concepts of the `__s45` project, from site configuration and path management to components, data handling, event sourcing, querying, and finally, the shared Store. While each chapter focused on a specific piece, remember that these components work together to create a complete application. Hopefully, this gives you a solid foundation for understanding and working with `__s45` projects. Good luck!

---

Generated by [AI Codebase Knowledge Builder](https://github.com/The-Pocket/Tutorial-Codebase-Knowledge)