---
type: "agent_requested"
---

# Site45 Base Utilities Cursor Rules

<rule>
name: store_util
description: Persistence store helper (Store.php)
filters:
  - type: file_path
    pattern: "Store\.php$"
actions:
  - type: suggest
    message: |
      - Проверьте класс `Store`: методы `set()`, `get()`, `clear()` для хранения временных данных.
      - Убедитесь, что данные надежно сериализуются и десериализуются.
      - Проверьте защиту от коллизий ключей и очистку устаревших записей.
</rule>

<rule>
name: file_saver
description: FileSaver utility for handling file uploads
filters:
  - type: file_path
    pattern: "FileSaver\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `FileSaver.php`: методы сохранения, удаления файлов.
      - Проверьте использование `file_exists()`, `file_put_contents()` и обработку ошибок.
      - Убедитесь, что права на директории корректно устанавливаются и валидируются пути.
</rule>

<rule>
name: data_creator
description: Creator utility for mapping data to objects
filters:
  - type: file_path
    pattern: "Creator\.php$"
actions:
  - type: suggest
    message: |
      - Изучите `Creator.php`: метод `create($object, $data)`.
      - Проверьте, как свойства объекта заполняются из массива/DTO.
      - Убедитесь, что проверяются существующие свойства и предотвращаются инъекции.
</rule>

<rule>
name: logger_utility
description: Logger for API and exceptions
filters:
  - type: file_path
    pattern: "Logger\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `Logger.php`: методы `add()`, `exception()`.
      - Проверьте, куда сохраняются логи (`_logs`), уровень логирования и формат.
      - Убедитесь, что логируются только необходимые данные и нет утечек.
</rule>

<rule>
name: value_object
description: Base VO class for Data Transfer Objects
filters:
  - type: file_path
    pattern: "VO\.php$"
actions:
  - type: suggest
    message: |
      - Изучите `VO.php`: базовый класс для VO.
      - Проверьте, как создаются объекты через `create()`.
      - Убедитесь, что VO валидируются и используются корректно.
</rule>

<rule>
name: base_query
description: Query interface for Site45
filters:
  - type: file_path
    pattern: "Query\.php$"
actions:
  - type: suggest
    message: |
      - Откройте `Query.php`: общий интерфейс для запросов.
      - Проверьте методы `execute()`, `setFilter()`.
      - Убедитесь, что наследники `QueryFromEvents` правильно реализуют нужные методы.
</rule>
