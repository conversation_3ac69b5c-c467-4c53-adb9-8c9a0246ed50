---
type: "always_apply"
---

# PDF и Обработка Изображений в IndreamsPhuket

<rule>
name: pdf_generation_workflow
description: Процесс генерации PDF с изображениями недвижимости
filters:
  - type: file_path
    pattern: "s45_phuket_pdf\\.inc$"
actions:
  - type: suggest
    message: |
      ## Генерация PDF в системе
      
      - Основной обработчик находится в файле `s45_phuket_pdf.inc` - функция `s45_phuket_pdf()`
      - URL для генерации PDF: `/PhuketPdf/НОМЕР_ОБЪЕКТА/[режим]/[debug]`
        - `НОМЕР_ОБЪЕКТА` - номер объекта недвижимости
        - `режим` - `guest` (по умолчанию), `my` или `clean`
        - `debug` - опциональный параметр для вывода HTML вместо PDF
      - PDF создаётся через mPDF версии 6.0
      - Для включения отладки используйте URL вида `/PhuketPdf/123/clean/debug`
</rule>

<rule>
name: image_path_processing
description: Как система обрабатывает пути к изображениям
filters:
  - type: file_path
    pattern: "s45_base\\.lib\\.inc$"
  - type: file_content
    pattern: "function s45_imgSrcR|function s45_imgSrc"
actions:
  - type: suggest
    message: |
      ## Обработка путей к изображениям
      
      - Для генерации URL изображений используются **две ключевые функции**:
        - `s45_imgSrc($fileDto, $styleName)` - обёртка, которая вызывает `s45_imgSrcR()`
        - `s45_imgSrcR(&$fileDto, $styleName)` - основная функция
      
      - Система **не полагается на свойство `uri`** в объектах фотографий!
      - Вместо этого используется свойство **`id`** объекта `$fileDto`, которое может быть:
        - Внешним URL (если начинается с "http")
        - ID файла (для файлов, загруженных через админку)
      
      - **Алгоритм определения пути к файлу**:
        1. Если `$fileDto->id` содержит "http" и "Site45/Sets" - это статический файл из компонента
        2. Если `$fileDto->id` содержит "pictures" - это фото агентов из специальной директории
        3. Если `$fileDto->id` содержит "http" - это файл со старого сайта, который кэшируется локально
        4. В остальных случаях - это имя файла, загруженного через админку и хранящегося в `/files/tmp/`
</rule>

<rule>
name: pdf_debug_diagnostics
description: Инструменты отладки PDF и изображений
filters:
  - type: file_path
    pattern: "debug_pdf_images"
actions:
  - type: suggest
    message: |
      ## Отладка проблем с PDF и изображениями
      
      - **Режим отладки PDF** - добавьте `/debug` в конец URL:
        - Пример: `/PhuketPdf/123/clean/debug`
        - В этом режиме будет показан HTML, а не PDF, с возможностью скачать PDF
      
      - **Различия между режимами отладки и обычным**:
        - В режиме debug объекты свежие, берутся из формы или текущего запроса
        - В обычном режиме объекты загружаются из кэша или базы данных
        - При наличии ошибок сериализации в EventStore, это приводит к разному поведению
      
      - **Основные проверки при проблемах с изображениями**:
        1. Проверьте файл в директории `/files/tmp/[ID]`
        2. Для внешних URL - проверьте файл в `/files/old/[md5].jpg`
        3. Проверьте, что свойство `id` корректно задано в объекте фотографии
        4. Выполните тестовый вызов s45_imgSrc() с объектом фотографии
        5. Включите отладку mPDF: `$mpdf->showImageErrors = true; $mpdf->debug = true;`
</rule>

<rule>
name: pdf_image_special_behavior
description: Особенности работы изображений в PDF
filters:
  - type: file_path
    pattern: "PhuketPdf"
actions:
  - type: suggest
    message: |
      ## Особенности изображений в PDF
      
      - **Важно**: mPDF работает с локальными физическими путями к файлам
      - Изображения в PDF **используют s45_imgSrc()**, который:
        - Генерирует URL на основе ID файла (не URI)
        - Ищет файлы в директориях `tmp` и `old`
        - Применяет стиль изображения (напр. `S45_IMST_500X300_CROP`)
      
      - **Модульная структура шаблона PDF**:
        - Основной шаблон `PhuketPdf.tpl.php` разделен на секции
        - Использует включаемые файлы через `include $this->dir . '/res/inc/X.inc'`:
          - `header.inc`: Логотип, фото агента, QR-код, контакты
          - `chars.inc`: Таблица характеристик объекта
          - `price.inc`: Таблица цен аренды
          - `photos.inc`: Галерея фотографий
          - `footer.inc`: Ссылка на объект на сайте
      
      - **Управление выводом**:
        - Шаблон адаптирует контент в зависимости от размера страницы
        - Для корректного вывода разрывов страниц используйте `<pagebreak>`
</rule>

<rule>
name: uri_vs_id_property
description: Различия между свойствами uri и id в объектах изображений
filters:
  - type: file_content
    pattern: "s45_imgSrc|s45_imgSrcR|FileDto"
actions:
  - type: suggest
    message: |
      ## Свойства `id` в объектах фотографий
      
      - **Ключевое различие**: система использует `id` для определения пути к файлу
      
      - **Архитектурное обоснование**:
        - Универсальность идентификации: один и тот же механизм работает с разными типами источников
        - Гибкость хранения: физический путь определяется динамически
        - Поддержка разных сценариев: внешние URL, статические файлы, загруженные файлы
        - Масштабируемость при миграции: сохранение ссылок на файлы со старого сайта
      
      - **Реализация**:
        ```php
        // Создание FileDto при загрузке
        protected function getUploaded($file) {
          $newFile = new FileDto();
          $newFile->id = date('Ymd_His').'_'. rand(1000, 9999).'_'.s45_trans($file['name'], TRUE);
          // ... другие поля ...
          return $newFile;
        }
        
        // Определение физического пути на основе id
        function s45_imgSrcR(&$fileDto, $styleName = '') {
          // Логика выбора стратегии определения пути
          if(strpos('qwerty'.$fileDto->id, 'http') AND strpos('qwerty'.$fileDto->id, 'Site45/Sets')){
            // Файл из компонента
          } else if(strpos($fileDto->id, 'pictures')){
            // Фото агентов
          } else if(strpos('qwerty'.$fileDto->id, 'http')){
            // Внешний URL
          } else {
            // Загруженный файл
          }
        }
        ```
        
      - **Важно**: При создании новых компонентов всегда используйте `id` как основной идентификатор файла
</rule>

<rule>
name: event_sourcing_serialization
description: Особенности сериализации в Event Sourcing
filters:
  - type: file_path
    pattern: "EventStore\\.php$|PhuketPropertyAR\\.php$"
actions:
  - type: suggest
    message: |
      ## Сериализация в Event Sourcing
      
      - **Жизненный цикл объектов**:
        1. Загрузка данных формы в `PhuketPropertyAR`
        2. Сохранение через `EventStore::addEvent()`
        3. Сериализация объекта в поле `payload`
        4. Десериализация при восстановлении состояния
        5. Обновление read-модели (`_phuket_Property`)
      
      - **Правильный подход к сериализации**:
        ```php
        // В EventStore::addEvent()
        $this->eventTable->payload = serialize($payload);
        
        // В EventQuery::exec() или аналогичных методах
        $event->payload = unserialize($eventRow->payload);
        ```
      
      - **Распространенные ошибки**:
        - Использование `json_decode(json_encode($payload))` теряет типы данных
        - Обработка ошибок через `@unserialize()` скрывает проблемы
        - Несоответствие типов данных при обновлении read-модели
      
      - **Для корректной работы с изображениями в PDF**:
        - Убедитесь, что объекты `FileDto` сериализуются корректно
        - Проверьте, что поле `id` сохраняется при сериализации/десериализации
        - Используйте `serialize($payload)` вместо преобразований через JSON
</rule>

<rule>
name: nginx_pdf_configuration
description: Настройка Nginx для работы с PDF
filters:
  - type: file_path
    pattern: "nginx.*\\.conf$"
actions:
  - type: suggest
    message: |
      ## Настройка Nginx для PDF
      
      - **Обработка запросов к `/PhuketPdf/`**:
        - URL не имеет расширения `.php`, поэтому запрос проксируется на Apache
        - Apache запускает Drupal, который обрабатывает URL через `hook_menu`
        - В `s45_phuket.module` зарегистрирован маршрут `PhuketPdf/%`
      
      - **Кэширование статических PDF-файлов**:
        - Настройте корректно `expires` для файлов PDF (рекомендуется 24h)
        - Добавьте MIME-тип `application/pdf` в `gzip_types`
        - Оптимальная конфигурация для статического контента:
          ```nginx
          location ~* ^.+\.(jpg|jpeg|gif|png|pdf|docx|doc)$ {
              expires 24h;
              log_not_found off;
              access_log off;
              add_header Pragma public;
              add_header Cache-Control "public";
              try_files $uri $uri/ @fallback;
          }
          ```
      
      - **Оптимизация для больших файлов**:
        - Увеличьте `client_max_body_size` до 128MB или больше
        - Настройте `proxy_read_timeout` для длительных запросов
        - Добавьте буферизацию для больших ответов:
          ```nginx
          proxy_buffers 16 16k;
          proxy_buffer_size 16k;
          ```
</rule>

<rule>
name: multilingual_pdf_generation
description: Генерация PDF для разных языков
filters:
  - type: file_path
    pattern: "s45_phuket_pdf\\.inc$|PhuketPdf\\.tpl\\.php$"
actions:
  - type: suggest
    message: |
      ## Многоязычные PDF-документы
      
      - **Определение языка**:
        - Система использует `$GLOBALS['language']->language`
        - Функция `s45_lang()` извлекает текст для текущего языка
      
      - **В шаблонах PDF**:
        - Для локализованного текста используйте `<?php print s45_lang($this->propertyDto->name); ?>`
        - Для фиксированных текстов используйте подготовленные переводы
      
      - **Особенности английской версии**:
        - Может испытывать проблемы с изображениями из-за ошибок сериализации
        - Проблема часто проявляется в английской версии из-за особенностей загрузки данных
      
      - **Рекомендации**:
        - Проверяйте шаблоны на всех языках в режиме debug и без него
        - Добавьте дополнительное логирование для отладки проблем с языками
        - При создании новых шаблонов проверяйте корректность локализации
</rule>

<rule>
name: pdf_performance_optimization
description: Оптимизация производительности генерации PDF
filters:
  - type: file_path
    pattern: "s45_phuket_pdf\\.inc$"
  - type: file_content
    pattern: "s45_phuket_pdf|mPDF"
actions:
  - type: suggest
    message: |
      ## Оптимизация производительности PDF
      
      - **Кэширование PDF-файлов**:
        - Сохраняйте сгенерированные PDF в директории `S45_SITES_DIR.'/'.SiteConf::getId().'/pdf/'.$propertyNumber`
        - Проверяйте существование файла перед генерацией
        - Инвалидируйте кэш при изменении объекта недвижимости
      
      - **Оптимизация изображений**:
        - Используйте оптимизированные стили изображений
        - Контролируйте размер загружаемых изображений через `S45_IMST_*`
        - Минимизируйте количество изображений на странице
      
      - **Настройки mPDF**:
        - Оптимизируйте параметры конструктора mPDF:
          ```php
          $mpdf = new mPDF('utf-8', array(220, 280), '', '', 10, 10, 7, 7, 10, 10);
          ```
        - Используйте сжатие:
          ```php
          $mpdf->SetCompression(true);
          ```
        - Отключите неиспользуемые возможности:
          ```php
          $mpdf->simpleTables = true;
          $mpdf->packTableData = true;
          ```
      
      - **PHP настройки**:
        - Установите достаточный `memory_limit` (минимум 256M)
        - Увеличьте `max_execution_time` для генерации больших PDF
</rule>