---
type: "always_apply"
---

# Miscellaneous __s45 Modules Cursor Rules

<rule>
name: s45_imagestyles
description: Image styles definitions in s45_imagestyles.module
filters:
  - type: file_path
    pattern: "s45_imagestyles\.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_imagestyles.module`: константы размеров и функции `s45_imagestyles_image_default_styles()`, `s45_imagestyles_image_scale_and_crop()`, `s45_imagestyles_image_scale()`.
      - Проверьте, что размеры соответствуют требованиям дизайна и правильно регистрируются.
      - Убедитесь в корректности параметров `upscale`, `width`, `height`.
</rule>

<rule>
name: s45_page_state
description: Session store and state inspection in s45_state.inc
filters:
  - type: file_path
    pattern: "s45_state\.inc$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_page_state()`: функции очистки `$_SESSION['s45']['store']` и `states`.
      - Проверьте `drupal_set_message()`, `drupal_goto()` и `dsm()` вызовы.
      - Убедитесь, что функции используются только в режиме отладки.
</rule>

<rule>
name: s45_test_module
description: Test page module in s45_test.module
filters:
  - type: file_path
    pattern: "s45_test\.module$"
actions:
  - type: suggest
    message: |
      - Изучите `s45_test_menu()` и `s45_test_page()`: тестовый маршрут `s45test`.
      - Проверьте использование `Path::create()->addAlias()` и пример работы с CompoRepo.
      - Убедитесь, что тестовый модуль не влияет на продакшн и закомментирован.
</rule>

<rule>
name: s452_page
description: Page callback in s452_base
filters:
  - type: file_path
    pattern: "s452_base\.page\.inc$"
actions:
  - type: suggest
    message: |
      - Откройте `s452_base.page.inc`: функция `s452_page()` обрабатывает страницуаль `s45`.
      - Проверьте `drupal_flush_all_caches()`, `CompoScanner::getForAllDirs()`, `SiteConfLoadQuery`.
      - Убедитесь, что `s452_add_jsCore()`, `s45_addAllRes()`, `s45_add_theme2()` и `s452_render()` вызываются корректно.
</rule>
