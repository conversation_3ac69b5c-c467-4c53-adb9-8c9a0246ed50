<?php

/**
 * Скрипт для диагностики проблем с PDF на англоязычной версии сайта
 */

// Подключаем Drupal
define('DRUPAL_ROOT', getcwd());
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Подключаем необходимые классы
use Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery;
use Site45\Base\SiteConf;

// Подключаем класс PhuketPdf
require_once 'sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Compo/Common/PhuketPdf/PhuketPdf.php';

echo "=== ДИАГНОСТИКА PDF НА АНГЛОЯЗЫЧНОЙ ВЕРСИИ ===\n\n";

// Принудительно устанавливаем английский язык для тестирования
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$GLOBALS['language']->language = 'en';

// 1. Проверяем текущий язык и домен
echo "1. ПРОВЕРКА ЯЗЫКА И ДОМЕНА:\n";
echo "   - HTTP_HOST: " . $_SERVER['HTTP_HOST'] . "\n";
echo "   - Текущий язык: " . $GLOBALS['language']->language . "\n";
echo "   - SiteConf::getId(): " . SiteConf::getId() . "\n";
echo "   - Base URL: " . $GLOBALS['base_url'] . "\n\n";

// 2. Тестируем загрузку объекта недвижимости
$propertyNumber = '1001'; // Используем тестовый номер
echo "2. ЗАГРУЗКА ОБЪЕКТА НЕДВИЖИМОСТИ (номер: {$propertyNumber}):\n";

$searchResult = PhuketPropertyQuery::create(array('number' => $propertyNumber, 'mode' => 'full'))->exec();
if (isset($searchResult->rows[0])) {
    $propertyDto = $searchResult->rows[0];
    $propertyDto = PhuketPropertyQuery::create(NULL, FALSE)->load($propertyDto->id);
    
    echo "   ✓ Объект найден: " . s45_lang($propertyDto->name) . "\n";
    echo "   - ID: " . $propertyDto->id . "\n";
    echo "   - Координаты: " . $propertyDto->re_latitude . ", " . $propertyDto->re_longitude . "\n";
    echo "   - Количество фото: " . count($propertyDto->photos) . "\n\n";
    
    // 3. Тестируем генерацию URL изображений
    echo "3. ТЕСТИРОВАНИЕ URL ИЗОБРАЖЕНИЙ:\n";
    if (!empty($propertyDto->photos)) {
        $photo = $propertyDto->photos[0];
        echo "   Первое фото:\n";
        echo "   - ID: " . (isset($photo->id) ? $photo->id : 'НЕТ') . "\n";
        
        if (function_exists('s45_imgSrc')) {
            try {
                $imgUrl = s45_imgSrc($photo, 'S45_IMST_500X300_CROP');
                echo "   - URL через s45_imgSrc: " . $imgUrl . "\n";
                
                // Проверяем, является ли URL корректным
                if (strpos($imgUrl, 'public://') !== false) {
                    echo "   ⚠ ПРОБЛЕМА: URL содержит 'public://' - это неправильно для HTML/PDF\n";
                    
                    // Пробуем исправить
                    $correctedUrl = file_create_url($imgUrl);
                    echo "   - Исправленный URL: " . $correctedUrl . "\n";
                    
                    // Проверяем физическое существование файла
                    $realPath = drupal_realpath($imgUrl);
                    if ($realPath && file_exists($realPath)) {
                        echo "   ✓ Файл существует: " . $realPath . "\n";
                    } else {
                        echo "   ✗ Файл НЕ существует: " . ($realPath ?: 'путь не определен') . "\n";
                    }
                } else {
                    echo "   ✓ URL выглядит корректно\n";
                }
                
            } catch (Exception $e) {
                echo "   ✗ ОШИБКА при генерации URL: " . $e->getMessage() . "\n";
            }
        } else {
            echo "   ✗ Функция s45_imgSrc недоступна\n";
        }
    } else {
        echo "   ✗ У объекта нет фотографий\n";
    }
    
    echo "\n";
    
    // 4. Тестируем генерацию карты
    echo "4. ТЕСТИРОВАНИЕ ГЕНЕРАЦИИ КАРТЫ:\n";
    if ($propertyDto->re_latitude && $propertyDto->re_longitude) {
        // Создаем экземпляр PhuketPdf для тестирования
        $phuketPdf = new PhuketPdf();
        $staticMapUrl = $phuketPdf->getCachedStaticMap(
            $propertyDto->re_latitude, 
            $propertyDto->re_longitude, 
            $GLOBALS['language']->language,
            '500x300'
        );
        
        echo "   - URL карты: " . $staticMapUrl . "\n";
        
        if (strpos($staticMapUrl, 'public://') !== false) {
            echo "   ⚠ ПРОБЛЕМА: URL карты содержит 'public://' - это неправильно для HTML\n";
            
            // Проверяем физическое существование файла карты
            $realMapPath = drupal_realpath($staticMapUrl);
            if ($realMapPath && file_exists($realMapPath)) {
                echo "   ✓ Файл карты существует: " . $realMapPath . "\n";
                echo "   - Исправленный URL карты: " . file_create_url($staticMapUrl) . "\n";
            } else {
                echo "   ✗ Файл карты НЕ существует: " . ($realMapPath ?: 'путь не определен') . "\n";
            }
        } else {
            echo "   ✓ URL карты выглядит корректно\n";
        }
    } else {
        echo "   ✗ У объекта нет координат для карты\n";
    }
    
    echo "\n";
    
    // 5. Проверяем настройки mPDF
    echo "5. ПРОВЕРКА НАСТРОЕК mPDF:\n";
    if (function_exists('s45_add_mpdf')) {
        s45_add_mpdf();
        echo "   ✓ Библиотека mPDF подключена\n";
        
        // Проверяем версию mPDF
        if (class_exists('mPDF')) {
            echo "   ✓ Класс mPDF доступен\n";
        } else {
            echo "   ✗ Класс mPDF недоступен\n";
        }
    } else {
        echo "   ✗ Функция s45_add_mpdf недоступна\n";
    }
    
} else {
    echo "   ✗ Объект с номером {$propertyNumber} не найден\n";
    echo "   Попробуйте другой номер объекта\n";
}

echo "\n=== КОНЕЦ ДИАГНОСТИКИ ===\n";
