<?php

/**
 * Тестирование PDF на русской версии сайта
 */

// Подключаем Drupal
define('DRUPAL_ROOT', getcwd());
require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Принудительно устанавливаем русский язык и домен
$_SERVER['HTTP_HOST'] = 'indreamsphuket.ru';
$_SERVER['REQUEST_URI'] = '/PhuketPdf/1001/guest/debug';
$GLOBALS['language']->language = 'ru';

echo "=== ТЕСТИРОВАНИЕ PDF НА РУССКОЙ ВЕРСИИ ===\n\n";

// Подключаем функцию генерации PDF
require_once 'sites/all/modules/__s45/s45_phuket/s45_phuket_pdf.inc';

// Имитируем аргументы URL
$_GET['q'] = 'PhuketPdf/1001/guest/debug';
$GLOBALS['_drupal_path'] = 'PhuketPdf/1001/guest/debug';

echo "Генерируем HTML версию PDF для русской версии...\n";

try {
    $html = s45_phuket_pdf();
    
    echo "✓ HTML успешно сгенерирован\n";
    echo "Длина HTML: " . strlen($html) . " символов\n\n";
    
    // Проверяем наличие изображений в HTML
    preg_match_all('/<img[^>]+src=["\']([^"\']+)["\'][^>]*>/i', $html, $matches);
    
    if (!empty($matches[1])) {
        echo "Найдено изображений: " . count($matches[1]) . "\n";
        
        foreach ($matches[1] as $i => $src) {
            echo "  Изображение " . ($i + 1) . ": " . $src . "\n";
            
            // Проверяем корректность URL для русской версии
            if (strpos($src, 'indreamsphuket.ru') !== false) {
                echo "    ✓ URL содержит правильный домен .ru\n";
            } elseif (strpos($src, 'indreamsphuket.com') !== false) {
                echo "    ⚠ URL содержит .com вместо .ru\n";
            } elseif (strpos($src, 'http://.') !== false) {
                echo "    ✗ ПРОБЛЕМА: Некорректный URL с 'http://.'\n";
            } else {
                echo "    ✓ URL выглядит корректно (локальный или внешний)\n";
            }
        }
    } else {
        echo "⚠ Изображения не найдены в HTML\n";
    }
    
    // Сохраняем HTML в файл для просмотра
    $test_file = 'test_pdf_output_ru.html';
    file_put_contents($test_file, $html);
    echo "\nHTML сохранен в файл: {$test_file}\n";
    
} catch (Exception $e) {
    echo "✗ ОШИБКА при генерации HTML: " . $e->getMessage() . "\n";
}

echo "\n=== КОНЕЦ ТЕСТИРОВАНИЯ ===\n";
