# Screen Performance Optimization Plan

## What Went Wrong

Our previous implementation caused display issues with property photos, showing them as a simple list rather than the expected carousel/gallery. This happened because:

1. **Template Structure Disruption**: Our output buffering approach in the template file disrupted the normal rendering process, particularly affecting JavaScript initialization for the carousel.

2. **Premature Exit**: The caching logic used `exit()` after serving cached content, which prevented <PERSON><PERSON><PERSON> from properly completing the page rendering process.

3. **CSS/JS Loading Issues**: Cached content likely didn't include all required script and style dependencies needed by the carousel.

4. **DOM Ready Events**: The JavaScript initialization might depend on specific DOM ready events that were impacted by our caching approach.

## Component Analysis

Before implementing performance improvements, we need to understand the structure and bottlenecks of the PhuketPropScreen component:

### 1. Component Structure

- **PHP Class (PhuketPropScreen.php)**: Contains the `beforeRender()` method that likely makes multiple database queries to fetch property data, characteristics, photos, etc.
- **Template (PhuketPropScreen.tpl.php)**: Renders HTML structure with complex photo gallery/carousel
- **JavaScript (PhuketPropScreen.js)**: Handles interactivity, especially for photo navigation
- **CSS (PhuketPropScreen.css)**: Contains styling for the screen display

### 2. Performance Bottlenecks

- **Database Queries**: Multiple uncached database calls in the `beforeRender()` method
- **Image Processing**: Repeated loading of the same images without proper caching
- **Inefficient DOM Manipulation**: Possible excessive repaints and layout thrashing in JavaScript
- **SVG Icons**: Multiple file_get_contents() calls for the same SVG icons on each request
- **No HTTP Caching**: Missing browser cache headers for static resources

## Revised Approach

I recommend a less invasive approach that maintains all Drupal rendering processes while still achieving performance improvements:

### 1. Property Data Caching (Low Risk)

```php
function PhuketPropScreen_getPropertyDto($propertyId) {
  // Try cache first
  $cacheId = 'screen_property_' . $propertyId;
  $cache = cache_get($cacheId);
  
  if ($cache && !empty($cache->data)) {
    return $cache->data;
  }
  
  // Cache miss - query database
  $result = PhuketPropertyQuery::create(array(
    'number' => $propertyId,
  ), FALSE)->exec();

  $propertyDto = null;
  if (isset($result->rows[0])) {
    $propertyDto = $result->rows[0];
    // Store in cache for 30 days
    cache_set($cacheId, $propertyDto, 'cache', time() + 2592000);
  }

  return $propertyDto;
}
```

### 2. SVG Icons Caching (Low Risk)

```php
function PhuketPropScreen_getCharsList($propertyDto, $path) {
  // Cache icons to avoid file_get_contents on each request
  static $icons = array();
  
  if (empty($icons)) {
    $iconFiles = array('bedrooms', 'bathrooms', 'area_common', 'area_plot', 'furniture', 
                      'view', 'to_beach', 'rental_income', 'completion_date');
    
    foreach ($iconFiles as $icon) {
      $iconFile = $path . '/' . $icon . '.svg';
      if (file_exists($iconFile)) {
        $icons[$icon] = file_get_contents($iconFile);
      } else {
        $icons[$icon] = ''; // Avoid repeated file checks
      }
    }
  }
  
  // Rest of the function unchanged
  // ...
}
```

### 3. Manager and USD Rate Caching (Low Risk)

Similar low-risk caching for other auxiliary data.

### 4. Image Optimization

```php
// In beforeRender() method of PhuketPropScreen
public function beforeRender($props) {
  // Existing code...
  
  // Optimize photo data for screen display
  if (!empty($this->propertyDto->photos)) {
    foreach ($this->propertyDto->photos as &$photo) {
      // Pre-generate image style URLs to avoid processing during render
      $photo->thumbnailUrl = s45_imgSrcR($photo->uri, 'S45_IMST_600X600_CROP');
      $photo->fullsizeUrl = s45_imgSrcR($photo->uri, 'S45_IMST_1900X1000_SCALE');
      
      // Add width/height attributes to prevent layout shifts
      $photo->dimensions = array(
        'width' => 600,
        'height' => 600,
      );
    }
  }
  
  // More code...
}
```

### 5. JavaScript Optimization

Create an optimized version of the gallery/carousel script:

```javascript
// Optimized PhuketPropScreen.js
(function($, window, document) {
  // Cache DOM selections
  const $carousel = $('.property-gallery');
  const $thumbnails = $('.miniatures__image');
  let activeIndex = 0;
  let imagesPreloaded = false;
  
  // Preload all gallery images after initial render
  function preloadImages() {
    if (imagesPreloaded) return;
    
    $thumbnails.each(function() {
      const fullSizeUrl = $(this).data('fullsize');
      if (fullSizeUrl) {
        const img = new Image();
        img.src = fullSizeUrl;
      }
    });
    
    imagesPreloaded = true;
  }
  
  // Switch active image with minimal DOM updates
  function showImage(index) {
    if (index === activeIndex) return;
    
    // Update active thumbnail with class toggle instead of removing/adding
    $thumbnails.eq(activeIndex).removeClass('active');
    $thumbnails.eq(index).addClass('active');
    
    // Update main image
    const $mainImg = $('.property-gallery__main-image');
    const newSrc = $thumbnails.eq(index).data('fullsize');
    
    // Only change src if it's different (prevents unnecessary reloads)
    if ($mainImg.attr('src') !== newSrc) {
      $mainImg.attr('src', newSrc);
    }
    
    activeIndex = index;
  }
  
  // Initialize with event delegation (less memory usage)
  $(document).ready(function() {
    // Use event delegation for thumbnail clicks
    $('.miniatures').on('click', '.miniatures__image', function(e) {
      e.preventDefault();
      const index = $(this).index();
      showImage(index);
    });
    
    // Start preloading images after a short delay
    setTimeout(preloadImages, 1000);
  });
})(jQuery, window, document);
```

### 6. Static Resource Generation (Separate Tool)

Instead of modifying the core template flow, create an administrative tool that:
1. Generates static HTML versions of screens
2. Serves these static versions from a designated directory
3. Provides explicit links for use on external displays

### 7. Browser-Side Caching (No Server Changes)

Add HTTP headers to encourage aggressive browser caching:

```php
drupal_add_http_header('Cache-Control', 'public, max-age=2592000'); // 30 days
drupal_add_http_header('Expires', gmdate('D, d M Y H:i:s \G\M\T', time() + 2592000));
```

### 8. Database Query Optimization

```php
// In PhuketPropertyQuery class
protected function optimizeForScreenDisplay() {
  // Add specific JOINs only needed for screen display
  // Use existing indexes on commonly filtered fields
  $this->query->join('_phuket_Project', 'pp', 'pp.id = p.project');
  
  // Add index hints if necessary for large tables
  // $this->query->addExpression('USE INDEX (idx_locality_subloc)');
  
  // Limit returned fields to only what's needed for screen display
  $this->query->fields('p', array('id', 'number', 'name', 'price_sale', 'price_rent', 'areaCommon'));
  
  return $this;
}
```

### 9. Reduced Repaints (JavaScript Solution)

Add a small JavaScript utility that reduces screen repaints which often cause performance issues on lower-end displays:

```javascript
// StabilityHelper.js
(function() {
  // Prevent needless repaints
  document.body.style.overscrollBehavior = 'none';
  
  // Preload images to prevent flickering
  function preloadImages() {
    const images = document.querySelectorAll('.miniatures__image');
    images.forEach(img => {
      const fullSizeUrl = img.src.replace('600X600_CROP', '1900X1000_SCALE');
      const preloadImg = new Image();
      preloadImg.src = fullSizeUrl;
    });
  }
  
  // Run once DOM is ready
  window.addEventListener('DOMContentLoaded', preloadImages);
})();
```

## Implementation Plan

1. **Phase 1**: Implement the low-risk data caching functions in `funcs.inc`
   - Property data caching
   - SVG icon caching
   - Manager and USD rate caching

2. **Phase 2**: Optimize the component structure
   - Refactor beforeRender() to use cached data
   - Pre-generate image URLs
   - Implement the optimized JavaScript gallery

3. **Phase 3**: Add HTTP cache headers for browser-side caching
   - Set appropriate Cache-Control and Expires headers
   - Configure entity tags (ETags) for efficient validation

4. **Phase 4**: Optimize database queries
   - Add necessary indexes to _phuket_Property table
   - Optimize JOINs in PhuketPropertyQuery
   - Add index hints for complex queries

5. **Phase 5**: Create the static HTML generator as a separate administrative tool
   - Build admin interface for generating static versions
   - Implement cron job for regular updates

6. **Phase 6**: Add the StabilityHelper.js script for reducing repaints
   - Implement and test on target devices
   - Measure performance improvements

## Expected Benefits

- **Reduced Database Load**: Data caching will significantly reduce database queries
- **Faster Responses**: Cached property data will speed up page generation
- **Optimized Images**: Pre-generated image URLs and proper dimensions will improve loading time
- **Efficient JavaScript**: Optimized gallery code will reduce memory usage and improve response time
- **Better Database Performance**: Query optimizations will speed up data retrieval
- **Smoother Display**: StabilityHelper.js will reduce screen flicker and repaints
- **Fallback Option**: Static HTML provides a reliable option for very low-end displays

This approach maintains the original template structure while still providing significant performance improvements, particularly for displays that struggle with slow loading or partial image loading. 