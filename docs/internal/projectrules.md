## Безопасная работа с DTO/AR и API-ответами
- При работе с DTO/AR и формировании API-ответов всегда проверяйте существование и тип каждого свойства перед использованием.
- Не обращайтесь к вложенным объектам/массивам без проверки на is_object/is_array и isset.
- Для новых полей — аналогично: сначала проверка, потом использование.
- Это предотвращает фатальные ошибки и обеспечивает стабильность API даже при неполных или изменённых данных. 