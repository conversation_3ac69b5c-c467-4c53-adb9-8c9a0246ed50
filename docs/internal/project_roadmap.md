# Дорожная Карта (Roadmap) Улучшений и Развития Проекта indreamsphuket.com

Этот документ представляет собой стратегический план по улучшению, рефакторингу и развитию кастомной кодовой базы проекта `indreamsphuket.com`, основанный на всестороннем анализе его текущего состояния.

## 1. Резюме Текущего Состояния

Проект представляет собой функциональный сайт на Drupal 7 с **очень высокой степенью кастомизации**, реализованной через набор модулей `s45_*`, кастомную UI систему `Compo`, паттерн Event Sourcing для основных сущностей и отдельные Read Model таблицы для оптимизации чтения.

**Сильные стороны:**

*   **Функциональность:** Система выполняет свои основные задачи (управление недвижимостью, отображение, генерация фидов, обработка форм).
*   **Event Sourcing (Концепция):** Наличие лога событий (`_s45_events`) предоставляет потенциал для аудита и восстановления данных.
*   **Модульность (Частично):** Разделение логики по модулям `s45_*` и компонентам `Compo` обеспечивает некоторую структурированность.

**Слабые стороны / Ключевые Проблемы:**

*   **Критическая Уязвимость Безопасности:** Жестко закодированные учетные данные SMTP в `s45_phuket_form_api.inc`.
*   **Высокий Технический Долг:**
    *   **Сериализация Данных:** Хранение сложных данных в сериализованном виде в БД (`_s45_events.payload`, `_phuket_Property.propertyDto` и др.) – основная архитектурная проблема, затрудняющая запросы, миграцию и повышающая риски.
    *   **Изоляция от Drupal API:** Кастомные системы UI (`Compo`), URL (`s45_path`), переводов (`LangVO`) работают параллельно или заменяют стандартные, более мощные и поддерживаемые механизмы Drupal, что усложняет интеграцию, обновление и использование стандартных инструментов.
    *   **Жестко Закодированные Значения:** Множество конфигурационных параметров, ID, списков, правил редиректа, адресов email и т.д. прописаны прямо в коде.
    *   **Процедурный Код:** Обширное использование `.inc` файлов вместо ООП (сервисов, классов).
    *   **Использование `$GLOBALS`:** Для кэширования и передачи состояния.
*   **Проблемы с Качеством Кода:** Недостаточное комментирование, дублирование кода (например, `getDefPaymentTerms`), использование небезопасных практик (`@unserialize`, ручные `include_once`, недостаточная санитизация/экранирование в некоторых местах).
*   **Поддерживаемость и Читаемость:** Высокая сложность кастомных систем и недостаток документации/комментариев затрудняют понимание, отладку и внесение изменений.
*   **Производительность:** Потенциальные узкие места в запросах к БД (отсутствие/неоптимальность индексов, запросы `LIKE '%...%'`), (де)сериализация, синхронное обновление Read Model, отсутствие агрегации для ресурсов `Compo`.
*   **Отсутствие Тестирования:** Нет автоматических тестов, что повышает риск регрессий при изменениях.
*   **Устаревшая Платформа:** Drupal 7 и, вероятно, PHP 5.x не получают обновлений безопасности и ограничивают использование современных практик.

## 2. Краткосрочные Улучшения (Быстрые Победы, Критические Исправления)

**Приоритет: Высокий (Выполнять немедленно/в первую очередь)**

*   **[КРИТИЧЕСКИЙ] Устранение Уязвимости SMTP (Задача 4.3):** Удалить учетные данные SMTP из `s45_phuket_form_api.inc` и перенести в `settings.php`.
    *   *Влияние:* Критическое повышение безопасности. *Сложность:* Низкая.
*   **[ВЫСОКИЙ] Улучшение Документации и Комментирования (Задачи 1.*):** Добавить PHPDoc и inline-комментарии к ключевым классам и файлам (`AR`, `PhuketPropertyAR`, `PhuketAdminPropertyEditor`, `PhuketPropertyQuery`, `QueryFromEvents`, `Compo`, `CompoRepo`, `Path`, `Redirect`, `s45_path.module`, `s45_phuket_seo.inc`, `s45_phuket_lib.inc`, `s45_phuket_export.inc`, `s45_phuket_form_api.inc`, `template.php`, `property_photos_download.js` и т.д.). Актуализировать `PROJECT_DOCS.md`, создать README для модулей.
    *   *Влияние:* Значительное улучшение поддерживаемости и скорости разработки. *Сложность:* Низкая-Средняя (требует времени).
*   **[ВЫСОКИЙ] Улучшение Логирования и Обработки Ошибок (Задачи 5.*):** Заменить `dsm()` на `watchdog()`, убрать `@unserialize` с добавлением проверки и логирования, заменить кастомное логирование в скриптах на `watchdog()`, улучшить обработку ошибок в `CompoRepo::compoCreate`.
    *   *Влияние:* Улучшение стабильности, упрощение диагностики проблем. *Сложность:* Низкая-Средняя.
*   **[ВЫСОКИЙ] Вынос Простых Жестко Закодированных Значений (Задачи 6.* - частично):** Вынести очевидные и безопасные для изменения значения (ID локаций, ID мебели, ID опций, ключ sitemap) в константы или переменные Drupal.
    *   *Влияние:* Повышение гибкости и читаемости. *Сложность:* Низкая.
*   **[ВЫСОКИЙ] Составление Списка Зависимостей (Задача 8):** Создать полный список модулей/тем и их версий.
    *   *Влияние:* Основа для планирования обновлений. *Сложность:* Низкая.

## 3. Среднесрочные Задачи (Рефакторинг, Оптимизация)

**Приоритет: Средний (Выполнять после краткосрочных)**

*   **[СРЕДНИЙ] Рефакторинг Обработки Форм (Задачи 6.8, 6.9, 10.19):** Вынести адреса email и названия форм в конфигурацию. Исследовать `FormApi2` и `prSiteMailing`, рассмотреть переход на Drupal Mail API.
    *   *Влияние:* Улучшение гибкости, безопасности, поддерживаемости. *Сложность:* Средняя.
*   **[СРЕДНИЙ] Рефакторинг Корневых Скриптов (Частично) (Задачи 9.1, 7.2, 4.2):** Переписать генерацию XML для 1-2 фидов на `DOMDocument`/`XMLWriter`. Оптимизировать SQL-запросы в 1-2 фидах. Улучшить безопасность генерации HTML в описаниях.
    *   *Влияние:* Повышение надежности, производительности, безопасности фидов. *Сложность:* Средняя.
*   **[СРЕДНИЙ] Оптимизация Производительности (Частично) (Задача 7):** Проанализировать и оптимизировать 1-2 самых медленных SQL-запроса (вероятно, в поиске или `handle...` методах), добавив индексы. Включить и протестировать `Lazyloader`.
    *   *Влияние:* Заметное улучшение производительности в узких местах. *Сложность:* Средняя (требует анализа БД и тестирования).
*   **[СРЕДНИЙ] Улучшение Качества Кода (Задачи 1.2, 6.7, 10.11, 10.15, 10.18, 10.21):** Рефакторинг установки значений по умолчанию в `PhuketPropertyAR`, замена списков ID пользователей на роли, восстановление автосоздания `_s45_aliases`, устранение дублирования `getDefPaymentTerms`, исправление URL изображений в источнике, обеспечение `data-property-id` для JS.
    *   *Влияние:* Улучшение читаемости, надежности, поддерживаемости. *Сложность:* Низкая-Средняя.
*   **[СРЕДНИЙ] Улучшение Системы URL (Задачи 6.5, 10.13):** Вынести редиректы поиска в конфигурацию/БД. Исследовать и исправить/удалить обработку редиректов из `_s45_redirects`.
    *   *Влияние:* Повышение гибкости управления редиректами. *Сложность:* Средняя.

## 4. Долгосрочные Перспективы (Стратегические Изменения)

**Приоритет: Низкий (Требуют значительных ресурсов и планирования)**

*   **[ДОЛГОСРОЧНЫЙ] Рефакторинг Сериализованных Данных (Задача 10):** Полная миграция данных из сериализованных полей в реляционную структуру. **Это ключевой шаг для будущего развития и миграции.**
    *   *Влияние:* Кардинальное улучшение производительности запросов, целостности данных, поддерживаемости, возможности миграции. *Сложность:* Очень высокая.
*   **[ДОЛГОСРОЧНЫЙ] Миграция Кастомных Систем на Drupal API:**
    *   Перевод UI с `Compo` на Twig/Render API (Задача 11.4).
    *   Перевод кастомной системы переводов на `t()`/Locale API (Задача 11.1).
    *   Перевод кастомной системы URL на Pathauto/Redirect (Связано с 6.5, 10.13).
    *   Интеграция ресурсов `Compo` с агрегацией Drupal (Задача 11.3).
    *   Интеграция с `Metatag` (Задача 11.6).
    *   *Влияние:* Упрощение поддержки, использование стандартных инструментов, повышение совместимости, необходимо для миграции на D8/9+. *Сложность:* Очень высокая.
*   **[ДОЛГОСРОЧНЫЙ] Рефакторинг Архитектуры:**
    *   Перенос всей логики из корневых скриптов и `.inc` файлов в сервисы/классы/команды (Задачи 11, 11.5).
    *   Оптимизация/рефакторинг Event Sourcing (перенос обновлений в Cron/Queue, оптимизация `handle...`) (Задачи 10.3, 10.5, 10.6).
    *   Устранение `$GLOBALS` (Задачи 10.9, 10.14).
    *   *Влияние:* Улучшение структуры, тестируемости, производительности. *Сложность:* Высокая.
*   **[ДОЛГОСРОЧНЫЙ] Внедрение Автоматизированного Тестирования (Задача 12):** Настройка PHPUnit/Behat и написание тестов для критической функциональности.
    *   *Влияние:* Повышение стабильности, снижение риска регрессий, ускорение рефакторинга. *Сложность:* Средняя-Высокая (требует времени и навыков).
*   **[ДОЛГОСРОЧНЫЙ] Обновление Платформы (Задача 13):** Миграция на последнюю версию Drupal и PHP.
    *   *Влияние:* Безопасность, производительность, доступ к современным возможностям. *Сложность:* Очень высокая (требует выполнения большинства предыдущих долгосрочных задач).

## 5. Предложения по Новым Модулям

Эти модули могли бы улучшить структуру и управляемость проекта, следуя существующему стилю именования `s45_*`:

1.  **`s45_config_manager`:**
    *   **Назначение:** Предоставить централизованный UI (или Drush команды) для управления конфигурационными значениями, которые сейчас жестко закодированы (фильтры фидов, ID опций, списки email, ключ sitemap и т.д.). Мог бы использовать переменные Drupal (`variable_get/set`) или кастомную таблицу.
    *   **Польза:** Устранение жестко закодированных значений, упрощение настройки и развертывания между окружениями.

2.  **`s45_queue_processor`:**
    *   **Назначение:** Реализовать обработчики Drupal Queue API для выполнения ресурсоемких задач в фоновом режиме. Сюда можно было бы перенести обновление Read Model таблиц (из `QueryFromEvents::eventsHandle`) и генерацию фидов (из корневых скриптов).
    *   **Польза:** Улучшение отзывчивости сайта (убирает задержки при сохранении данных), повышение надежности фоновых задач.

3.  **`s45_feeds_manager`:**
    *   **Назначение:** Централизовать логику генерации всех фидов (как из корневых скриптов, так и из `s45_phuket_export.inc`). Мог бы предоставлять общий интерфейс/сервис для добавления новых фидов и управления их форматами (используя `DOMDocument`/`XMLWriter`).
    *   **Польза:** Устранение дублирования, стандартизация генерации фидов, упрощение добавления новых партнеров.

4.  **`s45_api_client`:**
    *   **Назначение:** Если планируются интеграции с внешними API (кроме существующих фидов), этот модуль мог бы инкапсулировать логику взаимодействия с ними (аутентификация, запросы, обработка ответов, кеширование, логирование ошибок).
    *   **Польза:** Централизация и стандартизация работы с внешними API.

5.  **`s45_refactoring_helpers`:**
    *   **Назначение:** Временный модуль, содержащий скрипты и утилиты для выполнения крупных рефакторингов (например, миграции сериализованных данных, обновления структуры таблиц через `hook_update_N`). После завершения рефакторинга модуль можно отключить/удалить.
    *   **Польза:** Организация и упрощение процесса выполнения сложных задач по рефакторингу.

---

Эта дорожная карта предоставляет структурированный подход к улучшению проекта, начиная с критических исправлений и заканчивая стратегическими архитектурными изменениями.