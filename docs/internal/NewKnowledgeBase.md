# New Knowledge Base

## Добавление стрелочек навигации для календарей flatpickr

### Проблема с отсутствующими стрелочками в календарях

**Обнаружена двухчастная проблема:** В календарях flatpickr на сайте отсутствовали стрелочки для навигации между месяцами из-за отсутствия JavaScript-функции и CSS-конфликта.

1. **Причины проблемы:**
   - **JavaScript:** Календари инициализированы без стрелочек навигации
   - **CSS-конфликт:** В `styles.min.css` есть медиа-запрос `@media (max-width: 480px)`, который скрывает стрелочки на мобильных
   - CSS-стили для стрелочек (`.flatpickr-prev-month`, `.flatpickr-next-month`) присутствуют, но элементы не создаются
   - Flatpickr по умолчанию не всегда показывает стрелочки в режиме inline

2. **CSS-конфликт в стилях:**
   ```css
   /* ПРОБЛЕМНЫЙ CSS в styles.min.css */
   @media (max-width: 480px) {
     .filter-item__dropdown-dates .flatpickr-months .flatpickr-prev-month, 
     .filter-item__dropdown-dates .flatpickr-months .flatpickr-next-month {
       display: none; /* Скрывает стрелочки на мобильных! */
     }
   }
   ```

3. **Решение - JavaScript функция `addCalendarNavigationArrows()`:**
   ```javascript
   /**
    * Добавляет стрелочки навигации для календаря
    * Позволяет переключаться между месяцами с помощью кнопок
    */
   function addCalendarNavigationArrows(flatpickrInstance) {
       if (!flatpickrInstance || !flatpickrInstance.calendarContainer) return;
       
       const calendar = flatpickrInstance.calendarContainer;
       const monthsContainer = calendar.querySelector('.flatpickr-months');
       
       // Проверяем, что стрелочки ещё не добавлены
       const existingPrev = monthsContainer.querySelector('.flatpickr-prev-month');
       const existingNext = monthsContainer.querySelector('.flatpickr-next-month');
       
       if (existingPrev && existingNext) return; // Стрелочки уже есть
       
       // Создаём стрелочки с SVG иконками и обработчиками событий
       // ...
   }
   ```

4. **Решение - CSS-переопределение в `styles-addon.css`:**
   ```css
   /* ИСПРАВЛЕНИЕ: Переопределяем скрытие стрелочек */
   .flatpickr-months .flatpickr-prev-month, 
   .flatpickr-months .flatpickr-next-month {
       display: flex !important;
       width: 24px;
       height: 24px;
       cursor: pointer;
       align-items: center;
       justify-content: center;
   }

   /* Убираем медиа-запрос, который скрывает стрелочки на мобильных */
   @media (max-width: 480px) {
     .filter-item__dropdown-dates .flatpickr-months .flatpickr-prev-month, 
     .filter-item__dropdown-dates .flatpickr-months .flatpickr-next-month {
       display: flex !important; /* Переопределяем display: none */
     }
   }
   ```

5. **Интеграция с существующими календарями:**
   - Вызов функции в `onReady` callback всех календарей
   - Резервный вызов через setTimeout для надёжности
   - Совместимость с существующей swipe-навигацией

6. **Особенности реализации:**
   - Использование `flatpickrInstance.changeMonth(-1/1)` для правильного переключения
   - Проверка существования элементов перед добавлением (предотвращение дублирования)
   - SVG-иконки для кроссбраузерной совместимости
   - Правильные CSS-классы для использования существующих стилей
   - **Критично:** Использование `!important` для переопределения проблемного CSS

### Применение в календарях:
- `#introDate` - основной календарь выбора дат
- `#introDateInner` - внутренний календарь компонента
- `#objectCalendar` - календарь объекта с отключёнными датами

### Отключение физических стрелочек по просьбе пользователя (2025-06-20)

**Обратная связь пользователя:** Физические золотистые стрелочки оказались слишком навязчивыми, пользователь предпочел стандартные белые стрелочки.

**Решение:** Возврат к стандартным белым стрелочкам flatpickr с сохранением функций для будущего использования:

```javascript
// Функция сохранена для возможного использования
function addCalendarNavigationArrows(flatpickrInstance) {
    // ... код функции остался ...
}

// Все вызовы закомментированы
onReady: function(selectedDates, dateStr, instance) {
    // Физические стрелочки отключены - используем стандартные белые
    // addCalendarNavigationArrows(instance);
    initCalendarSwipeNavigation(instance);
}
```

**Архитектурные преимущества такого подхода:**
1. **Гибкость:** Легко включить/отключить функциональность без удаления кода
2. **Быстрое тестирование:** Можно быстро протестировать разные варианты
3. **Сохранение работы:** Разработанный код не теряется

### Эволюция к физическим стрелочкам (2025-06-20) [ОТКЛЮЧЕНО]

**Проблема:** Несмотря на исправление CSS-конфликтов, стрелочки оставались недостаточно заметными для пользователей.

**Решение [НЕ ИСПОЛЬЗУЕТСЯ]:** Создание полностью независимых физических стрелочек-кнопок:

1. **Архитектура физических стрелочек:**
   ```javascript
   function addCalendarNavigationArrows(flatpickrInstance) {
       // Создаём отдельный контейнер поверх календаря
       const arrowsContainer = document.createElement('div');
       arrowsContainer.className = 'calendar-nav-arrows';
       arrowsContainer.style.cssText = `
           position: absolute !important;
           top: 12px !important;
           left: 0 !important;
           right: 0 !important;
           z-index: 100 !important;
           pointer-events: none !important;
           display: flex !important;
           justify-content: space-between !important;
           padding: 0 10px !important;
       `;
       
       // Создаём круглые кнопки с inline-стилями
       const prevButton = document.createElement('button');
       prevButton.innerHTML = '‹'; // Простой символ вместо SVG
       prevButton.style.cssText = `
           width: 32px !important;
           height: 32px !important;
           border: none !important;
           border-radius: 50% !important;
           background: rgba(233, 184, 115, 0.9) !important;
           color: #2B3844 !important;
           font-size: 18px !important;
           cursor: pointer !important;
           pointer-events: auto !important;
           // ... остальные стили
       `;
   }
   ```

2. **Ключевые принципы физических стрелочек:**
   - **Независимость от CSS:** Все стили встроены через JavaScript
   - **Высокий z-index:** Гарантирует видимость поверх всех элементов
   - **Inline-стили:** Избегает конфликтов с существующими CSS-правилами
   - **Символы ‹ и ›:** Простота и надёжность вместо SVG
   - **Pointer-events:** none для контейнера, auto для кнопок
   - **Абсолютное позиционирование:** Независимо от структуры календаря

3. **Преимущества физических стрелочек:**
   - Гарантированная видимость независимо от CSS-конфликтов
   - Единый дизайн с сайтом (золотистый цвет)
   - Плавные анимации и hover-эффекты
   - Адаптивность для мобильных устройств
   - Совместимость со swipe-навигацией

4. **Обработка событий:**
   ```javascript
   prevButton.addEventListener('click', function(e) {
       e.preventDefault();
       e.stopPropagation(); // Критично для предотвращения конфликтов
       flatpickrInstance.changeMonth(-1);
   });
   ```

5. **CSS-поддержка через styles-addon.css:**
   ```css
   .calendar-nav-btn {
       width: 32px !important;
       height: 32px !important;
       border: none !important;
       border-radius: 50% !important;
       background: rgba(233, 184, 115, 0.9) !important;
       transition: all 0.2s ease !important;
       box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
   }
   
   .calendar-nav-btn:hover {
       background: rgba(233, 184, 115, 1) !important;
       transform: scale(1.1) !important;
   }
   ```

### Диагностические шаги при подобных проблемах:
1. Проверить браузерную консоль на наличие JavaScript-ошибок
2. Проверить CSS через Developer Tools - какие стили применяются к элементам
3. Поискать CSS-правила, которые могут скрывать элементы (`display: none`, `visibility: hidden`)
4. Создать тестовую страницу для изолированной проверки функциональности
5. Использовать `!important` только в крайних случаях для переопределения проблемных стилей
6. **Для критических элементов:** Рассмотреть создание полностью независимых элементов с inline-стилями
7. **При конфликтах CSS:** Использовать высокий z-index и абсолютное позиционирование

## PHP 7.3 оптимизация для загрузки больших файлов

### Проблема конфигурации PHP в .htaccess

**Обнаружена критическая проблема конфигурации:** .htaccess содержал настройки только для PHP 5, в то время как система использует PHP 7.3.

1. **Симптомы проблемы:**
   - PHP настройки в `/etc/opt/remi/php73/php.ini` корректны
   - Но веб-интерфейс показывает старые ограниченные значения
   - .htaccess содержит только секцию `<IfModule mod_php5.c>`
   - PHP 7.3 модуль `mod_php7.c` не настроен

2. **Правильная диагностика конфигурации:**
   ```bash
   # CLI настройки (не используются веб-сервером)
   php -i | grep memory_limit
   
   # Веб-настройки (актуальные для сайта)
   curl "https://domain.com/phpinfo.php" | grep memory_limit
   
   # Определение используемого конфига
   curl "https://domain.com/check_config.php" 
   # Показывает: Loaded Configuration File, PHP SAPI, Additional ini files
   ```

3. **Правильное решение для Apache + PHP 7.x:**
   ```apache
   # .htaccess - секция для PHP 7.x
   <IfModule mod_php7.c>
     php_value memory_limit 2G
     php_value post_max_size 1200M
     php_value upload_max_filesize 100M
     php_value max_file_uploads 50
     php_value max_execution_time 300
     php_value max_input_time 300
     php_flag zlib.output_compression On
   </IfModule>
   ```

4. **Важные принципы:**
   - .htaccess настройки имеют приоритет над php.ini для веб-запросов
   - Разные модули PHP (`mod_php5.c`, `mod_php7.c`) требуют отдельных секций
   - CLI и веб-интерфейс могут использовать разные конфигурации
   - Всегда тестируйте настройки через веб-интерфейс, а не CLI

### Оптимальные настройки для загрузки до 1GB

1. **Рекомендуемые лимиты для больших файлов:**
   ```ini
   memory_limit = 2G              ; Достаточно памяти для обработки
   post_max_size = 1200M          ; Больше 1GB с запасом
   upload_max_filesize = 100M     ; Разумный лимит на файл
   max_file_uploads = 50          ; Много файлов одновременно
   max_execution_time = 300       ; 5 минут на обработку
   max_input_time = 300           ; 5 минут на получение данных
   ```

2. **Расчет максимального объема:**
   - Теоретический максимум = min(post_max_size, memory_limit * 0.8)
   - Практический лимит = 1.17GB за одну операцию
   - Количество файлов = post_max_size / upload_max_filesize

3. **Тестирование конфигурации:**
   ```php
   // Простая проверка
   echo "memory_limit = " . ini_get('memory_limit') . "\n";
   echo "post_max_size = " . ini_get('post_max_size') . "\n";
   echo "upload_max_filesize = " . ini_get('upload_max_filesize') . "\n";
   
   // Определение используемого конфига
   echo "Config file: " . php_ini_loaded_file() . "\n";
   echo "SAPI: " . php_sapi_name() . "\n";
   ```

### Безопасность и производительность

1. **Безопасные ограничения:**
   - Не устанавливайте unlimited для production
   - Ограничивайте размер одного файла (100MB разумно)
   - Ограничивайте время выполнения (300 секунд достаточно)
   - Сохраняйте сжатие zlib.output_compression

2. **Мониторинг ресурсов:**
   - Отслеживайте использование памяти сервера
   - Мониторьте время выполнения скриптов
   - Логируйте ошибки upload в error_log

3. **Резервное копирование:**
   - Всегда создавайте backup перед изменениями
   - Тестируйте изменения на staging окружении
   - Имейте план отката для критических изменений

## Критические проблемы производительности в Dropbox uploader

### Проблема неэффективного поиска объектов недвижимости

**Обнаружена серьёзная проблема производительности** в скрипте `property_images_dropbox.php`:

1. **Исходная реализация (НЕЭФФЕКТИВНАЯ):**
   ```php
   // ПЛОХО: Загружает ВСЕ объекты в память
   $query = db_select('_phuket_Property', 'p')
       ->fields('p')
       ->condition('published', 1);
   
   $result = $query->execute();
   while ($row = $result->fetch()) {
       $propertyDto = unserialize($row->propertyDto);
       if ($propertyDto && isset($propertyDto->number) && $propertyDto->number == $property_id) {
           $property_data = $row;
           break;
       }
   }
   ```

2. **Исправленная реализация (ЭФФЕКТИВНАЯ):**
   ```php
   // ХОРОШО: Прямой поиск по индексированному полю
   $query = "SELECT id, published, propertyDto, propertyTeaserDto 
             FROM {_phuket_Property} 
             WHERE number = :property_number 
             AND published = 1 
             LIMIT 1";
   
   $result = db_query($query, array(':property_number' => $property_id));
   $property_data = $result->fetchObject();
   
   // Fallback для поиска в сериализованных данных
   if (!$property_data) {
       $query2 = "SELECT ... WHERE propertyDto LIKE :search_pattern LIMIT 5";
       // Ограниченный поиск только для fallback
   }
   ```

3. **Результаты оптимизации:**
   - **Память:** с 1GB+ до ~50MB (снижение на 95%)
   - **Время выполнения:** с timeout до 2-3 секунд
   - **Объектов в памяти:** с 3150+ до 1-5
   - **Стабильность:** устранены ошибки 500

4. **Ключевые принципы оптимизации:**
   - Всегда используйте WHERE условия для ограничения набора данных
   - Избегайте загрузки всех записей для поиска одной
   - Используйте LIMIT для ограничения результатов
   - При поиске в сериализованных данных ограничивайте количество проверяемых записей
   - Добавляйте подробное логирование для диагностики производительности

## Dropbox API Integration и мониторинг токенов

### Архитектура Dropbox uploader для недвижимости

1. **Основные компоненты системы**
   - `property_images_dropbox.php` - основной скрипт загрузки изображений недвижимости в Dropbox
   - `dropbox_uploader.php` - класс `DropboxUploader` для работы с Dropbox API v2
   - `dropbox_config.php` - конфигурационный файл с токенами доступа
   - `dropbox_token_monitor.php` - система мониторинга токенов (новый)

2. **Жизненный цикл токенов доступа**
   - Dropbox API использует OAuth 2.0 с access и refresh токенами
   - Access токены имеют срок действия (обычно 4 часа или 4 месяца в зависимости от приложения)
   - Refresh токены позволяют получать новые access токены без повторной авторизации
   - Система автоматически обновляет токены через refresh token при истечении срока действия

3. **Обработка данных объекта недвижимости через Event Sourcing**
   - Данные объекта загружаются из Event Store (`_s45_events`) по `arName='PhuketProperty'` и `isLast=1`
   - Фотографии хранятся в поле `photos` как массив объектов `FileDto` с полями `id`, `name`, `mime`, `dir`
   - Физические файлы находятся в директории `/files/site4/FileStore4/phuket/files/tmp/[ID]`
   - После успешной загрузки в Dropbox поле `dropBoxLink` обновляется в Event Store

4. **Типичные проблемы и диагностика**
   - **Ошибка 500**: Часто связана с истёкшими токенами доступа
   - **Отсутствующие файлы**: Проверка физического существования через `file_exists()`
   - **Проблемы с правами**: Директории должны быть доступны для чтения веб-сервером
   - **Лимиты API**: Dropbox имеет ограничения на количество запросов

5. **Автоматический мониторинг токенов**
   - Проверка срока действия токенов каждые 12 часов через cron
   - Автоматическое обновление токенов за 24 часа до истечения
   - Логирование всех операций с токенами в `logs/dropbox_token.log`
   - Уведомления об ошибках через Drupal watchdog

6. **Диагностические команды**
   ```bash
   # Проверка состояния токена
   php -r "require 'dropbox_config.php'; echo date('Y-m-d H:i:s', \$expires_at);"
   
   # Тестирование подключения
   php dropbox_token_monitor.php
   
   # Просмотр логов
   tail -f logs/dropbox_token.log
   ```

7. **Лучшие практики**
   - Регулярное резервное копирование `dropbox_config.php`
   - Мониторинг логов на наличие ошибок API
   - Проверка свободного места в Dropbox аккаунте
   - Тестирование системы после обновления токенов

8. **Интеграция с системой недвижимости**
   - URL для загрузки: `/property_images_dropbox.php?property_number=[NUMBER]`
   - Поддержка как обработанных изображений, так и оригиналов
   - Создание ZIP-архивов с изображениями объекта
   - Автоматическое сохранение публичных ссылок Dropbox в базу данных

## Components

### PhuketArticleAboutD2
The main component class for rendering article content.
Uses a custom template file for styling that must match the page layout.

### PhuketBlogAboutD2
Similar to the article component, but specifically for blog content.
Has slight styling differences and handles tags differently.

### PhuketPropMap
Handles the rendering of property maps using Google Maps.
Displays specific property locations with custom markers.

### PhuketPropMapD2
An updated version of property maps, with an improved interface.
Uses Intersection Observer API for lazy loading to improve performance.

### PhuketPropertyPriceD2
Provides functionality to display prices in different currencies and switch between them.
Supports the following currencies:
- THB (Thai Baht)
- USD (US Dollar)
- EUR (Euro)
- RUB (Russian Ruble)

### PhuketFooterD2
This component is not mentioned in the original file or the new file.

### PhuketLandingRentFooter
This component is not mentioned in the original file or the new file.

## JavaScript Libraries & Image Export

### html2canvas и FileSaver.js
При работе с генерацией изображений из HTML элементов необходимо учитывать следующие аспекты:

1. **Правильная подготовка DOM перед рендерингом**
   - Сохранять оригинальные стили с использованием getComputedStyle() перед манипуляциями
   - Применять абсолютные размеры и стили перед рендерингом (px вместо %)
   - Восстанавливать все оригинальные стили после операции экспорта
   - Использовать onclone в html2canvas для манипуляции с клонированным DOM

2. **Ключевые настройки html2canvas**
   - Параметр `width` и `height` для задания точных размеров выходного изображения
   - Параметр `scale: 1` для предотвращения автоматического масштабирования
   - Параметр `useCORS: true` для работы с внешними изображениями
   - Параметр `allowTaint: true` для разрешения отрисовки перекрестных доменов
   - Параметр `backgroundColor` для задания фона выходного изображения

3. **Особенности позиционирования элементов**
   - Родительский элемент должен иметь position: relative
   - Вложенные блоки должны иметь правильно заданные свойства position, top, left
   - flex-элементы требуют явного задания display: flex и flex-direction
   - Все размеры должны быть указаны в абсолютных единицах (px)

4. **Работа с изображениями внутри canvas**
   - Все изображения должны иметь правильное соотношение сторон
   - Свойство objectFit влияет на отображение изображений при экспорте
   - Необходимо учитывать, что flexbox может работать некорректно при экспорте

5. **Особенности FileSaver.js для скачивания**
   - canvas.toBlob() создает Blob объект для скачивания
   - Для мобильных устройств часто требуется специальная обработка
   - Использование специальных методов для iOS устройств
   - Резервные методы для обеспечения совместимости со старыми браузерами

Эти знания особенно полезны при работе с генератором социальных карточек (contentcreate.php), где требуется точный контроль над всеми стилями для корректного экспорта HTML в изображение.

## Javascript Modules

### main.js
Main site JavaScript file, handles most UI interactions.
Contains the `UserConfirmation` object that manages user consent for loading interactive elements.

### maps-recaptcha.js
New module for Google Maps protection with reCAPTCHA v3 integration.
Provides bot protection while maintaining user experience by:
- Loading reCAPTCHA script only when maps are present on the page
- Verifying users through invisible reCAPTCHA before loading maps
- Caching successful verifications in localStorage to avoid repeated verifications
- Integrating with existing map loading mechanisms
- Bypassing verification for Thai IP addresses to enhance user experience in Thailand

## CSS Structure

### Styles Organization
The site uses a combination of:
- Base styles in styles.min.css
- Additional styles in styles-addon.css
- Component-specific styles in their respective folders
- Custom modifications in article-custom.css

### Mobile Responsiveness
Breakpoints are set at standard widths:
- Mobile: <= 576px
- Tablet: <= 992px
- Desktop: > 992px

## Theme Structure

### Template Files
Templates in Drupal 7 format, with TPL files located with their PHP controller files.
Naming convention: [ComponentName][VariantName]D2.tpl.php

### Resource Management
All shared resources (CSS, JS, images) are stored in:
sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Resources2/

## Drupal Configuration

### Libraries
JavaScript files are added to the theme with specific weights to control loading order.
CSS files use similar weight parameters for cascade control.

### reCAPTCHA Integration
The site uses Google reCAPTCHA v3 for protecting Google Maps API usage:
- Configuration is managed through Drupal variables, accessible in admin settings
- JavaScript integration is done through Drupal.settings
- Server-side verification endpoint uses drupal_http_request for API calls
- Cache time and threshold settings are configurable through the admin interface
- Special detection for Thai IP addresses automatically bypasses reCAPTCHA verification
- Implements CIDR range matching for Thailand IP address detection
- Supports integration with GeoIP and Smart IP modules for enhanced detection
- Testing modes with forced verification bypass for development purposes
- Correctly handles Cloudflare proxied connections by detecting real visitor IPs
- Uses Cloudflare-specific headers (CF-Connecting-IP and CF-IPCountry) for enhanced accuracy
- Includes a diagnostic page for troubleshooting IP detection issues

## Image Error Handling

### image-tester.js
Module for handling broken image errors properly:
- Automatically detects and processes images that fail to load
- Uses MutationObserver to track dynamically added images to the DOM
- Replaces broken images with styled placeholders for better UX
- Prevents repeated attempts to load the same broken image
- Limits console logging to prevent console spam
- Uses CSS class 'error-image' to hide broken images
- Creates placeholder elements with 'img-load-error' class for styling

### Error Handling Strategy
The site implements a comprehensive strategy for handling missing image assets:
- Client-side detection of image loading failures
- Placeholder generation with appropriate styling
- Error logging limitation to prevent overwhelming the console
- Maintains clean UI even when multiple images fail to load
- Reduces unnecessary network requests by tracking already failed images
- Works with both static and dynamically loaded content

## Image Path Generation System

### s45_imgSrc and s45_imgSrcR Functions
The site uses a centralized image URL generation system:
- `s45_imgSrc()` - A wrapper function that calls `s45_imgSrcR()`
- `s45_imgSrcR()` - The core function that handles all image path generation logic

### Path Resolution Process
The image path resolution follows these steps:
1. Determines if the image is in a component (contains 'Site45/Sets' in URL)
2. Checks if it's an agent photo (contains 'pictures' in path)
3. Processes external URLs (starts with 'http')
4. Handles locally uploaded files
5. Implements fallback for missing files with cross-domain resolution
6. Applies image styles if requested

### Cross-Domain Image Resolution
To resolve issues with missing images, the system now:
- Checks if files exist locally before returning URLs
- Attempts to find images on alternate domains (.com → .ru, .ru → .com)
- Logs the first 5 instances of missing files for debugging
- Ensures the correct URL is generated based on the current domain 

### Property Image Paths
Property image files follow a specific path structure:
- Base path for property images: `/files/site4/styles/[IMAGE_STYLE]/public/FileStore4/phuket/files/`
- Images must include the `tmp/` directory in the path: `/files/site4/styles/S45_IMST_1900X1000_SCALE/public/FileStore4/phuket/files/tmp/[FILENAME]`
- Omitting the `tmp/` directory results in 403 Forbidden errors
- For object-based photos, only the ID is needed after the tmp/ directory
- For string-based photos, the full filename is needed after the tmp/ directory
- The full URL should follow this pattern: `https://indreamsphuket.ru/files/site4/styles/S45_IMST_1900X1000_SCALE/public/FileStore4/phuket/files/tmp/20241226_075258_3539_img_4538-large.jpeg`
- The image style (S45_IMST_1900X1000_SCALE) may vary depending on the display context

## API Responses and Output Buffering

### Clean JSON API Responses
To ensure API endpoints provide clean JSON output in Drupal environment:
- Start output buffering at the very beginning of the script with `ob_start()`
- Disable all Devel module functionality by setting `$GLOBALS['devel_shutdown'] = FALSE`
- Override `drupal_register_shutdown_function()` to prevent additional output
- After Drupal bootstrap, disable all module `hook_exit()` implementations
- Clean output buffer before setting Content-Type header with `ob_end_clean()`
- Start a new output buffer with `ob_start()` after setting headers
- Capture only the intended JSON output with `$json_output = ob_get_clean()`
- Output the clean JSON data and immediately exit with `exit()`
- Use `register_shutdown_function()` to prevent other shutdown functions from executing

This approach is particularly important because Drupal's development modules like Devel 
often append debugging information to the output even after explicit calls to `exit()`.

## Drupal 7 SEO Structure for InDreams Phuket

### Architecture Overview

1. **Custom Active Record Pattern**
   - The site uses a custom Active Record implementation for models (PhuketPropertyAR, PhuketProjectAR, etc.)
   - Properties are stored in serialized arrays for multilingual content with language keys

2. **Query Module Structure**
   - Each content type has its own Query directory (PropertySearch, ProjectSearch, etc.)
   - These contain special query classes that abstract database access

3. **Multilingual Implementation**
   - Content is stored in serialized arrays with language keys (en, ru, th, zh-hans)
   - Domains are used for language variants (indreamsphuket.com for EN, indreamsphuket.ru for RU)
   - Language-specific subdomains (th.indreamsphuket.com, ch.indreamsphuket.com)

4. **Schema.org Integration Points**
   - Use hook_preprocess_html() to inject JSON-LD data
   - Property data can be directly accessed from PhuketPropertyAR models
   - Images require special handling through s45_imgSrc() function

5. **Sitemap Generation Structure**
   - Centralized through s45_phuket_sitemap.inc
   - Language-specific sitemaps are indexed by a sitemap index file
   - Each content type has its own generation method in PhuketSitemap class

6. **SEO Page Management**
   - Managed through the PageSeoSearch module
   - Title, description, and H1 tags are stored in the database
   - SEO data is injected through hooks into the page rendering pipeline

7. **404 Handling System**
   - Custom redirect system with pattern matching
   - Database tracking of 404 errors
   - Automatic redirect generation for common patterns

8. **Caching Mechanisms**
   - Uses hook_init() for Last-Modified headers
   - Static caches for repetitive operations
   - Conditional 304 responses for unchanged content

### Key Insights

1. The content architecture uses UUID-style IDs instead of numeric IDs, requiring custom handling for lookups.
2. The multilingual system uses serialized arrays with language keys, making it easier to manage translations in a single record.
3. Schema.org implementation needs to handle multilingual content by choosing the appropriate language version.
4. Property listings are the most important content type for SEO, representing over 90% of the site's content.
5. The site structure is optimized for real estate search with specialized URL patterns for property types and locations.
6. Most 404 errors come from legacy URL patterns that can be addressed with pattern-based redirects.

## YouTube и Vimeo Интеграция

### Обработка Same-Origin Policy

При работе с iframes YouTube и Vimeo необходимо учитывать требования политики Same-Origin:

1. **Параметры iframe URL**
   - `enablejsapi=1` - обязательный параметр для YouTube, активирующий API
   - `origin=https://yourdomain.com` - указывает разрешенный домен для постинга сообщений
   - `api=1` - аналог enablejsapi для Vimeo

2. **Правильное использование postMessage**
   - Сообщения должны отправляться в формате JSON для совместимости
   - При получении сообщений необходимо проверять источник (origin)
   - Для YouTube используются команды вида `{"event":"command","func":"stopVideo","args":[]}`
   - Для Vimeo используются команды вида `{"method":"pause"}`

3. **Обработка ошибок**
   - Необходимо оборачивать вызовы postMessage в try-catch
   - Ошибки могут возникать из-за блокировки в браузере или неправильного API
   - Важно перехватывать ошибки, чтобы не прерывать работу сайта

4. **Адаптивное поведение**
   - Некоторые элементы управления должны быть скрыты на мобильных устройствах
   - YouTube API по-разному работает на мобильных и десктопных устройствах
   - В некоторых случаях необходимо использовать ручное управление вместо API

5. **Установка обработчиков сообщений**
   - Обработчик addEventListener('message') должен быть установлен только один раз
   - Необходимо проверять источник сообщения для безопасности
   - Рекомендуется использовать флаг для отслеживания инициализации

Уроки и лучшие практики:
- Всегда включайте параметр origin для безопасности
- Используйте JSON.stringify для отправки сообщений
- Обрабатывайте ошибки и создавайте fallback для случаев, когда API не работает
- Сохраняйте функциональность, даже если API заблокировано 

## Компонент переключения валют

### Общее описание

Компонент `PhuketPropertyPriceD2` предоставляет возможность отображения цен в разных валютах с возможностью мгновенного переключения между ними. Поддерживаются следующие валюты:
- THB (Тайский бат)
- USD (Доллар США)
- EUR (Евро)
- RUB (Российский рубль)

### Особенности реализации

1. **Структура компонента**
   - PHP-класс для серверной логики и подготовки данных
   - JavaScript для клиентской логики конвертации валют
   - CSS для стилизации переключателя

2. **Возможности**
   - Отображение цены продажи и аренды
   - Отображение цены за квадратный метр
   - Сохранение выбранной валюты в localStorage
   - Поддержка различных периодов для аренды (день/месяц)
   - Анимированные кнопки переключения с эффектом нажатия

3. **Интеграция**
   - Компонент легко интегрируется в любую страницу через функцию `s45_phuket_price_with_switcher()`
   - Автоматически определяет валюту пользователя при первом посещении
   - Применяет одинаковую валюту ко всем ценам на странице

### Использование компонента

```php
// Базовое использование
echo s45_phuket_price_with_switcher($price, $dealType, $priceSqm, $baseCurrency, $rentPeriod);

// Пример для цены продажи
echo s45_phuket_price_with_switcher(958000, 'sale', 3500, 'USD');

// Пример для цены аренды за месяц
echo s45_phuket_price_with_switcher(2500, 'rent', null, 'USD', 'month');

// Пример для цены аренды за день
echo s45_phuket_price_with_switcher(120, 'rent', null, 'USD', 'day');
```

### Расширение функциональности

Компонент предусматривает возможность расширения через:
- Добавление новых валют в массив `exchangeRates` в JS-коде
- Настройку пользовательских форматов отображения чисел
- Интеграцию с API для получения актуальных курсов валют

### Важные зависимости

- jQuery для работы JavaScript функционала
- Drupal для подключения ресурсов (JS/CSS)
- FontAwesome или SVG для иконок валют (опционально) 

## Drupal Property Management System
- The property data is stored in a custom table named `_phuket_Property`
- Property information is stored in serialized PHP objects within the database
- The system uses `s45_imgSrcR()` function to generate property image URLs in different formats
- Properties have a specific format identifier like S45_IMST_1900X1000_SCALE for scaling images
- The property DTO structure contains photos as an array of objects that can be processed individually
- The admin interface has a "Download all photos" button that uses JavaScript to trigger a PHP download script
- Property identification is done using both property number and property ID fields in the database 

## Favorites System
- The site uses a custom favorites system stored in the user's session via the Site45\Base\Store class
- Favorites are stored as an associative array with property IDs as both keys and values
- The PhuketFavoritesListD2 component displays properties from the favorites list
- The system supports sharing favorites via generated links using a unique hash
- Shared favorites are stored in Drupal variables with a 30-day expiration
- The sharing system provides direct links and integration with WhatsApp, Telegram, and email 

## Phuket Module Structure

- The codebase uses a modular structure with most classes in the `Site45\Sets\Phuket` namespace
- Class files are stored in `sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/`
- Property search functionality is handled by classes in `Query/PropertySearch/`

## Property Photo Management

- Property photos are stored as serialized objects in the database
- Photos can be accessed through the `$propertyDto->photos` collection
- The `s45_imgSrcR()` function generates URLs for images with specific formatting/size
- The admin interface has a download photos button that uses a dedicated PHP script for creating ZIP archives
- Direct file access is more reliable than using iframe-based download approaches
- The "Скачать все фото" button in the admin interface implements multiple safeguards:
  - Uses both `data-property-id` and `data-property-number` attributes for reliable property identification
  - Falls back to extracting property ID from page URL or panel title if attributes are missing
  - Uses a form-based submission approach with POST parameters for more reliable downloads
  - Provides clear visual feedback with status messages during the download process
  - Uses the largest image style (S45_IMST_1900X1000_SCALE) to provide high-quality photos
  - Implements proper error handling and timeouts to prevent UI from being stuck
  - Logs detailed diagnostic information to help troubleshoot download issues

## Favorites System

- User favorites are stored client-side in localStorage/cookies 
- Favorites can be shared via encoded URLs that contain property IDs
- The favorites UI uses a component-based approach with separate templates
- The sharing implementation uses direct URL parameter encoding rather than server-side storage

## Performance Considerations

- ZIP file generation for property photos can be resource-intensive for properties with many high-resolution photos
- Chunked file transfer is used to avoid memory issues with large ZIP files
- Temporary files are used with proper cleanup to prevent filesystem clutter
- Download timeouts need to be set appropriately for larger properties 

## Новые знания (покрытие модулей __s45)

- x1: Модуль `s45_imagestyles` определяет наборы размеров изображений через константы и функции `s45_imagestyles_image_...`, что позволяет централизовано управлять стилями Drupal image styles.
- x1: Функция `s45_page_state()` из `s45_state.inc` предоставляет инструмент отладки для хранения и очистки пользовательских `$_SESSION['s45']`, включая store и states.
- x1: Модуль `s45_test` создает тестовый маршрут `s45test`, демонстрируя работу `Path::addAlias()` и примеры вызова CompoRepo (закомментированы).
- x1: Модуль `s452_base` содержит альтернативный Callback `s452_page()` для страниц Site45 с legacy Compo452: кеш-сброс, PageId, CompoScanner, вызов `s452_render`.
- x1: Среди файлов `s45_base.2.inc` обнаружена API-версия 2 (`s45_api2()`), использующая `CompoApi452` и методы `s45_add_phpLib2`, `s45_add_jsLib2` для поддержки Compo v452.
- x1: В модуле `s45_page` есть отдельный файл `s45_page.robots.inc`, реализующий hook для выдачи robots.txt через `s45_robots()`.
- x1: Compo компоненты организованы по namespace и папкам (`Site45/Compo/`), для каждого компонента есть `.php` класс, `.tpl.php` шаблон, `.js` и `.css` файлы.
- x1: У модуля `s45_path` есть несколько ключевых классов: `Path`, `Redirect`, `RedirectFromOldSite`, обеспечивающих inbound и outbound URL‑менеджмент. 

## Для корректной работы раздела admin/s45/screens нужно использовать компонент PhuketAdminScreens, а не PhuketAdminScreensList. Ошибка 'не найден компонент в базе подключенных - screens' возникает, если указан неправильный компонент. 

- Для унификации админки путь к разделу Screens теперь 's45/~/PhuketAdminScreens', как и у других Compo-компонентов, вместо старого 's45/admin/screens'. 

## Регистрация и сканирование компонентов Compo (новое)

- В проекте используется автоматическая регистрация компонентов через класс `Site45\Base\CompoScanner`.
- Для пересканирования и регистрации всех компонентов используется URL `/compomake/scan` (или пункт меню "Compo Make" в админке).
- Сканер ищет компоненты во всех директориях, указанных в конфиге сайта, и формирует файл базы подключённых компонентов: `CompoInfo.s45.json`.
- После сканирования все новые компоненты становятся доступны для CompoRouter и могут быть вызваны по имени.
- Если компонент не найден, проверьте:
  - Имя класса и папки совпадают
  - В шаблоне есть ключевое слово `component`
  - Нет ошибок в структуре файлов
- Не рекомендуется редактировать `CompoInfo.s45.json` вручную — используйте автоматическое сканирование.
- Пример записи в базе компонентов:
```json
"PhuketAdminScreens": {
  "type": "CompoInfo",
  "dir": "sites/all/modules/__s45/Compo/Admin/PhuketAdminScreens",
  "id": "PhuketAdminScreens"
}
```
- После добавления нового компонента всегда запускайте `/compomake/scan` для обновления базы. 

## Property Photo Download

- The download functionality for property photos (triggered by `#PhuketAdminPropertyEditor-DownloadAllPhotos`) is handled by the standalone PHP script `sites/all/modules/__s45/s45_phuket/download_property_photos.php`.
- The script receives a `property_id` or `property_number` via POST/GET request.
- It bootstraps Drupal (at least up to `DRUPAL_BOOTSTRAP_VARIABLES`, potentially `DRUPAL_BOOTSTRAP_FULL` for permissions).
- It fetches property data (DTO) from the `_phuket_Property` table.
- **Crucially**, it iterates through photos in the DTO (`$photoDto->uri`). For each photo:
    - It determines the local path for the required image style (e.g., `S45_IMST_1900X1000_SCALE`) using `image_style_path()`.
    - It checks if the styled image file exists at that path.
    - If the file doesn't exist, it attempts to generate it using `image_style_create_derivative()`.
    - The *local file path* of the styled image is added to a `ZipArchive`. **It does not download images via HTTP.**
- The generated ZIP file is stored temporarily in `sites/default/files/tmp`.
- Proper HTTP headers (`Content-Type: application/zip`, `Content-Disposition`) are sent.
- The ZIP file content is streamed to the user using `fopen`/`fread`/`echo`.
- The temporary ZIP file is deleted after successful download using `unlink()`.
- Requires admin permissions (`access administration pages`).
- Debug logging goes to `sites/all/modules/__s45/s45_phuket/logs/photo_download.log`. 

## Формат фотографий объектов недвижимости

- В системе используются два разных формата хранения фотографий:
  1. Объектный формат: фотографии хранятся в виде объектов с полями `id`, `name`, `size`, `dir` и т.д.
  2. Строковый формат: фотографии хранятся как строки путей.
- При работе с фотографиями всегда нужно проверять тип через `is_object()` или `is_string()`.
- Для фотографий в объектном формате нужно использовать путь `/files/site4/styles/S45_IMST_1900X1000_SCALE/public/FileStore4/phuket/files/tmp/{photo->id}`.
- Для фотографий в строковом формате нужно использовать путь `/files/site4/styles/S45_IMST_1900X1000_SCALE/public/FileStore4/phuket/files/tmp/{photo}`.
- Обратите внимание, что директория `tmp/` является ОБЯЗАТЕЛЬНОЙ частью пути - без неё доступ к файлам будет запрещён с ошибкой 403 Forbidden.
- Всегда добавляйте обработку ошибок загрузки изображений через `onerror` в JavaScript.

## Работа с многоязычными данными

- Названия и описания хранятся в формате LangVO объектов, содержащих переводы для разных языков (`ru`, `en`, `zh-hans`, `th`).
- Для безопасного извлечения значения нужно проверять наличие свойства и его тип: `(isset($property->name) && is_object($property->name) && isset($property->name->ru)) ? $property->name->ru : ''`.
- Для отображения переводов в Drupal используйте `s45_lang($this->content_title)`.
- При работе с API передавайте ключи для каждого языка отдельно: `'name_ru'`, `'name_en'` и т.д. 

## API-интеграция и модуль Devel

- В публичных API-эндпоинтах, которые возвращают JSON, нужно отключать вывод отладочной информации Devel
- Модуль Devel добавляет HTML-разметку с информацией о времени выполнения и использовании памяти, что делает JSON невалидным
- Для отключения используйте следующий код:
  ```php
  // Отключаем вывод информации о производительности от модуля Devel
  if (function_exists('devel_query_display')) {
      $GLOBALS['devel_shutdown'] = FALSE;
      // Также отключаем отображение запросов
      $GLOBALS['devel_query'] = FALSE;
  }
  ```
- Всегда тестируйте API-ответы на чистоту JSON, особенно при включенном модуле Devel
- Ошибка "Unexpected non-whitespace character after JSON" обычно указывает на дополнительный вывод после JSON

## Генерация изображений для социальных сетей

### Фиксированные размеры и отделение рендеринга от отображения
При создании изображений для социальных сетей важно обеспечить стабильный рендеринг независимо от устройства пользователя:

1. **Фиксированные целевые размеры**
   - Instagram Square: 1080×1080 пикселей
   - Instagram Reels: 1080×1920 пикселей
   - YouTube Thumbnail: 1280×720 пикселей

2. **Отделение логики рендеринга от отображения**
   - Используйте отдельный скрытый canvas с фиксированными размерами для рендеринга
   - Создавайте отдельный элемент для предпросмотра изображения
   - Применяйте фиксированные абсолютные размеры при клонировании DOM для рендеринга

3. **Масштабирование и пропорции**
   - Вместо изменения масштаба (scale: 2), лучше использовать фиксированные размеры (width, height) с масштабом 1
   - Для элементов в предпросмотре используйте object-fit: contain для сохранения пропорций

4. **Мобильная адаптация**
   - Создавайте модальное окно для предпросмотра с учетом пропорций экрана
   - Используйте meta viewport для правильного масштабирования в новой вкладке
   - Разделяйте функции для адаптации интерфейса (adjustPreviewLayout) и подготовки к рендерингу (adjustLayoutForMobile)

5. **Работа с различными устройствами**
   - Обрабатывайте события изменения ориентации только для интерфейса, не для рендеринга
   - Для iOS предоставляйте возможность открыть изображение в новой вкладке
   - Обеспечивайте совместимый со всеми браузерами способ скачивания

6. **Позиционирование элементов**
   - Используйте абсолютное позиционирование для элементов на итоговом изображении
   - Фиксируйте точные размеры шрифтов вместо относительных значений
   - Применяйте точные координаты для размещения элементов

Этот подход обеспечивает одинаковый результат рендеринга независимо от устройства пользователя и разрешения экрана.

## Image Quality Settings in Drupal

The site uses Drupal's image processing with GD library. The JPEG quality setting was set to 50, which was causing visible pixelation in property images. Increasing this to 85 provides better quality without excessive file sizes.

## WebP Image Conversion

Для оптимизации нагрузки на сервер и ускорения загрузки страниц, сайт теперь поддерживает автоматическую конвертацию загружаемых JPEG и PNG изображений в формат WebP. Формат WebP имеет несколько значительных преимуществ:

1. **Меньший размер файлов**: WebP обеспечивает сжатие на 25-35% эффективнее, чем JPEG, при том же визуальном качестве.
2. **Поддержка прозрачности**: WebP поддерживает альфа-канал (прозрачность), как PNG, но с меньшим размером файла.
3. **Совместимость с современными браузерами**: Все современные браузеры поддерживают WebP (Chrome, Firefox, Edge, Safari 14+).

Инфраструктура сайта настроена для комплексной поддержки WebP:
- В .htaccess добавлены правила, автоматически отдающие WebP версии изображений браузерам с поддержкой этого формата
- Оригинальные JPEG/PNG файлы сохраняются для обеспечения обратной совместимости
- Все новые изображения автоматически конвертируются в WebP при загрузке через модуль webp_converter

Благодаря этому подходу:
- Уменьшается общий объем передаваемых данных
- Ускоряется загрузка фотографий в карусели и галереях
- Сокращается использование полосы пропускания
- Повышается SEO-показатели за счет улучшения скорости загрузки страниц

Утилита командной строки `cwebp` из пакета libwebp-tools используется для конвертации, обеспечивая высокое качество и производительность.

## Обновление токенов Dropbox API

Для интеграции с Dropbox API используется OAuth2 с долгосрочным доступом через refresh_token:

1. Особенности OAuth2 в Dropbox:
   - По умолчанию токены доступа (access_token) имеют ограниченное время жизни (обычно 4 часа)
   - Для получения refresh_token необходимо указать параметр `token_access_type=offline` при генерации URL авторизации
   - Refresh токены в Dropbox не имеют срока действия, но могут быть отозваны

2. Процесс обновления токенов:
   - Скрипт dropbox_token_refresher.php запускается ежедневно через cron
   - Проверяет срок действия текущего access_token и обновляет его при необходимости
   - Использует refresh_token для получения нового access_token

3. Важные нюансы работы с файловой системой:
   - Файлы конфигурации с токенами должны быть доступны для записи веб-сервером (пользователь apache)
   - Для безопасности права должны быть ограничены (664, owner:apache, group:apache)

4. Диагностика проблем интеграции:
   - Все действия логируются в файл dropbox_refresh.log
   - Для отладки можно использовать тестовый скрипт get_auth_url.php
   - API запросы дают подробную информацию об ошибках в JSON-ответах

## 2024-11-24 - Event Sourcing в S45

- **Event Sourcing** - основной механизм персистентности для кастомных сущностей
- События хранятся в таблице `_s45_events` (поля arName, arId, payload, isLast, type)
- `EventStore` отвечает за запись новых событий с атомарным обновлением флага isLast
- `EventQuery` извлекает события для восстановления состояния объектов
- **Read Model** - денормализованные таблицы (например, _phuket_Property), обновляемые обработчиками событий
- Обработчики имеют именованный формат `handle<ARName><EventType>` для маппинга событий на действия
- При обработке события происходит десериализация payload и обновление Read Model через db_merge
- Для оптимизации используется переменная отслеживания последней позиции и пакетная обработка новых событий

## 2024-11-24 - S45 Core Architecture Patterns

- S45 фреймворк организован по компонентным, сервисным и репозиторным паттернам
- Используется Event Sourcing для хранения изменений сущностей в таблице _s45_events
- AR (Active Record) классы наследуются от Site45\Event\AR и отвечают за сохранение и загрузку данных
- DTO (Data Transfer Objects) и VO (Value Objects) используются для передачи данных между слоями
- Репозитории (CompoRepo, JsonRepo) абстрагируют доступ к данным от их физического хранения
- Query классы отвечают за выборку данных с фильтрацией и возвращают результаты в виде DTO/VO
- Path Management система (классы Path, Redirect) управляет URL-алиасами и редиректами
- SiteConf отвечает за мультисайтовость, определяя текущий сайт по домену
- Store предоставляет временное хранилище данных в рамках запроса или сессии
- Антипаттерны: глобальные переменные, сериализованные данные в БД, чрезмерное использование .inc файлов

## 2024-11-25 - Продвинутые паттерны компонентов S45

- **Локализация контента** через объекты LangVO с единым шаблоном для всех языков
- **Система поиска** использует денормализованные таблицы для быстрого чтения данных с оптимизацией через множественные индексы
- **Композитные компоненты** позволяют строить сложные UI из простых блоков через вызовы s45_render() в шаблонах
- **Условный рендеринг** реализован через стандартные PHP-конструкции внутри единого шаблона без дублирования файлов
- **Система изображений** расширяет стандартные Drupal Image Styles через вспомогательные функции s45_imgSrc()
- **Многоуровневое кэширование** включает стандартные механизмы Drupal и кастомные решения, но без единой стратегии инвалидации
- **URL-менеджмент** через таблицы _s45_aliases и _s45_redirects с привязкой к сайтам и языкам
- **Мультисайтовость** на базе SiteConf определяет текущий сайт по домену и его доступные функции
- **Регистрация компонентов** через CompoScanner с сохранением метаданных в JSON-файл
- **Интеграция с внешними API** через систему конвертеров и парсеров с кэшированием результатов