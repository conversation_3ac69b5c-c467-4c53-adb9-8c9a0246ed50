# CHANGELOG

## [1.0.4] - 2024-11-25

### Documentation & IDE Support

- Enhanced IDE integration by adding detailed S45 framework architecture documentation:
  - Created comprehensive documentation for S45 core architectural patterns
  - Added detailed explanation of AR/DTO/VO patterns used in the codebase
  - Documented Repository and Query patterns
  - Added documentation for Path Management system
  - Documented SiteConf configuration system
  - Added Store pattern documentation for temporary data storage
  - Documented file structure and naming conventions
  - Identified and documented antipatterns and complexity issues
  - Created detailed documentation of Event Sourcing system including handlers and Read Model updates
  - Added examples of typical Event Sourcing patterns and optimization techniques
  - Created detailed rules for localization system with LangVO and s45_lang()
  - Documented property search system and query optimization patterns
  - Added rules for composite components and nested rendering patterns
  - Documented conditional rendering in templates
  - Added rules explaining image styles system and optimization
  - Created rules for understanding multilevel caching strategies
  - Documented path management and URL aliases system
  - Added detailed explanation of multisite configuration through SiteConf
  - Documented component scanning and registration system
  - Added rules for SEO implementation and external API integrations

## [1.0.3] - 2024-11-XX

### Новый функционал

- **Интеграция с Dropbox для фотографий объектов**
  - Создан скрипт property_images_dropbox.php для загрузки фотографий на Dropbox
  - Добавлена возможность автоматической генерации публичных ссылок на загруженные файлы
  - Интеграция с полем dropBoxLink в форме редактирования объекта
  - Добавлен JavaScript для удобного добавления ссылок на фотографии из формы
  - Улучшена функциональность: фотографии загружаются по отдельности в папку на Dropbox вместо ZIP-архива
  - Добавлена кнопка "Загрузить в Dropbox" на русском языке рядом с полем
  - Улучшено определение ID объекта при загрузке фотографий

- **Скачивание фотографий в один клик**
  - Создан скрипт property_images_download.php для скачивания всех фотографий объекта
  - Добавлен простой веб-интерфейс download_property_images.html для удобного скачивания
  - Реализовано автоматическое создание ZIP-архива с фотографиями объекта
  - Добавлена поддержка всех опубликованных объектов недвижимости
  - Реализовано скачивание фотографий в высоком разрешении 1900x1000

### Улучшения и исправления

- **Генератор карточек для соцсетей**
  - Исправлена проблема с искажением изображений при скачивании (сжатие и смещение влево)
  - Полностью переработан механизм рендеринга с использованием изолированного клона DOM вне видимой области
  - Добавлен детальный контроль над стилями всех элементов для корректного отображения в скачиваемом изображении
  - Реализовано точное позиционирование специфических элементов для каждого формата карточки
  - Устранена зависимость результата от условий отображения в браузере пользователя
  - Улучшена работа с изображениями внутри карточки для корректного масштабирования
  - Оптимизирована работа с flexbox-элементами для предотвращения искажений
  - Улучшено восстановление DOM после операций экспорта для предотвращения визуальных артефактов

## [1.0.2] - 2023-07-08

### Новый функционал

- **Переключатель валют**
  - Добавлен стильный переключатель валют с золотистым дизайном в стиле сайта
  - Реализовано автоматическое сохранение выбранной валюты в localStorage
  - Добавлена поддержка THB, USD, EUR и RUB валют
  - Реализовано мгновенное конвертирование цен в реальном времени
  - Переключатель добавлен на страницы недвижимости

## [1.0.1] - 2023-07-07

### Bugfixes

## 2023-09-12
- Fixed bug in property list display with long titles
- Made image galleries fully responsive on mobile
- Improved cache settings for better performance

## 2023-09-11
- Added support for new property types
- Fixed validation in contact forms
- Added new icons for amenities

## 2023-09-10
- Initial setup of new design system
- Migrated property database to new structure
- Set up multilingual support

## 2023-05-25
- Modified CSS for header tags to maintain original hierarchy in article template
- Added new CSS file article-custom.css for custom header styles
- Modified blog template to maintain original header hierarchy

## 2023-05-26
- Added reCAPTCHA v3 integration for protecting Google Maps API usage
- Created maps-recaptcha.js for handling reCAPTCHA verification
- Added admin configuration panel for reCAPTCHA settings
- Modified map component to use reCAPTCHA verification before loading
- Implemented server-side verification endpoint for reCAPTCHA tokens
- Added automatic reCAPTCHA bypass for Thai IP addresses
- Implemented detection of Thailand IP ranges
- Enhanced IP detection to properly work with Cloudflare
- Added diagnostic page for troubleshooting IP detection

## 2023-05-27
- Fixed issue with reCAPTCHA initialization and multiple attempts
- Added global flag to prevent re-initialization of map component
- Improved error handling in reCAPTCHA verification
- Added CSS and JavaScript for handling broken image errors
- Implemented image error handler to prevent console spam
- Added placeholder for broken images with improved UX
- Limited error logging to first 5 image errors to prevent console spam

## 2023-05-28
- Fixed core issue with image paths and domains in s45_imgSrcR function
- Added cross-domain image fetching when files don't exist locally
- Implemented fallback mechanism for missing images across domains
- Added logging for missing images to help track down problematic URLs
- Limited logging to first 5 missing files to prevent log flooding

## [0.0.1] - 2024-05-XX
### Безопасность
- Добавлена защита с использованием reCAPTCHA v3 для загрузки Google Maps API
- Добавлен обход защиты reCAPTCHA для тайских IP-адресов
- Исправлена ошибка инициализации путей с использованием drupal_get_path()

### Оптимизация
- Добавлена функция для обработки ошибок загрузки изображений и предотвращения повторной инициализации
- Улучшена обработка Cloudflare IP-адресов с использованием заголовка X-Forwarded-For
- Добавлены диагностические сообщения в watchdog для отслеживания reCAPTCHA инициализации

### Интерфейс пользователя
- Добавлен индикатор загрузки для карт Google Maps
- Добавлена обработка ошибок для отображения уведомлений пользователю при проблемах с reCAPTCHA 

## [1.0.0] - 2023-07-06

### SEO Improvements

- **Multilingual SEO Implementation**
  - Added proper hreflang implementation with x-default tags
  - Created path translation function for consistent URLs across languages
  - Fixed language tags to support all site languages (en, ru, th, zh-hans)

- **Sitemap Enhancement**
  - Created sitemap index file referencing language-specific sitemaps
  - Fixed empty sitemaps by adding missing content types
  - Added automatic sitemap generation via cron
  - Added proper image support in sitemaps for properties
  - Implemented proper language handling in all sitemaps

- **Schema.org Implementation**
  - Added RealEstateListing schema.org markup for properties
  - Added Residence schema.org markup for projects
  - Implemented schema across all language variants
  - Added image, price, and location data to schema

- **Performance & Caching Improvements**
  - Implemented proper Last-Modified headers for content types
  - Added conditional 304 Not Modified responses for unchanged content
  - Optimized header generation with caching

- **Error Handling & Redirects**
  - Implemented comprehensive 404 error handler
  - Created redirect system for old URLs and common patterns
  - Added 404 tracking and analysis system
  - Fixed 15,000+ 404 errors through pattern-based redirects

- **Metadata Optimization**
  - Fixed missing and duplicate meta descriptions
  - Implemented proper title tag handling with minimum length
  - Addressed H1 tag issues (missing, duplicate, multiple)
  - Added default metadata for missing content

- **Robots.txt Cleanup**
  - Removed references to indreamsphuket.ru
  - Removed Host directive
  - Added proper sitemap references for all languages 

## [1.0.1] - 2023-07-07

### Bugfixes

- **YouTube API Integration**
  - Fixed cross-origin issues in YouTube iframe API
  - Added proper origin parameter to iframe URLs
  - Implemented secure postMessage handlers for video players
  - Added site-wide fix for YouTube video embedding
  - Fixed console errors with YouTube widget API
  - Added proper event bubbling for embedded content
  - Improved Vimeo player integration with proper API parameters 

## [Unreleased]

### Updated
- Updated social media links in PhuketFooterD2 footer component with correct URLs from landing page
- Added proper links to contact information in PhuketFooterD2 footer (WhatsApp and mailto links)
- Removed Russian phone number with area code 495 from the footer
- Improved automatic redirect to https://indreamsphuket.ru/s45/admin after admin login using multiple methods for reliability
- Added strikethrough styling for old prices in property cards with responsive design
- Fixed spacing in rental property cards by adding non-breaking space after "от" text
- Added dynamic price histogram in filter modals that displays real property price distribution with animation
- Implemented preloading of histogram data for immediate display without waiting for AJAX
- Added cron job for daily updating of histogram cache
- Created admin interface for manual cache management 
- Added space between price value and currency code (THB) in property cards
- Fixed space between price and currency display in property cards with CSS margin 
- Fixed uneven property card heights by applying fixed height to all cards for consistent display 
- Fixed display of sold properties in recommended section by adding isSaled=0 filter 
- Improved property diversity in recommended section by limiting to max 1 property per project 
- Changed default tab selection to "Продажа" instead of "Аренда" in main filter and recommended section
- Increased rental property search limit to 20 in recommended section to ensure full display of 6 unique properties 
- Further extended vertical decorative lines in "Why clients choose InDreams" section to 400px height for optimal visual balance 

### Added
- Created download_property_images.php script for downloading property photos, compressing them into an archive and storing in /root/ directory
- Fixed property photos download functionality in admin interface using direct database query approach
- Added favorites sharing functionality allowing users to create and share property selections via link, WhatsApp, Telegram, and email
- Improved property photos download functionality in admin interface
- Enhanced security for admin-only photo download feature
- Added better error handling and logging for download process
- Added diagnostic test page for property photo downloads
- Усовершенствована система интеграции с Dropbox:
  - Создан скрипт get_auth_url.php для генерации правильного URL авторизации с offline доступом
  - Обновлен скрипт dropbox_uploader.php для корректного сохранения refresh_token
  - Переработан скрипт dropbox_token_refresher.php для использования refresh_token и автоматического обновления токенов
  - Исправлены проблемы с правами доступа к конфигурационному файлу
- Создан модуль webp_converter для автоматической конвертации новых загружаемых изображений в формат WebP с качеством 85%
- Добавлен административный интерфейс для настройки качества WebP и тестирования конвертации
- Внедрены механизмы логирования результатов конвертации в watchdog

### Changed
- Simplified favorites sharing implementation to use direct URL encoding instead of API-based storage
- Updated JavaScript for property photo downloads to use more reliable methods
- Increased timeout for property photo downloads to allow for larger properties
- Added comprehensive debugging for property photo downloads
- Increased JPEG quality setting from 50 to 85 to improve image quality in property carousels and listings

### Fixed
- Fixed "Download all photos" button in property admin interface to correctly download property images archive
- Fixed variable inconsistency in favorites page template that was causing rendering issues ($is_shared_view vs $this->isSharedView)
- Fixed download button in admin interface to reliably download property photos
- Improved download reliability by implementing form-based submission approach
- Fixed PHP download script to support both GET and POST requests for better security
- Added multiple fallback download methods for broader browser compatibility
- Optimized file downloading for large property photo archives
- Improved download reliability by implementing direct download approach
- Fixed issues with output buffering in property photo download script
- Fixed JavaScript download handling to use more reliable iframe-based method
- Fixed property download to work with both UUID and property number formats
- Enhanced property download to automatically extract property ID from URL and panel title
- Improved database queries for property lookups with complex OR conditions
- Fixed API response in get_property_data.php by ensuring that debugging information is completely removed from JSON output through aggressive output buffering
- Fixed image paths in get_property_data.php to correctly include the /tmp/ directory, resolving 403 Forbidden errors

## 2025-03-30
- Updated English sitemap with enhanced image information
- Improved sitemap accessibility for search engines
- Added proper sitemap validation and reporting
- Fixed issues with sitemap references in robots.txt

## 2025-04-06
- Updated English sitemap with enhanced image information
- Improved sitemap accessibility for search engines
- Added proper sitemap validation and reporting
- Fixed issues with sitemap references in robots.txt

## 2025-04-13
- Updated English sitemap with enhanced image information
- Improved sitemap accessibility for search engines
- Added proper sitemap validation and reporting
- Fixed issues with sitemap references in robots.txt

- Исправлен page callback для admin/s45/screens: теперь используется компонент PhuketAdminScreens вместо PhuketAdminScreensList (устранена ошибка с отсутствием компонента screens)
- Унифицирован путь для раздела Screens в админке: теперь используется 's45/~/PhuketAdminScreens' вместо 's45/admin/screens'.

- Fixed property photos download: Replaced HTTP fetching of styled images with direct access to local files, ensuring generation if needed. Removed debug output. Added proper file streaming and cleanup.

## 26.04.2024
- Улучшен генератор карточек для соцсетей (contentcreate.php):
  - Добавлено отображение названия объекта и локации
  - Добавлена функция скачивания карточки как изображения
  - Добавлена мобильная адаптивность
  - Улучшена обработка ошибок загрузки изображений
  - Добавлена загрузка объекта по URL-параметру
  - Добавлены новые форматы карточек: Instagram квадратный (1:1), Instagram Reels/Stories (9:16), YouTube превью (16:9)
  - Реализованы специфические элементы для каждого формата (YouTube логотип, кнопка воспроизведения, REELS метка)
  - Добавлен выбор формата карточки с автоматическим изменением размеров и дизайна
  - Добавлена возможность передачи формата через URL-параметр
  - Оптимизирована генерация изображений разного разрешения в зависимости от выбранного формата
- Улучшена обработка фотографий в API недвижимости (get_property_data.php):
  - Корректная обработка различных форматов фотографий
  - Добавлены дополнительные данные (локация, название на разных языках) 
  - Исправлена проблема с отладочной информацией в JSON-ответе через использование агрессивной буферизации вывода
  - Исправлен путь к фотографиям с добавлением директории /tmp/, что решает ошибку 403 Forbidden

## 2025-04-27
- Updated English sitemap with enhanced image information
- Improved sitemap accessibility for search engines
- Added proper sitemap validation and reporting
- Fixed issues with sitemap references in robots.txt

## 29.04.2024
- Улучшен генератор карточек для соцсетей (contentcreate.php):
  - Исправлена проблема наложения элементов при формировании изображения
  - Добавлена независимость генерации от размера экрана просмотра
  - Внедрены фиксированные размеры для форматов: Instagram Square (1080×1080), Instagram Reels (1080×1920), YouTube Thumbnail (1280×720)
  - Улучшена мобильная версия с правильным отображением превью
  - Добавлены локализованные кнопки в мобильном интерфейсе
  - Оптимизирована работа с различными ориентациями экрана
  - Улучшено качество генерируемых изображений

### Fixed
- Завершена работа над функцией "Скачать все фото" в интерфейсе редактирования недвижимости:
  - Улучшена надежность обнаружения ID и номера объекта недвижимости
  - Добавлены два атрибута для кнопки: data-property-id и data-property-number для более надежной работы
  - Оптимизировано создание zip-архива с использованием наиболее крупного стиля изображений (S45_IMST_1900X1000_SCALE)
  - Улучшен пользовательский интерфейс с более информативными статусными сообщениями
  - Доработана обработка ошибок при создании и скачивании фотографий
  - Гарантировано правильное отображение кнопки с текстом "Скачать все фото"

## 2025-05-04
- Updated English sitemap with enhanced image information
- Improved sitemap accessibility for search engines
- Added proper sitemap validation and reporting
- Fixed issues with sitemap references in robots.txt
