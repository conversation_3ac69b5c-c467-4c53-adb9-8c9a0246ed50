# Missing Details Cursor Rules

## 1. EventManager

- Цель: Найти и проанализировать реализацию класса `EventManager` и связанные методы.
- Шаги:
  1. Выполнить grep по `class EventManager` для определения файла.
  2. Изучить методы `getEventHandlerClasses()`, `storeEvent()`, `getLastEvent()`.
  3. Проверить наличие транзакционной обёртки через `db_transaction()`.
- Ожидаемый результат: Точный путь к файлу и список ключевых методов с их логикой.
+ Ожидаемый результат: Точный путь к файлу и список ключевых методов с их логикой.
+ ### Найдено:
+ - Класс `EventStore` расположен в `sites/all/modules/__s45/s45_base/classes/Site45/Event/EventStore.php`.
+ - Основные методы:
+   - `addEvent($arName, $arId, $name, $payload, $note = NULL)`: сохраняет событие, помечает предыдущие события (`isLast = 0`), устанавливает флаги и сохраняет через `EventTable`, использует транзакцию `db_transaction()`.
+   - `getIP()`: возвращает IP клиента из заголовков.
+ - Централизованного вызова обработчиков (`triggerEventHandlers`) в классе нет, события только сохраняются.

## 2. Base Compo Class

- Цель: Найти базовый класс `Site45\Compo\Compo` и понять механизм рендеринга и привязки данных.
- Шаги:
  1. Найти файл `Compo.php` в директории `sites/all/modules/__s45` или в автозагрузке.
  2. Изучить методы `beforeRender()`, использование `s45_toObject()` и `s45_render()`.
  3. Проверить, как публичные свойства передаются в шаблон.
- Ожидаемый результат: Полный обзор методов рендера и подготовки данных.
+ Ожидаемый результат: Полный обзор методов рендера и подготовки данных.
+ ### Найдено:
+ - Файл `Compo.php` находится в `sites/all/modules/__s45/s45_base/classes/Site45/Compo/Compo.php`.
+ - Ключевые методы:
+   - `apiGetRendered()`: вызывает `beforeRender()`, собирает HTML и ресурсы, возвращает `RenderedCompoDto`.
+   - `getCompoRes()`: определяет JS/CSS файлы компонента.
+   - `getCompoHtmlFull()`, `getCompoHtml()`: формируют HTML компонента, оборачивают его, выполняют пост-обработку.
+   - `setAttributes()`, `addCompoHtmlWrapper()`, `postProcessor()`: устанавливают атрибуты и выполняют завершающую обработку.
+   - `apiLoadEditor()`, `apiSaveEditor()`: API-методы для редактирования компонента.
+ - Используется `s45_toObject()` для приведения входных параметров к объекту, и `s45_render()` при вызове компонентов.

## 3. Drupal Integration

- Цель: Понять, как компоненты Compo регистрируются и вызываются в Drupal.
- Шаги:
  1. grep по `hook_menu` и `menu_router` в коде.
  2. Найти места, где URL компонентов связываются с классами и их методами API (например, `apiSubmit`).
  3. Проверить реализацию проверки прав доступа (`access callback`).
- Ожидаемый результат: Список маршрутов и правил доступа для Compo.
+ Ожидаемый результат: Список маршрутов и правил доступа для Compo.
+ ### Найдено:
+ - В файле `s45_base.module` функция `s45_base_menu()` регистрирует маршруты:
+   - `compo45` → `s45_compo_api`, доступ `s45guest`, тип `MENU_CALLBACK`.
+   - `compomake` → `s45_compo_make`, доступ `s45admin`, тип `MENU_CALLBACK`.
+   - `s45store` → `s45_base_store`, доступ `s45admin`, тип `MENU_NORMAL_ITEM`.
+   - `s45_robots_settings` → форма `s45_base_settings`, доступ `s45admin`, тип `MENU_NORMAL_ITEM`.
+   - `robots.txt` → `s45_robots`, доступ `s45guest`, тип `MENU_NORMAL_ITEM`.
+ - Права определяются в `s45_base_permission()` как `s45guest` и `s45admin`.

## 4. Caching and Queues

- Цель: Определить использование кэширования и очередей в Event Sourcing и компонентах.
- Шаги:
  1. grep по `cache_get\|cache_set` для поиска мест использования Cache API.
  2. grep по `db_transaction` и ключевым словам `Queue` для выявления очередей.
  3. Оценить, где целесообразно добавить кэширование или асинхронную очередь.
- Ожидаемый результат: Перечень функций/классов, где используется кэш или очередь.
+ Ожидаемый результат: Перечень функций/классов, где используется кэш или очередь.
+ ### Найдено:
+ - Транзакции: `db_transaction()` вызывается в `EventStore::addEvent()`.
+ - В `s45_base` отсутствуют прямые вызовы `cache_get`/`cache_set`.
+ - Кандидаты для кэширования: результаты `EventQuery::exec()`, ресурсы компонентов в `getCompoRes()`.
+ - Очередь (Queue API) не используется, для обновления read-моделей рекомендуется внедрить Drupal Queue API.

## 5. Tests and Migrations

- Цель: Найти тесты и обновления БД через hook_update_N.
- Шаги:
  1. grep по `hook_update_N` во всех `*.install` файлах.
  2. Просмотреть каталоги `tests` рядом с AR, Query и Compo.
  3. Составить список существующих тестов и необходимых миграций.
- Ожидаемый результат: Карта тестовых файлов и миграций для базы данных.
+ Ожидаемый результат: Карта тестовых файлов и миграций для базы данных.
+ ### Найдено:
+ - В модулях `__s45` нет реализаций `hook_update_N` в файлах `.install`.
+ - Каталоги `tests` рядом с AR, Event, Compo либо отсутствуют, либо не содержат тестов.
+ - Необходимы миграции для:
+   - Создания таблицы `_s45_events` с индексом `(arName, arId, isLast)`.
+   - Создания проекционных таблиц `_phuket_Property` и `_phuket_Reservation`. 