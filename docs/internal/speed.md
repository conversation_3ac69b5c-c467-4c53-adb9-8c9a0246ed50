# Анализ производительности сайта indreamsphuket.com

## Общая информация о системе
- **Версия Drupal**: 7.67
- **Версия PHP**: 5.4.16 с Zend OPcache v7.0.5
- **Веб-сервер**: Nginx + Apache (проксирование)
- **Фреймворк**: Кастомный S45 (на базе Drupal)

## Архитектура сайта

### Ядро S45 Framework
Сайт построен на кастомном фреймворке S45, который расширяет Drupal 7:

1. **Архитектура Event Sourcing**:
   - События хранятся в таблице `_s45_events` с сериализованными данными (42,306 записей)
   - Денормализованные данные в отдельных таблицах (`_phuket_Property`, `_phuket_Project` и др.)
   - Обработка событий через классы `QueryFromEvents`
   - Ключевые классы: `EventStore`, `EventQuery`, `EventTable`, `AR` (Aggregate Root)

2. **Компонентная UI система**:
   - Кастомные компоненты "Compo" для элементов интерфейса
   - Файлы расположены в `sites/all/modules/__s45/Compo/`
   - Система регистрации компонентов с JSON-конфигурацией
   - Раздельные шаблоны и логика (PHP, TPL.PHP, JS, CSS)

3. **Многоязычная поддержка**:
   - 4 языка: английский, русский, тайский, китайский
   - Кастомная система переводов с объектами `LangVO`
   - 18,146 URL алиасов в таблице `_s45_aliases`

## Модули для оптимизации производительности

### Включенные модули (критичные для производительности)
- **HTTPRequest Library** (httprl): 7.x-1.14
- **Cache Expiration** (expire): 7.x-2.0-rc4
- **WebP Converter**: 7.x-1.0 - конвертирует загруженные изображения в WebP
- **ImageAPI Optimize**: 7.x-2.0-beta2 - оптимизация изображений
- **Kraken.io**: 7.x-1.0-alpha5 - внешний сервис оптимизации изображений
- **jQuery Update** (jquery_update): 7.x-3.0-alpha5
- **Token** (token): 7.x-1.8+1-dev
- **X Autoload** (xautoload): 7.x-5.7

### Отключенные модули для оптимизации
- **AdvAgg (Advanced CSS/JS Aggregation)**: 7.x-2.36
  - AdvAgg Bundler
  - AdvAgg Compress CSS
  - AdvAgg Compress Javascript
- **Varnish**: 7.x-1.9
- **Boost**: 7.x-1.2
- **Boost Crawler**: 7.x-1.2
- **Lazy-load**: 7.x-1.4
- **Image Lazyloader**: 7.x-1.5
- **reSmush.it**: 7.x-2.0
- **OptiPic**: 7.x-1.25
- **Metatag**: 7.x-1.22
- **Cloudflare**: 7.x-2.x-dev
- **Facebook Pixel**: 7.x-1.1

## Состояние кэширования Drupal

Большинство настроек кэширования **отключены**:
- `cache`: 0 (Выключено)
- `block_cache`: 0 (Выключено)
- `page_cache_maximum_age`: 10800 (3 часа)
- `cache_lifetime`: 0 (Кэш очищается при любом обновлении)
- Множество кэшированных CSS/JS файлов в `drupal_css_cache_files` и `drupal_js_cache_files`

## Серверная конфигурация

### Конфигурация Nginx

```nginx
# /etc/nginx/vhosts/www-root/indreamsphuket.com.conf (фрагмент)
server {
    server_name indreamsphuket.com www.indreamsphuket.com;
    charset off;
    index index.php index.html;
    disable_symlinks if_not_owner from=$root_path;
    include /etc/nginx/vhosts-includes/*.conf;
    include /etc/nginx/vhosts-resources/indreamsphuket.com/*.conf;
    access_log /var/www/httpd-logs/indreamsphuket.com.access.log;
    error_log /var/www/httpd-logs/indreamsphuket.com.error.log notice;
    ssi on;
    set $root_path /var/www/www-root/data/www/indreamsphuket.com;
    root $root_path;
    
    # Настройки сжатия
    gzip on;
    gzip_comp_level 5;
    gzip_disable "msie6";
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript image/svg+xml;
    
    location / {
        location ~ [^/]\.ph(p\d*|tml)$ {
            try_files /does_not_exists @fallback;
        }
        location ~* ^.+\.(jpg|jpeg|gif|png|svg|js|css|mp3|ogg|mpe?g|avi|zip|gz|bz2?|rar|swf|webp|woff|woff2)$ {
            expires 24h;
            try_files $uri $uri/ @fallback;
        }
        location / {
            try_files /does_not_exists @fallback;
        }
    }
    
    location @fallback {
        include /etc/nginx/vhosts-resources/indreamsphuket.com/dynamic/*.conf;
        proxy_pass http://127.0.0.1:8080;
        proxy_redirect http://127.0.0.1:8080 /;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        access_log off;
    }
    
    listen *************:80;
}
```

**Ограничение запросов**:
```nginx
# /etc/nginx/vhosts-resources/indreamsphuket.com/dynamic/reqlimit.conf
limit_req_status 429;
error_page 429 @blacklist;
limit_req zone=indreamsphuket.com burst=100;
```

**Глобальное ограничение запросов**:
```nginx
# /etc/nginx/conf.d/isplimitreq.conf (частичный)
limit_req_zone $binary_remote_addr zone=indreamsphuket.com:6400k rate=25r/s;
```

### Конфигурация Apache

```apache
# /etc/httpd/conf/vhosts/www-root/indreamsphuket.com.conf
<VirtualHost 127.0.0.1:8080>
    ServerName indreamsphuket.com
    DocumentRoot /var/www/www-root/data/www/indreamsphuket.com
    ServerAdmin <EMAIL>
    AddDefaultCharset off
    SuexecUserGroup www-root www-root
    CustomLog /var/www/httpd-logs/indreamsphuket.com.access.log combined
    ErrorLog /var/www/httpd-logs/indreamsphuket.com.error.log
    <FilesMatch "\.ph(p[3-5]?|tml)$">
        SetHandler application/x-httpd-php
    </FilesMatch>
    <IfModule php5_module>
        Include /etc/httpd/users-php/www-root.conf
        Include /etc/httpd/vhosts-php/indreamsphuket.com.conf
        php_admin_value sendmail_path "/usr/sbin/sendmail -t -i -f <EMAIL>"
        php_admin_value upload_tmp_dir "/var/www/www-root/data/mod-tmp"
        php_admin_value session.save_path "/var/www/www-root/data/mod-tmp"
        php_admin_value open_basedir "/var/www/www-root/data:."
    </IfModule>
    SetEnvIf X-Forwarded-Proto https HTTPS=on
    ServerAlias www.indreamsphuket.com
    DirectoryIndex index.php index.html
</VirtualHost>
```

### Настройки .htaccess
```apache
# Сжатие контента
php_flag zlib.output_compression On

# Настройки Expires
<IfModule mod_expires.c>
  ExpiresActive On
  ExpiresDefault "access plus 1 month"
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType text/javascript "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# Deflate компрессия
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css text/javascript application/javascript
</IfModule>

# Cache-Control
Header set Cache-Control "public, max-age=3600"
```

### Google PageSpeed
На сервере установлен модуль Google PageSpeed, но он не настроен на уровне .htaccess для сайта indreamsphuket.com.

## Выявленные проблемы производительности

1. **Проблемы денормализации базы данных**:
   - Большие сериализованные данные в полях `payload`
   - Минимальное индексирование колонок для фильтрации
   - Отсутствие композитных индексов для частых запросов

2. **Проблемы кэширования**:
   - Отключено ядро кэширования Drupal
   - Многие модули кэширования доступны, но отключены
   - Нет настройки CDN

3. **Загрузка ресурсов**:
   - Множество JS/CSS файлов загружаются отдельно
   - Отключена расширенная агрегация
   - Частичная оптимизация изображений (реализована конвертация в WebP)

4. **Настройка сервера**:
   - Базовые заголовки истечения срока действия
   - GZIP сжатие включено
   - Нет явных признаков HTTP/2 или серверного кэширования
   - PHP 5.4.16 (устаревшая версия)
   - OPcache включен для CLI, но возможно не оптимально настроен

5. **Event Sourcing и архитектурные проблемы**:
   - Большая таблица событий (42,306 записей) без стратегии архивации
   - Неэффективная стратегия обновления read-моделей
   - Потенциальные проблемы сериализации/десериализации больших объектов

## Рекомендации по оптимизации

1. **Включение и настройка критических модулей**:
   - AdvAgg для агрегации и сжатия CSS/JS
   - Varnish для полного кэширования страниц
   - Lazy loading для изображений
   - Metatag для SEO-оптимизации
   - Cloudflare для CDN и защиты

2. **Оптимизация базы данных**:
   - Добавление недостающих индексов в таблицы read-моделей
   - Денормализация часто запрашиваемых данных
   - Внедрение кэширования read-моделей

3. **Оптимизация изображений**:
   - Конвертация WebP уже реализована, но можно расширить на адаптивные изображения
   - Добавление ленивой загрузки для изображений
   - Настройка адаптивной подачи изображений

4. **Изменения в архитектуре компонентов**:
   - Пересмотр и оптимизация цепочки рендеринга S45
   - Внедрение edge-side includes для динамического контента
   - Разделение критических путей рендеринга

5. **Серверные улучшения**:
   - Обновление PHP до более новой версии (7.x или 8.x)
   - Внедрение HTTP/2
   - Настройка более агрессивного кэширования браузера
   - Рассмотрение интеграции CDN
   - Оптимизация настроек OPcache

6. **Архитектурные улучшения для Event Sourcing**:
   - Внедрение стратегии архивации старых событий
   - Оптимизация запросов к read-моделям
   - Рассмотрение возможности предзагрузки и кэширования часто используемых объектов

Сайт использует сложный кастомный фреймворк с Event Sourcing в его основе. Оптимизация производительности должна учитывать уникальную архитектуру, одновременно используя стандартные паттерны производительности Drupal. 