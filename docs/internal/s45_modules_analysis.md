# Глубокий Анализ Кастомных Модулей s45 (indreamsphuket.com) - v3 (Финальный)

Этот документ представляет собой детальный анализ кастомных модулей с префиксом `s45_`, используемых на сайте `indreamsphuket.com`, дополненный результатами углубленного исследования ключевых компонентов и файлов. Цель — предоставить исчерпывающее понимание их архитектуры, функциональности, выявленных проблем и потенциальных путей улучшения, включая оценку влияния на производительность и возможности миграции.

## I. Общая Архитектура и Назначение Модулей s45

Кастомные модули `s45_*` формируют ядро бизнес-логики и уникальной функциональности сайта, работая поверх Drupal 7. Они реализуют:

1.  **Управление Контентом Недвижимости:** Основная предметная область (объекты, проекты, пользователи, опции).
2.  **Кастомную Систему UI:** Компонентный подход (`Compo/`) для рендеринга интерфейса.
3.  **Кастомную Систему Хранения Данных:** Event Sourcing для основных сущностей (`s45_base`, `_s45_events`) и Read Model (`_phuket_Property`) для оптимизации чтения/поиска.
4.  **Кастомную Систему URL:** Управление алиасами и редиректами (`s45_path`).
5.  **Кастомную Систему SEO:** Управление мета-тегами и H1 (`s45_phuket_seo.inc`).
6.  **Кастомное Управление Профилями Пользователей:** Расширение стандартных профилей дополнительными полями (`s45_phuket_lib.inc`).
7.  **Обработку Веб-Форм:** Прием данных, сохранение через Event Sourcing, отправка уведомлений по email (`s45_phuket_form_api.inc`, `s45_phuket_form_api2.inc`).
8.  **Интеграции и Экспорт:** Генерация фидов для партнеров (через корневые скрипты и `s45_phuket_export.inc`), работа с API.
9.  **Управление Стилями Изображений:** Определение кастомных стилей (`s45_imagestyles`).
10. **Вспомогательные Функции и Библиотеки:** Общие утилиты, подключение JS/CSS (`s45_phuket_lib.inc`).

**Основные Модули и их Роли:**

*   **`s45_base`:**
    *   **Назначение:** Фундамент для других `s45_*` модулей. Предоставляет базовые классы, сервисы и концепции.
    *   **Ключевые Компоненты:** `Event\AR`, `Event\EventStore`, `Event\EventQuery`, `Base\QueryFromEvents`, `Compo\Compo`, `Base\CompoRepo`, `Base\JsonRepo`, `Base\SiteConf`, базовые DTO.
    *   **Зависимости:** `xautoload`, `jquery_update`, `locale`.

*   **`s45_phuket`:**
    *   **Назначение:** Основной модуль, реализующий бизнес-логику, связанную с недвижимостью Пхукета.
    *   **Ключевые Компоненты:** Доменные Сущности (AR), Query Классы, Read Model Таблицы, DTO, UI Компоненты (`Compo/`), Сервисы (`PropertyPriceHistogram`, `PhuketSchemaGenerator`), Конвертеры Фидов, Хуки Drupal, Процедурные Файлы (`.inc` для SEO, экспорта, форм, PDF, утилит, миграции, запросов, библиотек).
    *   **Зависимости:** `s45_base`.

*   **`s45_path`:**
    *   **Назначение:** Управление URL алиасами и редиректами.
    *   **Ключевые Компоненты:** `Path\Path`, `Path\Redirect`, `Path\RedirectFromOldSite`, `hook_url_inbound_alter`, `s45_path_url()`.
    *   **Зависимости:** `s45_base`.

*   **`s45_imagestyles`:**
    *   **Назначение:** Определение кастомных стилей изображений Drupal.
    *   **Ключевые Компоненты:** `hook_image_default_styles()`.
    *   **Зависимости:** Нет явных.

*   **Другие `s45_*` модули:** (`s45_page`, `s45_phuket_login`, `s45_test`, `s45_vendor`, `s452_base`) - требуют отдельного изучения, но, вероятно, играют вспомогательные роли.

## II. Ключевые Архитектурные Решения и Их Последствия

1.  **Event Sourcing + Read Model:**
    *   **Как работает:** Изменения сущностей (Property, Project, Compo, User, Form, Reservation) сохраняются как события в `_s45_events`. Для поиска используется Read Model (`_phuket_Property`), обновляемый через `QueryFromEvents`.
    *   **Плюсы:** Аудит.
    *   **Минусы:** Сложность, производительность записи, риск рассинхронизации, сложность запросов к истории.

2.  **Хранение Сложных Данных через Сериализацию:**
    *   **Как работает:** DTO, `LangVO`, массивы хранятся как сериализованные строки в `_s45_events.payload` и `_phuket_Property` (`propertyDto`, `name`).
    *   **Плюсы:** Гибкость схемы.
    *   **Минусы:** Невозможность SQL-запросов к вложенным данным, производительность (де)сериализации, проблемы с целостностью и миграцией.

3.  **Кастомная Компонентная Система (`Compo/`)**:
    *   **Как работает:** UI строится из PHP классов + TPL файлов. Рендеринг через `Compo.php`. Состояние хранится через `CompoRepo` (Event Sourcing + JSON fallback).
    *   **Плюсы:** Модульность UI.
    *   **Минусы:** Изоляция от Drupal API (темизация, ресурсы, переводы), сложность.

4.  **Кастомная Система URL (`s45_path`)**:
    *   **Как работает:** Контролирует URL через хуки, функции и таблицу `_s45_aliases`. Обрабатывает редиректы (частично).
    *   **Плюсы:** Полный контроль над URL.
    *   **Минусы:** Изоляция от Drupal API, сложность, жестко закодированные редиректы.

5.  **Кастомная Система Переводов (`LangVO`, `s45_lang()`, `$this->translate`)**:
    *   **Как работает:** Тексты хранятся как объекты/массивы с ключами языков. Используются кастомные функции/массивы.
    *   **Плюсы:** (Нет явных).
    *   **Минусы:** Изоляция от Drupal API, немасштабируемость, трудоемкость управления.

6.  **Процедурный Код в `.inc` Файлах:**
    *   **Как работает:** Значительная часть логики вынесена в `.inc` файлы.
    *   **Плюсы:** (Нет явных).
    *   **Минусы:** Ухудшает читаемость, структуру, тестируемость.

7.  **Использование `$GLOBALS`:** Применяется для кэширования (`$GLOBALS['AllCompoData']`) и передачи данных (`$GLOBALS['s45']['PageSeo']`).
    *   **Плюсы:** Простота реализации (на первый взгляд).
    *   **Минусы:** Нарушает инкапсуляцию, затрудняет отладку.

8.  **Жестко Закодированные Значения:** Множество ID, списков пользователей, правил редиректа, конфигураций фидов, условий оплаты, **учетных данных SMTP**, адресов email и т.д. жестко прописаны в коде.
    *   **Плюсы:** (Нет явных).
    *   **Минусы:** Затрудняет поддержку, **создает уязвимости безопасности**, усложняет настройку.

9.  **Обработка Форм и Email:**
    *   **Как работает:** Кастомные эндпоинты (`form_api`, `form_api2`) принимают POST-данные, сохраняют их через Event Sourcing (`PhuketFormAR`, `PhuketReservationAR`) и отправляют email через кастомную библиотеку `prSiteMailing` с использованием SMTP `mail.ru`.
    *   **Плюсы:** Централизованная обработка форм.
    *   **Минусы:** **Критическая уязвимость** (учетные данные SMTP в коде), жестко закодированные адреса получателей и названия форм, зависимость от кастомной email-библиотеки.

## III. Детальный Аудит Модулей и Механизмов

### 1. Механизм Переключения Тем/Дизайна

*   **Технический Механизм:** Нет стандартного переключения тем Drupal. Вместо этого переключается **конфигурация компонентов `Compo`** (через переменную `s45_phuket_config_file`, выбирающую `Compo.s45.json` или `Compo.s45_new.json`) и **наборы CSS/JS ресурсов** (через функции `s45_phuket_add_theme*()` в `s45_phuket_lib.inc`). Логика выбора набора ресурсов неясна, возможно, связана с `s452_base` или `hook_preprocess_page`.
*   **Управление Состоянием:** Через Drupal переменную `s45_phuket_config_file`, изменяемую администратором.
*   **Загрузка Стилей:** Основные стили/скрипты "тем" загружаются через `drupal_add_css/js`. Ресурсы самих компонентов `Compo` (`*.js`, `*.css`) загружаются кастомным механизмом (`Compo::getCompoRes`) и **не агрегируются**.
*   **Проблемы:** Конфликты CSS/JS между наборами ресурсов, производительность (из-за отсутствия агрегации ресурсов `Compo`), непрозрачность логики выбора набора ресурсов.
*   **Оценка Качества:** Механизм функционален, но непрозрачен и частично неэффективен (ресурсы `Compo`).

### 2. Модуль `s45_base`

*   **Функциональность:** Основа архитектуры: базовые классы Event Sourcing (`AR`, `EventStore`, `EventQuery`), UI Компонентов (`Compo`, `CompoRepo`), работа с JSON (`JsonRepo`), определение сайта (`SiteConf`), базовые DTO (`LangVO`).
*   **Качество Кода:**
    *   *Читаемость:* Средняя (ООП, но мало комментариев, `$GLOBALS`).
    *   *Производительность:* Потенциальные проблемы в `QueryFromEvents`, `CompoRepo` (`$GLOBALS`), `Compo`/`AR` (`json_decode(json_encode)`).
    *   *Безопасность:* Нет явных уязвимостей, но `$GLOBALS` - плохая практика.
    *   *Стандарты:* Частично PSR.
    *   *Обработка Ошибок:* Минимальная.
    *   *Поддерживаемость:* Средняя (требует понимания кастомных концепций).
*   **Проблемы:** `$GLOBALS`, неоптимальный код, потенциальные проблемы производительности, недостаток комментариев/обработки ошибок.
*   **Улучшения:** Заменить `$GLOBALS`, оптимизировать код, добавить комментарии/обработку ошибок, рассмотреть перенос обновления Read Model в Cron/Queue.

### 3. Модуль `s45_phuket`

*   **Функциональность:** Ядро бизнес-логики: сущности недвижимости (AR), поиск (Query), Read Model, DTO, UI компоненты (`Compo`), сервисы (гистограмма цен, генератор Schema.org), конвертеры фидов, хуки Drupal, процедурная логика (`.inc` файлы: SEO, экспорт, формы, PDF, утилиты, либы).
*   **Качество Кода:**
    *   *Читаемость:* Низкая-Средняя (большой размер, `.inc` файлы, смешение ООП/процедурного кода, мало комментариев, кастомные переводы).
    *   *Производительность:* Потенциальные проблемы в Query (`handle...`, `LIKE`), `afterSetProps`, загрузке SEO настроек.
    *   *Безопасность:* **Критическая уязвимость SMTP**. Потенциальный XSS (нет явного экранирования). Жестко закодированные ID для доступа.
    *   *Стандарты:* Смешанные.
    *   *Обработка Ошибок:* Непоследовательная (`@unserialize`).
    *   *Поддерживаемость:* Низкая (сложность, размер, тех. долг, документация).
*   **Проблемы:** Уязвимость SMTP, сериализация данных, кастомные переводы, большой размер и сложность, процедурный код в `.inc`, жестко закодированные значения (ID, email, названия форм, списки пользователей), потенциальные проблемы производительности и XSS, ошибки.
*   **Улучшения:** **Исправить SMTP.** Рефакторинг сериализованных данных, переход на `t()`, рефакторинг `.inc` в сервисы, устранение жестко закодированных значений, внедрение экранирования вывода, оптимизация запросов, улучшение логирования/документации.

### 4. Модуль `s45_path`

*   **Функциональность:** Управление URL алиасами (`_s45_aliases`) и редиректами (`_s45_redirects` - обработка закомментирована). Заменяет стандартные механизмы.
*   **Качество Кода:**
    *   *Читаемость:* Средняя (ручные `include_once`, жестко закодированные редиректы).
    *   *Производительность:* Зависит от индексов `_s45_aliases`. Обработка дубликатов неоптимальна.
    *   *Безопасность:* Нет явных уязвимостей.
    *   *Стандарты:* Использует Database API, но и ручные `include_once`.
    *   *Обработка Ошибок:* Минимальная.
    *   *Поддерживаемость:* Средняя (жестко закодированные редиректы, `include_once`, отключенное автосоздание таблицы).
*   **Проблемы:** Жестко закодированные редиректы, `include_once`, отключенное автосоздание таблицы, неясный статус редиректов из БД, обработка дубликатов.
*   **Улучшения:** Вынести редиректы, устранить `include_once`, исследовать/исправить редиректы из БД, восстановить автосоздание таблицы, улучшить обработку дубликатов, проверить индексы, добавить комментарии.

### 5. Модуль `s45_imagestyles`

*   **Функциональность:** Определение кастомных стилей изображений Drupal.
*   **Качество Кода:**
    *   *Читаемость:* Высокая.
    *   *Производительность:* Не влияет.
    *   *Безопасность:* Нет проблем.
    *   *Стандарты:* Соответствует.
    *   *Обработка Ошибок:* Не требуется.
    *   *Поддерживаемость:* Высокая (кроме закомментированного кода).
*   **Проблемы:** Закомментированный код.
*   **Улучшения:** Добавить комментарии, удалить закомментированный код.

### 6. Файл `s45_phuket_lib.inc`

*   **Функциональность:** Вспомогательные функции: списки ID пользователей (админы/агенты), отладочные функции (`s45_phuket_ar`, `s45_phuket_history`), подключение CSS/JS для "тем", реализация `hook_form_alter` для профиля пользователя, сохранение кастомных данных профиля в `PhuketUserAR`, дублирование `getDefPaymentTerms`, AJAX эндпоинты для поиска слов/персон.
*   **Качество Кода:**
    *   *Читаемость:* Средняя (смесь разной логики).
    *   *Производительность:* Подключение ресурсов может быть неоптимальным.
    *   *Безопасность:* Жестко закодированные ID пользователей для проверок доступа.
    *   *Стандарты:* Смешанные.
    *   *Обработка Ошибок:* Минимальная (использование `dsm`).
    *   *Поддерживаемость:* Средняя (из-за смешения логики и жестко закодированных значений).
*   **Проблемы:** Жестко закодированные ID, дублирование кода, неоптимальное подключение ресурсов, использование `dsm`.
*   **Улучшения:** Заменить ID на роли, устранить дублирование, оптимизировать подключение ресурсов, заменить `dsm`, добавить комментарии.

### 7. Файл `s45_phuket_seo.inc`

*   **Функциональность:** Управление SEO: установка canonical URL, генерация hreflang, установка meta description, загрузка и применение кастомных SEO настроек (title, description, h1, keywords, text, canonical) для страниц с учетом GET-параметров (из `PhuketPageSeoAR`).
*   **Качество Кода:**
    *   *Читаемость:* Средняя (использование `$GLOBALS`, сложная логика `s45_phuket_seo_setForPage`).
    *   *Производительность:* Загрузка всех SEO настроек (`PhuketPageSeoQuery` с limit 300) на каждой странице неэффективна.
    *   *Безопасность:* Нет явных проблем.
    *   *Стандарты:* Использует хуки Drupal, но также `$GLOBALS` и кастомные переводы (`s45_lang`).
    *   *Обработка Ошибок:* Есть `try...catch` в `s45_phuket_get_translated_path`.
    *   *Поддерживаемость:* Средняя (из-за `$GLOBALS`, неэффективной загрузки, жестко закодированного списка языков/доменов).
*   **Проблемы:** Неэффективная загрузка SEO настроек, использование `$GLOBALS`, жестко закодированный список языков/доменов.
*   **Улучшения:** Оптимизировать загрузку SEO настроек, заменить `$GLOBALS`, вынести список языков/доменов в конфигурацию, рассмотреть переход на модуль Metatag, добавить комментарии.

### 8. Файл `s45_phuket_export.inc`

*   **Функциональность:** Генерация XML-фидов по URL (`/export/*.xml`) для Hipflat, Fazwaz, App.
*   **Качество Кода:**
    *   *Читаемость:* Средняя. Логика проста, но зависит от скрытой работы `PhuketExportQuery` и Конвертеров.
    *   *Производительность:* Зависит от производительности `PhuketExportQuery`. Сборка XML конкатенацией может быть неоптимальна для очень больших фидов.
    *   *Безопасность:* Нет явных проблем.
    *   *Стандарты:* Использует Database API (внутри Query), но конкатенацию строк для XML.
    *   *Обработка Ошибок:* Минимальная.
    *   *Поддерживаемость:* Средняя (требует понимания Query/Конвертеров, содержит "костыль" для URL изображений).
*   **Проблемы:** Зависимость от скрытой логики Query/Конвертеров, "костыль" для URL изображений.
*   **Улучшения:** Исследовать Query/Конвертеры, устранить `str_replace` для URL, добавить комментарии.

### 9. Файл `s45_phuket_form_api.inc`

*   **Функциональность:** API эндпоинт (`/s45/form_api/{formName}`) для приема данных форм, сохранения через Event Sourcing (`PhuketFormAR`, `PhuketReservationAR`), отправки email уведомлений через `prSiteMailing` и SMTP `mail.ru`.
*   **Качество Кода:**
    *   *Читаемость:* Средняя.
    *   *Производительность:* Зависит от скорости сохранения событий и отправки email.
    *   *Безопасность:* **Критическая уязвимость SMTP.**
    *   *Стандарты:* Использует Event Sourcing, но кастомную email библиотеку и жестко закодированные значения.
    *   *Обработка Ошибок:* Минимальная.
    *   *Поддерживаемость:* Низкая (из-за уязвимости и жестко закодированных значений).
*   **Проблемы:** **Уязвимость SMTP**, жестко закодированные email адреса и названия форм, зависимость от кастомной email библиотеки.
*   **Улучшения:** **Исправить SMTP.** Вынести email адреса и названия форм, исследовать `prSiteMailing` и рассмотреть переход на Drupal Mail API, добавить комментарии.

### 10. Файл `s45_phuket_form_api2.inc`

*   **Функциональность:** Точка входа для API эндпоинта (`/s45/form_api2/{formId}`), делегирующая обработку классу `FormApi2`.
*   **Качество Кода:** Простой и читаемый.
*   **Проблемы:** Неясна точная роль и отличия от `form_api`.
*   **Улучшения:** Исследовать `FormApi2.php`, добавить комментарии.

### 11. Файл `property_photos_download.js`

*   **Функциональность:** JS для кнопки скачивания фото в админке.
*   **Качество Кода:**
    *   *Читаемость:* Средняя (сложная логика получения ID).
    *   *Производительность:* Не критично.
    *   *Безопасность:* Нет явных проблем.
    *   *Стандарты:* Использует `$(document).ready()` вместо `Drupal.behaviors`.
    *   *Обработка Ошибок:* Есть таймаут, но нет реальной обратной связи от сервера.
    *   *Поддерживаемость:* Низкая (из-за хрупкой логики получения ID).
*   **Проблемы:** Хрупкая логика получения ID, отсутствие обратной связи от сервера, использование `$(document).ready()`.
*   **Улучшения:** Обеспечить надежное получение ID через `data-*` атрибут, реализовать AJAX для обратной связи, перейти на `Drupal.behaviors`, добавить комментарии.

## IV. Пути Улучшения и Рефакторинга (Сводка)

(Детальные задачи см. в `project_improvement_plan.md`)

1.  **Безопасность (Критический Приоритет):**
    *   **Немедленно:** Удалить учетные данные SMTP из кода (Задача 4.3).
    *   **Высокий:** Аудит и исправление XSS в TPL файлах (Задача 4). Санитизация `$_GET` (Задача 4.1). Безопасная генерация HTML в фидах (Задача 4.2).

2.  **Улучшение Качества Кода и Документации (Высокий Приоритет, Низкая Сложность):**
    *   **Документирование:** Добавление комментариев ко всем кастомным файлам и классам (Задачи 1.*). Актуализация внешней документации (Задача 2). Создание README (Задача 3).
    *   **Логирование и Ошибки:** Улучшение логирования, замена `dsm`, удаление `@` (Задачи 5.*).
    *   **Вынос Констант/ID/Конфигурации:** Устранение жестко закодированных значений (Задачи 6.*).
    *   **Чистка Кода:** Удаление дублирования, перенос CSS, рефакторинг установки значений по умолчанию и т.д. (Задачи 1.6, 1.2, 10.15).
    *   **Зависимости:** Составление списка (Задача 8).

3.  **Устранение Технического Долга по Данным (Высокий Приоритет, Сложно):**
    *   Рефакторинг сериализованных данных в реляционную структуру (Задача 10).
    *   Оптимизация хранения списков ID (Задача 10.4).

4.  **Рефакторинг Корневых Скриптов (Средний Приоритет, Средняя Сложность):**
    *   Перенос логики в модули Drupal (Задачи 11, 11.5).
    *   Стандартизация генерации XML/фидов (Задача 9.1).
    *   Оптимизация запросов (Задача 7.2).

5.  **Улучшение Кастомной Системы URL (Средний Приоритет, Средняя Сложность):**
    *   Вынос правил редиректа (Задача 6.5).
    *   Исследование/исправление редиректов из `_s45_redirects` (Задача 10.13).
    *   Устранение `include_once` (Задача 10.10).
    *   Улучшение обработки дубликатов алиасов (Задача 10.12).
    *   Анализ индексов `_s45_aliases` (Задача 7.3).

6.  **Рефакторинг Компонентной Системы (Низкий Приоритет, Высокая Сложность):**
    *   Интеграция с Drupal API (Переводы - 11.1, Ресурсы - 11.3).
    *   Исследование `CompoRepo` (10.7).
    *   Оптимизация кода (10.8).
    *   Замена `$GLOBALS` (10.9).
    *   (Долгосрочно) Переход на Render API/Twig (11.4).

7.  **Оптимизация Event Sourcing / Read Model (Низкий Приоритет, Высокая Сложность):**
    *   Оптимизация обновления Read Model (10.3).
    *   Перенос обновления в Cron/Queue API (10.5, 10.6).
    *   Анализ индексов Read Model (`_phuket_Property`) (7.1).

8.  **Улучшение Обработки Форм (Средний Приоритет):**
    *   Вынос адресов email и названий форм (Задачи 6.8, 6.9).
    *   Исследование `FormApi2` и `prSiteMailing`, рассмотрение перехода на Drupal Mail API (Задача 10.19, 10.20).

## V. Оценка Улучшения Производительности (Приблизительно)

*   **Рефакторинг Сериализованных Данных:** **+30-60%** к скорости сложных запросов.
*   **Оптимизация Запросов (включая индексы):** **+20-50%** к скорости поиска, фидов.
*   **Оптимизация Обновления Read Model (Queue API):** **Устранение задержек** при сохранении.
*   **Интеграция Ресурсов Compo с Агрегацией Drupal:** **+10-25%** к скорости загрузки страниц.
*   **Улучшение Качества Кода и Документации:** **+50-100%** к производительности разработчика.

## VI. Миграция на Drupal 8/9+ и PHP 8+

**Сложность:** Очень высокая. Требует практически полного переписывания кастомных частей.

**Ключевые Блокеры:**

1.  **Кастомная Компонентная Система (`Compo/`)**: Требует замены на Twig/Render API.
2.  **Кастомная Система Переводов**: Требует замены на `t()`/Locale API.
3.  **Сериализованные Данные**: Требуют рефакторинга *до* миграции контента.
4.  **Кастомная Система URL (`s45_path`)**: Требует миграции данных в Pathauto/Redirect.
5.  **Процедурный Код (`.inc`) и Корневые Скрипты**: Требуют рефакторинга в сервисы/классы/команды.
6.  **Зависимости Drupal 7**: Требуют обновления/замены.
7.  **Совместимость с PHP 8+**: Требует адаптации кода.

**Рекомендация:** Рассматривать как проект по переписыванию. Приоритезировать устранение уязвимостей, рефакторинг хранения данных и улучшение качества кода на Drupal 7 как подготовку.

## VII. Заключение

Кастомные модули `s45` представляют собой сложную, функциональную, но обремененную значительным техническим долгом систему. Ключевые проблемы: **критическая уязвимость безопасности (SMTP пароль в коде)**, повсеместное использование сериализации данных, кастомные системы UI/URL/переводов, изолированные от Drupal API, большое количество жестко закодированных значений и процедурного кода.

**Немедленные действия:** Устранить уязвимость SMTP.

**Краткосрочные улучшения (низкий риск):** Сосредоточиться на документировании, улучшении логирования, выносе жестко закодированных значений (особенно ID и конфигураций), исправлении XSS и других безопасных задачах из плана.

**Среднесрочные и долгосрочные улучшения:** Планировать и постепенно выполнять рефакторинг хранения данных, корневых скриптов, системы URL и обработки форм, а также рассматривать интеграцию с Drupal API там, где это возможно. Внедрение автоматического тестирования крайне рекомендуется перед любыми крупными изменениями.

Успешное выполнение даже части безопасных и осторожных задач из плана значительно повысит стабильность сайта и вашу производительность при работе с этим кодом.