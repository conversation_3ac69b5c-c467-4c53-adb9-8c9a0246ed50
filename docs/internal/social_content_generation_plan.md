# Техническое Задание (ТЗ) и План Реализации: Генерация Контента из Соцсетей

## 1. Введение

**Цель:** Интегрировать в компонент админки `PhuketAdminPropertyEditor` механизм для инициирования процесса генерации контента для объекта недвижимости на основе данных из социальных сетей (например, Instagram). Внешний сервис (n8n) будет отвечать за извлечение и обработку данных из соцсетей, а Drupal должен предоставить интерфейс для запуска этого процесса и приема сгенерированных данных.

**Контекст:** Текущий редактор `PhuketAdminPropertyEditor` позволяет вручную вводить все данные об объекте. Новая функция упростит создание контента, используя существующую информацию из постов в соцсетях.

## 2. Техническое Задание (ТЗ)

### 2.1. Пользовательский Интерфейс (UI)

1.  **Элементы управления:**
    *   Добавить поле ввода "URL Поста в Соцсети" (или аналогичное) в одной из вкладок редактора (например, на основной вкладке рядом с полями описания или в отдельной секции/вкладке "Генерация Контента").
    *   Добавить кнопку "Сгенерировать Контент" рядом с этим полем.
2.  **Логика UI:**
    *   Кнопка "Сгенерировать Контент" активна только если в поле ввода есть URL (или валидный идентификатор).
    *   При нажатии на кнопку:
        *   Собирается URL из поля ввода и ID текущего редактируемого объекта недвижимости (`propertyId`).
        *   Выполняется AJAX-запрос к новому API-методу в Drupal.
        *   Пользователю отображается уведомление о том, что процесс генерации запущен (например, "Запрос на генерацию контента отправлен..."). Кнопка может временно блокироваться.
        *   *Опционально:* Может быть добавлен индикатор статуса генерации (например, "Ожидает генерации", "Генерация...", "Завершено").

### 2.2. Бэкенд (Drupal)

1.  **API Метод для Триггера (в `PhuketAdminPropertyEditor.php`):**
    *   Создать новый публичный метод, например, `apiTriggerSocialContentGeneration($propertyId, $socialMediaUrl)`.
    *   Метод принимает ID объекта (`propertyId`) и URL поста (`socialMediaUrl`).
    *   **Действие:** Метод формирует и отправляет запрос (например, POST webhook) на заданный URL вашего n8n workflow, передавая `propertyId` и `socialMediaUrl`. URL для n8n должен быть конфигурируемым (например, через переменные Drupal или настройки модуля).
    *   **Ответ:** Метод возвращает статус (`status: 1` в случае успешной отправки запроса к n8n) и сообщение для пользователя.
2.  **Endpoint для Приема Данных от n8n:**
    *   Создать новый путь (menu callback) в Drupal (например, `api/v1/n8n/update-property-content`).
    *   **Аутентификация:** Endpoint должен проверять подлинность запроса от n8n (например, через секретный токен в заголовке или параметре запроса).
    *   **Входные данные:** Endpoint ожидает получить данные в формате JSON (POST-запрос), содержащие:
        *   `propertyId`: ID объекта для обновления.
        *   `generatedData`: Объект с сгенерированным контентом (например, `description_ru`, `description_en`, `image_urls`, `tags` и т.д. - структура по согласованию с n8n).
        *   `token`: Секретный токен для аутентификации.
    *   **Логика:**
        *   Проверить токен.
        *   Загрузить `PhuketPropertyAR` по `propertyId`.
        *   Обновить соответствующие поля объекта AR данными из `generatedData`.
        *   *Обработка изображений:* Если n8n возвращает URL изображений (`image_urls`), необходимо реализовать логику их скачивания, сохранения в файловую систему Drupal (например, в `files/site4/FileStore4/phuket/files/PropertyAR/{propertyId}/`) и добавления информации о файлах в поле `photos` объекта AR. Это может быть сложной частью и требует отдельной проработки (ошибки скачивания, дубликаты и т.д.). Возможно, на первом этапе n8n будет только генерировать текст.
        *   Сохранить обновленный `PhuketPropertyAR` (`$propertyAR->save()`).
    *   **Ответ n8n:** Вернуть статус операции (успех/ошибка).
3.  **Конфигурация:**
    *   Добавить возможность настройки URL для n8n webhook и секретного токена для callback endpoint (например, через `admin/config/services/s45_phuket`).

### 2.3. Взаимодействие с n8n (Ожидания от n8n)

1.  n8n workflow принимает `propertyId` и `socialMediaUrl`.
2.  n8n извлекает данные из соцсети, обрабатывает их (генерирует тексты, подготавливает URL изображений).
3.  n8n отправляет POST-запрос на callback endpoint Drupal (`api/v1/n8n/update-property-content`), передавая `propertyId`, сгенерированные данные (`generatedData`) и секретный `token`.

### 2.4. Обратная связь Пользователю

*   После успешного обновления данных через callback endpoint, в идеале, редактор `PhuketAdminPropertyEditor` должен обновиться автоматически, чтобы показать новые данные. Это можно реализовать через WebSocket или периодический опрос статуса, но на первом этапе можно ограничиться тем, что пользователь должен будет вручную обновить страницу редактора после получения уведомления о завершении генерации (уведомление может прийти от n8n или быть реализовано через отдельный механизм).

## 3. План Реализации

1.  **Подготовка:**
    *   Согласовать точную структуру данных (`generatedData`), которую n8n будет отправлять.
    *   Определить место размещения новых UI элементов (URL, кнопка) в редакторе.
    *   Настроить переменные/конфигурацию для n8n webhook URL и секретного токена.
2.  **Бэкенд (Drupal):**
    *   **Endpoint Приема:**
        *   Реализовать `hook_menu()` для пути `api/v1/n8n/update-property-content`.
        *   Написать функцию-коллбэк:
            *   Аутентификация по токену.
            *   Парсинг входящих данных.
            *   Загрузка `PhuketPropertyAR`.
            *   Обновление текстовых полей.
            *   *(Опционально/Этап 2)* Реализация логики скачивания и добавления изображений.
            *   Сохранение `PhuketPropertyAR`.
            *   Логирование операции.
    *   **API Триггера:**
        *   Добавить метод `apiTriggerSocialContentGeneration` в класс `PhuketAdminPropertyEditor`.
        *   Реализовать отправку запроса на n8n webhook URL с `propertyId` и `socialMediaUrl`.
        *   Добавить обработку ошибок запроса к n8n.
3.  **Фронтенд (Drupal):**
    *   **HTML:** Добавить поле ввода URL и кнопку "Сгенерировать Контент" в соответствующий `.inc` или `.tpl.php` файл редактора.
    *   **JavaScript:**
        *   Добавить JS-файл или код в существующий для обработки клика по кнопке.
        *   Реализовать AJAX-запрос к `apiTriggerSocialContentGeneration`.
        *   Показать уведомление пользователю (успех/ошибка отправки запроса).
        *   *(Опционально)* Реализовать блокировку кнопки/индикатор процесса.
4.  **Тестирование:**
    *   Протестировать отправку запроса от кнопки к API триггера.
    *   Протестировать отправку запроса от API триггера к n8n (можно использовать mock-сервис или реальный n8n).
    *   Протестировать Endpoint Приема: отправить тестовый POST-запрос (например, через `curl` или Postman), имитирующий n8n, и проверить обновление данных объекта недвижимости.
    *   Протестировать весь цикл end-to-end (если n8n workflow уже готов).
5.  **Документация и Завершение:**
    *   Добавить комментарии в код.
    *   Обновить `CHANGELOG.md`.
    *   Обновить `NewKnowledgeBase.md` (если были изучены новые аспекты).
    *   Проверить соответствие стандартам кодирования.

## 4. Открытые вопросы / Доработки

*   Детальная проработка механизма скачивания и сохранения изображений.
*   Реализация механизма уведомления пользователя о завершении генерации и обновления данных в редакторе (автоматическое обновление vs ручное).
*   Обработка ошибок на стороне n8n и информирование пользователя.
*   Нужно ли генерировать контент для разных языков или только для основного? (Зависит от возможностей n8n и требований). 