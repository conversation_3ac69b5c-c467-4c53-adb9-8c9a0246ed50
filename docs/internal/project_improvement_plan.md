# План Улучшений Проекта: indreamsphuket.com (Версия для Production, v3)

Этот документ описывает приоритезированный список задач для улучшения сайта `indreamsphuket.com`, основанный на углубленном анализе кастомного кода (`s45_*`, `Compo/`, корневые скрипты, `.inc` файлы, JS) и файла `indreams_full.md`. Задачи подобраны с учетом минимизации рисков для **рабочего (production)** окружения.

## Приоритеты Задач

*   **[КРИТИЧЕСКИЙ - Безопасность]**: Задачи, требующие немедленного исправления из-за серьезных рисков безопасности.
*   **[ВЫСОКИЙ - Безопасно]**: Задачи с низким риском, которые можно выполнить на production относительно безопасно и которые принесут значительную пользу (обслуживание, безопасность, документация).
*   **[СРЕДНИЙ - Осторожно]**: Задачи, требующие большей осторожности, возможно, предварительного тестирования на staging-среде, но все еще выполнимые без глобальных изменений.
*   **[НИЗКИЙ - Требует Планирования]**: Сложные или рискованные задачи (крупный рефакторинг, архитектурные изменения), которые следует выполнять только после тщательного планирования и тестирования вне production.

---

## I. Безопасность (Критические и Осторожные Задачи)

*   **[КРИТИЧЕСКИЙ - Безопасность] Задача 4.3: Удаление учетных данных SMTP из кода.**
    *   **Описание:** Немедленно удалить жестко закодированные логин и пароль SMTP из функции `s45_phuket_form_mail` (`s45_phuket_form_api.inc`). Перенести их в конфигурацию Drupal (`settings.php` предпочтительнее для секретов) и получать их оттуда.
    *   **Риск:** Низкий (но критически важный для безопасности).

*   **[СРЕДНИЙ - Осторожно] Задача 4: Аудит и исправление экранирования вывода (XSS).**
    *   **Описание:** Проверить и добавить функции экранирования Drupal (`check_plain()`, `filter_xss()`) в кастомных `.tpl.php` файлах (в `sites/all/themes/site45/blocks/` и `sites/all/modules/__s45/Compo/`) для предотвращения XSS уязвимостей. Анализ показал их отсутствие.
    *   **Конкретные шаги:**
        1.  Выбрать 2-3 часто используемых компонента (`Compo/`) или шаблона темы (`site45/blocks/`).
        2.  Найти места вывода переменных без экранирования (например, `<?php print $variable; ?>` или `<?= $variable ?>`).
        3.  Применить `check_plain()` для простого текста или `filter_xss()` для HTML, который должен быть разрешен частично.
        4.  Тщательно протестировать отображение страниц после изменений.
    *   **Риск:** Средний (неправильное экранирование может сломать верстку, требует тестирования).

*   **[СРЕДНИЙ - Осторожно] Задача 4.1 (Безопасность): Санитизация `$_GET['propertyId']`**.
    *   **Описание:** Перед использованием значения `$_GET['propertyId']` в `PhuketAdminPropertyEditor::beforeRender`, применить функцию санитизации, например `check_plain()`, чтобы предотвратить потенциальные XSS атаки через URL, если это не делается где-то еще.
    *   **Конкретные шаги:** Заменить `$propertyId = $_GET['propertyId'];` на `$propertyId = isset($_GET['propertyId']) ? check_plain($_GET['propertyId']) : null;`.
    *   **Риск:** Низкий.

*   **[СРЕДНИЙ - Осторожно] Задача 4.2 (Безопасность): Безопасная генерация HTML в описании (`worldvillas_feed.php`)**.
    *   **Описание:** Пересмотреть генерацию HTML внутри CDATA для описания в `worldvillas_feed.php`. Вместо `strip_tags` и ручного добавления `<p>`, `<ul>` и т.д., использовать более безопасный подход, например, `filter_xss()` с разрешенными тегами или библиотеку для преобразования Markdown/текста в безопасный HTML.
    *   **Риск:** Средний (требует выбора и внедрения безопасного метода генерации HTML).

## II. Улучшение Кода и Документации (Безопасные Задачи)

*   **[ВЫСОКИЙ - Безопасно] Задача 1: Улучшение комментариев в коде (Общая).**
    *   **Описание:** Добавить PHPDoc блоки и inline-комментарии (на английском языке для консистентности) к ключевым функциям, методам и классам в кастомных модулях (`s45_*`, `Compo/`) и `.inc` файлах. Это значительно улучшит понимание кода.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.1 (Документация): Детализация комментариев для `PhuketPropertyAR`**.
    *   **Описание:** Добавить подробные PHPDoc комментарии ко *всем* публичным свойствам класса `PhuketPropertyAR`, объясняя их назначение, ожидаемый тип данных и возможные значения.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.3 (Документация): Комментирование `PhuketAdminPropertyEditor`**.
    *   **Описание:** Добавить PHPDoc блоки для класса `PhuketAdminPropertyEditor` и его методов, объясняя их назначение, параметры и возвращаемые значения. Добавить inline-комментарии для сложной логики.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.4 (Документация): Документирование связи Property -> Project Address.**
    *   **Описание:** Добавить комментарий в `PhuketAdminPropertyEditor::apiSaveProperty`, явно указывающий на обновление адреса связанного проекта.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.5 (Документация): Документирование `array_values` для photos/plans.**
    *   **Описание:** Добавить комментарий в `PhuketAdminPropertyEditor::apiSaveProperty`, объясняющий использование `array_values((array) ...)` для `photos` и `plans`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.6 (Улучшение кода): Перенос inline CSS в файл.**
    *   **Описание:** Переместить CSS стили для кнопки скачивания фото из `PhuketAdminPropertyEditor::beforeRender` в отдельный CSS файл.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.7 (Документация): Комментирование `PhuketPropertyQuery`**.
    *   **Описание:** Добавить PHPDoc для класса и ключевых методов, объясняя использование read model, процесс обновления, логику фильтрации и производных полей.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.8 (Документация): Документирование логики производных полей.**
    *   **Описание:** Добавить комментарии в `PhuketPropertyQuery::handlePhuketPropertySaved`, `getAdditionalSale`, `getAdditionalRent`, объясняющие логику расчета производных полей.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.9 (Документация): Комментирование `PhuketPropertyTable`**.
    *   **Описание:** Добавить PHPDoc комментарии к классу и его свойствам, объясняя назначение каждой колонки read model таблицы `_phuket_Property`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.10 (Документация): Комментирование `QueryFromEvents`**.
    *   **Описание:** Добавить PHPDoc для класса `QueryFromEvents`, объясняя механизм обработки событий, использование счетчиков, логику сброса таблицы и ограничение `$eventMaxCount`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.11 (Документация): Комментирование `Compo.php`**.
    *   **Описание:** Добавить PHPDoc для базового класса `Compo`, объясняя жизненный цикл рендеринга, обработку ресурсов, атрибутов, оберток, и механизм редактирования/сохранения.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.12 (Документация): Комментирование `CompoRepo`**.
    *   **Описание:** Добавить PHPDoc для класса `CompoRepo`, объясняя логику загрузки (Events -> JSON -> Class), кэширование, сохранение через Event Sourcing, фильтрацию свойств и зависимости.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.13 (Документация): Исследование и документирование роли `Compo.s45.json`**.
    *   **Описание:** Выяснить точное назначение JSON файла (`Compo.s45.json` / `Compo.s45_new.json`). Задокументировать это.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.14 (Документация): Документирование `CompoInfo.s45.json`**.
    *   **Описание:** Описать формат и назначение файла `CompoInfo.s45.json` и процесс добавления новых компонентов.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.15 (Документация): Комментирование `worldvillas_feed.php`**.
    *   **Описание:** Добавить комментарии, объясняющие логику скрипта, фильтрацию, получение описания, генерацию XML и статистику.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.16 (Документация): Комментирование `s45_path.module`**.
    *   **Описание:** Добавить комментарии к `hook_url_inbound_alter` и `s45_path_url`, объясняя последовательность обработки URL и логику генерации.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.17 (Документация): Комментирование `Path.php`**.
    *   **Описание:** Добавить PHPDoc для класса и методов, объясняя работу с таблицей `_s45_aliases` и логику добавления уникальных алиасов.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.18 (Документация): Комментирование `Redirect.php`**.
    *   **Описание:** Добавить PHPDoc, объясняя логику редиректа с системных путей на алиасы и указывая на закомментированную логику.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.19 (Документация): Комментирование `s45_phuket_seo.inc`**.
    *   **Описание:** Добавить комментарии к функциям, объясняя логику установки canonical, hreflang, meta description, загрузки и применения SEO-настроек.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.20 (Документация): Комментирование `s45_imagestyles.module`**.
    *   **Описание:** Добавить комментарии к константам и хуку, объясняя назначение стилей. Рассмотреть удаление закомментированных стилей.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.21 (Документация): Комментирование `s45_phuket_lib.inc`**.
    *   **Описание:** Добавить комментарии к функциям, объясняя их назначение (хуки, темы, отладка, профиль пользователя).
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.22 (Документация): Комментирование `s45_phuket_export.inc`**.
    *   **Описание:** Добавить комментарии, объясняющие роль функции, использование `PhuketExportQuery` и конвертеров, фикс URL изображений.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.23 (Документация): Комментирование `s45_phuket_form_api.inc`**.
    *   **Описание:** Добавить комментарии, объясняющие логику обработки форм, сохранение AR, отправку почты, использование `FormApi2` и **проблему безопасности с SMTP**.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.24 (Документация): Комментирование `s45_phuket_form_api2.inc`**.
    *   **Описание:** Добавить комментарии, объясняющие назначение эндпоинта и делегирование обработки классу `FormApi2`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.25 (Документация): Комментирование `template.php`**.
    *   **Описание:** Добавить комментарии к функциям, объясняя отключение CSS и добавление классов в `<body>`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 1.26 (Документация): Комментирование `property_photos_download.js`**.
    *   **Описание:** Добавить/улучшить комментарии, объясняя логику извлечения ID, цепочку методов скачивания, механизм обратной связи и таймаут.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 2: Актуализация основной документации.**
    *   **Описание:** Проверить и обновить `docs/PROJECT_DOCS.md`, чтобы он точно отражал текущую архитектуру.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 3: Создание README для ключевых модулей.**
    *   **Описание:** Создать `README.md` для `s45_base`, `s45_phuket`, `s45_path`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 8: Составление списка зависимостей и версий.**
    *   **Описание:** Создать полный список модулей, тем, версий Drupal/PHP.
    *   **Риск:** Низкий.

## III. Стабильность и Качество Кода (Осторожные Задачи)

*   **[СРЕДНИЙ - Осторожно] Задача 5.3 (Улучшение кода): Удаление подавления ошибок `@unserialize`**.
    *   **Описание:** Убрать `@` перед `unserialize` в `PhuketPropertyQuery` и добавить проверку результата + логирование ошибок.
    *   **Риск:** Низкий (позволит увидеть скрытые ошибки).

*   **[СРЕДНИЙ - Осторожно] Задача 5.4 (Улучшение кода): Улучшение обработки ошибок в `compoCreate`**.
    *   **Описание:** В `CompoRepo::compoCreate` заменить `drupal_set_message` на `watchdog()`. Добавить проверку `file_exists` перед `require_once`.
    *   **Риск:** Низкий.

*   **[СРЕДНИЙ - Осторожно] Задача 5.5 (Улучшение кода): Замена кастомного логирования в `worldvillas_feed.php`**.
    *   **Описание:** Заменить `file_put_contents($log_file, ...)` на `watchdog()`.
    *   **Риск:** Низкий.

*   **[ВЫСОКИЙ - Безопасно] Задача 5.6 (Улучшение кода): Замена `dsm()` в отладочных функциях.**
    *   **Описание:** В `s45_phuket_ar` и `s45_phuket_history` заменить `dsm()` на `watchdog()` или проверку прав.
    *   **Риск:** Низкий.

*   **[СРЕДНИЙ - Осторожно] Задача 6: Вынос очевидных жестко закодированных значений (Общая).**
    *   **Описание:** Найти и перенести простые жестко закодированные значения (ключ sitemap, ID локаций, ID мебели, ID опций, адреса email, названия форм, конфигурация фидов) в переменные Drupal или конфигурацию.
    *   **Риск:** Низкий-Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 1.2 (Улучшение кода): Рефакторинг установки значений по умолчанию в `afterSetProps`.**
    *   **Описание:** Сделать установку значений по умолчанию в `PhuketPropertyAR::afterSetProps` более явной (инициализация при объявлении или отдельный метод).
    *   **Риск:** Низкий-Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 6.7 (Улучшение кода): Вынос списков ID пользователей.**
    *   **Описание:** Заменить жестко закодированные массивы ID в `s45_phuket_adminUsers()` и `s45_phuket_agentUsers()` на использование ролей Drupal.
    *   **Риск:** Низкий-Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 10.11 (Улучшение кода): Восстановление автосоздания таблицы `_s45_aliases`**.
    *   **Описание:** Раскомментировать код в `Path::checkTableAliases`.
    *   **Риск:** Низкий.

*   **[СРЕДНИЙ - Осторожно] Задача 10.15 (Рефакторинг): Устранение дублирования `getDefPaymentTerms`**.
    *   **Описание:** Оставить одну реализацию функции `getDefPaymentTerms` и вызывать ее из разных мест. Вынести условия в конфигурацию.
    *   **Риск:** Низкий.

*   **[СРЕДНИЙ - Осторожно] Задача 10.18 (Рефакторинг): Устранение `str_replace` для URL изображений.**
    *   **Описание:** Исправить URL изображений в источнике данных вместо замены на лету в `s45_phuket_export.inc`.
    *   **Риск:** Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 10.21 (Улучшение кода): Обеспечение `data-property-id`**.
    *   **Описание:** Убедиться, что `data-property-id` всегда устанавливается на кнопке скачивания фото. Упростить JS для получения ID.
    *   **Риск:** Низкий-Средний.

## IV. Производительность (Осторожные Задачи)

*   **[СРЕДНИЙ - Осторожно] Задача 7: Анализ и оптимизация одного запроса.**
    *   **Описание:** Идентифицировать и оптимизировать один медленный SQL-запрос (например, через `EXPLAIN` и добавление индекса).
    *   **Риск:** Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 7.2 (Производительность): Оптимизация запроса в `worldvillas_feed.php`**.
    *   **Описание:** Перенести PHP-фильтрацию в основной SQL-запрос.
    *   **Риск:** Средний.

*   **[СРЕДНИЙ - Осторожно] Задача 9.1 (Улучшение кода): Рефакторинг XML генерации в `worldvillas_feed.php`**.
    *   **Описание:** Переписать генерацию XML с использованием `DOMDocument` или `XMLWriter`.
    *   **Риск:** Средний.

## V. Сложные Задачи (Требуют Планирования - НЕ для немедленного выполнения на Production)

*   **[НИЗКИЙ - Требует Планирования] Задача 7.1 (Производительность): Анализ индексов Read Model.**
*   **[НИЗКИЙ - Требует Планирования] Задача 7.3 (Производительность): Анализ индексов `_s45_aliases`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10: Рефакторинг сериализованных данных.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.1 (Улучшение кода): Удаление кода миграции локации.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.2 (Улучшение кода): Исследование `json_decode(json_encode())` при копировании.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.3 (Рефакторинг): Оптимизация обновления Read Model.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.4 (Рефакторинг): Оптимизация хранения списков ID.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.5 (Рефакторинг): Перенос обновления Read Model в Cron.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.6 (Рефакторинг): Использование Queue API для обработки событий.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.7 (Рефакторинг): Исследование `CompoRepo`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.8 (Рефакторинг): Оптимизация `setAttributes`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.9 (Рефакторинг): Замена глобального кэша `$GLOBALS['AllCompoData']`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.10 (Рефакторинг): Устранение ручных `include_once` в `s45_path.module`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.12 (Рефакторинг): Улучшение обработки дубликатов алиасов.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.13 (Исследование): Исследование редиректов из `_s45_redirects`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.16 (Рефакторинг): Оптимизация подключения ресурсов тем.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.17 (Исследование): Изучение `PhuketExportQuery` и Конвертеров.**
*   **[НИЗКИЙ - Требует Планирования] Задача 10.19 (Исследование): Изучение `FormApi2` и `prSiteMailing`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.20 (Исследование): Изучение `FormApi2.php`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 10.22 (Рефакторинг): Рефакторинг скачивания фото с AJAX**.
*   **[НИЗКИЙ - Требует Планирования] Задача 11: Рефакторинг корневых скриптов (масштабный).**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.1 (Рефакторинг): Переход на стандартные переводы Drupal (`t()`).**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.3 (Рефакторинг): Интеграция с Drupal API (Ресурсы).**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.4 (Рефакторинг): Переход на Drupal Render API.**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.5 (Рефакторинг): Перенос `worldvillas_feed.php` в модуль Drupal.**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.6 (Рефакторинг): Интеграция с модулем Metatag.**
*   **[НИЗКИЙ - Требует Планирования] Задача 11.7 (Рефакторинг): Использование `Drupal.behaviors`**.
*   **[НИЗКИЙ - Требует Планирования] Задача 12: Внедрение автоматизированного тестирования.**
*   **[НИЗКИЙ - Требует Планирования] Задача 13: Обновление Drupal Core и модулей.**

---

## VI. Генераторы XML Фидов и URL

**Скрипты в корневом каталоге:**

*   `dev_feed_5000plus.php` -> Генерирует `property_feed_5000plus_sale.xml`, `property_feed_5000plus.xml` (Партнер: Tranio)
*   `dev_feed.php` -> Генерирует `fb_feed_sale_*.xml`, `fb_feed_rent_*.xml` (Партнер: Facebook Real Estate Ads)
*   `green-acres.php` -> Генерирует `green-acres.xml` (Партнер: Green-Acres)
*   `nestopia.php` -> Генерирует `nestopia.xml` (Партнер: Nestoria)
*   `worldvillas_feed.php` -> Генерирует `worldvillas.xml` (Партнер: WorldVillas)

**Коллбеки модуля (`s45_phuket_export.inc`):**

*   Доступ по URL `/export/hipflat.xml` (Партнер: Hipflat)
*   Доступ по URL `/export/fazwaz.xml` (Партнер: FazWaz)
*   Доступ по URL `/export/fazwazru.xml` (Партнер: FazWaz RU)
*   Доступ по URL `/export/app.xml` (Партнер: "App" - неясно)

*(Примечание: Существуют также генераторы CSV фидов, например `dev_feed_csv.php` / `FBFeedCsv.php`).*