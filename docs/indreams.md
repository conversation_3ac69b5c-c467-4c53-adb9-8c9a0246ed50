# Топология кастомного модуля сайта InDreams Phuket

## 1. Общая структура модуля `sites/all/modules/__s45`

```
sites/all/modules/__s45/
├── s45_phuket/                     # Основной модуль сайта
├── s45_base/                       # Базовый модуль, основа для работы других модулей
├── s45_test/                       # Модуль для тестирования
├── s45_vendor/                     # Внешние зависимости
├── s45_path/                       # Модуль для работы с путями
├── s45_imagestyles/                # Стили изображений
├── s45_page/                       # Модуль для работы со страницами
├── s452_base/                      # Альтернативная версия базового модуля
└── Compo/                          # Общие компоненты
```

## 2. Структура основного модуля `s45_phuket`

```
s45_phuket/
├── classes/                        # Директория с PHP классами
│   └── Site45/
│       └── Sets/
│           └── Phuket/             # Основной набор классов проекта
│               ├── Compo/          # Компоненты UI
│               │   ├── Admin/      # Административные компоненты
│               │   ├── Article/    # Компоненты для статей
│               │   ├── Chem/       # Компоненты для химических объектов
│               │   ├── Common/     # Общие компоненты
│               │   │   ├── PhuketGallery/    # Старая галерея
│               │   │   └── PhuketGalleryD2/  # Новая версия галереи для нового дизайна
│               │   ├── Forms/      # Формы
│               │   ├── Front/      # Фронтенд компоненты
│               │   ├── Landings/   # Компоненты посадочных страниц
│               │   ├── LandingRent/# Компоненты для аренды
│               │   ├── LandingSale/# Компоненты для продажи
│               │   ├── News/       # Компоненты новостей
│               │   ├── PhuketAgentEditor/ # Редактор агентов
│               │   ├── Property/   # Компоненты недвижимости
│               │   │   ├── PhuketPropMediaD2/  # Медиа недвижимости для нового дизайна
│               │   │   └── PhuketPropPlanD2/   # Планы и схемы для нового дизайна
│               │   ├── PropertyBlocks/ # Блоки недвижимости
│               │   ├── Reviews/    # Компоненты отзывов
│               │   ├── Screens/    # Экранные компоненты
│               │   ├── Serv/       # Сервисные компоненты
│               │   └── TvAds/      # Компоненты для ТВ-рекламы
│               │
│               ├── Converters/     # Конвертеры данных
│               │
│               ├── Domain/         # Доменная логика (AR - Active Record модели)
│               │   ├── PhuketPropertyAR.php        # Модель недвижимости
│               │   ├── PhuketProjectAR.php         # Модель проекта
│               │   ├── PhuketArticleAR.php         # Модель статьи
│               │   ├── PhuketNewsAR.php            # Модель новости
│               │   ├── PhuketReviewAR.php          # Модель отзыва
│               │   ├── PhuketServiceAR.php         # Модель услуги
│               │   ├── PhuketBoatAR.php            # Модель лодки
│               │   ├── PhuketExcursionAR.php       # Модель экскурсии
│               │   ├── PhuketClubAR.php            # Модель клуба
│               │   ├── PhuketUserAR.php            # Модель пользователя
│               │   ├── PhuketOptionAR.php          # Модель опции
│               │   ├── PhuketPageSeoAR.php         # Модель SEO страницы
│               │   ├── PhuketSelectionAR.php       # Модель подборки
│               │   ├── PhuketFormAR.php            # Модель формы
│               │   ├── PhuketFormConf.php          # Конфигурация формы
│               │   ├── PhuketFormConfAR.php        # Модель конфигурации формы
│               │   ├── PhuketPersonAR.php          # Модель персоны
│               │   ├── PhuketReservationAR.php     # Модель бронирования
│               │   └── IndreamsPropertyRepo.php    # Репозиторий недвижимости
│               │
│               ├── Dto/            # Data Transfer Objects
│               │   ├── ScreenDto.php               # DTO для экранов
│               │   ├── TvScreenDto.php             # DTO для ТВ-экранов
│               │   ├── ExcursionDto.php            # DTO для экскурсий
│               │   ├── PhuketAgentDto.php          # DTO для агентов
│               │   ├── PhuketArticleDto.php        # DTO для статей
│               │   ├── PhuketPodborkaDto.php       # DTO для подборок
│               │   └── PhuketReviewDto.php         # DTO для отзывов
│               │
│               ├── Form/           # Формы
│               │
│               ├── Funcs/          # Вспомогательные функции
│               │
│               ├── Parser/         # Парсеры
│               │
│               ├── Query/          # Поисковые системы и запросы к БД
│               │   ├── PropertySearch/             # Поиск недвижимости
│               │   │   ├── PhuketPropertyQuery.php # Класс запросов к БД
│               │   │   ├── PhuketPropertyTable.php # Таблица недвижимости
│               │   │   └── PhuketPropertyDto.php   # DTO для недвижимости
│               │   │
│               │   ├── Property2Search/            # Расширенный поиск
│               │   ├── Property3Search/            # Дополнительный поиск
│               │   ├── ProjectSearch/              # Поиск проектов
│               │   ├── RentSearch/                 # Поиск аренды
│               │   ├── ExportSearch/               # Экспорт данных
│               │   ├── ArticleSearch/              # Поиск статей
│               │   ├── NewsSearch/                 # Поиск новостей
│               │   ├── ReviewSearch/               # Поиск отзывов
│               │   ├── ServiceSearch/              # Поиск услуг
│               │   ├── UserSearch/                 # Поиск пользователей
│               │   ├── OptionSearch/               # Поиск опций
│               │   ├── PageSeoSearch/              # Поиск SEO страниц
│               │   ├── SelectionSearch/            # Поиск подборок
│               │   ├── SitemapSearch/              # Генерация карты сайта
│               │   ├── BoatSearch/                 # Поиск лодок
│               │   ├── ExcursionSearch/            # Поиск экскурсий
│               │   ├── ClubSearch/                 # Поиск клубов
│               │   ├── WordSearch/                 # Текстовый поиск
│               │   ├── TvScreenSearch/             # Поиск ТВ-экранов
│               │   └── (другие поисковые модули)
│               │
│               └── Resources/      # Ресурсы
│
├── config/                         # Конфигурационные файлы
│
├── s45_phuket.module               # Основной файл модуля
├── s45_phuket.info                 # Информация о модуле
├── s45_phuket_export.inc           # Функции экспорта
├── s45_phuket_form_api.inc         # API для форм
├── s45_phuket_form_api2.inc        # Дополнительный API для форм
├── s45_phuket_lib.inc              # Библиотечные функции
├── s45_phuket_pdf.inc              # Функции для работы с PDF
├── s45_phuket_perenos.inc          # Функции переноса данных
├── s45_phuket_perenos2.inc         # Дополнительные функции переноса
├── s45_phuket_query.inc            # Функции запросов
├── s45_phuket_seo.inc              # Функции для SEO
├── s45_phuket_sitemap.inc          # Функции для генерации карты сайта
└── s45_phuket_utils.inc            # Утилиты
```

## 3. Структура базового модуля `s45_base`

```
s45_base/
├── classes/                        # Директория с PHP классами
│
├── _New/                           # Новые функциональности
├── _repo/                          # Репозиторий
├── _logs/                          # Логи
│
├── s45_base.module                 # Основной файл модуля
├── s45_base.info                   # Информация о модуле
├── s45_base.lib.inc                # Библиотечные функции
├── s45_base.access.inc             # Функции доступа
├── s45_base.api2.inc               # API функции
├── s45_base.robots.inc             # Функции для robots.txt
└── s45_base.2.inc                  # Дополнительные функции
```

## 4. Иерархия зависимостей

```
s45_phuket (основной модуль сайта)
└── s45_base (базовый модуль)
    ├── xautoload (автозагрузка классов)
    ├── jquery_update (обновление jQuery)
    └── locale (локализация)
```

## 5. Основные сущности и их структура

### 5.1. Property (Недвижимость)
- **Таблица в БД**: `_phuket_Property`
- **Классы**:
  - Domain/PhuketPropertyAR.php - Active Record модель
  - Query/PropertySearch/PhuketPropertyQuery.php - класс запросов
  - Query/PropertySearch/PhuketPropertyTable.php - определение таблицы
  - Query/PropertySearch/PhuketPropertyDto.php - DTO объект

### 5.2. Project (Проект)
- **Таблица в БД**: `_phuket_Project`
- **Классы**:
  - Domain/PhuketProjectAR.php - Active Record модель
  - Query/ProjectSearch/* - классы поиска

### 5.3. Agent (Агент)
- **Таблица в БД**: `_phuket_Agent`
- **Классы**:
  - Domain/PhuketAgentAR.php - Active Record модель
  - Dto/PhuketAgentDto.php - DTO объект
  - Query/PhuketAgentQuery.php - класс запросов

### 5.4. Article (Статья)
- **Таблица в БД**: `_phuket_Article`
- **Классы**:
  - Domain/PhuketArticleAR.php - Active Record модель
  - Dto/PhuketArticleDto.php - DTO объект
  - Query/ArticleSearch/* - классы поиска
  - Query/PhuketArticleQuery.php - класс запросов

### 5.5. Review (Отзыв)
- **Таблица в БД**: `_phuket_Review`
- **Классы**:
  - Domain/PhuketReviewAR.php - Active Record модель
  - Dto/PhuketReviewDto.php - DTO объект
  - Query/ReviewSearch/* - классы поиска
  - Query/PhuketReviewQuery.php - класс запросов

## 6. API и точки входа

### 6.1. URL-маршруты (меню)
- `/s45phuket` - тестовая страница
- `/s45phuket-exec` - страница выполнения команд
- `/s45phuket-perenos2` - страница переноса данных
- `/s45-indreams-export` - страница экспорта
- `/s45PhuketWordSearch` - поиск по тексту
- `/s45PhuketPersonSearch` - поиск по персонам
- `/s45PhuketQuery` - страница запросов

### 6.2. Хуки Drupal
Определены в основном файле модуля s45_phuket.module

## 7. Ключевые файлы и их функциональность

### 7.1. Основные файлы модуля
- **s45_phuket.module**: Основной файл модуля с определением хуков Drupal
- **s45_phuket_utils.inc**: Вспомогательные функции
- **s45_phuket_export.inc**: Функции экспорта данных
- **s45_phuket_lib.inc**: Библиотечные функции
- **s45_phuket_query.inc**: Функции для запросов к БД
- **s45_phuket_form_api.inc**: API для работы с формами
- **s45_phuket_seo.inc**: Функции для SEO-оптимизации
- **s45_phuket_sitemap.inc**: Функции для генерации карты сайта

### 7.2. Ключевые классы
- **Domain/PhuketPropertyAR.php**: Основная Active Record модель недвижимости
- **Domain/IndreamsPropertyRepo.php**: Репозиторий недвижимости
- **Query/PropertySearch/PhuketPropertyQuery.php**: Основной класс запросов к БД для недвижимости

## 8. Архитектурные паттерны

### 8.1. Active Record
Использован для моделей данных (классы с суффиксом AR).
```php
class PhuketPropertyAR extends S45ActiveRecord {
    // ...
}
```

### 8.2. Repository
Использован для доступа к данным (IndreamsPropertyRepo.php).
```php
class IndreamsPropertyRepo {
    // ...
}
```

### 8.3. DTO (Data Transfer Objects)
Используются для передачи данных между слоями.
```php
class PhuketPropertyDto {
    // ...
}
```

### 8.4. Query Builder
Используется для построения сложных запросов (PhuketPropertyQuery.php).
```php
class PhuketPropertyQuery extends S45QueryBase {
    // ...
}
```

## 9. Структура базы данных

### 9.1. Основные таблицы
- `_phuket_Property` - Недвижимость
- `_phuket_Project` - Проекты
- `_phuket_Agent` - Агенты
- `_phuket_Article` - Статьи
- `_phuket_News` - Новости
- `_phuket_Review` - Отзывы
- `_phuket_Service` - Услуги

## 10. Рекомендации по доработке и изменению сайта

### 10.1. Общие принципы работы с модулем
1. **Система автозагрузки классов**: Модуль использует xautoload для автоматической загрузки классов по PSR-стандарту.
2. **Модульность**: Все функциональности разделены на модули в соответствующих директориях.
3. **ORM и Active Record**: Используется система Active Record для работы с моделями данных.
4. **DTO**: Для передачи данных между слоями используются DTO-объекты.

### 10.2. Добавление новой функциональности
1. **Новая сущность**:
   - Создать Active Record класс в директории Domain/
   - Создать DTO в директории Dto/
   - Создать Query классы в директории Query/
   - Добавить UI компоненты в директорию Compo/

2. **Новая страница**:
   - Добавить маршрут в s45_phuket.module
   - Создать компонент в директории Compo/
   - При необходимости добавить SEO-правила в s45_phuket_seo.inc

3. **Изменение существующей функциональности**:
   - Найти соответствующие классы и модифицировать их
   - При изменении структуры данных обновить DTO объекты

### 10.3. Работа с API
1. **Экспорт данных**:
   - Использовать функции из s45_phuket_export.inc
   - При необходимости использовать классы из Query/ExportSearch/

2. **Формы и валидация**:
   - Использовать API из s45_phuket_form_api.inc
   - Создавать компоненты форм в директории Compo/Forms/

### 10.4. Работа с SEO
1. **Мета-теги**:
   - Использовать функции из s45_phuket_seo.inc
   - При необходимости использовать классы из Query/PageSeoSearch/

2. **Sitemap**:
   - Использовать функции из s45_phuket_sitemap.inc
   - При необходимости использовать классы из Query/SitemapSearch/

### 10.5. Безопасность
1. **Валидация ввода**:
   - Всегда использовать функции валидации из s45_phuket_lib.inc
   - Использовать параметризованные запросы в Query классах

2. **Контроль доступа**:
   - Использовать системные функции доступа Drupal
   - При необходимости использовать функции из s45_base.access.inc

## 11. Примеры основных операций

### 11.1. Получение объекта недвижимости по ID
```php
// Через репозиторий
$repo = new \Site45\Sets\Phuket\Domain\IndreamsPropertyRepo();
$property = $repo->getPropertyById($id);

// Через Active Record
$property = \Site45\Sets\Phuket\Domain\PhuketPropertyAR::findById($id);
```

### 11.2. Поиск объектов недвижимости
```php
$query = new \Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery();
$query->addFilter('dealType', 'sale');
$query->addFilter('bedrooms', 2, '>=');
$query->setOrder('price_sale', 'ASC');
$results = $query->getResults();
```

### 11.3. Создание нового объекта недвижимости
```php
$property = new \Site45\Sets\Phuket\Domain\PhuketPropertyAR();
$property->name = ['en' => 'Villa Name', 'ru' => 'Название виллы'];
$property->dealType = 'sale';
$property->propertyType = 'villa';
$property->bedrooms = 3;
$property->price_sale = 500000;
$property->save();
```

### 11.4. Экспорт данных в XML
```php
// В файле s45_phuket_export.inc
function s45_phuket_export_property_to_xml($propertyId) {
  $repo = new \Site45\Sets\Phuket\Domain\IndreamsPropertyRepo();
  $property = $repo->getPropertyById($propertyId);
  
  $xml = new SimpleXMLElement('<property></property>');
  $xml->addChild('id', $property->id);
  $xml->addChild('name', $property->name['en']);
  // ...
  
  return $xml->asXML();
}
```

### 11.5. Создание компонента UI
```php
namespace Site45\Sets\Phuket\Compo\Property;

class PropertyCard {
  protected $property;
  
  public function __construct($property) {
    $this->property = $property;
  }
  
  public function render() {
    $output = '<div class="property-card">';
    $output .= '<h3>' . $this->property->name[LANGUAGE_NONE] . '</h3>';
    $output .= '<div class="price">' . $this->property->price_sale . '</div>';
    $output .= '</div>';
    
    return $output;
  }
}
```

## 12. Новая тема сайта и интегрированные компоненты

### 12.1. Структура темы сайта
Тема сайта расположена в директории `sites/all/themes/site45/` и имеет следующую структуру:
```
site45/
├── blocks/                         # Блоки темы
├── css/                            # CSS стили
├── site45.info                     # Информация о теме
└── template.php                    # Основной файл темы с хуками
```

### 12.2. Компоненты, адаптированные для нового дизайна
Для нового дизайна были созданы специальные компоненты с суффиксом D2, которые включают:

1. **PhuketGalleryD2** - Галерея изображений с поддержкой jBox
   - Расположение: `classes/Site45/Sets/Phuket/Compo/Common/PhuketGalleryD2/`
   - Особенности:
     - Улучшенная обработка ошибок загрузки изображений
     - Автоматический фоллбэк на placeholder
     - Интеграция с jBox для просмотра изображений в лайтбоксе

2. **PhuketPropMediaD2** - Компонент для отображения медиа на странице недвижимости
   - Расположение: `classes/Site45/Sets/Phuket/Compo/Property/PhuketPropMediaD2/`
   - Особенности:
     - Поддержка видео с YouTube и Vimeo
     - Отображение мастер-плана недвижимости
     - Улучшенная обработка ошибок загрузки изображений

3. **PhuketPropPlanD2** - Компонент для отображения планов недвижимости
   - Расположение: `classes/Site45/Sets/Phuket/Compo/Property/PhuketPropPlanD2/`
   - Особенности:
     - Отображение планов этажей и мастер-планов
     - Интеграция с jBox для просмотра увеличенных изображений
     - Улучшенная обработка ошибок загрузки

### 12.3. Вспомогательные функции и библиотеки для нового дизайна

1. **s45_add_jbox** - Функция для добавления JavaScript библиотеки jBox
   - Расположение: `sites/all/modules/__s45/s45_vendor/s45_vendor.module`
   - Назначение: добавляет лайтбокс для просмотра изображений и модальные окна

2. **s45_phuket_add_theme2** - Функция для загрузки ресурсов новой темы
   - Расположение: `sites/all/modules/__s45/s45_phuket/s45_phuket.module`
   - Назначение: загружает JS и CSS файлы для нового дизайна

3. **s45_imgSrc** - Функция для получения URL изображений с поддержкой стилей
   - Расположение: `sites/all/modules/__s45/s45_base/s45_base.lib.inc`
   - Назначение: формирует URL для изображений с учетом выбранного стиля

### 12.4. Особенности работы с изображениями в новой теме

1. **Стили изображений**
   - В модуле `s45_imagestyles` определены стили для нового дизайна
   - Основные стили:
     - `S45_IMST_1900X1000_SCALE` - для полноразмерных изображений в лайтбоксе
     - `S45_IMST_600X600_CROP` - для миниатюр в галерее

2. **Обработка ошибок изображений**
   - Все изображения имеют атрибут `onerror` для замены на placeholder в случае ошибки
   - Используется единый placeholder: `/sites/all/modules/__s45/Compo/Common/PhuketGallery/img/placeholder.jpg`

3. **jBox для просмотра изображений**
   - Используется для просмотра увеличенных изображений
   - Реализована специальная логика для предзагрузки и проверки изображений перед отображением

## 13. Рекомендации по работе с новой темой и компонентами

### 13.1. Создание новых компонентов для нового дизайна
1. Создавать компоненты с суффиксом D2 в соответствующих директориях
2. Включать улучшенную обработку ошибок для всех изображений
3. Использовать jBox для просмотра увеличенных изображений

### 13.2. Адаптация существующих компонентов
1. Создать копию компонента с суффиксом D2
2. Обновить HTML-структуру в соответствии с новым дизайном
3. Добавить вызов `s45_phuket_add_theme2()` для загрузки ресурсов новой темы
4. Использовать классы CSS из новой темы

### 13.3. Работа с изображениями
1. Всегда использовать функцию `s45_imgSrc()` для получения URL изображений
2. Добавлять обработчик ошибок для всех изображений
3. Использовать правильные стили изображений в зависимости от места использования

### 13.4. Интеграция с jBox
1. Добавлять вызов `s45_add_jbox()` в начале шаблона
2. Использовать атрибут `data-jbox-image` для группировки изображений
3. При необходимости настраивать дополнительные параметры jBox в JavaScript-коде

## 14. Статистика объектов недвижимости по пляжам

### 14.1. Распределение объектов по пляжам

| ID пляжа | Название пляжа | Количество объектов |
|----------|---------------|-------------------|
| 5ec0e378-019F-567D-0002-PhuketOption | Бангтао/Лагуна | 1795 |
| 5ec0e378-019F-567D-0001-PhuketOption | Найхарн | 292 |
| 5ec0e378-019F-567D-0007-PhuketOption | Камала | 253 |
| 5eccd644-00E0-9894-3DA1-PhuketOption | Равай | 236 |
| 5ec0e378-019F-567D-0008-PhuketOption | Сурин | 196 |
| 5ec0e378-019F-567D-0004-PhuketOption | Ката | 152 |
| 5eccd4c6-9E5D-2E96-3E4F-PhuketOption | Лаян | 129 |
| 5ec0e378-019F-567D-0005-PhuketOption | Карон | 118 |
| 5ec0e378-019F-567D-0006-PhuketOption | Найтон | 91 |
| 5ec0e378-019F-567D-0368-PhuketOption | Найянг | 79 |
| 5ec0e378-019F-567D-0014-PhuketOption | Таланг | 69 |
| 5ec0e378-019F-567D-0010-PhuketOption | Патонг | 54 |
| 5ec0e378-019F-567D-0012-PhuketOption | Май Кхао | 45 |
| 5ec0e378-019F-567D-0003-PhuketOption | Чалонг | 42 |
| 5ec0e378-019F-567D-0535-PhuketOption | Ко Кеу | 35 |
| 5ec0e378-019F-567D-0009-PhuketOption | Кату | 33 |
| 5ec0e378-019F-567D-0533-PhuketOption | Мыс Яму | 28 |
| 5ec0e378-019F-567D-0534-PhuketOption | Чентале | 25 |
| 5eccd406-2D47-CC12-933E-PhuketOption | Калим | 22 |
| 5eccd35b-4CE3-3385-F289-PhuketOption | Парадайз | 18 |
| 5eccd6f4-2AE6-60D7-EDA2-PhuketOption | Ката Ной | 15 |
| 5eccd419-758E-2781-7B43-PhuketOption | Лаем Синг | 12 |
| 5f0db1db-DA02-B0D0-B99E-PhuketOption | Ао Йон | 8 |
| 5eccd66d-25B0-BDC6-78A9-PhuketOption | Януй | 7 |
| 5fbba483-7D89-1015-0618-PhuketOption | Ао По Пьер | 5 |
| 5eccd2c8-AF47-4FA6-C6FF-PhuketOption | Три Транг | 4 |
| 5eccd26a-4209-C139-1AF4-PhuketOption | Фридом | 3 |
| 5ec0e378-019F-567D-0577-PhuketOption | Палай | 2 |
| 5eccd490-C13B-654D-F344-PhuketOption | Банана | 1 |
| 5ed746e4-9EB6-B6BC-BDA7-PhuketOption | Райе Пляж | 0 |
| 5ec0e378-019F-567D-0369-PhuketOption | Натай | 0 |
| 5ec0e378-019F-567D-0536-PhuketOption | Панва | 0 |
| 5ec0e378-019F-567D-0531-PhuketOption | Пханг Нга | 0 |
| 5ec0e378-019F-567D-0013-PhuketOption | Пхукет Таун | 0 |

### 14.2. Анализ распределения

1. **Популярные районы**:
   - Бангтао/Лагуна является самым популярным районом с 1795 объектами
   - Найхарн и Камала формируют второй эшелон популярности (200-300 объектов)
   - Равай и Сурин входят в топ-5 локаций

2. **Распределение по типам местности**:
   - Пляжные зоны (Бангтао, Найхарн, Камала, Сурин) доминируют по количеству объектов
   - Внутренние районы (Таланг, Кату) имеют меньшее количество объектов

3. **Особенности данных**:
   - Большой разрыв между лидером (Бангтао - 1795) и вторым местом (Найхарн - 292)
   - Равномерное распределение в средней группе (100-200 объектов)
   - Малое количество объектов в удаленных районах

### 14.3. Рекомендации по работе с данными

1. **Для разработчиков**:
   - Учитывать неравномерность распределения при проектировании поиска
   - Оптимизировать кэширование для популярных районов
   - Реализовать умную пагинацию для районов с большим количеством объектов

2. **Для контент-менеджеров**:
   - Уделять особое внимание качеству данных в популярных районах
   - Проверять актуальность информации пропорционально количеству объектов
   - Обеспечить полноту описаний для менее популярных районов

3. **Для SEO-оптимизации**:
   - Создать детальные посадочные страницы для топ-10 районов
   - Оптимизировать мета-теги с учетом количества объектов
   - Реализовать микроразметку с учетом популярности районов

## Заключение

Данная документация предоставляет общий обзор архитектуры и структуры кастомного модуля сайта InDreams Phuket, включая информацию о новой теме и связанных компонентах. Она может служить руководством для дальнейшей разработки и модификации сайта. При внесении изменений рекомендуется следовать существующей архитектуре и соблюдать принципы организации кода, используемые в проекте.

При необходимости более детальной информации по конкретным аспектам системы, рекомендуется изучить соответствующие файлы исходного кода и их документацию. 