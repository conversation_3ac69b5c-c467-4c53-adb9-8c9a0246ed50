# Hreflang Implementation Plan for InDreams Phuket

This document outlines a comprehensive step-by-step plan to fix the hreflang implementation issues identified in the SEO audit of the InDreams Phuket website. Proper hreflang implementation is critical for multilingual websites to ensure search engines understand language relationships between pages.

## Current Issues

1. Missing x-default hreflang tag
2. Incomplete return links between language versions
3. No proper path translation mechanism
4. No caching of hreflang tags
5. Inconsistent URL structures across language versions

## Implementation Timeline

| Phase | Task | Timeframe | Priority |
|-------|------|-----------|----------|
| Phase 1 | Code modifications in s45_phuket_seo.inc | Days 1-2 | HIGH |
| Phase 2 | URL structure standardization | Days 3-4 | HIGH |
| Phase 3 | Testing and verification | Day 5 | HIGH |
| Phase 4 | Monitoring | Ongoing | MEDIUM |

## Phase 1: Code Modifications (Days 1-2)

### Step 1: Create a New Hreflang Generation Function

Add this function to your `s45_phuket_seo.inc` file:

```php
/**
 * Generates proper hreflang links for all language versions.
 *
 * @param array $head_elements
 *   The HTML head elements array to modify.
 */
function s45_phuket_generate_hreflang(&$head_elements) {
  // Get current path
  $current_path = drupal_get_path_alias();
  
  // Define all language domains and their ISO codes
  $languages = array(
    'ru' => array('domain' => 'https://indreamsphuket.ru', 'name' => 'Russian'),
    'en' => array('domain' => 'https://indreamsphuket.com', 'name' => 'English'),
    'th' => array('domain' => 'https://th.indreamsphuket.com', 'name' => 'Thai'),
    'zh' => array('domain' => 'https://ch.indreamsphuket.com', 'name' => 'Chinese'),
  );
  
  // Add x-default hreflang (pointing to English version)
  $head_elements['hreflang_default'] = array(
    '#tag' => 'link',
    '#attributes' => array(
      'rel' => 'alternate',
      'href' => 'https://indreamsphuket.com/' . $current_path,
      'hreflang' => 'x-default',
    ),
    '#type' => 'html_tag',
  );
  
  // Add language-specific hreflang tags
  foreach ($languages as $lang_code => $lang_info) {
    // Get the translated path alias
    $translated_path = s45_phuket_get_translated_path($current_path, $lang_code);
    
    $head_elements['hreflang_' . $lang_code] = array(
      '#tag' => 'link',
      '#attributes' => array(
        'rel' => 'alternate',
        'href' => $lang_info['domain'] . '/' . $translated_path,
        'hreflang' => $lang_code,
      ),
      '#type' => 'html_tag',
    );
  }
}
```

### Step 2: Create a Path Translation Function

Add this function to handle path translation between languages:

```php
/**
 * Get translated path for the current path in target language.
 *
 * @param string $path
 *   The current path in the current language.
 * @param string $language
 *   The target language code.
 *
 * @return string
 *   The translated path.
 */
function s45_phuket_get_translated_path($path, $language_code) {
  // Path translation table structure:
  // - path_source
  // - language_source
  // - path_destination
  // - language_destination
  
  // If we have a custom path translation table:
  $query = db_select('custom_path_translations', 't')
    ->fields('t', array('path_destination'))
    ->condition('path_source', $path)
    ->condition('language_destination', $language_code);
  $result = $query->execute()->fetchField();
  
  if ($result) {
    return $result;
  }
  
  // Fall back to Drupal's path translation if available
  if (module_exists('i18n_path')) {
    $translations = i18n_path_get_translations($path);
    if (!empty($translations[$language_code])) {
      return $translations[$language_code];
    }
  }
  
  // If no translation found, return original path
  return $path;
}
```

### Step 3: Modify the html_head_alter Function

Update the existing `s45_phuket_html_head_alter` function with caching and proper hreflang implementation:

```php
/**
 * Implements hook_html_head_alter().
 */
function s45_phuket_html_head_alter(&$head_elements) {
  // Cache key based on current path and language
  $cid = 'metatags:' . current_path() . ':' . $GLOBALS['language']->language;
  
  // Check cache first
  if ($cache = cache_get($cid, 'cache_page')) {
    $head_elements = array_merge($head_elements, $cache->data);
    return;
  }
  
  // Existing metatag code
  $reqUri = preg_replace("#^/#", "", $_SERVER['REQUEST_URI']);
  
  // Handle canonical URL
  $canLink = 'https://' . preg_replace("#/$#", "", $_SERVER['SERVER_NAME']) . '/' . $reqUri;
  // Remove page number from URL
  $canLink = s45_phuket_seo_remove_page_number($canLink);
  
  if(isset($GLOBALS['s45']['PageSeo']['canonical']) && trim($GLOBALS['s45']['PageSeo']['canonical'])) {
    $canLink = 'https://'. preg_replace("#/$#", "", $_SERVER['SERVER_NAME']) . $GLOBALS['s45']['PageSeo']['canonical'];
  }
  
  $head_elements['canonical'] = array(
    '#tag' => 'link',
    '#attributes' => array(
      'rel' => 'canonical',
      'href' => $canLink,
    ),
    '#type' => 'html_tag',
  );
  
  // Generate hreflang elements
  s45_phuket_generate_hreflang($head_elements);
  
  // Remove generator tag
  unset($head_elements['system_meta_generator']);
  
  // Set description if available
  if(isset($GLOBALS['s45']['PageSeo']['description']) && trim($GLOBALS['s45']['PageSeo']['description'])) {
    $head_elements['descr'] = array(
      '#tag' => 'meta',
      '#attributes' => array(
        'name' => 'description',
        'content' => $GLOBALS['s45']['PageSeo']['description'],
      ),
      '#type' => 'html_tag',
    );
  }
  
  // Store in cache for future requests
  $cacheable_elements = array(
    'canonical' => $head_elements['canonical'],
    'descr' => isset($head_elements['descr']) ? $head_elements['descr'] : NULL,
  );
  
  // Add hreflang elements to cacheable elements
  foreach ($head_elements as $key => $value) {
    if (strpos($key, 'hreflang_') === 0) {
      $cacheable_elements[$key] = $value;
    }
  }
  
  cache_set($cid, $cacheable_elements, 'cache_page', CACHE_TEMPORARY);
}
```

## Phase 2: URL Structure Standardization (Days 3-4)

### Step 1: Audit Current URL Structure

1. Generate a list of all URLs across all language versions:

```bash
drush eval "print_r(s45_phuket_generate_url_report());"
```

2. Create a URL audit function in your module:

```php
/**
 * Generate a report of URL patterns across language versions.
 */
function s45_phuket_generate_url_report() {
  // Get a sample of paths (adapt to your content structure)
  $query = db_select('node', 'n');
  $query->fields('n', array('nid', 'title'));
  $query->condition('n.status', 1);
  $query->range(0, 100);
  $results = $query->execute();
  
  $report = array();
  
  foreach ($results as $result) {
    $node = node_load($result->nid);
    $path = drupal_get_path_alias('node/' . $node->nid);
    
    $urls = array();
    $urls['en'] = 'https://indreamsphuket.com/' . $path;
    $urls['ru'] = 'https://indreamsphuket.ru/' . $path;
    $urls['th'] = 'https://th.indreamsphuket.com/' . $path;
    $urls['zh'] = 'https://ch.indreamsphuket.com/' . $path;
    
    $report[$node->nid] = $urls;
  }
  
  return $report;
}
```

### Step 2: Standardize URL Patterns

1. Update path patterns using Pathauto module settings or custom logic:

```php
/**
 * Create consistent URL patterns across language versions.
 */
function s45_phuket_standardize_paths() {
  // Get all content types
  $types = node_type_get_types();
  
  foreach ($types as $type) {
    // Set consistent path patterns for each content type
    variable_set('pathauto_node_' . $type->type . '_pattern', '[node:content-type]/[node:title]');
  }
  
  // Update existing path aliases
  drush_invoke('pathauto-update');
}
```

### Step 3: Create Path Translation Table

Create a database table to store manual path translations if needed:

```php
/**
 * Implements hook_schema().
 */
function s45_phuket_schema() {
  $schema['s45_path_translations'] = array(
    'description' => 'Stores path translations between languages.',
    'fields' => array(
      'id' => array(
        'description' => 'Primary key.',
        'type' => 'serial',
        'unsigned' => TRUE,
        'not null' => TRUE,
      ),
      'path_source' => array(
        'description' => 'Source path.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
      ),
      'language_source' => array(
        'description' => 'Source language code.',
        'type' => 'varchar',
        'length' => 12,
        'not null' => TRUE,
      ),
      'path_destination' => array(
        'description' => 'Destination path.',
        'type' => 'varchar',
        'length' => 255,
        'not null' => TRUE,
      ),
      'language_destination' => array(
        'description' => 'Destination language code.',
        'type' => 'varchar',
        'length' => 12,
        'not null' => TRUE,
      ),
    ),
    'primary key' => array('id'),
    'indexes' => array(
      'path_source' => array('path_source'),
      'language_destination' => array('language_destination'),
    ),
  );
  
  return $schema;
}
```

## Phase 3: Testing and Verification (Day 5)

### Step 1: Test on Staging Environment

1. Deploy all changes to a staging environment
2. Check a representative sample of pages across all language versions
3. Verify proper hreflang implementation using:
   - Browser developer tools
   - [Google's hreflang testing tool](https://technicalseo.com/tools/hreflang/)
   - [Google Search Console's International Targeting report](https://search.google.com/search-console)

### Step 2: Automated Testing

Create an automated test to verify hreflang implementation:

```php
/**
 * Test the hreflang implementation.
 */
function s45_phuket_test_hreflang() {
  $paths = array(
    '',  // Homepage
    'about',
    'villas-for-sale',
    'apartments-for-rent',
    // Add other important paths
  );
  
  $issues = array();
  
  foreach ($paths as $path) {
    // Make an HTTP request to the page
    $url = 'https://indreamsphuket.com/' . $path;
    $response = drupal_http_request($url);
    
    if ($response->code == 200) {
      // Parse the HTML to extract hreflang tags
      $dom = new DOMDocument();
      @$dom->loadHTML($response->data);
      $xpath = new DOMXPath($dom);
      
      $hreflang_tags = $xpath->query('//link[@rel="alternate" and @hreflang]');
      $found_langs = array();
      
      foreach ($hreflang_tags as $tag) {
        $found_langs[] = $tag->getAttribute('hreflang');
      }
      
      // Check for required languages
      $required_langs = array('en', 'ru', 'th', 'zh', 'x-default');
      $missing_langs = array_diff($required_langs, $found_langs);
      
      if (!empty($missing_langs)) {
        $issues[$url] = 'Missing hreflang tags: ' . implode(', ', $missing_langs);
      }
    } else {
      $issues[$url] = 'Failed to load URL: ' . $response->code;
    }
  }
  
  return $issues;
}
```

### Step 3: Deploy to Production

1. After successful testing, deploy to production
2. Run verification tests again to confirm proper implementation
3. Submit all sitemaps to Google Search Console
4. Clear all caches: `drush cc all`

## Phase 4: Monitoring (Ongoing)

### Step 1: Set Up Regular Checks

Create a scheduled task to check hreflang implementation:

```php
/**
 * Implements hook_cron().
 */
function s45_phuket_cron() {
  // Run hreflang checks weekly
  $last_run = variable_get('s45_phuket_hreflang_check_last_run', 0);
  
  if (time() - $last_run > 604800) { // 1 week
    $issues = s45_phuket_test_hreflang();
    
    if (!empty($issues)) {
      // Log issues
      foreach ($issues as $url => $error) {
        watchdog('s45_phuket', 'Hreflang issue on @url: @error', 
          array('@url' => $url, '@error' => $error), WATCHDOG_WARNING);
      }
      
      // Notify site administrators
      $message = "Hreflang issues detected during automated check:\n\n";
      foreach ($issues as $url => $error) {
        $message .= "$url: $error\n";
      }
      
      $admin_mail = variable_get('site_mail', '');
      if ($admin_mail) {
        drupal_mail('s45_phuket', 'hreflang_issues', $admin_mail, 
          language_default(), array('message' => $message), $admin_mail);
      }
    }
    
    variable_set('s45_phuket_hreflang_check_last_run', time());
  }
}
```

### Step 2: Set Up Google Search Console Monitoring

1. Use Google Search Console to monitor:
   - International targeting issues
   - Language detection errors
   - Indexing status for each language version

2. Set up notifications for hreflang errors in Google Search Console

## Additional Recommendations

### Language Switcher Improvements

Update your language switcher to ensure it links to the correct translated versions:

```php
/**
 * Generate proper language switcher links.
 */
function s45_phuket_language_switcher() {
  $current_path = drupal_get_path_alias();
  $languages = array(
    'en' => array('domain' => 'https://indreamsphuket.com', 'name' => 'English'),
    'ru' => array('domain' => 'https://indreamsphuket.ru', 'name' => 'Russian'),
    'th' => array('domain' => 'https://th.indreamsphuket.com', 'name' => 'Thai'),
    'zh' => array('domain' => 'https://ch.indreamsphuket.com', 'name' => 'Chinese'),
  );
  
  $links = array();
  
  foreach ($languages as $lang_code => $lang_info) {
    $translated_path = s45_phuket_get_translated_path($current_path, $lang_code);
    $links[$lang_code] = array(
      'href' => $lang_info['domain'] . '/' . $translated_path,
      'title' => $lang_info['name'],
      'language' => $lang_code,
      'attributes' => array('class' => array('language-link')),
    );
  }
  
  return $links;
}
```

### Documentation for Editors

Create documentation for content editors to maintain proper multilingual URLs:

1. Guidelines for creating consistent content across languages
2. Instructions for properly translating URLs
3. Process for checking hreflang implementation when creating new content

## Success Metrics

Monitor these metrics to track the success of the hreflang implementation:

1. Google Search Console International Targeting errors (should decrease)
2. International organic traffic to English version (should increase)
3. Indexing ratio across language versions (should become more balanced)
4. Conversion rates from international visitors (should improve)

## Conclusion

This implementation plan provides a comprehensive approach to fixing the hreflang issues on the InDreams Phuket website. By following these steps, you'll establish proper language signals for search engines, which should lead to improved international SEO performance, particularly for the English version of the site.

Remember that hreflang implementation is just one part of a comprehensive international SEO strategy. Additional work on content quality, technical SEO, and local relevance will further enhance the site's performance in international search results. 