# Система исторических редиректов для объектов недвижимости

## Описание

Система исторических редиректов автоматически сохраняет до 5 старых URL для каждого объекта недвижимости при изменении его контента. Когда пользователь переходит по старой ссылке, система автоматически перенаправляет его на актуальный URL объекта с помощью 301 редиректа.

## Архитектура системы

### Основные компоненты

1. **Таблица `_phuket_property_redirects`** - хранит исторические URL
2. **Класс `PropertyRedirectManager`** - управляет редиректами
3. **Интеграция в `PhuketPropertyQuery`** - автоматическое создание редиректов
4. **Интеграция в `s45_path.module`** - обработка входящих запросов
5. **Cron-очистка** - автоматическое удаление устаревших редиректов

### Схема базы данных

```sql
CREATE TABLE `_phuket_property_redirects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `property_id` varchar(36) NOT NULL COMMENT 'UUID объекта недвижимости',
  `property_number` varchar(10) NOT NULL COMMENT 'Номер объекта недвижимости', 
  `old_alias` varchar(255) NOT NULL COMMENT 'Старый URL-алиас объекта',
  `lang_code` varchar(8) NOT NULL DEFAULT 'en' COMMENT 'Код языка',
  `created` int(11) NOT NULL COMMENT 'Время создания редиректа',
  `last_hit` int(11) DEFAULT NULL COMMENT 'Время последнего перехода',
  `hit_count` int(11) NOT NULL DEFAULT 0 COMMENT 'Количество переходов',
  PRIMARY KEY (`id`),
  KEY `idx_old_alias_lang` (`old_alias`, `lang_code`),
  KEY `idx_property_lang` (`property_id`, `lang_code`)
);
```

## Принцип работы

### 1. Создание редиректов

При сохранении объекта недвижимости (`handlePhuketPropertySaved`):

1. Система кэширует текущие алиасы объекта для всех языков
2. Создает новые алиасы на основе обновленного контента
3. Сравнивает старые и новые алиасы
4. Если алиас изменился, создает исторический редирект
5. Ограничивает количество редиректов до 5 на объект/язык

### 2. Обработка редиректов

При входящем запросе (`s45_path_url_inbound_alter`):

1. Система проверяет, является ли URL старым алиасом объекта
2. Если найден, получает текущий алиас объекта
3. Выполняет 301 редирект на актуальный URL
4. Записывает статистику использования редиректа

### 3. Автоматическая очистка

Через cron раз в неделю (`s45_phuket_cron`):

1. Удаляет редиректы старше года без переходов
2. Оставляет только последние 5 редиректов на объект
3. Логирует действия в watchdog

## Файлы системы

### Основные файлы

- `classes/PropertyRedirectManager.php` - основной класс управления
- `s45_phuket_redirect.inc` - файл подключения и 404-обработчики
- `property_redirects_migration.sql` - SQL-миграция для создания таблицы

### Интеграционные изменения

- `PhuketPropertyQuery.php:586` - метод `addAliases()` дополнен кэшированием и проверкой
- `s45_phuket.module:809` - добавлена cron-очистка
- `s45_path.module:46` - добавлена проверка исторических редиректов

## Методы PropertyRedirectManager

### Основные методы

```php
// Добавить исторический редирект
PropertyRedirectManager::addHistoricalRedirect($propertyId, $propertyNumber, $oldAlias, $langCode);

// Найти объект по старому алиасу
PropertyRedirectManager::findPropertyByOldAlias($oldAlias, $langCode);

// Получить текущий алиас объекта
PropertyRedirectManager::getCurrentAlias($propertyId, $langCode);

// Проверить изменения и создать редиректы
PropertyRedirectManager::checkAndCreateRedirects($propertyId, $propertyNumber, $langCodes);

// Кэшировать текущие алиасы
PropertyRedirectManager::cacheCurrentAliases($propertyId, $langCodes);

// Cron-очистка устаревших редиректов
PropertyRedirectManager::cronCleanup();
```

### Конфигурация

```php
// Максимум редиректов на объект/язык
const MAX_REDIRECTS_PER_PROPERTY = 5;

// Дни хранения неиспользуемых редиректов
const REDIRECT_CLEANUP_DAYS = 365;
```

## Поддерживаемые языки

Система работает со всеми языками сайта:
- `ru` - русский
- `en` - английский 
- `zh-hans` - китайский (упрощенный)
- `th` - тайский

## Логирование

Система ведет подробные логи через Drupal watchdog:

- **INFO**: Создание редиректов, выполнение переходов, cron-очистка
- **WARNING**: Ошибки в работе системы
- **ERROR**: Критические ошибки

Просмотр логов:
```bash
drush watchdog-show --severity=info --type=PropertyRedirect --count=20
```

## Статистика

Система собирает статистику использования редиректов:

- Время последнего перехода (`last_hit`)
- Количество переходов (`hit_count`)
- Автоматическая очистка неиспользуемых редиректов

## Мониторинг

### Запросы для мониторинга

```sql
-- Количество активных редиректов
SELECT lang_code, COUNT(*) as count 
FROM _phuket_property_redirects 
GROUP BY lang_code;

-- Топ-10 самых используемых редиректов
SELECT old_alias, hit_count, last_hit 
FROM _phuket_property_redirects 
ORDER BY hit_count DESC 
LIMIT 10;

-- Редиректы без переходов за последние 30 дней
SELECT COUNT(*) as unused_redirects
FROM _phuket_property_redirects 
WHERE last_hit IS NULL 
   OR last_hit < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY));
```

### Проверка работоспособности

1. **Тест создания редиректа**: Измените название объекта через админку
2. **Тест перехода**: Попробуйте перейти по старому URL
3. **Проверка статистики**: Убедитесь, что `hit_count` увеличился

## Безопасность

- Все входные данные проходят валидацию
- Используются подготовленные SQL-запросы
- 301 редиректы не влияют на SEO
- Ограничение количества редиректов предотвращает переполнение

## Производительность

- Индексы оптимизированы для быстрого поиска
- Автоматическая очистка предотвращает рост таблицы
- Кэширование алиасов минимизирует запросы к БД
- Ленивая загрузка класса при необходимости

## Совместимость

Система полностью совместима с существующей архитектурой s45:

- Event Sourcing
- Мультиязычность через LangVO
- Система алиасов Path
- Cron-задачи Drupal

## Устранение неполадок

### Редиректы не создаются

1. Проверьте, что таблица создана
2. Убедитесь, что кэш очищен (`drush cc all`)
3. Проверьте логи: `drush watchdog-show --type=PropertyRedirect`

### Редиректы не работают

1. Проверьте интеграцию в `s45_path.module`
2. Убедитесь, что класс `PropertyRedirectManager` доступен
3. Проверьте права доступа к файлам

### Проблемы с производительностью

1. Проверьте индексы таблицы
2. Запустите cron-очистку вручную
3. Оптимизируйте частоту проверок

## Дальнейшее развитие

Возможные улучшения системы:

1. **Интерфейс управления** - админский компонент для просмотра статистики
2. **Умная очистка** - анализ популярности редиректов
3. **Экспорт данных** - выгрузка статистики в CSV/Excel
4. **Уведомления** - алерты при критических ошибках
5. **API интеграция** - REST API для внешних систем 