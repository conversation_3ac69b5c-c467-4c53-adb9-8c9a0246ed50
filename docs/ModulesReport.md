# Отчет о состоянии модулей Drupal

## Включенные модули с отладочной функциональностью

1. **Devel** (7.x-1.7)
   - Модуль для разработки, не рекомендуется на production
   - Рекомендация: Отключить

2. **Cloudflare** (7.x-2.x-dev)
   - Обнаружен включенный режим отладки (cloudflare_debug: 1)
   - Рекомендация: Отключить отладку и обновить до стабильной версии

3. **Database logging (dblog)**
   - Системный модуль логирования
   - Рекомендация: Оставить включенным, но настроить ротацию логов

## Модули в dev версиях

1. **s45_base** (7.x-4.0-dev)
2. **s45_imagestyles** (7.x-4.0-dev)
3. **s45_page** (7.x-4.0-dev)
4. **s45_path** (7.x-4.0-dev)
5. **s45_phuket** (7.x-4.0-dev)
6. **s45_test** (7.x-4.0-dev)
7. **s45_vendor** (7.x-4.0-dev)

## Переменные отладки

```
cloudflare_debug: 1
cloudflare_helper_debug_mode: 0
expire_debug: '0'
```

## Рекомендации по оптимизации

1. Отключить модуль Devel на production
2. Отключить отладку Cloudflare (cloudflare_debug)
3. Проверить необходимость модуля s45_test на production
4. Рассмотреть возможность обновления dev версий модулей до стабильных

## Модули для проверки конфигурации

1. **jQuery Update** (7.x-3.0-alpha5)
   - Используется альфа-версия
   - Рекомендация: Обновить до стабильной версии

2. **ImageAPI Optimize** (7.x-2.0-beta2)
   - Beta версия
   - Рекомендация: Проверить наличие стабильной версии

## Активные темы

1. **Seven** (7.67) - Административная тема
2. **site45** (7.x-4.0-dev) - Основная тема сайта

## Следующие шаги

1. Отключить отладочный режим Cloudflare:
```bash
drush vset cloudflare_debug 0
```

2. Отключить модуль Devel:
```bash
drush dis devel -y
```

3. Проверить и очистить устаревшие записи в таблице watchdog:
```bash
drush sqlq "DELETE FROM watchdog WHERE timestamp < UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 30 DAY));"
```

4. Рассмотреть возможность использования syslog вместо dblog на production. 