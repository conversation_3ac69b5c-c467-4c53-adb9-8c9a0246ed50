# Руководство по поддержке Dropbox Uploader

## Обзор системы

Система загрузки изображений недвижимости в Dropbox состоит из:

- **`property_images_dropbox.php`** - основной скрипт загрузки
- **`dropbox_uploader.php`** - класс для работы с Dropbox API
- **`dropbox_config.php`** - конфигурация токенов доступа
- **`dropbox_token_monitor.php`** - мониторинг токенов (новый)

## Архитектура работы

1. **Получение данных объекта** из Event Store системы
2. **Загрузка изображений** с локального сервера в Dropbox
3. **Создание публичной ссылки** на архив с изображениями
4. **Сохранение ссылки** в базу данных через обновление события

## Типичные проблемы и решения

### 1. Ошибка 500 - Истёкший токен доступа

**Симптомы:**
- HTTP 500 error при загрузке
- Отсутствие детальной информации об ошибке

**Диагностика:**
```bash
php -f refresh_dropbox_token.php
```

**Решение:**
- Автоматическое обновление через мониторинг
- Ручное обновление токена при необходимости

### 2. Отсутствующие изображения объекта

**Диагностика:**
```sql
-- Проверяем объект в базе
SELECT arId, created, FROM_UNIXTIME(created) 
FROM _s45_events 
WHERE arName = 'PhuketProperty' AND payload LIKE '%"number":7071%'
ORDER BY created DESC LIMIT 1;
```

**Проверка файлов:**
```bash
# Проверяем физическое существование файлов
ls -la /path/to/files/site4/FileStore4/phuket/files/tmp/[FILE_ID]
```

### 3. Проблемы с правами доступа

**Проверка прав:**
```bash
# Права на директории
chmod 755 sites/all/modules/__s45/s45_phuket/logs/
chmod 644 sites/all/modules/__s45/s45_phuket/dropbox_config.php

# Права на скрипты
chmod +x sites/all/modules/__s45/s45_phuket/*.php
```

## Автоматический мониторинг

### Установка мониторинга

```bash
cd sites/all/modules/__s45/s45_phuket/
bash setup_dropbox_monitoring.sh
```

### Настройка cron

Добавляет задачу для проверки каждые 12 часов:
```cron
0 */12 * * * /usr/bin/php /path/to/dropbox_token_monitor.php >> /path/to/logs/cron.log 2>&1
```

### Логирование

**Основные логи:**
- `logs/dropbox_token.log` - лог мониторинга токенов  
- `logs/cron.log` - лог выполнения cron-задач

**Просмотр логов:**
```bash
# Последние события мониторинга
tail -f logs/dropbox_token.log

# Ошибки cron
tail -f logs/cron.log | grep ERROR
```

## Профилактические меры

### 1. Регулярные проверки

**Еженедельно:**
- Проверка логов на ошибки
- Тестирование одного объекта для загрузки

**Ежемесячно:**
- Проверка свободного места в Dropbox
- Анализ статистики использования API

### 2. Мониторинг производительности

```sql
-- Статистика загрузок по месяцам
SELECT 
  DATE_FORMAT(FROM_UNIXTIME(created), '%Y-%m') as month,
  COUNT(*) as uploads
FROM _s45_events 
WHERE arName = 'PhuketProperty' 
  AND payload LIKE '%dropBoxLink%'
  AND payload NOT LIKE '%"dropBoxLink":""%'
GROUP BY month 
ORDER BY month DESC;
```

### 3. Резервное копирование конфигурации

```bash
# Еженедельное резервное копирование
cp dropbox_config.php "backups/dropbox_config_$(date +%Y%m%d).php"

# Сохранение логов
tar -czf "backups/dropbox_logs_$(date +%Y%m%d).tar.gz" logs/
```

## Диагностические команды

### Быстрая проверка системы

```bash
# Проверка токена
php -r "
require 'dropbox_config.php';
echo 'Token expires: ' . date('Y-m-d H:i:s', \$expires_at) . PHP_EOL;
echo 'Hours until expiry: ' . round((\$expires_at - time()) / 3600, 1) . PHP_EOL;
"

# Тестирование подключения
php -f dropbox_token_monitor.php
```

### Проверка конкретного объекта

```php
// test_property.php
<?php
$propertyNumber = 7071; // Замените на нужный номер

// Поиск объекта в базе
$query = "SELECT * FROM _s45_events 
          WHERE arName = 'PhuketProperty' 
            AND payload LIKE '%\"number\":$propertyNumber%' 
            AND isLast = 1";

$result = db_query($query);
$event = $result->fetchAssoc();

if ($event) {
    $data = unserialize($event['payload']);
    echo "Объект найден: " . $data->number . PHP_EOL;
    echo "Фотографий: " . count($data->photos) . PHP_EOL;
    echo "Dropbox ссылка: " . ($data->dropBoxLink ?: 'отсутствует') . PHP_EOL;
} else {
    echo "Объект $propertyNumber не найден" . PHP_EOL;
}
?>
```

## Аварийное восстановление

### При полной потере токенов

1. **Повторная авторизация в Dropbox:**
   - Перейти в Dropbox App Console
   - Сгенерировать новые токены
   - Обновить `dropbox_config.php`

2. **Восстановление из резервной копии:**
   ```bash
   cp backups/dropbox_config_YYYYMMDD.php dropbox_config.php
   ```

### При потере доступа к Dropbox аккаунту

1. **Альтернативные хранилища:** Настроить загрузку в другие облачные сервисы
2. **Локальное архивирование:** Сохранение архивов на сервере до восстановления доступа

## Улучшения системы

### Рекомендуемые доработки

1. **Очередь загрузок:**
   ```php
   // Использование Drupal Queue API для фоновой обработки
   $queue = DrupalQueue::get('dropbox_uploads');
   $queue->createItem(['property_id' => $propertyId]);
   ```

2. **Уведомления об ошибках:**
   ```php
   // Интеграция с email-уведомлениями
   if ($uploadFailed) {
       mail('<EMAIL>', 'Dropbox Upload Failed', $errorDetails);
   }
   ```

3. **Множественные бэкапы:**
   - Параллельная загрузка в несколько облачных сервисов
   - Локальное сохранение архивов как резерв

## Контакты для поддержки

- **Техническая поддержка:** При проблемах с API или токенами
- **Системный администратор:** При проблемах с сервером или правами доступа
- **Dropbox Support:** При проблемах с аккаунтом или лимитами API

## Полезные ссылки

- [Dropbox API Documentation](https://www.dropbox.com/developers/documentation)
- [OAuth 2.0 Token Refresh](https://www.dropbox.com/developers/documentation/http/documentation#oauth2-token-refresh)
- [Drupal Cron Documentation](https://www.drupal.org/docs/administering-a-drupal-site/cron-automated-tasks) 