# Allow access to this directory
Options +Indexes
Options +FollowSymLinks

# Enable Rewrite Engine
RewriteEngine On
RewriteBase /docs/

# Don't apply rules to existing files or directories
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Route all other requests to index.html
RewriteRule ^(.*)$ index.html [L]

# Allow specific file types
<FilesMatch "\.(html|css|js|jpg|jpeg|png|gif|pdf|svg)$">
    Order Allow,Deny
    Allow from all
</FilesMatch>

# Add proper MIME types
AddType application/javascript .js
AddType text/css .css
AddType image/svg+xml .svg

# Cache control
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/html "access plus 1 day"
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Disable directory listings
#Options -Indexes 