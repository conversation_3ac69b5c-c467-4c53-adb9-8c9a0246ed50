# XML Sitemap Redesign Plan for InDreams Phuket

This document outlines a comprehensive plan to restructure and optimize the XML sitemaps for the InDreams Phuket website to improve search engine crawling and indexing across all language versions.

## Current Sitemap Issues

Based on the SEO audit, the following issues have been identified:

1. No proper segmentation of content by language
2. Missing hreflang annotations in sitemaps
3. Inefficient organization of content types
4. Outdated or missing property listings in sitemaps
5. Non-optimized update frequency settings
6. Missing image sitemap capabilities
7. No clear prioritization of important content
8. Missing sitemap index file to organize multiple sitemaps

## Sitemap Redesign Goals

The redesigned sitemap structure will:

1. Properly segment content by language (EN, RU, TH, ZH)
2. Separate content by type (properties, articles, pages)
3. Include hreflang references for all multilingual content
4. Implement proper update frequencies based on content type
5. Include image information for enhanced image search visibility
6. Prioritize content according to business value
7. Implement proper sitemap index file

## Implementation Plan

### Phase 1: Sitemap Architecture Design (Day 1)

#### Step 1: Define Sitemap Structure

Create a sitemap index file that references the following specialized sitemaps:

1. **Language-specific sitemaps**:
   - `sitemap-en.xml`: English content
   - `sitemap-ru.xml`: Russian content
   - `sitemap-th.xml`: Thai content
   - `sitemap-zh.xml`: Chinese content

2. **Content type sitemaps** (for each language):
   - `sitemap-en-properties.xml`: English property listings
   - `sitemap-en-articles.xml`: English articles/blog posts
   - `sitemap-en-pages.xml`: English static pages
   - (Same pattern for other languages)

3. **Special sitemaps**:
   - `sitemap-images.xml`: All images (with language annotations)
   - `sitemap-news.xml`: Fresh content (if applicable)

#### Step 2: Configure Priority and Change Frequency

Implement the following priority and change frequency settings:

| Content Type | Priority | Change Frequency |
|--------------|----------|------------------|
| Homepage | 1.0 | daily |
| Property listings | 0.8 | weekly |
| Location pages | 0.7 | monthly |
| Category pages | 0.6 | monthly |
| Articles/Blog | 0.5 | weekly |
| Static pages | 0.4 | monthly |
| Images | 0.3 | monthly |

### Phase 2: Module Configuration and Setup (Day 2)

#### Step 1: Configure XML Sitemap Module

1. Install or update the required modules:
   ```bash
   drush dl xmlsitemap xmlsitemap_node xmlsitemap_menu xmlsitemap_i18n
   drush en xmlsitemap xmlsitemap_node xmlsitemap_menu xmlsitemap_i18n -y
   ```

2. Configure base XML sitemap settings at `admin/config/search/xmlsitemap/settings`:
   - Set "Cron limit" to 100
   - Enable "Include images in sitemap"
   - Set "Default priority" to 0.5

#### Step 2: Configure Entity Types and Bundles

Configure sitemap settings for each content type at `admin/config/search/xmlsitemap/settings/node`:

```php
// Programmatically configure XML sitemap settings for content types
function s45_phuket_configure_sitemap_content_types() {
  $content_types = array(
    'property' => array('priority' => 0.8, 'status' => 1),
    'article' => array('priority' => 0.5, 'status' => 1),
    'page' => array('priority' => 0.4, 'status' => 1),
    'location' => array('priority' => 0.7, 'status' => 1),
  );
  
  foreach ($content_types as $type => $settings) {
    variable_set('xmlsitemap_settings_node_' . $type, array(
      'status' => $settings['status'],
      'priority' => $settings['priority'],
    ));
  }
}
```

#### Step 3: Create Sitemap Segmentation by Language

Implement language-specific sitemap generation:

```php
/**
 * Generate language-specific sitemaps.
 */
function s45_phuket_generate_language_sitemaps() {
  $languages = array('en', 'ru', 'th', 'zh');
  
  foreach ($languages as $language) {
    // Create a context for this language
    $context = array(
      'language' => $language,
    );
    
    // Create a sitemap for this language
    xmlsitemap_create_sitemap($context);
  }
}
```

### Phase 3: Custom Implementation (Days 3-4)

#### Step 1: Create Custom Sitemap Generation Module

Create a custom module to extend XML Sitemap functionality:

```php
/**
 * @file
 * S45 Phuket Sitemap module.
 */

/**
 * Implements hook_xmlsitemap_link_alter().
 */
function s45_phuket_sitemap_xmlsitemap_link_alter(array &$link, array $context) {
  // Add hreflang alternatives to sitemap links
  if ($link['type'] == 'node' && isset($link['id'])) {
    $node = node_load($link['id']);
    
    // Check if this node has translations
    if (module_exists('entity_translation') && entity_translation_get_handler('node', $node)->getTranslations()) {
      $translations = entity_translation_get_handler('node', $node)->getTranslations()->data;
      $alternates = array();
      
      foreach ($translations as $langcode => $translation) {
        $url = url('node/' . $link['id'], array(
          'absolute' => TRUE,
          'language' => (object) array('language' => $langcode),
        ));
        
        $alternates[$langcode] = $url;
      }
      
      // Add x-default (pointing to English or site default)
      $url = url('node/' . $link['id'], array(
        'absolute' => TRUE,
        'language' => (object) array('language' => 'en'),
      ));
      $alternates['x-default'] = $url;
      
      $link['alternates'] = $alternates;
    }
  }
}

/**
 * Implements hook_xmlsitemap_index_links().
 */
function s45_phuket_sitemap_xmlsitemap_index_links(array $links) {
  // Customize the sitemap index
  foreach ($links as &$link) {
    // Add custom attributes to sitemap index links if needed
  }
  
  return $links;
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function s45_phuket_sitemap_form_xmlsitemap_sitemap_edit_form_alter(&$form, &$form_state) {
  // Add additional settings to the sitemap edit form
}
```

#### Step 2: Add Support for Images in Sitemap

Enhance the sitemap with image information:

```php
/**
 * Implements hook_xmlsitemap_element_alter().
 */
function s45_phuket_sitemap_xmlsitemap_element_alter(array &$element, array $link, array $sitemap) {
  // Add image information to sitemap elements for property nodes
  if ($link['type'] == 'node' && isset($link['id'])) {
    $node = node_load($link['id']);
    
    if ($node->type == 'property') {
      // Get property images
      $images = field_get_items('node', $node, 'field_images');
      
      if (!empty($images)) {
        $element['image:image'] = array();
        
        foreach ($images as $image) {
          $image_url = file_create_url($image['uri']);
          
          $image_element = array(
            'image:loc' => $image_url,
          );
          
          // Add optional image metadata
          if (isset($image['title']) && !empty($image['title'])) {
            $image_element['image:title'] = $image['title'];
          }
          
          if (isset($image['alt']) && !empty($image['alt'])) {
            $image_element['image:caption'] = $image['alt'];
          }
          
          $element['image:image'][] = $image_element;
        }
      }
    }
  }
}
```

#### Step 3: Implement Custom Sitemap Index

Create a sitemap index file that organizes all sitemaps:

```php
/**
 * Create a custom sitemap index file.
 */
function s45_phuket_generate_sitemap_index() {
  $sitemap_index = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
  $sitemap_index .= '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
  
  // Add language-specific sitemaps
  $languages = array('en', 'ru', 'th', 'zh');
  foreach ($languages as $language) {
    $sitemap_index .= '  <sitemap>' . "\n";
    $sitemap_index .= '    <loc>' . url('sitemap-' . $language . '.xml', array('absolute' => TRUE)) . '</loc>' . "\n";
    $sitemap_index .= '    <lastmod>' . gmdate('Y-m-d\TH:i:s\Z') . '</lastmod>' . "\n";
    $sitemap_index .= '  </sitemap>' . "\n";
  }
  
  // Add content-type specific sitemaps
  $content_types = array('properties', 'articles', 'pages');
  foreach ($languages as $language) {
    foreach ($content_types as $type) {
      $sitemap_index .= '  <sitemap>' . "\n";
      $sitemap_index .= '    <loc>' . url('sitemap-' . $language . '-' . $type . '.xml', array('absolute' => TRUE)) . '</loc>' . "\n";
      $sitemap_index .= '    <lastmod>' . gmdate('Y-m-d\TH:i:s\Z') . '</lastmod>' . "\n";
      $sitemap_index .= '  </sitemap>' . "\n";
    }
  }
  
  // Add special sitemaps
  $sitemap_index .= '  <sitemap>' . "\n";
  $sitemap_index .= '    <loc>' . url('sitemap-images.xml', array('absolute' => TRUE)) . '</loc>' . "\n";
  $sitemap_index .= '    <lastmod>' . gmdate('Y-m-d\TH:i:s\Z') . '</lastmod>' . "\n";
  $sitemap_index .= '  </sitemap>' . "\n";
  
  $sitemap_index .= '</sitemapindex>';
  
  // Write the sitemap index to a file
  file_put_contents(DRUPAL_ROOT . '/sitemap.xml', $sitemap_index);
}
```

### Phase 4: Testing and Verification (Day 5)

#### Step 1: Validate Sitemaps

1. Validate the structure and syntax of all sitemaps using online tools:
   - [XML Sitemap Validator](https://www.xml-sitemaps.com/validate-xml-sitemap.html)
   - [Google Search Console Sitemap Testing Tool](https://search.google.com/search-console)

2. Check for common sitemap errors:
   - Invalid XML format
   - Missing URLs
   - Incorrect hreflang annotations
   - Incorrect URLs
   - Missing or invalid lastmod dates

#### Step 2: Test Sitemap Coverage

Create a script to verify sitemap completeness:

```php
/**
 * Verify that all published content is included in sitemaps.
 */
function s45_phuket_verify_sitemap_coverage() {
  // Get all published nodes
  $query = db_select('node', 'n')
    ->fields('n', array('nid', 'type', 'language'))
    ->condition('n.status', 1)
    ->execute();
  
  $nodes = $query->fetchAll();
  
  // Load all sitemap links
  $sitemap_links = db_select('xmlsitemap', 'x')
    ->fields('x', array('id', 'type', 'subtype', 'language'))
    ->condition('x.type', 'node')
    ->condition('x.status', 1)
    ->execute()
    ->fetchAllAssoc('id');
  
  // Compare and find missing nodes
  $missing_nodes = array();
  
  foreach ($nodes as $node) {
    if (!isset($sitemap_links[$node->nid])) {
      $missing_nodes[] = $node;
    }
  }
  
  return $missing_nodes;
}
```

#### Step 3: Submit Sitemaps to Search Engines

1. Submit the sitemap index to Google Search Console
2. Submit to Bing Webmaster Tools
3. Submit to Yandex Webmaster (especially important for Russian content)
4. Add sitemap reference to robots.txt:
   ```
   Sitemap: https://indreamsphuket.com/sitemap.xml
   ```

### Phase 5: Automated Maintenance (Ongoing)

#### Step 1: Set Up Regular Sitemap Regeneration

Configure sitemap regeneration via cron:

```php
/**
 * Implements hook_cron().
 */
function s45_phuket_sitemap_cron() {
  // Regenerate sitemaps weekly
  $last_generated = variable_get('s45_phuket_sitemap_last_generated', 0);
  
  if (time() - $last_generated > 604800) { // 1 week
    // Regenerate all sitemaps
    xmlsitemap_regenerate();
    
    // Regenerate custom sitemap index
    s45_phuket_generate_sitemap_index();
    
    // Log completion
    watchdog('s45_phuket_sitemap', 'Sitemaps regenerated successfully', array(), WATCHDOG_INFO);
    
    // Update last generated time
    variable_set('s45_phuket_sitemap_last_generated', time());
  }
}
```

#### Step 2: Implement Monitoring for Sitemap Issues

Create a monitoring system for sitemap health:

```php
/**
 * Check for sitemap issues.
 */
function s45_phuket_check_sitemap_health() {
  $issues = array();
  
  // Check if sitemap files exist
  $sitemap_files = glob(DRUPAL_ROOT . '/sitemap*.xml');
  if (empty($sitemap_files)) {
    $issues[] = 'No sitemap files found';
  }
  
  // Check sitemap index
  if (!file_exists(DRUPAL_ROOT . '/sitemap.xml')) {
    $issues[] = 'Sitemap index file missing';
  }
  
  // Check for empty sitemaps
  foreach ($sitemap_files as $file) {
    $content = file_get_contents($file);
    if (strpos($content, '<url>') === FALSE) {
      $issues[] = 'Empty sitemap: ' . basename($file);
    }
  }
  
  // Check for missing URLs
  $missing_nodes = s45_phuket_verify_sitemap_coverage();
  if (!empty($missing_nodes)) {
    $issues[] = count($missing_nodes) . ' nodes missing from sitemaps';
  }
  
  return $issues;
}
```

## Additional Optimization Recommendations

### Custom Sitemap for Property Listings

For property listings, which are a key content type, implement enhanced sitemap functionality:

```php
/**
 * Generate specialized property listing sitemap.
 */
function s45_phuket_generate_property_sitemap() {
  // Start XML document
  $output = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
  $output .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" ' .
             'xmlns:image="http://www.google.com/schemas/sitemap-image/1.1" ' .
             'xmlns:xhtml="http://www.w3.org/1999/xhtml">' . "\n";
  
  // Get all published property nodes
  $query = db_select('node', 'n');
  $query->join('field_data_field_property_type', 'pt', 'n.nid = pt.entity_id');
  $query->fields('n', array('nid', 'title', 'language', 'changed'));
  $query->condition('n.type', 'property');
  $query->condition('n.status', 1);
  $result = $query->execute();
  
  foreach ($result as $property) {
    $node = node_load($property->nid);
    $url = url('node/' . $node->nid, array('absolute' => TRUE));
    
    $output .= '  <url>' . "\n";
    $output .= '    <loc>' . $url . '</loc>' . "\n";
    $output .= '    <lastmod>' . date('c', $node->changed) . '</lastmod>' . "\n";
    $output .= '    <changefreq>weekly</changefreq>' . "\n";
    $output .= '    <priority>0.8</priority>' . "\n";
    
    // Add alternate language versions
    $languages = array('en', 'ru', 'th', 'zh');
    foreach ($languages as $langcode) {
      if ($langcode != $node->language) {
        $alternate_url = url('node/' . $node->nid, array(
          'absolute' => TRUE,
          'language' => (object) array('language' => $langcode),
        ));
        
        $output .= '    <xhtml:link rel="alternate" hreflang="' . $langcode . '" href="' . $alternate_url . '" />' . "\n";
      }
    }
    
    // Add x-default
    $default_url = url('node/' . $node->nid, array(
      'absolute' => TRUE,
      'language' => (object) array('language' => 'en'),
    ));
    $output .= '    <xhtml:link rel="alternate" hreflang="x-default" href="' . $default_url . '" />' . "\n";
    
    // Add images
    $images = field_get_items('node', $node, 'field_images');
    if (!empty($images)) {
      foreach ($images as $image) {
        $image_url = file_create_url($image['uri']);
        
        $output .= '    <image:image>' . "\n";
        $output .= '      <image:loc>' . $image_url . '</image:loc>' . "\n";
        
        if (!empty($image['title'])) {
          $output .= '      <image:title>' . check_plain($image['title']) . '</image:title>' . "\n";
        }
        
        if (!empty($image['alt'])) {
          $output .= '      <image:caption>' . check_plain($image['alt']) . '</image:caption>' . "\n";
        }
        
        $output .= '    </image:image>' . "\n";
      }
    }
    
    $output .= '  </url>' . "\n";
  }
  
  $output .= '</urlset>';
  
  // Write to file
  file_put_contents(DRUPAL_ROOT . '/sitemap-properties.xml', $output);
  
  return TRUE;
}
```

### Sitemap for New Content

Create a specialized sitemap for recently added content:

```php
/**
 * Generate a sitemap for newly added content.
 */
function s45_phuket_generate_recent_content_sitemap() {
  $output = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
  $output .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
  
  // Get content created in the last 30 days
  $query = db_select('node', 'n');
  $query->fields('n', array('nid', 'title', 'language', 'created', 'changed'));
  $query->condition('n.status', 1);
  $query->condition('n.created', time() - (30 * 86400), '>');
  $query->orderBy('n.created', 'DESC');
  $result = $query->execute();
  
  foreach ($result as $node_data) {
    $url = url('node/' . $node_data->nid, array('absolute' => TRUE));
    
    $output .= '  <url>' . "\n";
    $output .= '    <loc>' . $url . '</loc>' . "\n";
    $output .= '    <lastmod>' . date('c', $node_data->changed) . '</lastmod>' . "\n";
    $output .= '    <changefreq>daily</changefreq>' . "\n";
    $output .= '    <priority>0.9</priority>' . "\n";
    $output .= '  </url>' . "\n";
  }
  
  $output .= '</urlset>';
  
  // Write to file
  file_put_contents(DRUPAL_ROOT . '/sitemap-recent.xml', $output);
  
  return TRUE;
}
```

## Implementation Timeline

| Phase | Task | Timeframe | Priority |
|-------|------|-----------|----------|
| Phase 1 | Sitemap Architecture Design | Day 1 | HIGH |
| Phase 2 | Module Configuration | Day 2 | HIGH |
| Phase 3 | Custom Implementation | Days 3-4 | HIGH |
| Phase 4 | Testing and Verification | Day 5 | HIGH |
| Phase 5 | Automated Maintenance | Ongoing | MEDIUM |

## Conclusion

This sitemap redesign plan addresses the current issues with the InDreams Phuket website's XML sitemaps. By implementing proper segmentation by language and content type, along with the inclusion of hreflang annotations and image information, the new sitemap structure will significantly improve search engine crawling and indexing.

The implementation should be rolled out incrementally, starting with the high-priority tasks of architecture design and module configuration, followed by custom implementation and testing. Regular monitoring and maintenance will ensure that the sitemaps remain effective over time.

After implementation, monitor Google Search Console for improvements in crawling and indexing patterns, particularly for the English version of the site which is targeted for international visitors. 