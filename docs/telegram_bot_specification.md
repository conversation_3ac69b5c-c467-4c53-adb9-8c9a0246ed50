# InDreams Phuket Telegram Bot Technical Specification

## Project Overview
A Telegram bot for InDreams Phuket real estate agency that automatically publishes property listings from an XML feed to Telegram channels/groups.

## Core Requirements

### Data Source
- [✓] Integration with HipFlat XML feed (`https://indreamsphuket.com/s45-indreams-export/hipflat.xml`)
- [ ] Local database for properties with caching
- [ ] Regular data synchronization (every 3 hours)

### Bot Features
- [ ] Multi-language support (Russian/English)
- [ ] Property search with filters (bedrooms, price, location)
- [ ] Auto-collections based on criteria
- [ ] Favorite properties for users
- [ ] Media galleries with property photos
- [ ] Location sharing on map
- [ ] Direct contact with listing agent

### Technical Specifications
- **Programming Language**: PHP 8.0+
- **Database**: SQLite 3
- **Required Extensions**: 
  - curl
  - xml
  - sqlite3
  - json
  - gd (for image processing)
- **Deployment**: VPS with Ubuntu 20.04+

## Database Schema

### Core Tables
- `properties` - Property listings with all details
- `photos` - Property images (cached locally)
- `features` - Property features and amenities
- `collections` - Curated property collections
- `users` - Bot users and their preferences
- `favorites` - User's favorite properties

## XML Feed Structure
```xml
<rss xmlns:atom="http://www.w3.org/2005/Atom" version="2.0">
  <channel>
    <item>
      <status>available</status>
      <propertyType>house</propertyType>
      <title>Property Title</title>
      <published>YYYY-MM-DD HH:MM:SS</published>
      <updated>YYYY-MM-DD HH:MM:SS</updated>
      <link>https://example.com/property/123</link>
      <refId>123</refId>
      <coordinates>
        <lat>0.000000</lat>
        <lng>0.000000</lng>
      </coordinates>
      <place>Location</place>
      <beds>3</beds>
      <baths>2</baths>
      <photos>
        <photo>https://example.com/image1.jpg</photo>
        <!-- More photos -->
      </photos>
      <!-- Other fields -->
    </item>
    <!-- More items -->
  </channel>
</rss>
```

## Implementation Progress

### Phase 1: Foundation ⏱ Deadline: ___/___/___
- [ ] Project structure setup
- [ ] XML feed parser
- [ ] SQLite database setup
- [ ] Basic Telegram API integration

### Phase 2: Core Features ⏱ Deadline: ___/___/___
- [ ] Property listing and display
- [ ] Image caching and galleries
- [ ] Search functionality
- [ ] Automatic collections
- [ ] User preferences

### Phase 3: Advanced Features ⏱ Deadline: ___/___/___
- [ ] Multi-language support
- [ ] Location sharing
- [ ] Agent contact functions
- [ ] Personalized recommendations
- [ ] Admin dashboard

### Phase 4: Testing & Deployment ⏱ Deadline: ___/___/___
- [ ] Unit testing
- [ ] Integration testing
- [ ] Performance optimization
- [ ] Server deployment
- [ ] Monitoring setup

## Development Notes

```
# Development environment setup
DATE: 

# XML feed parsing implementation
DATE: 

# Database schema implementation
DATE: 

# Telegram API integration
DATE: 

# Search functionality
DATE: 

# Image processing and caching
DATE: 

# Performance optimizations
DATE: 

# Deployment notes
DATE: 
```

## Resources
- Telegram Bot API Documentation: https://core.telegram.org/bots/api
- PHP Telegram Bot SDK: https://github.com/php-telegram-bot/core
- XML Parsing in PHP: https://www.php.net/manual/en/book.simplexml.php
- SQLite Documentation: https://www.sqlite.org/docs.html 