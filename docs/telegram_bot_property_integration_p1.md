# Интеграция Telegram-бота с сайтом InDreams Phuket (Часть 1)

## Введение

Данная документация описывает процесс создания Telegram-бота для автоматической публикации новых объектов недвижимости с сайта InDreams Phuket в канале Telegram. Решение использует существующую систему управления недвижимостью на Drupal 7 с кастомным модулем s45_phuket.

## Архитектура решения

### Общая схема работы:

1. **Мониторинг новых объектов** - Система регулярно проверяет наличие новых объектов недвижимости
2. **Формирование сообщения** - При появлении нового объекта формируется сообщение для Telegram
3. **Отправка в Telegram** - Сообщение отправляется через Telegram Bot API
4. **Публикация в канале** - Сообщение публикуется в заданном канале или группе

### Компоненты системы:

1. **База данных Drupal** - Хранит все объекты недвижимости в таблице `_phuket_Property`
2. **PHP-скрипт** - Обнаруживает новые объекты и отправляет их в Telegram
3. **Cron задача** - Запускает скрипт с заданной периодичностью
4. **Telegram Bot API** - Интерфейс для взаимодействия с Telegram

## Структура данных недвижимости

### Основные поля объекта недвижимости

Объекты недвижимости хранятся в таблице `_phuket_Property` со следующей структурой:

| Поле | Тип | Описание |
|------|-----|----------|
| id | varchar(36) | Уникальный идентификатор |
| number | int | Номер объекта |
| created | int | Дата создания (Unix timestamp) |
| changed | int | Дата изменения (Unix timestamp) |
| published | int | Статус публикации (0/1) |
| name | text | Сериализованный объект с названиями на разных языках |
| dealType | varchar | Тип сделки (sale/rent) |
| propertyType | varchar | Тип недвижимости (villa/apartment/townhouse/penthouse/hotel) |
| price_sale | int | Цена продажи |
| price_rent | int | Цена аренды |
| price_longtime | int | Цена долгосрочной аренды |
| areaCommon | int | Общая площадь |
| bedrooms | int | Количество спален |
| bathrooms | int | Количество ванных |
| lat | float | Широта |
| lng | float | Долгота |
| propertyDto | text | Сериализованный полный объект данных (DTO) |
| propertyTeaserDto | text | Сериализованный сокращенный объект данных |

### Поля DTO объекта (PhuketPropertyDto)

Объект данных (DTO) содержит все атрибуты недвижимости:

```php
class PhuketPropertyDto extends Dto {
  public $type = 'PhuketProperty';
  public $published = -1;
  public $dealType;         // Тип сделки
  public $propertyType;     // Тип недвижимости
  public $number;           // Номер объекта
  public $name;             // Название объекта (мультиязычное)
  public $description;      // Описание (мультиязычное)
  public $priceSale;        // Цена продажи
  public $priceRent;        // Цена аренды
  public $priceRentLongTime; // Цена долгосрочной аренды
  public $areaCommon;       // Общая площадь
  public $areaPlot;         // Площадь участка
  public $bedrooms;         // Кол-во спален
  public $bathrooms;        // Кол-во ванных
  public $photos = array(); // Массив фотографий
  public $videoLink;        // Ссылка на видео
  public $re_country;       // Страна
  public $re_locality;      // Район
  public $re_subLocality;   // Подрайон
  public $re_latitude;      // Широта
  public $re_longitude;     // Долгота
  public $isRecommended;    // Рекомендуемый
  public $isPermium;        // Премиум
  public $isSaled;          // Продан
  public $isInvest;         // Инвестиционный
}
``` 