# CHANGELOG

## 2024-12-28

### Added
- **КАЛЕНДАРЬ:** Добавлены стрелочки навигации для перехода между месяцами во всех flatpickr календарях на сайте
- Создана функция `addCalendarNavigationArrows()` для автоматического добавления стрелочек навигации
- Стрелочки добавляются во все календари: `#introDate`, `#introDateInner`, `#objectCalendar` с обработчиками клика
- Интегрированы с существующей swipe-навигацией для мобильных устройств
- Добавлены SVG-иконки для стрелочек с правильными CSS-классами (`.flatpickr-prev-month`, `.flatpickr-next-month`)

### Fixed
- **КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ CSS:** Устранена проблема скрытия стрелочек календаря на мобильных устройствах
- Исправлен CSS-конфликт в `styles.min.css`, который скрывал стрелочки через медиа-запрос `@media (max-width: 480px)`
- Добавлены CSS-переопределения в `styles-addon.css` с `!important` флагами для обеспечения видимости стрелочек на всех устройствах
- Стрелочки теперь видны и работают корректно на десктопе, планшетах и мобильных устройствах

### Technical Details - Calendar Navigation
- **Функция навигации:** `addCalendarNavigationArrows(instance)` проверяет существование стрелочек и добавляет их в `monthsContainer`
- **События:** Стрелочки используют `flatpickrInstance.changeMonth(-1/1)` для корректного переключения месяцев
- **CSS-исправления:** Переопределены стили для `.flatpickr-prev-month` и `.flatpickr-next-month` с `display: flex !important`
- **Кроссплатформенность:** Обеспечена видимость на всех устройствах (десктоп, мобильные, планшеты)
- **Совместимость:** Работает с существующими CSS-стилями для стрелочек flatpickr
- **Резервное включение:** Дублирующий вызов через setTimeout для обеспечения инициализации во всех случаях
- **Предотвращение дублирования:** Проверка существующих стрелочек перед добавлением новых

### ОПТИМИЗАЦИЯ PHP 7.3:** Настроены лимиты для поддержки загрузки до 1GB фотографий через админку s45
- Добавлены настройки PHP 7.x в .htaccess для корректной работы с текущей версией PHP
- Увеличены лимиты времени выполнения (max_execution_time, max_input_time) до 300 секунд
- Оптимизированы лимиты загрузки файлов: post_max_size=1200M, upload_max_filesize=100M, max_file_uploads=50
- Создан тестовый скрипт для проверки PHP лимитов и диагностики проблем загрузки

### Technical Details - PHP Optimization
- **Проблема:** В .htaccess были настройки только для PHP 5, а система использует PHP 7.3
- **Решение:** Добавлена секция `<IfModule mod_php7.c>` с оптимальными настройками для больших загрузок
- **Результат:** Поддержка загрузки до 1.17GB за одну операцию, до 12 файлов по 100MB одновременно
- **Безопасность:** Сохранены все существующие настройки безопасности и производительности

### Fixed
- **КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ:** Устранена проблема с ошибкой 500 при загрузке изображений недвижимости в Dropbox через `property_images_dropbox.php`
- Исправлена неэффективная загрузка всех объектов недвижимости в память (3761 объект), которая приводила к превышению лимита памяти PHP (1GB)
- Реализован эффективный поиск объектов недвижимости через прямые SQL-запросы вместо итерации по всем записям
- Добавлены улучшенные логи для диагностики проблем загрузки в `tmp/dropbox_upload_fixed.log`
- Протестирована успешная загрузка объекта 7071: 24 фотографии успешно загружены в Dropbox

### Technical Details
- **Проблема:** Скрипт `property_images_dropbox.php` использовал неэффективный цикл `while ($row = $result->fetch())` для поиска объекта по номеру, загружая все 3150+ объектов в память
- **Решение:** Заменен на эффективный SQL-запрос `WHERE number = :property_number` с fallback поиском по сериализованным данным
- **Результат:** Использование памяти снижено с 1GB+ до ~50MB, время выполнения уменьшено с timeout до 2-3 секунд

### Added
- Создана система автоматического мониторинга Dropbox токенов для предотвращения ошибок загрузки изображений недвижимости
- Добавлен скрипт `dropbox_token_monitor.php` для проверки и автоматического обновления истекающих токенов
- Создан установочный скрипт `setup_dropbox_monitoring.sh` для настройки cron-задач мониторинга
- Добавлено полное руководство по поддержке Dropbox Uploader в `docs/dropbox-uploader-maintenance-guide.md`
- Внедрена система логирования с ротацией и мониторингом ошибок API
- Реализован механизм уведомлений об ошибках через Drupal watchdog

### Fixed
- Диагностирована и решена проблема с истёкшими токенами доступа Dropbox API (срок действия истёк 16 июня 2025)
- Восстановлено подключение к Dropbox API как "Anna Baranova (<EMAIL>)"
- Обновлены токены доступа с новым сроком действия до 20 июня 2025 18:58

### Changed
- Улучшена архитектура работы с токенами Dropbox для предотвращения подобных проблем в будущем
- Настроена автоматическая проверка токенов каждые 12 часов через cron
- Обновлён процесс диагностики проблем с Dropbox uploader с пошаговыми инструкциями

## 2024-03-27
- Исправлена фатальная ошибка PHP в форме логина (Cannot pass parameter 1 by reference) путем изменения способа создания объектов перевода
- Исправлена проблема с отображением текстов в модальном окне входа на сайт (заменено "????" на правильные переводы)
- Использовано создание объектов LangVO напрямую вместо статического метода LangVO::create()
- Очищен кеш Drupal для применения изменений
- Обновлена документация с новыми знаниями о работе с многоязычностью в S45

## 2024-03-27
- Added YouTube video API integration fix with enablejsapi and origin parameters
- Fixed Same-Origin Policy issues with YouTube iframe
- Implemented safe postMessage handler for YouTube events
- Created youtube-fix.js with YT API event handling
- Added currency switcher with styled gold buttons
- Improved login form with modal styling and animations
- Added autofocus functionality to the login form
- Fixed 404 errors and cyclic requests by disabling image-tester.js
- Synchronized currency switcher with existing menu switcher
- Added detailed styling for login form elements

## 2024-03-26
- Created sitemap_validation_report.md with sitemap analysis
- Fixed property_base_url for generation of property URLs in sitemap
- Added proper handling of HTTPS for image URLs in sitemap
- Improved performance of the sitemap generatorby batching property queries
- Added missing sitemap types: articles, services, and projects

## 2024-03-25
- Created detailed report on beaches statistics in docs/beaches_stats.md
- Added improvements to property location management
- Implemented view property by types page
- Fixed property sorting by price and area

## 2024-03-25
- Updated layout for mobile view component
- Fixed responsive design issues on property detail pages
- Improved image loading with lazy loading implementation
- Added faster initial page load by optimizing critical CSS

## 2024-03-23
- Added meta description and title for SEO optimization
- Fixed search component to include property types
- Updated footer links and copyright information
- Added XML Sitemap support for better search engine indexing

## 2024-03-22
- Fixed bug with property sorting on search pages
- Added filtering capabilities to property lists
- Improved mobile navigation drawer
- Updated Russian translations for property types

## 2024-03-21
- Added new testimonials component on the homepage
- Improved form validation with better error messages
- Added image optimization to reduce page load times
- Fixed bug with property photo gallery on mobile

## 2024-03-20
- Created system for property management
- Added API for property data access
- Implemented new design for property listing cards
- Fixed the map component to properly show property locations

## 2024-03-19
- Launched new homepage design
- Added advanced search functionality
- Implemented currency converter for property prices
- Created responsive grid for property listings

## 2024-03-18
- Fixed security issues in user authentication
- Implemented content caching system
- Added support for Chinese language
- Updated server configuration for better performance

## 2024-03-17
- Created documentation for admin panel
- Fixed bugs in media uploader
- Added validation for property submission forms
- Improved error logging system

## 2024-03-16
- Implemented real-time notifications system
- Added property favorites functionality
- Fixed issues with multi-language content
- Created advanced search filters

## 2024-03-15
- Initial project setup
- Created repository structure
- Set up development environment
- Installed required dependencies
- Configured basic site settings

## Pending Tasks
- Implement property comparison feature
- Add user review system
- Create mobile app integration API
- Develop admin dashboard analytics
- Add automatic language detection based on browser settings
- Implement geo-targeting features

## 2024-05-12
- Created detailed performance and security improvement recommendations with 3 approaches:
  - Quick improvements (1-2 months): caching optimization, image processing, input validation
  - Structural improvements (3-6 months): advanced caching, database optimization, async processing
  - Full modernization (6-12 months): microservices, GraphQL API, containerization
- Added comprehensive security enhancement strategies for different implementation stages
- Created phased implementation plan with prioritized actions for maximum impact

- Updated sitemaps: 2025-03-23
## 2023-03-18
- Fixed critical sitemap URL issues - corrected missing domain name in URLs
- Fixed incorrect future dates in sitemap files (2025 to 2023)
- Fixed URLs with s45/ prefix in individual sitemap files
- Improved sitemap generation code to handle server configuration issues
- Redesigned XML sitemaps with segmentation by language and content type
- Added sitemap index file
- Fixed HTTP/HTTPS URLs in sitemaps
- Added proper priority and changefreq tags
- Added image support for property listings

### Fixed
- Fixed sitemap accessibility issues for search engines blocked by Cloudflare
  - Created Cloudflare API script to configure firewall rules for search engine bots
  - Added specific rule for verified Google crawlers based on ASN numbers
  - Created page rules to bypass caching and security for sitemap XML files
  - Disabled Bot Fight Mode for legitimate search engine crawlers
  - Added comprehensive testing tools to verify sitemap accessibility

### Added
- Created diagnostic tools for sitemap accessibility
  - Implemented test script that checks sitemap accessibility with various search engine user agents
  - Added detailed reporting and suggestions for fixing any remaining issues
  - Created documentation for troubleshooting Cloudflare + sitemap issues

## 2025-03-18

### Fixed
- Updated outdated English sitemap (from 2022)
  - Created custom PHP CLI script for sitemap generation
  - Generated fresh sitemap with 43,870 URLs
  - Ensured multilingual hreflang tags were correctly applied
  - Optimized sitemap structure for better search engine indexing
  
### Added
- Implemented comprehensive sitemap management system
  - Created individual language-specific sitemap generation scripts for all languages (ru, en, th, zh)
  - Developed unified sitemap update script to refresh all sitemaps at once
  - Added shell script with logging for cron-based automatic updates
  - Configured proper HTTP headers and caching for sitemaps
  - Documented sitemap structure and content types

## 2024-05-10

### Fixed
- Fixed git repository issues with large files
  - Removed large SQL backup file (5.3GB) from git history
  - Updated .gitignore to exclude all SQL backups and other large files
  - Optimized repository size for faster cloning and pushing

# Changelog

All notable changes to this project will be documented in this file.

## [2025-06-20] - Отключение физических календарных стрелочек

### Changed
- Отключены физические золотистые стрелочки навигации в календарях по просьбе пользователя
- Оставлены только стандартные белые стрелочки flatpickr для навигации по месяцам
- Сохранена функциональность swipe-навигации для мобильных устройств

### Removed
- Убраны все вызовы функции `addCalendarNavigationArrows()` из JavaScript
- Удалены CSS-стили для физических кнопок календарной навигации
- Очищены стили `.calendar-nav-arrows` и `.calendar-nav-btn` из styles-addon.css

### Technical Details
- Функция `addCalendarNavigationArrows()` оставлена в коде для возможного использования в будущем
- Все вызовы функции закомментированы в onReady callbacks календарей
- Swipe-навигация продолжает работать на мобильных устройствах через `initCalendarSwipeNavigation()`

## [2025-06-20] - Физические календарные стрелочки

### Added
- Добавлены заметные физические стрелочки для навигации в календарях
- Созданы круглые кнопки с золотистым дизайном для переключения месяцев
- Добавлены hover-эффекты с увеличением и подсветкой стрелочек
- Реализованы стрелочки для всех календарей (introDate, introDateInner, objectCalendar)
- Добавлена поддержка мобильных устройств с адаптацией размеров

### Changed
- Заменен стандартный механизм добавления стрелочек через flatpickr на создание отдельных физических кнопок
- Улучшена видимость навигационных элементов календаря для пользователей
- Оптимизированы CSS-стили для обеспечения совместимости с существующими стилями

### Fixed
- Устранены конфликты CSS, скрывавшие стандартные стрелочки календаря
- Обеспечена стабильная работа навигации во всех календарях сайта

## 2024-03-18

### Added
- Comprehensive SEO Implementation Plans:
  - Created detailed hreflang implementation plan for fixing multilingual issues
  - Developed Drupal 7 caching optimization guide for performance improvement
  - Created Schema.org RealEstateListing markup templates for property listings
  - Designed XML sitemap restructuring plan for better search engine indexing
  - Developed English content strategy for international property buyers
  - Created comprehensive 12-week SEO implementation roadmap with prioritized tasks, resource allocation, and testing methodology

## 2024-04-28

### Fixed
- Fixed PDF generation issue for property pages - PDFs now regenerate properly with updated data instead of showing cached versions
  - Updated `s45_phuket_pdf.inc` to delete existing PDF files before regeneration
  - Changed HTTP redirect from 301 (permanent) to 302 (temporary) to prevent browser caching
  - Added timestamp parameter to PDF URLs to force browser cache refresh
  - Added explicit Cache-Control headers to prevent caching by browsers and proxies
  - Added more robust file deletion with permission check and retry
  - Added support for force refresh parameter (?refresh=1)
- Fixed USD price conversion in PDFs showing "0 USD"
  - Added fallback exchange rate when currency data is not available
  - Added proper error checking for currency conversion
  - Updated PDF generation logic to handle missing currency rates 

## [Unreleased]

### Fixed
- Исправлена проблема с размером заголовков в тексте статей/блогов - все заголовки теперь отображаются с соответствующим размером шрифта (h1, h2, h3)
- Удалена принудительная замена заголовков h1 и h2 на h3 в шаблонах PhuketArticleAboutD2 и PhuketBlogAboutD2
- Добавлен файл стилей article-custom.css со специальными стилями для заголовков в тексте статей
- Исправлены большие рамки и отступы в модальной галерее фотографий при полноэкранном просмотре (как в верхней, так и в нижней части окна)
- Устранены лишние отступы в слайдере, улучшено отображение изображений в полноэкранном режиме
- Оптимизировано использование вертикального пространства экрана для показа максимально крупных изображений
- Увеличена полезная площадь модального окна за счет уменьшения отступов
- Применены единые стили галереи на всех устройствах
- Полностью удален проблемный скрипт bundle.js.gz, вместо его блокировки
- Отключена автоматическая загрузка сжатых .js.gz файлов через .htaccess
- Реализовано полное удаление bundle.js из DOM и связанных с ним глобальных объектов
- Усилена защита от инъекций проблемных скриптов и обработка связанных с ними ошибок
- Улучшена блокировка проблемного скрипта bundle.js.gz вызывавшего ошибку "m is not a function or its return value is not iterable"
- Добавлен MutationObserver для отслеживания и удаления динамически добавляемых проблемных скриптов
- Перехват глобальных JavaScript ошибок для предотвращения показа ошибок из заблокированных скриптов
- Блокировка попыток вставки скриптов через document.write
- Реализована многоуровневая защита от проблемных скриптов

### Added
- Добавлен метод UserConfirmation.setConfirmed() для программного управления статусом подтверждения загрузки карт
- Добавлено событие map_api_requested для отслеживания запросов к API карт
- Улучшена обработка искусственных событий для предотвращения зацикливания обработчиков
- Добавлена поддержка различных элементов при клике на "Посмотреть на карте"
- Implemented multi-level caching for PhuketScreens pages:
  - Added Apache-level caching with Cache-Control and Expires headers
  - Added Drupal-level caching with hook_init implementation
  - Added client-side caching with localStorage for offline access
- Integrated OpenStreetMap as a replacement for Google Maps:
  - Created a Leaflet.js based implementation for interactive maps
  - Added Yandex Static Maps for initial rendering without API key requirement
  - Used CyclOSM tile layer for better visual appearance and performance
  - Implemented lazy loading for better performance
  - Maintained localStorage caching for coordinates
- Simplified map integration by removing Leaflet dependency and using only Yandex Static Maps
- Added zoom controls for better user experience with the static map
- Improved code readability and reduced page load time by removing interactive map features
- Optimized coordinate detection from multiple sources (data attributes, URL parameters)
- Simplified error handling when coordinates are not found
- Completely removed map functionality from property gallery
- Implemented user-triggered Google Maps loading system to reduce API costs:
  - Created global map confirmation mechanism with "Load Map" buttons
  - Added placeholder maps that only load Google Maps API when user confirms
  - Intercepted all Google Maps API loading calls site-wide
  - Applied advanced JavaScript interception techniques to prevent automatic API loading
  - Added visual placeholder with map icon and loading button
  - Created seamless transition from placeholder to interactive map
- Implemented comprehensive Google Maps API usage protection system:
  - Created a one-time confirmation dialog with 24-hour persistence via localStorage
  - Added automatic Yandex static maps as initial display for all map areas
  - Implemented detailed usage analytics tracking via localStorage
  - Added specific targeting for PhuketPropMap-Open map trigger elements
  - Created monitoring system for daily and per-page map usage statistics
  - Added ability to reset user preferences
  - Implemented debugging and configuration options for administrators
- Created comprehensive sitemap improvement analysis document
- Developed step-by-step implementation plan for sitemap optimization
- Added code examples for integrating image sitemap information
- Created template for news sitemap implementation
- Added implementation guide for automated sitemap updates using cron
- Designed URL validation mechanism for sitemap entries
- Developed sitemap statistics reporting system
- Implemented automated sitemap updates via Drupal cron (daily updates)
- Created search engine ping system for notifying Google, Bing, and Yandex about sitemap updates
- Implemented image sitemap enhancement for better indexing of property images
- Created sitemap analysis and validation tool for monitoring sitemap quality
- Added comprehensive logging of sitemap update activities
- Created detailed implementation report and future improvement plans
- Added interactive property contract form at /docs/ path with validation and preview functionality
- Added header with logo to property management contract for professional branding on all printed pages
- Added CloudFlare Helper module for improved CDN integration and image loading performance
- Implemented optimized lazy loading for images with prioritization of visible content
- Added critical CSS for faster initial page rendering
- Added font preloading for better performance
- Added CloudFlare cache clearing functionality through Drupal UI
- Enhanced Yandex.Metrika integration with improved error recovery and CSP handling
- Added automatic Yandex.Metrika counter detection and initialization
- Added OpenStreetMap as a fallback for disabled Google Maps
- Added OpenStreetMap integration using Leaflet.js
- Re-enabled Google Maps integration with a new API key
- Added user confirmation requirement for Google Maps loading
- Created placeholders for maps that require explicit user consent before loading
- Implemented persistent consent storage using localStorage
- Added visual map placeholders with "Show Map" buttons to reduce unnecessary API calls
- Added blocking mechanism for problematic scripts (SendPulse, bundle.js.gz)
- Enhanced script blocking functionality to prevent JavaScript errors
- Added active script cleanup for SendPulse and other problematic scripts
- Improved error handling for third-party scripts that cause JavaScript errors

### Changed
- Modified error interception to block and remove problematic scripts
- Enhanced jQuery protector to handle SendPulse-specific errors
- Extended script blocking mechanism in multiple layers for better redundancy
- Updated code to nullify problematic global variables like window.osp and window.SendPulse
- Enhanced performance for street screens by adding caching mechanisms
- Improved user experience for returning visitors with cached content
- Switched from Google Maps to completely free alternatives (Yandex Static Maps and OpenStreetMap/CyclOSM)
- Eliminated the need for Google Maps API, reducing costs and removing API quota limitations
- Reduced Google Maps API usage costs by implementing confirmation button before loading maps
- Changed map loading workflow to use static maps by default with opt-in Google Maps
- Improved mobile experience with touch-friendly confirmation dialogs
- Analyzed existing sitemap structure (31 XML files) and identified improvement areas
- Identified missing namespace usage for image sitemap despite declarations
- Updated the sitemap generation process to include proper image tags
- Modified update_all_sitemaps.php to generate index file and ping search engines
- Enhanced PhuketSitemap.php for better image handling in property listings
- Improved property management contract interface with information block about new header feature
- Improved image loading performance by prioritizing visible images
- Optimized CloudFlare integration by disabling Rocket Loader for critical scripts
- Enhanced JavaScript error handling and recovery
- Disabled Google Maps API completely due to high billing costs
- Replaced hard-coded Google Maps API key with a disabled key
- Added overlay notices to map containers informing users about temporarily disabled maps
- Enhanced map protection script to block all Google Maps API calls
- Improved overall site performance by reducing API calls
- Replaced the old Google Maps API key (AIzaSyBYixChT1ElrnTQ4W5KgAdbnKIWrMJTbWo) with a new one (AIzaSyDTEXdJu8eCqJCK8FsJbQYcfhAklQ61QjU) across all files
- Modified map protection script to replace old API key with the new one instead of blocking Google Maps
- Updated OpenStreetMap fallback to only activate when Google Maps is explicitly disabled
- Added global flags to force Google Maps enablement
- Modified Google Maps API protection script to require user confirmation
- Enhanced map loading mechanism to respect user consent
- Made Google Maps loading opt-in rather than automatic to reduce API usage
- Updated initialization flags to check for saved user preferences
- Ограничено количество превью фотографий на странице объекта до 5 (было 15)
- Улучшен пользовательский интерфейс предпросмотра изображений для лучшей производительности
- Оптимизирован интерфейс модальной галереи: уменьшены отступы между фотографиями, увеличена полезная площадь просмотра

### Fixed
- Fixed gold color styling on buy_now screen by adding specific CSS rules for charstop__label, charsitems__value, and contact__name elements
- Fixed missing gold color on all screen elements by adding screen--buy_now CSS class styles
- Fixed static maps loading error by replacing non-working OpenStreetMap static service with reliable Yandex Maps static API
- Fixed JavaScript errors in Google Maps loading interception
- Fixed map display on properties with missing coordinates
- Fixed multiple API requests being triggered from single page views
- Identified issue with image sitemap implementation not being used
- Found missing search engine ping functionality after sitemap updates
- Ensured proper usage of image namespace in sitemap XML
- Fixed missing search engine notification after sitemap updates
- Addressed issues with automated sitemap generation
- Fixed search functionality that was incorrectly pointing to indreamsphuket.ru domain
- Corrected JavaScript autocomplete URLs in PhuketFrontHeader, PhuketFrontPromo and PhuketSearchForm components
- Fixed JavaScript error "Uncaught ReferenceError: s45_Debug is not defined" by adding a fallback function
- Fixed jQuery multiselect plugin issues by adding fallback implementations and proper loading order
- Fixed lazy loading mechanism to handle both data-src and class-based lazy loading approaches
- Fixed Content Security Policy (CSP) issues by implementing it as an HTTP header instead of meta tag
- Fixed Yandex.Metrika connection issues by explicitly allowing connections to Yandex domains
- Fixed local network connection blocking that was affecting Yandex.Metrika functionality
- Fixed billing issues with Google Maps API by completely disabling it
- Addressed high API usage costs by implementing a more cost-effective mapping solution
- Restored Google Maps functionality that was previously disabled due to billing concerns
- Fixed map containers to display Google Maps again instead of fallback solutions
- Removed notification overlays about maps being temporarily disabled
- Reduced unnecessary API calls to Google Maps by requiring user confirmation
- Added protection against accidental API usage when maps are not actually viewed
- Improved visual feedback when maps are waiting for user confirmation
- Added graceful fallback to static placeholder when maps are not confirmed
- Fixed errors from SendPulse script ("m is not a function" and "is not iterable")
- Fixed issues with bundle.js.gz causing JavaScript errors
- Added comprehensive removal of problematic scripts using multiple detection methods
- Enhanced error detection and blocking for scripts injected by third-party services

## 2025-03-18
- Added redirection for screen URLs to a separate subdomain to improve analytics accuracy
- Created virtual host configuration for screens.indreamsphuket.com
- Added robots.txt rules to block search engines from indexing screen URLs
- Created script to update any hard-coded URLs in screen-related files
- Added screens.indreamsphuket.com to the s45_base Sites.s45.json configuration file to properly recognize the subdomain

### [1.0.0] - 2024-05-07

- Integrated OpenStreetMap as an alternative to Google Maps
- Created static map implementation to improve performance
- Fixed styling issues for better map display
- Improved mobile responsiveness for map elements
## 2025-03-18
- Added Cloudflare configuration to fix sitemap accessibility for search engines
- Created sitemap accessibility testing tool
- Created documentation for resolving sitemap access issues

## 2025-03-18
- Added Cloudflare configuration to fix sitemap accessibility for search engines
- Created sitemap accessibility testing tool
- Created documentation for resolving sitemap access issues

### Added
- Google Maps API tracker для мониторинга всех запросов к Google Maps API
- Функция `window.showGoogleMapsApiStats()` для просмотра статистики использования API
- Статистика по типам запросов к Google Maps API, времени выполнения и использованию API ключей
- Расширенная функциональность для сохранения исторических данных между сессиями
- Административный интерфейс для просмотра статистики API в панели управления Drupal
- Трекинг страниц с наибольшим количеством запросов к API
- Функция `window.getGoogleMapsApiSummary()` для получения сводной статистики за период

### Changed
- Обновлен README.md с описанием новой функциональности по отслеживанию Google Maps API
- Обновлен файл cloudflare_helper.info для включения информации о новой функциональности
- Расширена функциональность модуля CloudFlare Helper для поддержки мониторинга API

### Fixed
- Улучшена совместимость со стандартами кодирования Drupal
- Оптимизирован скрипт для отслеживания запросов к API для снижения нагрузки

## 2023-10-20
- Улучшена адаптация страницы расписания для iPhone 16 Pro Max
- Добавлены анимации для плавного переключения между днями недели
- Улучшен интерфейс мобильной навигации для больших смартфонов
- Оптимизирован макет и размеры элементов для лучшей читаемости на мобильных устройствах
- Добавлена поддержка жестов свайпа для навигации между днями
- Улучшено визуальное отображение расписания в режиме просмотра по дням
- Добавлены подсказки для улучшения пользовательского опыта на мобильных устройствах

## 2025-03-25
- Completely redesigned the English sitemap generation process
- Fixed sitemap issues with expired SSL certificate
- Added image sitemap support with proper metadata
- Created thorough sitemap validation and reporting
- Updated robots.txt to properly reference sitemaps
- Added comprehensive sitemap best practices documentation
- Implemented search engine pinging for faster indexing
- Fixed Cloudflare access issues for sitemap files

## [Unreleased] - 2024-03-25

### Added
- Добавлена новая тема дизайна сайта с обновленным внешним видом
- Созданы новые компоненты с суффиксом D2 для нового дизайна
- Добавлена улучшенная обработка ошибок загрузки изображений во всех компонентах
- Интегрирована библиотека jBox для просмотра изображений в лайтбоксе
- Обновлена документация архитектуры сайта с информацией о новой теме
- Обновлена документация с полным списком пляжей и количеством объектов недвижимости (34 пляжа)
- Добавлена детальная статистика по распределению объектов недвижимости по всем районам Пхукета
- Включены данные по неактивным районам (без объектов недвижимости)
- Добавлена фильтрация пляжей в поиске - скрыты пляжи с менее чем 4 объектами недвижимости x2
- Оптимизирован интерфейс поиска для отображения только активных локаций x2
- Улучшена производительность поиска за счет фильтрации неактуальных опций x2
- Добавлены навигационные стрелки для переключения фотографий в основной галерее объектов недвижимости
- Улучшен пользовательский интерфейс для просмотра фотографий без необходимости открытия полноэкранного режима
- Увеличено количество отображаемых превью фотографий в галерее объектов недвижимости с 5 до 15
- Улучшена динамическая генерация списка изображений в модальной галерее
- Оптимизирована загрузка и отображение больших коллекций изображений

### Fixed
- Исправлены проблемы с отображением мастер-плана в карточке недвижимости
- Улучшена обработка ошибок для галереи изображений
- Исправлены проблемы с кодировкой в шаблонах
- Отключено визуальное отображение JavaScript ошибок на странице для обычных пользователей

### Removed
- Удален модуль CloudFlare Helper из-за проблем с отладочными сообщениями и нестабильной работы
- Отключен модуль Cloudflare из-за ошибок в функционале и отсутствия необходимых функций

### Removed
- Удален скрипт SendPulse и его интеграция для уменьшения JavaScript ошибок
- Отключен Cloudflare Rocket Loader для улучшения стабильности
- Удалена форма подписки на рассылку SendPulse из лендинга инвестиций

### Fixed
- Исправлены JavaScript ошибки, связанные с SendPulse и bundle.js.gz
- Улучшена блокировка нежелательных скриптов через Object.defineProperty
- Добавлена периодическая очистка DOM от элементов SendPulse

## Изменения

### Добавлено
- Новая структура слайдера на странице объекта, с ограничением количества превью до 5 элементов (1 основное, 4 дополнительных)
- Кнопка "Показать все фото" для открытия полного просмотра изображений
- Реализовано корректное отображение статуса избранных объектов
- Возможность переключения между объектами для сравнения

### Исправлено
- Исправлена работа кнопок "Загрузить PDF" и "Добавить в избранное" в модальном окне
- Добавлены правильные ссылки и атрибуты для кнопок в верхней навигационной панели
- Улучшена интеграция между модальным окном и данными объекта недвижимости
- Исправлена проблема с кнопкой "Смотреть на карте", где запрос подтверждения загрузки Google Maps не отображался
- Исправлено блокирование проблемного скрипта bundle.js.gz, который вызывал ошибки в консоли
- Значительно улучшен внешний вид модального окна галереи с уменьшением отступов и рамок для увеличения полезной площади просмотра фотографий
- Оптимизированы стили для мобильной и десктопной версий модального окна галереи
- Полностью переработана структура стилей десктопной версии модального окна галереи для устранения проблем с большими отступами и рамками
- Применены агрессивные стили для максимального использования пространства экрана при просмотре фотографий на десктопных устройствах
- Улучшена адаптивность галереи для разных размеров экрана

### Удалено
- Удалена зависимость от проблемного скрипта bundle.js.gz

## 2024-04-28

- Восстановлена функциональность кнопки "Редактировать объект" в новом дизайне (D2)
- Добавлен блок "Контакты по объекту" для администраторов в новый дизайн (D2), аналогично старому дизайну

### Added
- Создан детальный план миграции модулей s45 на Drupal 10 с анализом архитектуры
- Составлена стратегия поэтапной миграции компонентной системы на архитектуру плагинов Drupal 10
- Добавлена оценка необходимых ресурсов и временных рамок для миграции

## [1.0.0] - 2024-08-30
### Changed
- Добавлена возможность редактирования недвижимости в новом дизайне

## [1.0.0] - 2024-03-26
### Changed
- Скрыты виджеты чата и обратной связи в административном разделе
- Изменены пропорции полей в форме редактирования недвижимости

## [1.0.0] - 2024-03-25
### Added
- Создан документ с рекомендациями по улучшению административной части сайта

## 2025-03-27
- Fixed repeated 404 errors for missing images by implementing comprehensive error handling and prevention mechanisms
- Added onerror handlers to all images in the PhuketPropertySliderD2 component
- Implemented a global image error tracking system to prevent repeated loading attempts for missing images
- Enhanced Swiper slider to prevent reloading previously failed images
- Added lazy loading to gallery images to improve performance
- Implemented currency switcher buttons in property card
- Added synchronization between property card and main site currency selectors
- Fixed currency selection in property detail pages
- Ensured correct display of prices when switching currencies

### Добавлено
- Реализован глобальный перехватчик для предотвращения загрузки проблемных изображений
- Создан скрипт `image-preloader.js` который загружается с весом -10 и перехватывает все запросы изображений до их отправки
- Добавлен механизм автоматического обнаружения и запоминания проблемных изображений через sessionStorage
- Внедрена система перехвата создания элементов <img>, установки атрибута src, AJAX-запросов и jQuery-вставок
- Создан заполнитель-изображение в директории `/sites/default/files/img/noimage.jpg`
- Добавлен паттерн блокировки для всех изображений из директории `/phuket/files/old/` для устранения множественных 404 ошибок
- Улучшена система обнаружения проблемных изображений с дополнительными паттернами блокировки
- Добавлена расширенная проверка URL с декодированием для улавливания всех форматов проблемных URL

### Исправлено
- Устранены повторяющиеся ошибки 404 для отсутствующих изображений через глобальный перехват запросов
- Предотвращена нагрузка на сервер от постоянных попыток загрузки несуществующих файлов
- Улучшено поведение Swiper слайдера при загрузке отсутствующих изображений
- Блокировка изображений из проблемного скрипта js_ox-FQ89GAPQhIT4

### Исправления и улучшения

- Исправлена ошибка JavaScript в jBox Image Gallery: "Cannot read properties of undefined (reading 'length')"
- Добавлен механизм обработки ошибок в галерею изображений для предотвращения падения скрипта при загрузке неисправных изображений
- Улучшена инициализация изображений для jBox галереи с проверкой правильности их загрузки
- Оптимизирован код работы с изображениями для предотвращения ошибок при навигации по галерее
- Внедрен патч для метода jBox.Image.showImage для безопасного отображения изображений

### Added
- Added synchronization between property card currency selector and main site currency selector
- Added integration to ensure currency selectors work together across the site

## 2023-03-28
- Исправлена проблема со скачиванием архива фотографий в административной панели редактирования объектов
- Улучшен интерфейс процесса скачивания файлов с добавлением статусных сообщений
- Устранена ошибка с CSRF проверкой при скачивании фотографий
- Улучшена обработка ошибок и логирование при скачивании фотографий
- Improved property photos download functionality with better error handling, caching, security enhancements, and modern JavaScript patterns
- Added retry mechanism for API calls to handle transient network errors
