# Архитектура модулей s45 и план миграции на Drupal 10

## 1. Анализ текущей архитектуры модулей s45

### 1.1 Обзор модульной системы

Модули s45 представляют собой комплексную систему расширений Drupal 7, организованную в виде иерархии модулей с четкой структурой зависимостей. Основные модули включают:

- **s45_base** - базовый модуль, предоставляющий основу для всех других модулей
- **s45_phuket** - основной функциональный модуль, реализующий бизнес-логику
- **s45_vendor** - модуль для интеграции сторонних библиотек
- **s45_path** - модуль для управления URL и маршрутизацией
- **s45_phuket_login** - модуль авторизации
- **s45_imagestyles** - модуль для обработки изображений
- **Compo** - модуль, содержащий компоненты для построения интерфейса

### 1.2 Базовая архитектура (s45_base)

#### 1.2.1 Ключевые классы и компоненты

- **Site45\Base\CompoApi2** - API для работы с компонентами
- **Site45\Base\CompoRepo** - репозиторий компонентов
- **Site45\Base\JsonRepo** - репозиторий для работы с JSON данными
- **Site45\Base\PageId** - управление идентификаторами страниц
- **Site45\Base\SiteConf** - конфигурация сайта
- **Site45\Base\Store** - хранилище данных сессии
- **Site45\Base\Logger** - система логирования

#### 1.2.2 Паттерны проектирования

В модуле s45_base реализованы следующие паттерны:
- **Repository** - для доступа к данным
- **Factory** - для создания объектов
- **Data Transfer Object (DTO)** - для передачи данных между компонентами
- **Value Object (VO)** - для представления неизменяемых значений

### 1.3 Функциональная архитектура (s45_phuket)

#### 1.3.1 Компонентная система

Вся функциональность сайта реализована через компоненты, которые находятся в директории `Site45/Sets/Phuket/Compo`. Компоненты структурированы по функциональным разделам:

- **Admin** - административный интерфейс (42 компонента)
- **Front** - компоненты главной страницы (31 компонент)
- **Property** - компоненты объектов недвижимости (27 компонентов)
- **PropertyBlocks** - блоки для объектов недвижимости (19 компонентов)
- **LandingSale/LandingRent** - лендинги для продажи и аренды
- **Article/News/Reviews** - контентные разделы

Каждый компонент включает:
- PHP класс для бизнес-логики
- TPL-файл для шаблона отображения
- JavaScript для клиентской логики
- CSS для стилей

#### 1.3.2 Система запросов

Система запросов в `Site45/Sets/Phuket/Query` обеспечивает доступ к данным через:
- Отдельные запросы для каждой сущности (PropertySearch, ArticleSearch и т.д.)
- Базовые классы запросов (PhuketAgentQuery, PhuketArticleQuery)

#### 1.3.3 Многоязычная система

Реализована через собственный механизм с использованием:
- **LangVO** - объект для хранения мультиязычных строк
- **s45_lang()** - функция для получения строки на текущем языке
- Поддержка русского, английского, китайского и тайского языков

### 1.4 Сторонние библиотеки (s45_vendor)

Модуль s45_vendor предоставляет интеграцию со следующими библиотеками:
- Bootstrap 4.4.1
- jQuery UI
- Select2
- Fancybox
- Font Awesome 5
- Vue.js
- Swiper
- jBox
- CKEditor
- другие библиотеки

## 2. Стратегия миграции на Drupal 10

### 2.1 Основные проблемы и вызовы

1. **PHP 8.1+ совместимость**:
   - Замена устаревших PHP-функций
   - Добавление типизации
   - Обновление синтаксиса обработки массивов и строк

2. **Изменения API Drupal**:
   - Переход от хуков к сервисам и плагинам
   - Переход от TPL-шаблонов к Twig
   - Изменение системы маршрутизации
   - Изменение механизма отображения интерфейса

3. **Компонентная система**:
   - Трансформация собственной компонентной системы в плагины Drupal 10
   - Адаптация шаблонов компонентов к Twig

4. **Управление данными**:
   - Перенос данных из собственного JSON-хранилища в Entity API
   - Адаптация системы запросов к Database API Drupal 10

### 2.2 Поэтапный план миграции

#### 2.2.1 Этап 1: Анализ и подготовка (1-2 месяца)

1. **Детальный аудит кода**:
   - Анализ всех модулей с использованием Upgrade Status и Drupal Rector
   - Выявление критических несовместимостей
   - Составление детального списка необходимых изменений

2. **Подготовка тестовой среды**:
   - Настройка Drupal 10 с минимальным набором модулей
   - Настройка системы контроля версий для отслеживания изменений
   - Разработка автоматизированных тестов для критических функций

3. **Разработка архитектуры преобразования**:
   - Проектирование новой структуры модулей
   - Определение стратегии для переноса компонентной системы
   - Планирование миграции пользовательских данных

#### 2.2.2 Этап 2: Миграция базовой системы (2-3 месяца)

1. **Создание основы s45_base для Drupal 10**:
   - Переписывание базовых классов на объектно-ориентированную архитектуру с пространствами имен
   - Трансформация JsonRepo в сервис Drupal
   - Адаптация системы Store к State API Drupal 10
   - Обновление системы логирования

2. **Разработка механизма совместимости**:
   - Создание прослойки совместимости для обеспечения перехода от старого API к новому
   - Реализация фасадов для новых сервисов
   - Создание абстракций для работы с данными

3. **Прототипирование новой компонентной системы**:
   - Создание плагинной системы на основе Plugin API Drupal 10
   - Разработка механизма миграции шаблонов с TPL на Twig
   - Тестирование на прототипах наиболее сложных компонентов

#### 2.2.3 Этап 3: Миграция функциональных модулей (3-4 месяца)

1. **Миграция модуля s45_phuket**:
   - Преобразование компонентов в плагины
   - Переписывание системы запросов с использованием Entity API
   - Адаптация многоязычной системы к Entity Translation

2. **Преобразование административного интерфейса**:
   - Переработка Admin компонентов в соответствии с Drupal 10 Admin UI
   - Обновление форм с использованием Form API Drupal 10
   - Интеграция с системой Drupal 10 Views для отображения данных

3. **Адаптация публичного интерфейса**:
   - Миграция Front компонентов
   - Переработка Property компонентов
   - Обновление системы отображения контента

#### 2.2.4 Этап 4: Миграция данных и интеграция (2-3 месяца)

1. **Создание структуры данных в Drupal 10**:
   - Определение Entity Types для всех сущностей
   - Настройка полей и связей между сущностями
   - Настройка View Modes для разных контекстов отображения

2. **Разработка миграционных скриптов**:
   - Создание миграторов для каждого типа данных
   - Реализация валидации и очистки данных при миграции
   - Разработка системы обратной совместимости для старых URL

3. **Интеграция сторонних библиотек**:
   - Обновление библиотек до версий, совместимых с Drupal 10
   - Интеграция через систему Library API
   - Замена устаревших библиотек на современные аналоги

#### 2.2.5 Этап 5: Тестирование и запуск (1-2 месяца)

1. **Комплексное тестирование**:
   - Функциональное тестирование всех компонентов
   - Тестирование производительности
   - Тестирование безопасности и доступности

2. **Оптимизация и документация**:
   - Оптимизация производительности
   - Детальная документация новой архитектуры
   - Документация API для разработчиков

3. **Стратегия запуска**:
   - Поэтапный запуск функциональности
   - Мониторинг системы после запуска
   - Поддержка и быстрое реагирование на проблемы

### 2.3 Технические рекомендации по миграции

#### 2.3.1 Модули для замены функциональности

| Текущая функциональность | Модули Drupal 10 |
|----------------------------|------------------|
| Компонентная система | Paragraphs, Layout Builder, Block Types |
| JSON хранилище | Entity API, Configuration API, State API |
| Шаблонизация TPL | Twig Templates, Components module |
| Многоязычность | Entity Translation, Content Translation |
| Медиа и файлы | Media API, File API |
| SEO функциональность | Metatag, Pathauto, Redirect |

#### 2.3.2 Рефакторинг кода для PHP 8.1+

1. **Типизация**:
   - Добавить типы параметров и возвращаемых значений
   - Использовать строгую типизацию где возможно
   - Применить типизацию свойств классов

2. **Современные конструкции**:
   - Заменить `array()` на `[]`
   - Использовать null coalescing оператор (`??`)
   - Применить spread operator и короткие конструкции

3. **Объектно-ориентированные подходы**:
   - Использовать dependency injection
   - Применить современные ООП практики
   - Использовать трейты для повторного использования кода

#### 2.3.3 Стратегия переноса компонентной системы

1. **Анализ компонентов**:
   - Классифицировать компоненты по типам (блоки, страницы, формы)
   - Определить зависимости между компонентами
   - Выделить повторяющиеся паттерны

2. **Планирование преобразования**:
   - Для простых блоков UI - использовать Block Plugin API
   - Для сложных компонентов - комбинировать Layout Builder и Paragraphs
   - Для форм и административных страниц - использовать Form API и Admin UI

3. **Преобразование TPL в Twig**:
   - Создать универсальный конвертер шаблонов
   - Перенести логику PHP из TPL в препроцессоры
   - Адаптировать переменные шаблонов к новому формату

## 3. Оценка ресурсов и сроков

### 3.1 Требуемая команда

| Роль | Количество | Ответственность |
|------|------------|-----------------|
| Технический архитектор | 1 | Проектирование новой архитектуры, принятие технических решений |
| Drupal 10 разработчики | 3-4 | Миграция модулей, рефакторинг кода, адаптация компонентов |
| Frontend-разработчики | 2 | Миграция шаблонов и JavaScript кода, адаптация CSS |
| QA-инженеры | 2 | Тестирование и контроль качества |
| DevOps-инженер | 1 | Настройка окружения, автоматизация, развертывание |
| Технический писатель | 1 | Документация кода и архитектуры |

### 3.2 Временные рамки

Общая продолжительность проекта: **8-12 месяцев**

| Этап | Продолжительность | Примечание |
|------|-------------------|------------|
| Анализ и подготовка | 1-2 месяца | Критически важен для успеха проекта |
| Миграция базовой системы | 2-3 месяца | Основа для всех остальных модулей |
| Миграция функциональных модулей | 3-4 месяца | Наиболее трудоемкий этап |
| Миграция данных и интеграция | 2-3 месяца | Требует тщательного планирования |
| Тестирование и запуск | 1-2 месяца | Включает исправление обнаруженных проблем |

### 3.3 Оценка рисков и их минимизация

| Риск | Вероятность | Влияние | Стратегия минимизации |
|------|-------------|---------|----------------------|
| Несовместимость с Drupal 10 API | Высокая | Высокое | Предварительный анализ несовместимостей, создание слоя совместимости |
| Проблемы с производительностью | Средняя | Высокое | Профилирование кода, оптимизация, кэширование |
| Сложности миграции данных | Высокая | Среднее | Тщательное планирование миграции, тестирование на копиях реальных данных |
| Несовместимость сторонних библиотек | Средняя | Среднее | Раннее выявление проблемных библиотек, поиск альтернатив |
| Увеличение сроков | Высокая | Среднее | Регулярный мониторинг прогресса, гибкое планирование |

## 4. Заключение

Миграция модулей s45 на Drupal 10 является сложным, но выполнимым проектом. Ключом к успеху будет тщательное планирование, поэтапный подход и всестороннее тестирование. Предложенная стратегия позволяет минимизировать риски и обеспечить успешную миграцию, сохраняя основную бизнес-логику и расширяя возможности системы за счет использования современных API Drupal 10.

Архитектура модулей s45 имеет хорошую структуру и организацию, что обеспечивает возможность поэтапной миграции. Основным вызовом будет трансформация уникальной компонентной системы в плагиновую архитектуру Drupal 10, но предложенный план предлагает жизнеспособное решение этой проблемы.

Итоговый результат миграции должен обеспечить не только совместимость с Drupal 10, но и создать более поддерживаемую, расширяемую и соответствующую современным стандартам архитектуру, которая будет служить надежной основой для дальнейшего развития системы. 