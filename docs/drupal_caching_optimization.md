# Drupal 7 Caching Optimization Guide for InDreams Phuket

This comprehensive guide provides detailed instructions for optimizing caching in your Drupal 7 real estate website to significantly improve performance and address the issues identified in the SEO audit.

## Table of Contents

1. [Current Performance Issues](#current-performance-issues)
2. [Core Drupal Caching Configuration](#core-drupal-caching-configuration)
3. [Database Caching Optimization](#database-caching-optimization)
4. [Advanced Module Configurations](#advanced-module-configurations)
5. [External Caching Systems](#external-caching-systems)
6. [Server-Level Caching](#server-level-caching)
7. [Image and Asset Optimization](#image-and-asset-optimization)
8. [Monitoring and Maintenance](#monitoring-and-maintenance)
9. [Implementation Plan](#implementation-plan)

## Current Performance Issues

Analysis of your website reveals the following performance bottlenecks:

1. Slow Time to First Byte (TTFB) > 600ms
2. High page load times (average > 5 seconds)
3. Heavy database queries on every page load
4. Unoptimized JavaScript and CSS delivery
5. Large image files slowing down page rendering
6. No efficient page caching strategy implemented
7. No external caching system in place

## Core Drupal Caching Configuration

### Step 1: Enable Basic Drupal Cache Settings

Navigate to `admin/config/development/performance` and configure:

```
Performance Settings:
✓ Cache pages for anonymous users
✓ Cache blocks
✓ Aggregate and compress CSS files
✓ Aggregate JavaScript files
Minimum cache lifetime: 1 hour
Expiration of cached pages: 4 hours
```

### Step 2: Configure Performance Variables via Drush

Alternatively, set these values via Drush for more precise control:

```bash
# Enable page caching
drush vset cache 1

# Enable block caching
drush vset block_cache 1

# Enable CSS aggregation
drush vset preprocess_css 1

# Enable JS aggregation
drush vset preprocess_js 1

# Set minimum cache lifetime (3600 seconds = 1 hour)
drush vset cache_lifetime 3600

# Set page cache maximum age (14400 seconds = 4 hours)
drush vset page_cache_maximum_age 14400
```

### Step 3: Optimize settings.php Cache Configuration

Edit your `sites/default/settings.php` file to include these optimized cache settings:

```php
/**
 * Enhanced database cache settings
 */
$conf['cache_backends'][] = 'sites/all/modules/contrib/authcache/authcache.cache.inc';
$conf['cache_class_cache_page'] = 'DrupalDatabaseCache';

// Disable core block caching in favor of module-based caching
$conf['block_cache'] = TRUE;

// Increase cached item lifetimes
$conf['cache_lifetime'] = 3600;
$conf['page_cache_maximum_age'] = 14400;

// Optimize page caching
$conf['page_compression'] = TRUE;
$conf['cache'] = TRUE;
$conf['preprocess_css'] = TRUE;
$conf['preprocess_js'] = TRUE;

// Cache "content" table
$conf['cache_content'] = TRUE;
```

## Database Caching Optimization

### Step 1: Optimize MySQL Configuration

Edit your MySQL configuration file (typically `/etc/my.cnf` on CentOS 7) to include these optimizations:

```ini
[mysqld]
# Increase query cache
query_cache_size = 128M
query_cache_limit = 4M

# InnoDB settings
innodb_buffer_pool_size = 2G  # Adjust based on available server memory
innodb_log_file_size = 256M
innodb_flush_method = O_DIRECT
innodb_flush_log_at_trx_commit = 2

# Other optimizations
max_connections = 200
table_open_cache = 4000
thread_cache_size = 16
```

After making these changes, restart MySQL:

```bash
systemctl restart mysqld
```

### Step 2: Optimize Database Tables Regularly

Set up a regular maintenance script to optimize database tables:

```bash
#!/bin/bash
# /var/www/scripts/optimize_db.sh

# Connect to MySQL and optimize tables
mysql -u[username] -p[password] -e "
USE indreamsphuket;
OPTIMIZE TABLE accesslog;
OPTIMIZE TABLE cache;
OPTIMIZE TABLE cache_block;
OPTIMIZE TABLE cache_bootstrap;
OPTIMIZE TABLE cache_field;
OPTIMIZE TABLE cache_filter;
OPTIMIZE TABLE cache_form;
OPTIMIZE TABLE cache_image;
OPTIMIZE TABLE cache_menu;
OPTIMIZE TABLE cache_page;
OPTIMIZE TABLE cache_path;
OPTIMIZE TABLE cache_update;
OPTIMIZE TABLE cache_views;
OPTIMIZE TABLE cache_views_data;
OPTIMIZE TABLE watchdog;
"

# Clear Drupal cache
cd /var/www/www-root/data/www/indreamsphuket.com
drush cc all
```

Add this script to crontab to run weekly:

```bash
0 3 * * 0 /var/www/scripts/optimize_db.sh > /var/log/db_optimize.log 2>&1
```

### Step 3: Implement Query Caching in Custom Modules

Update your custom modules (especially `s45_phuket`) to implement query caching:

```php
/**
 * Example function showing how to implement query caching in custom modules.
 */
function s45_phuket_get_properties($filters = array()) {
  // Create a cache ID based on the function arguments
  $cid = 'properties:' . md5(serialize($filters));
  
  // Check if we have a cached result
  if ($cache = cache_get($cid, 'cache')) {
    return $cache->data;
  }
  
  // If no cache, execute the query
  $query = db_select('node', 'n');
  $query->join('field_data_field_property_type', 'pt', 'n.nid = pt.entity_id');
  $query->fields('n', array('nid', 'title', 'created'));
  $query->condition('n.type', 'property');
  $query->condition('n.status', 1);
  
  // Apply filters
  foreach ($filters as $field => $value) {
    if ($field == 'property_type') {
      $query->condition('pt.field_property_type_value', $value);
    }
    // Add more filter conditions as needed
  }
  
  $result = $query->execute()->fetchAll();
  
  // Cache the result for 1 hour
  cache_set($cid, $result, 'cache', time() + 3600);
  
  return $result;
}
```

## Advanced Module Configurations

### Step 1: Configure Advanced CSS/JS Aggregation (AdvAgg)

The AdvAgg module is already installed. Configure it for optimal performance:

```bash
# Enable core AdvAgg modules
drush en advagg advagg_mod advagg_css_compress advagg_js_compress -y

# Set optimal configuration
drush vset advagg_enabled 1
drush vset advagg_core_groups 1
drush vset advagg_use_httprl 1
drush vset advagg_mod_js_async 1
drush vset advagg_mod_js_defer 1
drush vset advagg_mod_css_translate 1
drush vset advagg_mod_js_footer 1
drush vset advagg_js_compress_packer 1
drush vset advagg_css_compress_with_comments 1
```

#### Manual Configuration (Alternative)

You can also configure AdvAgg through the UI at `admin/config/development/performance/advagg`:

1. Enable "Use AdvAgg for all CSS/JS"
2. Enable "Create .gz files" under AdvAgg Settings
3. Under "Modifications" settings:
   - Enable "Move JS to the footer"
   - Enable "Defer JavaScript Execution"
   - Enable "Use async attribute on all scripts"
4. Under compression settings:
   - Enable "Compress JavaScript with JSMin+"
   - Enable "Compress CSS with YUI"

### Step 2: Configure Boost Module

The Boost module is already installed. Configure it for optimal performance:

```bash
# Enable Boost
drush en boost -y

# Configure Boost
drush vset boost_enabled 1
drush vset boost_lifetime_max 86400
drush vset boost_cacheability_option 5
drush vset boost_normal_lifetime 3600
```

Add these settings to your `.htaccess` file in the document root:

```apache
# Boost configuration
<IfModule mod_rewrite.c>
  # Boost rules
  RewriteCond %{REQUEST_METHOD} ^GET$
  RewriteCond %{REQUEST_URI} ^/index.php$
  RewriteCond %{QUERY_STRING} ^q=system/files/.*$
  RewriteRule .* %{REQUEST_URI} [L,QSA]

  # Caching for anonymous users
  RewriteCond %{REQUEST_METHOD} ^GET$
  RewriteCond %{HTTP_COOKIE} !SESS [NC]
  RewriteCond %{HTTP:Cache-Control} !no-cache
  RewriteCond %{HTTP:Pragma} !no-cache
  RewriteCond %{DOCUMENT_ROOT}/cache/%{HTTP_HOST}/%{REQUEST_URI}_%{QUERY_STRING}.html -s
  RewriteRule .* cache/%{HTTP_HOST}/%{REQUEST_URI}_%{QUERY_STRING}.html [L,T=text/html]
</IfModule>
```

### Step 3: Configure Views Caching

For all your views, especially property listing views, apply these caching settings:

1. Edit each view and click on "Advanced" in the right column
2. Under "Caching", click to configure
3. Set the following values:
   - Query results caching: Cache query results for 1 hour
   - Rendered output caching: Cache rendered output for 1 hour

Via Drush, you can set default caching for views:

```bash
# Set default views caching
drush vset views_exposed_filter_any_required 1
drush vset views_skip_cache 0
drush vset views_show_additional_queries 0
```

### Step 4: Configure Entity Cache

Install and configure Entity Cache to improve entity loading performance:

```bash
# Download and enable EntityCache
drush dl entitycache
drush en entitycache -y
```

Add these lines to `settings.php`:

```php
// Enable entity cache
$conf['entitycache_enabled'] = TRUE;

// Configure entity cache
$conf['entitycache_custom'] = array(
  'node' => TRUE,
  'taxonomy_term' => TRUE,
  'file' => TRUE,
  'user' => TRUE,
);
```

## External Caching Systems

### Step 1: Install and Configure Memcached

Install Memcached on your CentOS 7 server:

```bash
# Install Memcached
yum install memcached -y

# Configure Memcached to use 128MB of RAM
sed -i 's/CACHESIZE="64"/CACHESIZE="128"/g' /etc/sysconfig/memcached

# Start Memcached and enable at boot
systemctl start memcached
systemctl enable memcached
```

Install Drupal Memcache module:

```bash
# Download and install the module
drush dl memcache
drush en memcache -y
```

Add these settings to `settings.php`:

```php
// Memcache configuration
$conf['cache_backends'][] = 'sites/all/modules/memcache/memcache.inc';
$conf['cache_default_class'] = 'MemCacheDrupal';
$conf['memcache_servers'] = array('localhost:11211' => 'default');
$conf['memcache_bins'] = array(
  'cache' => 'default',
  'cache_bootstrap' => 'default',
  'cache_block' => 'default',
  'cache_filter' => 'default',
  'cache_page' => 'default',
  'cache_field' => 'default',
  'cache_menu' => 'default',
  'cache_path' => 'default',
  'cache_form' => 'default',
);

// Don't store database cache in memcache
$conf['cache_class_cache_form'] = 'DrupalDatabaseCache';
```

### Step 2: Configure Varnish Cache (Optional)

The varnish module is already installed. Follow these steps to implement Varnish on your server:

```bash
# Install Varnish
yum install varnish -y

# Configure Varnish to listen on port 80 (and move Apache to 8080)
```

Basic Varnish configuration for `/etc/varnish/default.vcl`:

```vcl
vcl 4.0;

# Define backend server (your Apache)
backend default {
    .host = "127.0.0.1";
    .port = "8080";
    .connect_timeout = 600s;
    .first_byte_timeout = 600s;
    .between_bytes_timeout = 600s;
}

# Remove cookies for static files
sub vcl_recv {
    # Remove cookies for stylesheets, scripts and images
    if (req.url ~ "(?i)\.(css|js|jpg|jpeg|gif|png|ico)(\?[a-z0-9]+)?$") {
        unset req.http.Cookie;
        return(hash);
    }
    
    # Remove all cookies for static files
    if (req.url ~ "^/sites/default/files/") {
        unset req.http.Cookie;
        return(hash);
    }
    
    # Don't cache these paths
    if (req.url ~ "^/status\.php$" ||
        req.url ~ "^/update\.php$" ||
        req.url ~ "^/admin$" ||
        req.url ~ "^/admin/.*$" ||
        req.url ~ "^/user$" ||
        req.url ~ "^/user/.*$" ||
        req.url ~ "^/flag/.*$" ||
        req.url ~ "^/\?q=flag/.*$" ||
        req.url ~ "^/s45/.*$") {
        return(pass);
    }
    
    # Always cache these URLs
    if (req.url ~ "^/sites/default/files/.*\.js$" ||
        req.url ~ "^/sites/default/files/.*\.css$" ||
        req.url ~ "^/sites/default/files/.*\.jpg$" ||
        req.url ~ "^/sites/default/files/.*\.png$" ||
        req.url ~ "^/sites/default/files/.*\.gif$" ||
        req.url ~ "^/sites/default/files/.*\.ico$") {
        unset req.http.Cookie;
        return(hash);
    }
}

sub vcl_backend_response {
    # Cache static files
    if (bereq.url ~ "(?i)\.(css|js|jpg|jpeg|gif|png|ico)(\?[a-z0-9]+)?$") {
        unset beresp.http.set-cookie;
        set beresp.ttl = 24h;
    }
    
    # Cache HTML for 1 hour
    if (bereq.url ~ "\.html$" || beresp.http.Content-Type ~ "text/html") {
        unset beresp.http.set-cookie;
        set beresp.ttl = 1h;
    }
}
```

Enable and configure the Drupal Varnish module:

```bash
# Enable Varnish module
drush en varnish -y

# Configure Varnish
drush vset varnish_version 4
drush vset varnish_control_terminal 127.0.0.1:6082
drush vset varnish_control_key "secret"
drush vset varnish_cache_clear 1
```

## Server-Level Caching

### Step 1: Configure Apache with mod_expires

Add the following to your Apache configuration (typically in `/etc/httpd/conf/httpd.conf` or a separate file in `/etc/httpd/conf.d/`):

```apache
<IfModule mod_expires.c>
  ExpiresActive On
  
  # Set default expiry times
  ExpiresDefault "access plus 1 month"
  
  # Set expiry by type
  ExpiresByType text/css "access plus 1 year"
  ExpiresByType text/javascript "access plus 1 year"
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType application/x-javascript "access plus 1 year"
  ExpiresByType image/gif "access plus 1 year"
  ExpiresByType image/jpeg "access plus 1 year"
  ExpiresByType image/png "access plus 1 year"
  ExpiresByType image/webp "access plus 1 year"
  ExpiresByType image/svg+xml "access plus 1 year"
  ExpiresByType image/x-icon "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/x-font-woff "access plus 1 year"
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"
  
  # Don't cache dynamic content
  ExpiresByType text/html "access plus 0 seconds"
  ExpiresByType application/xml "access plus 0 seconds"
  ExpiresByType application/json "access plus 0 seconds"
  
  # Don't set cookies on static resources
  <FilesMatch "\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2)$">
    Header unset Set-Cookie
  </FilesMatch>
</IfModule>
```

### Step 2: Configure mod_deflate for Compression

Add this configuration to Apache:

```apache
<IfModule mod_deflate.c>
  # Enable compression
  AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css
  AddOutputFilterByType DEFLATE application/x-javascript application/javascript text/javascript
  AddOutputFilterByType DEFLATE application/json
  AddOutputFilterByType DEFLATE application/xml application/xhtml+xml
  AddOutputFilterByType DEFLATE image/svg+xml
  
  # Don't compress images and PDFs (already compressed)
  SetEnvIfNoCase Request_URI \.(?:gif|jpe?g|png|pdf)$ no-gzip dont-vary
  
  # Set compression level
  DeflateCompressionLevel 9
</IfModule>
```

### Step 3: Configure PHP-FPM (if applicable)

If you're using PHP-FPM, optimize its configuration in `/etc/php-fpm.d/www.conf`:

```ini
; Set process manager to dynamic
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

; Enable opcode caching
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
```

## Image and Asset Optimization

### Step 1: Configure Image Optimization Modules

Configure the installed image optimization modules (imageapi_optimize, kraken):

```bash
# Enable image optimization modules
drush en imageapi_optimize kraken -y

# Configure Kraken API settings (you need an account)
drush vset kraken_api_key "your-api-key"
drush vset kraken_api_secret "your-api-secret"
```

In the UI, navigate to `admin/config/media/image-toolkit` and ensure the optimize toolkit is selected.

### Step 2: Configure Lazy Loading

Enable and configure the lazyloader module:

```bash
# Configure lazy loading
drush vset lazyloader_enabled 1
drush vset lazyloader_distance 0
drush vset lazyloader_placeholder "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
```

Add this JavaScript to your theme to enhance lazy loading of images:

```javascript
// Add to your theme's scripts.js file
(function ($) {
  Drupal.behaviors.lazyImages = {
    attach: function (context, settings) {
      // Initialize lazy loading for images
      $("img.lazy").once('lazy').lazyload({
        effect: "fadeIn",
        threshold: 200
      });
    }
  };
})(jQuery);
```

## Monitoring and Maintenance

### Step 1: Set Up Regular Cache Clearing

Create a script for regular cache maintenance:

```bash
#!/bin/bash
# /var/www/scripts/cache_maintenance.sh

# Change to Drupal root
cd /var/www/www-root/data/www/indreamsphuket.com

# Clear expired caches but maintain operational caches
drush cache-clear all

# Rebuild Registry
drush registry-rebuild

# Clear boost cache older than 1 day
find cache/ -type f -mtime +1 -delete

# Log maintenance completion
echo "Cache maintenance completed at $(date)" >> /var/log/drupal/cache_maintenance.log
```

Add to crontab to run daily:

```bash
0 2 * * * /var/www/scripts/cache_maintenance.sh
```

### Step 2: Set Up Performance Monitoring

Install and configure the performance logging module:

```bash
# Download and enable performance monitoring
drush dl performance
drush en performance -y

# Configure performance logging
drush vset performance_detail 1
drush vset performance_nodrush 1
drush vset performance_threshold_accesses 2
```

Create a script to analyze performance data:

```bash
#!/bin/bash
# /var/www/scripts/analyze_performance.sh

# Path to Drupal installation
DRUPAL_PATH="/var/www/www-root/data/www/indreamsphuket.com"

# Database credentials
DB_USER="your_db_user"
DB_PASS="your_db_password"
DB_NAME="your_db_name"

# Run analysis query
mysql -u${DB_USER} -p${DB_PASS} ${DB_NAME} -e "
  SELECT path, AVG(bytes) as avg_bytes, 
  AVG(ms) as avg_ms, 
  COUNT(*) as view_count
  FROM performance_summary
  WHERE timestamp > (UNIX_TIMESTAMP() - 86400)
  GROUP BY path
  ORDER BY avg_ms DESC
  LIMIT 20
" > ${DRUPAL_PATH}/performance_report_daily.txt

# Email report to administrator
mail -s "Daily Performance Report - InDreams Phuket" <EMAIL> < ${DRUPAL_PATH}/performance_report_daily.txt
```

Add to crontab:

```bash
0 7 * * * /var/www/scripts/analyze_performance.sh
```

## Implementation Plan

### Phase 1: Core Caching (Day 1)

1. Enable and configure core Drupal caching
2. Implement database optimization
3. Configure AdvAgg module

### Phase 2: Advanced Caching (Days 2-3)

1. Configure Boost module
2. Set up Views caching
3. Install and configure EntityCache
4. Implement Memcached

### Phase 3: Server Optimization (Day 4)

1. Configure Apache caching headers
2. Set up compression
3. Configure PHP optimizations

### Phase 4: Asset Optimization (Day 5)

1. Configure image optimization modules
2. Implement lazy loading
3. Set up monitoring and maintenance scripts

### Verification and Testing

After implementing each phase, verify improvements using these tools:

1. PageSpeed Insights: https://pagespeed.web.dev/
2. GTmetrix: https://gtmetrix.com/
3. WebPageTest: https://www.webpagetest.org/
4. Lighthouse audit in Chrome DevTools

## Conclusion

This comprehensive caching strategy addresses the performance issues identified in the SEO audit. By implementing these recommendations, you should see significant improvements in page load times, server response times, and overall site performance.

Remember to implement changes incrementally and test after each major change to ensure the site remains stable. Monitor the performance metrics regularly to identify any new bottlenecks that may emerge as traffic patterns change. 