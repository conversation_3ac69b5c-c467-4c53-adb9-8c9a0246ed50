# Sitemap Best Practices for InDreams Phuket

This guide provides a comprehensive overview of sitemap best practices, with specific recommendations for our website. Following these guidelines will help ensure optimal indexing of our content by search engines.

## What is a Sitemap?

A sitemap is an XML file that provides search engines with a structured list of all the pages on your website that you want them to crawl and index. It helps search engines understand your website structure and discover content they might otherwise miss.

## Google's Sitemap Requirements

Google's requirements for sitemaps are:

1. **Format**: Valid XML formatted according to the sitemap protocol
2. **Size**: Maximum 50MB (uncompressed) and 50,000 URLs per sitemap file
3. **Encoding**: UTF-8 encoding
4. **Index File**: For multiple sitemaps, use a sitemap index file
5. **Updates**: Sitemaps should be updated when content changes

## Our Sitemap Implementation

Our sitemap implementation follows these best practices:

1. **Multiple Language Support**: Separate sitemaps for English and Russian content 
2. **Content Type Segmentation**: Separate sitemaps for properties, projects, articles, etc.
3. **Hreflang Tags**: Cross-referencing between language versions
4. **Image Support**: Enhanced property listings with image information
5. **Regular Updates**: Automated daily updates using cron

## Sitemap Structure

### Sitemap Index

```xml
<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>https://indreamsphuket.com/sitemap-en-properties.xml</loc>
    <lastmod>2025-03-23T10:00:00Z</lastmod>
  </sitemap>
  <!-- Additional sitemap references -->
</sitemapindex>
```

### Individual Sitemaps

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  <url>
    <loc>https://indreamsphuket.com/property/luxury-villa-patong</loc>
    <lastmod>2025-03-23T10:00:00Z</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
    <xhtml:link rel="alternate" hreflang="ru" href="https://indreamsphuket.ru/property/luxury-villa-patong"/>
    <xhtml:link rel="alternate" hreflang="en" href="https://indreamsphuket.com/property/luxury-villa-patong"/>
    <xhtml:link rel="alternate" hreflang="x-default" href="https://indreamsphuket.com/property/luxury-villa-patong"/>
    <image:image>
      <image:loc>https://indreamsphuket.com/images/properties/villa1.jpg</image:loc>
      <image:title>Luxury Villa in Patong</image:title>
      <image:caption>Beautiful 3-bedroom villa with sea view</image:caption>
    </image:image>
  </url>
  <!-- Additional URLs -->
</urlset>
```

## Important Elements in Sitemaps

1. **`<loc>`**: The URL of the page (required)
2. **`<lastmod>`**: Last modification date (recommended)
3. **`<changefreq>`**: How frequently the page changes (optional)
4. **`<priority>`**: Relative importance of the page (optional)
5. **`<xhtml:link>`**: Alternative language versions (for multilingual sites)
6. **`<image:image>`**: Image metadata (for image-rich content)

## Best Practices for Our Team

### 1. URL Selection and Structure

- Include **only canonical URLs** in sitemaps
- Exclude URLs that are blocked by robots.txt
- Include all important pages that should be indexed
- Ensure all URLs are accessible (HTTP 200 status)

### 2. URL Properties

- **`<lastmod>`**: Always include and keep accurate
- **`<changefreq>`**:
  - `daily`: News articles, frequently updated property listings
  - `weekly`: Regular property listings, projects
  - `monthly`: Static pages like About Us, Privacy Policy
- **`<priority>`**:
  - `1.0`: Homepage
  - `0.8`: Primary content (featured properties)
  - `0.6`: Secondary content (regular listings)
  - `0.4`: Support pages

### 3. Multilingual Configuration

- Always include `hreflang` attributes for all language variants
- Include `x-default` for the default language version
- Ensure language versions point to the correct domain (indreamsphuket.com, indreamsphuket.ru)

### 4. Image Sitemaps

- Include images for all property listings
- Provide descriptive titles and captions
- Ensure image URLs are accessible

### 5. Sitemap Maintenance

- Update sitemaps when significant content changes
- Use the automated update script (update_sitemaps.sh)
- Validate sitemaps after major website changes
- Monitor Google Search Console for sitemap errors

## Submitting Sitemaps to Search Engines

Our automated script submits sitemaps to search engines, but you can also submit them manually:

1. **Google Search Console**: https://search.google.com/search-console
2. **Bing Webmaster Tools**: https://www.bing.com/webmasters/
3. **Yandex Webmaster**: https://webmaster.yandex.com/

## Troubleshooting

If you encounter issues with sitemaps:

1. Validate the XML format with our sitemap_validator.php script
2. Check for server issues preventing access to sitemap files
3. Verify that Cloudflare is configured correctly for sitemap access
4. Check SSL certificate validity and expiration
5. Review Google Search Console for specific errors

## Reference Scripts

We have several scripts to manage sitemaps:

- **update_sitemap_en.php**: Generates English sitemaps
- **sitemap_validator.php**: Validates sitemap structure
- **sitemap_enhanced_image.php**: Adds image information to property sitemaps
- **custom_sitemap_ping.php**: Notifies search engines about updates
- **update_sitemaps.sh**: Master script that runs all of the above

## Resources

- [Google's Sitemap Documentation](https://developers.google.com/search/docs/advanced/sitemaps/overview)
- [Sitemap Protocol](https://www.sitemaps.org/protocol.html)
- [Hreflang Tags with Sitemaps](https://developers.google.com/search/docs/advanced/crawling/localized-versions)
- [Image Sitemaps](https://developers.google.com/search/docs/advanced/sitemaps/image-sitemaps) 