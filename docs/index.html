<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Интерактивный договор - InDreams Phuket</title>
    <link rel="icon" href="/favicon.ico" type="image/x-icon">
    <meta name="description" content="Интерактивная форма для создания договора купли-продажи недвижимости в Пхукете">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="email"],
        input[type="tel"],
        select,
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .form-row {
            display: flex;
            gap: 20px;
        }
        .form-col {
            flex: 1;
        }
        .btn {
            background-color: #0056b3;
            color: white;
            border: none;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 16px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #003d7a;
        }
        .agreement-preview {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        h1 {
            color: #0056b3;
            margin-bottom: 30px;
        }
        .highlight {
            background-color: #fffbcc;
            padding: 2px;
        }
        .header-logo {
            text-align: center;
            margin-bottom: 20px;
        }
        .header-logo img {
            max-width: 180px;
        }
        .required-field::after {
            content: " *";
            color: red;
        }
        
        /* Адаптивность для мобильных устройств */
        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 10px;
            }
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header-logo">
            <a href="https://indreamsphuket.com/" target="_blank">
                <img src="indreams-logo.png" alt="InDreams Phuket Logo">
            </a>
        </div>
        
        <h1>Интерактивная форма договора</h1>
        
        <form id="contractForm">
            <div class="form-group">
                <h2>Информация о покупателе</h2>
                <div class="form-row">
                    <div class="form-col">
                        <label for="buyerName" class="required-field">ФИО покупателя</label>
                        <input type="text" id="buyerName" name="buyerName" required>
                    </div>
                    <div class="form-col">
                        <label for="buyerPassport" class="required-field">Паспорт</label>
                        <input type="text" id="buyerPassport" name="buyerPassport" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="form-row">
                    <div class="form-col">
                        <label for="buyerEmail" class="required-field">Email</label>
                        <input type="email" id="buyerEmail" name="buyerEmail" required>
                    </div>
                    <div class="form-col">
                        <label for="buyerPhone" class="required-field">Телефон</label>
                        <input type="tel" id="buyerPhone" name="buyerPhone" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <h2>Информация о недвижимости</h2>
                <div class="form-row">
                    <div class="form-col">
                        <label for="propertyType" class="required-field">Тип недвижимости</label>
                        <select id="propertyType" name="propertyType" required>
                            <option value="">Выберите тип</option>
                            <option value="apartment">Апартаменты</option>
                            <option value="villa">Вилла</option>
                            <option value="house">Дом</option>
                            <option value="land">Земельный участок</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label for="propertyAddress" class="required-field">Адрес объекта</label>
                        <input type="text" id="propertyAddress" name="propertyAddress" required>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <div class="form-row">
                    <div class="form-col">
                        <label for="propertyPrice" class="required-field">Цена (USD)</label>
                        <input type="text" id="propertyPrice" name="propertyPrice" required>
                    </div>
                    <div class="form-col">
                        <label for="paymentTerms" class="required-field">Условия оплаты</label>
                        <select id="paymentTerms" name="paymentTerms" required>
                            <option value="">Выберите условия</option>
                            <option value="full">Полная оплата</option>
                            <option value="installment">Рассрочка</option>
                            <option value="mortgage">Ипотека</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <h2>Дополнительные условия</h2>
                <label for="additionalTerms">Укажите дополнительные условия</label>
                <textarea id="additionalTerms" name="additionalTerms" rows="4"></textarea>
            </div>

            <div class="form-group">
                <button type="button" class="btn" id="previewBtn">Предпросмотр договора</button>
                <button type="button" class="btn" id="generatePdfBtn">Скачать PDF</button>
            </div>
        </form>

        <div class="agreement-preview" id="agreementPreview" style="display: none;">
            <h2>Предварительный просмотр договора</h2>
            <div id="agreementContent"></div>
        </div>
    </div>

    <script src="contract.js"></script>
</body>
</html> 