# Отчет по безопасности сервера indreamsphuket.com

## 1. Общая информация о системе

### 1.1 Операционная система
- **ОС**: CentOS Linux 7 (Core)
- **Ядро**: 3.10.0-1160.119.1.el7.x86_64
- **Архитектура**: x86_64

### 1.2 Веб-сервер и окружение
- **Веб-сервер**: Apache 2.4.6 (CentOS)
- **PHP**: 5.4.16 (<PERSON><PERSON>, устаревшая версия)
- **Drupal**: 7.67 (устаревшая версия)

## 2. Выявленные проблемы безопасности

### 2.1 Критические проблемы
1. **Устаревшая версия PHP (5.4.16)**
   - PHP 5.4 достиг конца жизненного цикла (EOL) в 2015 году
   - Не получает обновлений безопасности более 9 лет
   - Содержит множество известных уязвимостей, которые не будут исправлены

2. **Устаревшая версия Drupal (7.67)**
   - Текущая версия Drupal 7 - 7.99
   - Drupal 7 получит расширенную поддержку только до 5 января 2025 года

3. **Небезопасные права доступа к файлам**
   - Обнаружены PHP-файлы с правами 777 (rwxrwxrwx) в директории `/dumper/`
   - Файл `settings.php` имеет избыточные права доступа (755)

### 2.2 Проблемы средней критичности
1. **SELinux в режиме permissive**
   - SELinux установлен в режиме permissive, а не enforcing
   - В этом режиме нарушения политики безопасности регистрируются, но не блокируются

2. **Устаревшая версия Apache (2.4.6)**
   - Текущая стабильная версия Apache - 2.4.59
   - Версия 2.4.6 выпущена в 2013 году

### 2.3 Проблемы низкой критичности
1. **Отсутствие настроенного cron для Drupal**
   - Не обнаружены записи для регулярного запуска cron.php

## 3. Положительные аспекты безопасности
1. **Наличие Fail2ban**
   - Установлен Fail2ban для защиты от брутфорс-атак

2. **Правильные настройки PHP**
   - Разумные значения memory_limit (2G)
   - Разумные значения max_execution_time (300)

3. **Файрвол настроен**
   - Настроены правила iptables
   - Присутствуют правила для Fail2ban (f2b-sshd)

## 4. Рекомендации по улучшению безопасности

### 4.1 Критические обновления (приоритет высокий)
1. **Обновить PHP до актуальной версии**
   - Минимум до PHP 7.4 (поддерживается до 2023)
   - Рекомендуется PHP 8.2 или 8.3

2. **Обновить Drupal до актуальной версии**
   - Обновить до Drupal 7.99 (если не готовы к переходу на Drupal 9/10)
   - Начать планирование миграции на Drupal 10

3. **Исправить небезопасные права доступа к файлам**
   - Изменить права на PHP-файлы в директории `/dumper/` с 777 на 644
   - Изменить права на `settings.php` с 755 на 440 или 400

### 4.2 Улучшения средней критичности
1. **Включить режим enforcing для SELinux**
   - Перевести SELinux из режима permissive в режим enforcing
   - Проверить и исправить возможные ошибки в журналах SELinux

2. **Обновить Apache до актуальной версии**
   - Обновить Apache до версии 2.4.59 или новее

3. **Настроить файл robots.txt**
   - Создать или обновить файл robots.txt
   - Запретить индексацию административных разделов

### 4.3 Общие улучшения
1. **Настроить автоматическое выполнение cron**
   - Добавить задачу в crontab для регулярного запуска Drupal cron
   - Рекомендуемая настройка: `*/15 * * * * www-root wget -O /dev/null -q https://indreamsphuket.com/cron.php?cron_key=SECRET_KEY`

2. **Настроить HTTP Security Headers**
   - Добавить заголовки безопасности в конфигурацию Apache:
     - Content-Security-Policy
     - X-Content-Type-Options: nosniff
     - X-Frame-Options: SAMEORIGIN
     - Strict-Transport-Security: max-age=31536000; includeSubDomains

3. **Включить HTTPS**
   - Убедиться, что все соединения используют HTTPS
   - Настроить автоматический редирект с HTTP на HTTPS

4. **Регулярное резервное копирование**
   - Настроить автоматическое резервное копирование файлов и базы данных
   - Хранить резервные копии на внешнем хранилище

## 5. Долгосрочный план по улучшению безопасности

### 5.1 Планирование миграции
1. **Подготовка к миграции на Drupal 10**
   - Drupal 7 приближается к концу жизненного цикла (5 января 2025)
   - Начать инвентаризацию модулей и их совместимости с Drupal 10
   - Разработать план миграции с минимальными простоями

### 5.2 Обновление инфраструктуры
1. **Переход на более современную ОС**
   - CentOS 7 получит поддержку до 30 июня 2024 года
   - Рассмотреть переход на Rocky Linux 9, Alma Linux или Ubuntu LTS

2. **Внедрение контейнеризации**
   - Рассмотреть возможность использования Docker для изоляции компонентов
   - Упростить обновление компонентов в будущем

### 5.3 Внедрение мониторинга и аудита
1. **Настроить мониторинг безопасности**
   - Внедрить систему обнаружения вторжений (IDS)
   - Настроить оповещения о подозрительной активности

2. **Регулярный аудит безопасности**
   - Запланировать ежеквартальный аудит безопасности
   - Проверять журналы на наличие подозрительной активности

## 6. Заключение

Текущая инфраструктура сервера indreamsphuket.com имеет серьезные проблемы безопасности, связанные в первую очередь с устаревшим программным обеспечением. PHP 5.4 и Drupal 7.67 давно не получают обновлений безопасности и содержат известные уязвимости.

Рекомендуется как можно скорее выполнить критические обновления, особенно обновление PHP до современной версии и Drupal до последней доступной версии 7.x. Параллельно следует начать планирование миграции на Drupal 10, так как поддержка Drupal 7 заканчивается в начале 2025 года.

Долгосрочная стратегия должна включать полное обновление инфраструктуры, включая операционную систему, веб-сервер и другие компоненты, а также внедрение современных практик безопасности, таких как контейнеризация, мониторинг и регулярный аудит. 