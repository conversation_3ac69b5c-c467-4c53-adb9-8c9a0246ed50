# Архитектура Telegram-бота для InDreams Phuket

## Общее описание

Telegram-бот для агентства недвижимости InDreams Phuket предназначен для автоматизации взаимодействия с клиентами, предоставления информации о доступных объектах недвижимости и помощи в создании контента для социальных сетей. Особое внимание уделяется функциональности генерации мокапов с фотографиями объектов и брендированной рамкой для публикаций в Instagram и других социальных сетях.

## Архитектура системы

### Компоненты системы

1. **Telegram Bot API Client**
   - Обработка входящих сообщений и команд
   - Отправка ответов пользователям
   - Управление клавиатурами и интерфейсом бота

2. **Core Application**
   - Бизнес-логика бота
   - Управление состоянием пользовательских сессий
   - Маршрутизация команд к соответствующим обработчикам

3. **Property Service**
   - Интеграция с API сайта для получения данных об объектах недвижимости
   - Кэширование данных для ускорения работы
   - Фильтрация и поиск объектов по параметрам

4. **Content Generation Service**
   - Генерация текстов для постов с использованием AI
   - Создание мокапов с фотографиями объектов и брендированной рамкой
   - Подготовка хештегов и ключевых фраз

5. **Image Processing Service**
   - Обработка изображений объектов недвижимости
   - Создание мокапов с брендированной рамкой
   - Оптимизация изображений для социальных сетей

6. **Database Service**
   - Хранение данных о пользователях и их предпочтениях
   - Логирование действий пользователей
   - Хранение статистики использования бота

7. **Admin Panel**
   - Управление настройками бота
   - Просмотр статистики использования
   - Управление доступом пользователей

### Схема взаимодействия компонентов

```
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
| Telegram Bot   |<---->|  Core          |<---->| Property       |
| API Client     |      |  Application   |      | Service        |
|                |      |                |      |                |
+----------------+      +----------------+      +----------------+
                              ^   ^
                              |   |
                              v   v
+----------------+      +----------------+      +----------------+
|                |      |                |      |                |
| Content        |<---->| Image          |<---->| Database       |
| Generation     |      | Processing     |      | Service        |
| Service        |      | Service        |      |                |
+----------------+      +----------------+      +----------------+
                              ^
                              |
                              v
                        +----------------+
                        |                |
                        | Admin          |
                        | Panel          |
                        |                |
                        +----------------+
```

## Технический стек

### Backend

1. **Основной язык программирования**
   - PHP 8.0+ (для интеграции с существующим сайтом на Drupal)
   - Node.js 16+ (для обработки изображений и генерации мокапов)

2. **Фреймворки и библиотеки**
   - PHP Telegram Bot SDK (https://github.com/php-telegram-bot/core)
   - Laravel/Lumen (для API и административной панели)
   - Express.js (для сервиса обработки изображений)

3. **Базы данных**
   - Redis (для кэширования и хранения сессий)
   - SQLite (для локального хранения данных бота)
   - MySQL (для интеграции с существующей базой данных Drupal)

### API и интеграции

1. **Telegram Bot API**
   - Webhook для получения обновлений
   - Методы для отправки сообщений, фотографий и медиа-групп
   - Inline-клавиатуры для интерактивного взаимодействия

2. **DeepSeek API**
   - Генерация описаний объектов недвижимости
   - Создание текстов для постов в социальных сетях
   - Генерация хештегов и ключевых фраз

3. **Drupal API**
   - Получение данных об объектах недвижимости
   - Фильтрация и поиск объектов
   - Получение медиа-контента (фотографии, планы, видео)

4. **Instagram Graph API** (опционально)
   - Публикация постов напрямую в Instagram
   - Получение статистики по публикациям
   - Управление аккаунтом

### Обработка изображений

1. **Библиотеки**
   - Sharp (Node.js библиотека для обработки изображений)
   - Canvas (для создания мокапов и наложения брендированной рамки)
   - ImageMagick (для сложных операций с изображениями)

2. **Функциональность**
   - Ресайз и кроп изображений
   - Наложение брендированной рамки
   - Добавление текста и информации об объекте
   - Оптимизация для разных социальных сетей

### Хостинг и инфраструктура

1. **Сервер**
   - Выделенный VPS с Ubuntu 20.04+
   - Минимум 2 ГБ оперативной памяти
   - 20 ГБ дискового пространства

2. **Веб-сервер**
   - Nginx в качестве прокси-сервера
   - PHP-FPM для обработки PHP-запросов
   - PM2 для управления Node.js процессами

3. **Безопасность**
   - SSL-сертификаты для защищенного соединения
   - Firewall для ограничения доступа
   - Регулярное резервное копирование данных

## Модули и функциональность

### 1. Модуль поиска и просмотра объектов недвижимости

#### Функциональность
- Поиск объектов по различным параметрам (тип, цена, расположение, количество спален)
- Просмотр детальной информации об объектах
- Просмотр фотографий и планов объектов
- Получение геолокации объектов

#### Компоненты
- Интерфейс поиска с использованием inline-клавиатур
- Обработчики команд для различных параметров поиска
- Генератор карточек объектов недвижимости
- Интеграция с API сайта для получения данных

#### Примеры команд
- `/search` - Начать поиск объектов
- `/property <ID>` - Показать информацию об объекте по ID
- `/filter` - Настроить параметры фильтрации
- `/location` - Поиск объектов по местоположению

### 2. Модуль генерации контента для социальных сетей

#### Функциональность
- Создание готовых постов для Instagram, Facebook и других платформ
- Генерация описаний объектов на разных языках
- Создание мокапов с фотографиями объектов и брендированной рамкой
- Подготовка хештегов и ключевых фраз для постов

#### Компоненты
- Интеграция с DeepSeek API для генерации текстов
- Сервис обработки изображений для создания мокапов
- Библиотека шаблонов для разных социальных сетей
- Генератор хештегов на основе характеристик объекта

#### Примеры команд
- `/create_post <ID>` - Создать пост для объекта по ID
- `/mockup <ID> <template>` - Создать мокап с выбранным шаблоном
- `/generate_text <ID> <lang>` - Сгенерировать текст на выбранном языке
- `/hashtags <ID>` - Сгенерировать хештеги для объекта

### 3. Модуль создания мокапов с брендированной рамкой

#### Функциональность
- Выбор шаблона мокапа (Instagram, Facebook, Twitter и др.)
- Выбор фотографий объекта для мокапа
- Настройка информации, отображаемой в рамке (цена, количество спален, площадь и т.д.)
- Экспорт готового мокапа

#### Компоненты
- Библиотека шаблонов мокапов
- Сервис обработки изображений
- Генератор брендированной рамки
- Система настройки параметров мокапа

#### Технические аспекты
- Использование Canvas API для создания мокапов
- Динамическое размещение текста и информации
- Оптимизация изображений для разных социальных сетей
- Поддержка различных форматов и разрешений

#### Примеры шаблонов мокапов
1. **Instagram Post**
   - Размер: 1080x1080 px
   - Брендированная рамка внизу с логотипом и контактной информацией
   - Информация об объекте: цена, количество спален, площадь

2. **Instagram Story**
   - Размер: 1080x1920 px
   - Брендированная рамка сверху и снизу
   - Кнопка "Swipe Up" для перехода на страницу объекта

3. **Facebook Post**
   - Размер: 1200x630 px
   - Брендированная рамка справа с информацией об объекте
   - Логотип компании в углу

4. **Twitter Post**
   - Размер: 1200x675 px
   - Минималистичная рамка с основной информацией
   - Хештеги и ID объекта

### 4. Административный модуль

#### Функциональность
- Управление доступом пользователей
- Настройка параметров бота
- Просмотр статистики использования
- Управление шаблонами и контентом

#### Компоненты
- Веб-интерфейс для администраторов
- Система управления пользователями
- Генератор отчетов и статистики
- Редактор шаблонов мокапов

#### Примеры команд
- `/admin` - Доступ к административным функциям (только для администраторов)
- `/stats` - Показать статистику использования бота
- `/users` - Управление пользователями
- `/templates` - Управление шаблонами мокапов

## Процесс создания поста для социальных сетей

### Шаг 1: Выбор объекта недвижимости
1. Пользователь выбирает объект недвижимости из каталога или по ID
2. Бот отображает основную информацию об объекте и предлагает создать пост

### Шаг 2: Выбор социальной сети и шаблона
1. Пользователь выбирает социальную сеть (Instagram, Facebook, Twitter и др.)
2. Бот предлагает доступные шаблоны для выбранной социальной сети
3. Пользователь выбирает шаблон

### Шаг 3: Выбор фотографий
1. Бот отображает доступные фотографии объекта
2. Пользователь выбирает одну или несколько фотографий для поста
3. Бот предлагает опции кадрирования и обработки фотографий

### Шаг 4: Настройка информации в рамке
1. Бот предлагает выбрать информацию, которая будет отображаться в рамке
2. Пользователь выбирает параметры (цена, количество спален, площадь и т.д.)
3. Бот генерирует предварительный вид мокапа

### Шаг 5: Генерация текста поста
1. Бот предлагает сгенерировать текст поста с помощью AI
2. Пользователь выбирает язык и стиль текста
3. Бот генерирует текст и предлагает отредактировать его

### Шаг 6: Генерация хештегов
1. Бот автоматически генерирует релевантные хештеги на основе характеристик объекта
2. Пользователь может добавить или удалить хештеги
3. Бот формирует финальный набор хештегов

### Шаг 7: Предпросмотр и экспорт
1. Бот отображает предварительный вид поста (изображение + текст + хештеги)
2. Пользователь может внести корректировки
3. Бот экспортирует готовый пост (изображение и текст) для публикации

### Шаг 8: Публикация (опционально)
1. Бот предлагает опцию прямой публикации в социальные сети
2. Пользователь выбирает аккаунт для публикации
3. Бот публикует пост и предоставляет ссылку на опубликованный материал

## Примеры мокапов для социальных сетей

### Instagram Post (1080x1080 px)

```
+------------------------------------------+
|                                          |
|                                          |
|                                          |
|           ФОТОГРАФИЯ ОБЪЕКТА             |
|                                          |
|                                          |
|                                          |
+------------------------------------------+
|  LOGO  |  3 СПАЛЬНИ  |  150 м²  |  $500K |
+------------------------------------------+
|  #indreamsphuket #phuketrealestate #ID123|
+------------------------------------------+
```

### Instagram Story (1080x1920 px)

```
+------------------------------------------+
|  LOGO  |  INDREAMS PHUKET  |  КОНТАКТЫ   |
+------------------------------------------+
|                                          |
|                                          |
|                                          |
|                                          |
|           ФОТОГРАФИЯ ОБЪЕКТА             |
|                                          |
|                                          |
|                                          |
|                                          |
+------------------------------------------+
|  ВИЛЛА НА ПХУКЕТЕ  |  3 СПАЛЬНИ  |  $500K|
+------------------------------------------+
|            SWIPE UP TO VIEW              |
+------------------------------------------+
```

### Facebook Post (1200x630 px)

```
+--------------------------------------+----------------+
|                                      |                |
|                                      |  LOGO          |
|                                      |                |
|       ФОТОГРАФИЯ ОБЪЕКТА             |  3 СПАЛЬНИ     |
|                                      |                |
|                                      |  150 м²        |
|                                      |                |
|                                      |  $500,000      |
+--------------------------------------+----------------+
|  #indreamsphuket #phuketrealestate #luxuryvilla      |
+-----------------------------------------------------+
```

## Этапы разработки

### Этап 1: Проектирование и подготовка (2 недели)
- Разработка детальной архитектуры
- Создание прототипов интерфейсов
- Настройка окружения разработки
- Подготовка API для интеграции с сайтом

### Этап 2: Разработка базовых функций (3 недели)
- Реализация интеграции с Telegram Bot API
- Разработка базовых команд бота
- Интеграция с сайтом для получения данных об объектах
- Создание системы управления пользовательскими сессиями

### Этап 3: Разработка функций генерации контента (4 недели)
- Реализация сервиса генерации текстов с использованием DeepSeek API
- Разработка инструментов обработки изображений
- Создание шаблонов для мокапов
- Реализация системы генерации хештегов

### Этап 4: Разработка административного модуля (2 недели)
- Создание веб-интерфейса для администраторов
- Реализация системы управления пользователями
- Разработка генератора отчетов и статистики
- Создание редактора шаблонов мокапов

### Этап 5: Тестирование и оптимизация (2 недели)
- Тестирование всех функций бота
- Оптимизация производительности
- Исправление выявленных ошибок
- Проведение пользовательского тестирования

### Этап 6: Запуск и поддержка (1 неделя)
- Развертывание бота на продакшн-сервере
- Настройка мониторинга и логирования
- Обучение персонала работе с ботом
- Подготовка документации

## Требования к серверу

### Аппаратные требования
- CPU: 2+ ядра
- RAM: 4+ ГБ
- Дисковое пространство: 50+ ГБ SSD
- Сетевой канал: 100+ Мбит/с

### Программные требования
- ОС: Ubuntu 20.04 LTS или новее
- Веб-сервер: Nginx 1.18+
- PHP: 8.0+ с расширениями (curl, gd, mbstring, xml)
- Node.js: 16+ с npm
- Redis: 6+
- MySQL: 8+
- SSL-сертификат для защищенного соединения

## Заключение

Предложенная архитектура телеграм-бота для InDreams Phuket обеспечивает полноценную функциональность для поиска и просмотра объектов недвижимости, а также для генерации контента для социальных сетей. Особое внимание уделено модулю создания мокапов с брендированной рамкой, который позволит автоматизировать процесс подготовки материалов для публикаций в Instagram и других социальных сетях.

Реализация данного проекта позволит агентству недвижимости InDreams Phuket автоматизировать маркетинговые процессы, улучшить взаимодействие с клиентами и повысить эффективность продвижения объектов недвижимости в социальных сетях.
