<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Форма заполнения договора InDreams Phuket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            width: 100%;
            max-width: 1000px;
            margin: 0 auto;
            padding: 0;
        }
        .form-header {
            text-align: center;
            padding: 20px;
            background-color: white;
            margin-bottom: 20px;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .logo {
            max-width: 300px;
            margin: 0 auto 20px;
            display: block;
        }
        .form-content {
            background-color: white;
            padding: 30px;
            border-radius: 0 0 8px 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-section {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .form-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
            font-size: 18px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e74c3c;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        input:focus, textarea:focus, select:focus {
            border-color: #3498db;
            box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
            outline: none;
        }
        .row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -10px;
            margin-left: -10px;
        }
        .col {
            flex: 1;
            padding: 0 10px;
            min-width: 200px;
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn-preview {
            background-color: #2ecc71;
        }
        .btn-preview:hover {
            background-color: #27ae60;
        }
        .btn-reset {
            background-color: #e74c3c;
        }
        .btn-reset:hover {
            background-color: #c0392b;
        }
        .btn-container {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }
        .required::after {
            content: ' *';
            color: red;
        }
        .form-notification {
            padding: 15px;
            margin-bottom: 20px;
            background-color: #d4edda;
            border-radius: 4px;
            color: #155724;
            display: none;
        }
        .form-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 20px;
        }
        .form-tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .form-tab.active {
            background-color: #fff;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .info-block {
            background-color: #e8f4fc;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
        .info-block h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .info-block p {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-header">
            <img src="indreams-logo.png" alt="InDreams Phuket Logo" class="logo">
            <h1>Форма заполнения договора управления недвижимостью</h1>
            <p>Заполните все необходимые поля для создания договора</p>
        </div>
        
        <div class="form-content">
            <div class="info-block">
                <h3>Улучшенная версия договора</h3>
                <p>Теперь договор содержит логотип InDreams Phuket в верхнем колонтитуле на всех страницах при печати!</p>
                <p>Это позволяет придать документу профессиональный вид и узнаваемость бренда.</p>
            </div>
            
            <div id="notification" class="form-notification">
                Данные успешно сохранены! Вы можете просмотреть договор, нажав кнопку "Открыть договор".
            </div>
            
            <div class="form-tabs">
                <div class="form-tab active" onclick="showTab('basic-info')">Основная информация</div>
                <div class="form-tab" onclick="showTab('property-info')">Информация об объекте</div>
                <div class="form-tab" onclick="showTab('owner-info')">Информация о собственнике</div>
                <div class="form-tab" onclick="showTab('payment-info')">Банковская информация</div>
            </div>
            
            <form id="contractForm">
                <!-- Вкладка 1: Основная информация о договоре -->
                <div id="basic-info" class="tab-content active">
                    <div class="form-section">
                        <div class="form-title">Информация о договоре</div>
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="contract-number" class="required">Номер договора</label>
                                    <input type="text" id="contract-number" name="contract-number" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="contract-date" class="required">Дата договора</label>
                                    <input type="date" id="contract-date" name="contract-date" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="contract-start-date" class="required">Дата начала договора</label>
                                    <input type="date" id="contract-start-date" name="contract-start-date" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="contract-end-date" class="required">Дата окончания договора</label>
                                    <input type="date" id="contract-end-date" name="contract-end-date" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn" onclick="showTab('property-info')">Далее: Информация об объекте</button>
                    </div>
                </div>
                
                <!-- Вкладка 2: Информация об объекте -->
                <div id="property-info" class="tab-content">
                    <div class="form-section">
                        <div class="form-title">Информация об объекте</div>
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="property-number" class="required">Номер квартиры/виллы</label>
                                    <input type="text" id="property-number" name="property-number" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="property-deed" class="required">Номер свидетельства о праве собственности</label>
                                    <input type="text" id="property-deed" name="property-deed" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="property-address" class="required">Адрес объекта</label>
                            <input type="text" id="property-address" name="property-address" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="property-area" class="required">Площадь объекта (кв.м)</label>
                            <input type="number" id="property-area" name="property-area" step="0.01" min="0" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn" onclick="showTab('owner-info')">Далее: Информация о собственнике</button>
                    </div>
                </div>
                
                <!-- Вкладка 3: Информация о собственнике -->
                <div id="owner-info" class="tab-content">
                    <div class="form-section">
                        <div class="form-title">Информация о собственнике</div>
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-name" class="required">ФИО собственника</label>
                                    <input type="text" id="owner-name" name="owner-name" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-citizenship" class="required">Гражданство</label>
                                    <input type="text" id="owner-citizenship" name="owner-citizenship" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-passport" class="required">Номер паспорта</label>
                                    <input type="text" id="owner-passport" name="owner-passport" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-address" class="required">Адрес проживания</label>
                                    <input type="text" id="owner-address" name="owner-address" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-email" class="required">Email</label>
                                    <input type="email" id="owner-email" name="owner-email" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-phone" class="required">Номер телефона</label>
                                    <input type="tel" id="owner-phone" name="owner-phone" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="owner-details">Дополнительная информация о собственнике</label>
                            <textarea id="owner-details" name="owner-details" rows="2"></textarea>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <button type="button" class="btn" onclick="showTab('payment-info')">Далее: Банковская информация</button>
                    </div>
                </div>
                
                <!-- Вкладка 4: Банковская информация -->
                <div id="payment-info" class="tab-content">
                    <div class="form-section">
                        <div class="form-title">Банковские реквизиты для выплат</div>
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-bank" class="required">Банк</label>
                                    <input type="text" id="owner-bank" name="owner-bank" required>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label for="owner-account" class="required">Номер счета</label>
                                    <input type="text" id="owner-account" name="owner-account" required>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="btn-container">
                        <button type="button" class="btn btn-reset" id="reset-form">Сбросить форму</button>
                        <div>
                            <button type="submit" class="btn">Сохранить данные</button>
                            <button type="button" class="btn btn-preview" id="preview-contract">Открыть договор</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Populate form from localStorage
            populateFormFromStorage();
            
            // Form submission handler
            document.getElementById('contractForm').addEventListener('submit', function(e) {
                e.preventDefault();
                saveFormData();
                
                // Show notification
                const notification = document.getElementById('notification');
                notification.style.display = 'block';
                
                // Hide notification after 5 seconds
                setTimeout(function() {
                    notification.style.display = 'none';
                }, 5000);
            });
            
            // Reset form button
            document.getElementById('reset-form').addEventListener('click', function() {
                if (confirm('Вы уверены, что хотите сбросить все данные формы?')) {
                    resetForm();
                }
            });
            
            // Preview contract button
            document.getElementById('preview-contract').addEventListener('click', function() {
                // First save the current form data
                saveFormData();
                
                // Then open the contract in a new tab/window
                window.open('property-management-contract-full.html', '_blank');
            });
        });
        
        // Function to show a specific tab
        function showTab(tabId) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show the selected tab
            document.getElementById(tabId).classList.add('active');
            
            // Update tab buttons
            document.querySelectorAll('.form-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Find the tab button that corresponds to the tab ID and make it active
            document.querySelectorAll('.form-tab').forEach(tab => {
                if (tab.getAttribute('onclick').includes(tabId)) {
                    tab.classList.add('active');
                }
            });
        }
        
        // Function to populate form with data from localStorage
        function populateFormFromStorage() {
            const formElements = document.getElementById('contractForm').elements;
            
            for (let i = 0; i < formElements.length; i++) {
                const element = formElements[i];
                if (element.name && element.name !== '') {
                    const savedValue = localStorage.getItem(element.name);
                    if (savedValue !== null) {
                        element.value = savedValue;
                    }
                }
            }
        }
        
        // Function to save form data to localStorage
        function saveFormData() {
            const formElements = document.getElementById('contractForm').elements;
            
            for (let i = 0; i < formElements.length; i++) {
                const element = formElements[i];
                if (element.name && element.name !== '') {
                    localStorage.setItem(element.name, element.value);
                }
            }
        }
        
        // Function to reset form and clear localStorage
        function resetForm() {
            const formElements = document.getElementById('contractForm').elements;
            
            // Clear form
            document.getElementById('contractForm').reset();
            
            // Clear localStorage for form fields
            for (let i = 0; i < formElements.length; i++) {
                const element = formElements[i];
                if (element.name && element.name !== '') {
                    localStorage.removeItem(element.name);
                }
            }
            
            // Hide notification if visible
            document.getElementById('notification').style.display = 'none';
        }
        
        // Format date for display in contract
        function formatDate(dateString) {
            if (!dateString) return '';
            
            const date = new Date(dateString);
            return date.toLocaleDateString('ru-RU');
        }
    </script>
</body>
</html>
