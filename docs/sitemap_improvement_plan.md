# Sitemap Improvement Plan for InDreams Phuket

## Current Analysis Results

1. **Sitemap Structure Overview**:
   - 31 sitemap XML files exist in the system
   - Main index sitemap with proper structure
   - Sitemaps are segmented by language and content type (properties, projects, articles, etc.)
   - Proper namespace declarations (sitemaps.org and xhtml)
   - Image namespace is declared but not actively used

2. **Implementation Details**:
   - Update scripts (`update_all_sitemaps.php`) exist to generate sitemaps for different languages
   - Core sitemap functionality managed by `PhuketSitemap.php` class
   - Support for multiple languages (ru, en, th, zh)
   - Content segmentation (properties, projects, articles, pages, locations, etc.)
   - URL filtering based on robots.txt

3. **Missing Components**:
   - No active implementation of image sitemap tags despite namespace declaration
   - No video sitemap implementation
   - No news sitemap (for time-sensitive content)
   - No hreflang implementation for mobile variants
   - No automated ping to search engines after sitemap updates

## Improvement Plan

### Step 1: Implement Image Sitemap Information (Priority: High)

The code has preparations for image sitemap (`getPropertyImages()` method), but it's not being used:

```php
// Implementation plan for sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Funcs/PhuketSitemap.php
// Modify the getItem method to include image information

// Add to getPropertyItems():
$images = $this->getPropertyImages($row->nid);
$items .= $this->getItemWithImages($row->path, $row->changed, $row->priority, $images);

// Create new method:
protected function getItemWithImages($url, $changed, $priority = 0.5, $images = array())
{
  $item = $this->getItem($url, $changed, $priority);
  
  // Insert image tags before the closing </url> tag
  if (!empty($images)) {
    $closeTag = '</url>';
    $imageXml = '';
    
    foreach ($images as $image) {
      $imageXml .= '  <image:image>' . "\n";
      $imageXml .= '    <image:loc>' . htmlspecialchars($image['url']) . '</image:loc>' . "\n";
      if (!empty($image['title'])) {
        $imageXml .= '    <image:title>' . htmlspecialchars($image['title']) . '</image:title>' . "\n";
      }
      if (!empty($image['caption'])) {
        $imageXml .= '    <image:caption>' . htmlspecialchars($image['caption']) . '</image:caption>' . "\n";
      }
      $imageXml .= '  </image:image>' . "\n";
    }
    
    $item = str_replace($closeTag, $imageXml . $closeTag, $item);
  }
  
  return $item;
}
```

### Step 2: Create News Sitemap (Priority: Medium)

For time-sensitive content like news articles:

```php
// Create new file: update_news_sitemap.php

<?php
/**
 * Script to generate news sitemap for Google News
 */
require_once './includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

$news_sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
$news_sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">' . "\n";

// Get news content from the last 2 days (Google News requirement)
$query = db_select('node', 'n');
$query->fields('n', array('nid', 'title', 'created'));
$query->condition('n.type', 'news');
$query->condition('n.status', 1);
$query->condition('n.created', time() - (2 * 24 * 60 * 60), '>');
$query->orderBy('n.created', 'DESC');
$results = $query->execute();

foreach ($results as $row) {
  $node = node_load($row->nid);
  $url = url('node/' . $row->nid, array('absolute' => TRUE));
  
  $news_sitemap .= '  <url>' . "\n";
  $news_sitemap .= '    <loc>' . htmlspecialchars($url) . '</loc>' . "\n";
  $news_sitemap .= '    <news:news>' . "\n";
  $news_sitemap .= '      <news:publication>' . "\n";
  $news_sitemap .= '        <news:name>InDreams Phuket</news:name>' . "\n";
  $news_sitemap .= '        <news:language>en</news:language>' . "\n";
  $news_sitemap .= '      </news:publication>' . "\n";
  $news_sitemap .= '      <news:publication_date>' . date('Y-m-d\TH:i:sP', $row->created) . '</news:publication_date>' . "\n";
  $news_sitemap .= '      <news:title>' . htmlspecialchars($node->title) . '</news:title>' . "\n";
  $news_sitemap .= '    </news:news>' . "\n";
  $news_sitemap .= '  </url>' . "\n";
}

$news_sitemap .= '</urlset>';

// Save the sitemap
file_put_contents('sitemap-news.xml', $news_sitemap);
echo "News sitemap generated successfully.";
```

### Step 3: Setup Sitemap Cron Updates and Ping (Priority: High)

Create a module hook to automate sitemap regeneration:

```php
// Add to an existing custom module or create a new one:

/**
 * Implements hook_cron().
 */
function custom_sitemap_cron() {
  // Run update only once a day
  $last_run = variable_get('sitemap_last_update', 0);
  if (time() - $last_run > 86400) {
    // Update all sitemaps
    exec('php ' . DRUPAL_ROOT . '/update_all_sitemaps.php');
    
    // Ping search engines
    _custom_sitemap_ping_search_engines();
    
    // Update timestamp
    variable_set('sitemap_last_update', time());
  }
}

/**
 * Ping search engines about updated sitemaps.
 */
function _custom_sitemap_ping_search_engines() {
  $sitemap_url = url('sitemap.xml', array('absolute' => TRUE));
  
  // Ping Google
  $ping_url = 'https://www.google.com/ping?sitemap=' . urlencode($sitemap_url);
  drupal_http_request($ping_url);
  
  // Ping Bing
  $ping_url = 'https://www.bing.com/ping?sitemap=' . urlencode($sitemap_url);
  drupal_http_request($ping_url);
  
  // Log the ping
  watchdog('sitemap', 'Search engines pinged about updated sitemap.', array(), WATCHDOG_INFO);
}
```

### Step 4: Improve URL Validation and Monitoring (Priority: Medium)

Add URL validation to ensure all URLs in the sitemap are accessible:

```php
// Add to PhuketSitemap.php

/**
 * Validates that a URL returns a valid HTTP response
 */
protected function validateUrl($url) {
  static $cache = array();
  
  // Check cache first to avoid repeated checks
  if (isset($cache[$url])) {
    return $cache[$url];
  }
  
  // Skip validation for development environments
  if (isset($_SERVER['HTTP_HOST']) && 
      (strpos($_SERVER['HTTP_HOST'], 'dev.') === 0 || 
       strpos($_SERVER['HTTP_HOST'], 'local.') === 0)) {
    return true;
  }
  
  $result = false;
  try {
    $options = array(
      'method' => 'HEAD',
      'timeout' => 5,
      'max_redirects' => 3,
    );
    $response = drupal_http_request($url, $options);
    
    // Success codes are 2xx and 3xx
    $result = ($response->code >= 200 && $response->code < 400);
    
    // Log failures for review
    if (!$result) {
      watchdog('sitemap', 'Invalid URL in sitemap: @url (Code: @code)', 
               array('@url' => $url, '@code' => $response->code), 
               WATCHDOG_WARNING);
    }
  }
  catch (Exception $e) {
    watchdog('sitemap', 'Error checking URL @url: @error', 
             array('@url' => $url, '@error' => $e->getMessage()), 
             WATCHDOG_ERROR);
  }
  
  // Cache result
  $cache[$url] = $result;
  return $result;
}
```

### Step 5: Create Sitemap Statistics Reporting (Priority: Low)

```php
// Create file: sitemap_stats.php

<?php
/**
 * Generates sitemap statistics report
 */
require_once './includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Initialize statistics
$stats = array(
  'total_files' => 0,
  'total_urls' => 0,
  'by_type' => array(),
  'by_language' => array(),
  'file_sizes' => array(),
);

// Count sitemaps by type and language
foreach (glob("sitemap*.xml") as $sitemap) {
  $stats['total_files']++;
  $stats['file_sizes'][$sitemap] = round(filesize($sitemap) / 1024, 2) . ' KB';
  
  // Parse sitemap filename to get language and type
  if (preg_match('/sitemap-([a-z]{2})-([a-z]+)\.xml/', $sitemap, $matches)) {
    $language = $matches[1];
    $type = $matches[2];
    
    if (!isset($stats['by_language'][$language])) {
      $stats['by_language'][$language] = 0;
    }
    if (!isset($stats['by_type'][$type])) {
      $stats['by_type'][$type] = 0;
    }
    
    // Count URLs in this sitemap
    $content = file_get_contents($sitemap);
    $url_count = substr_count($content, '<loc>');
    
    $stats['total_urls'] += $url_count;
    $stats['by_language'][$language] += $url_count;
    $stats['by_type'][$type] += $url_count;
  }
}

// Output report
header('Content-Type: text/plain');
echo "Sitemap Statistics Report\n";
echo "------------------------\n\n";
echo "Total sitemap files: {$stats['total_files']}\n";
echo "Total URLs: {$stats['total_urls']}\n\n";

echo "URLs by language:\n";
foreach ($stats['by_language'] as $language => $count) {
  echo "  {$language}: {$count}\n";
}

echo "\nURLs by content type:\n";
foreach ($stats['by_type'] as $type => $count) {
  echo "  {$type}: {$count}\n";
}

echo "\nFile sizes:\n";
foreach ($stats['file_sizes'] as $file => $size) {
  echo "  {$file}: {$size}\n";
}
```

## Implementation Priority

1. **Immediate Actions**:
   - Complete image sitemap implementation (Step 1)
   - Setup automated sitemap generation via cron (Step 3)
   - Add ping to search engines (Step 3)

2. **Secondary Actions**:
   - Create news sitemap for time-sensitive content (Step 2)
   - Improve URL validation and monitoring (Step 4)

3. **Optional Enhancements**:
   - Create sitemap statistics reporting (Step 5)
   - Create mobile sitemap (if significant mobile-specific content exists)

## Expected Results

After implementing these improvements:

1. Better indexing of images in Google Image Search
2. Faster discovery and indexing of new content
3. Improved handling of time-sensitive content with news sitemap
4. Better monitoring and validation of sitemap URLs
5. Increased visibility through proper search engine pinging
6. More comprehensive reporting on sitemap effectiveness 