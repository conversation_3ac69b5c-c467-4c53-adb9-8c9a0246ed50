# Документация проекта InDreams

## Архитектура проекта

### Основная структура (sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/)
- `Compo/` - Компоненты
- `Converters/` - Конвертеры данных
- `Domain/` - Доменная логика
- `Dto/` - Data Transfer Objects
- `Form/` - Формы
- `Funcs/` - Вспомогательные функции
- `Parser/` - Парсеры
- `Query/` - Запросы к БД
- `Resources/` - Ресурсы

### Модули поиска и фильтрации (Query/)
#### Основные модули:
- `PropertySearch/` - Поиск недвижимости
- `Property2Search/` - Расширенный поиск недвижимости
- `Property3Search/` - Дополнительный поиск недвижимости
- `ProjectSearch/` - Поиск по проектам
- `RentSearch/` - Поиск по аренде
- `ExportSearch/` - Экспорт данных

#### Дополнительные модули:
- `ArticleSearch/` - Поиск статей
- `NewsSearch/` - Поиск ново��тей
- `ReviewSearch/` - Поиск отзывов
- `ServiceSearch/` - Поиск услуг
- `UserSearch/` - Поиск пользователей
- `OptionSearch/` - Поиск опций
- `PageSeoSearch/` - SEO страницы
- `SelectionSearch/` - Поиск подборок
- `SitemapSearch/` - Генерация карты сайта

#### Туристические модули:
- `BoatSearch/` - Поиск лодок
- `ExcursionSearch/` - Поиск экскурсий
- `ClubSearch/` - Поиск клубов

## Структура данных

### Основные сущности:
1. Property (Недвижимость)
2. Project (Проект)
3. Agent (Агент)
4. Review (Отзыв)
5. Article (Статья)
6. News (Новость)
7. Service (Услуга)
8. Excursion (Экскурсия)
9. Boat (Лодка)
10. Club (Клуб)

### Объект недвижимости (Property)
Основная сущность в системе, представленная в таблице `_phuket_Property`

#### Основные поля:
- `id` - Уникальный идентификатор (varchar(36))
- `number` - Номер объекта (int)
- `numberStr` - Номер объекта с ведущими нулями (varchar)
- `name` - Название (сериализованный массив по языкам)
- `dealType` - Тип сделки ('rent'|'sale')
- `propertyType` - Тип недвижимости:
  - villa
  - apartment
  - townhouse
  - penthouse
  - hotel

#### Статусы и флаги:
- `published` - Статус публикации (0|1)
- `isSaled` - Статус продажи (0|1)
- `isRecommended` - Рекомендованный объект (0|1)
- `isPermium` - Премиум объект (0|1)
- `isInvest` - Инвестиционный объект (0|1)
- `isNew` - Новый объект (0|1, на основе stage == 'stage_build')
- `isOwner` - От собственника (0|1, propositionType == 'private')
- `isDiscount` - Есть скидка (0|1)
- `isReservation` - В резерве (0|1)
- `onPromo` - На промо (0|1)

#### Характеристики:
- `areaCommon` - Общая площадь (int)
- `areaPlot` - Площадь участка (int)
- `bedrooms` - Количество спален (int)
- `floor` - Этаж (int)
- `re_builtYear` - Год постройки (varchar(16))

#### Цены:
- `price_sale` - Цена продажи (int, max 2000000000)
- `price_rent` - Цена аренды (int, max 2000000000)
- `price_longtime` - Цена долгосрочной аренды (int, max 2000000000)
- `priceTeaser` - Цена для тизера (int)
- `priceSaleOld` - Старая цена продажи (для скидок)

#### Локация:
- `lat` - Широта (float)
- `lng` - Долгота (float)
- `re_country` - Страна (ID)
- `re_locality` - Район (ID)
- `re_subLocality` - Подрайон (ID)

#### Дополнительные параметры:
- `stage` - Стадия (stage_build и др.)
- `holdType` - Тип владения
- `holdDocument` - Документы владения
- `garantDohod` - Гарантированный доход (int)
- `garantDohodLet` - Срок гарантированного дохода в годах (int)
- `videoLink` - Ссылка на видео
- `additional` - Дополнительные параметры (text)

#### DTO объекты:
```typescript
interface PropertyDto {
  id: string;
  number: string;
  name: {
    en: string;
    ru: string;
    'zh-hans': string;
    th: string;
  };
  description: {
    en: string;
    ru: string;
    'zh-hans': string;
    th: string;
  };
  bathrooms: number;
  bedrooms: number;
  re_builtYear: number;
  photos: PhotoDto[];
  project: {
    text: {
      en: string;
      ru: string;
      'zh-hans': string;
      th: string;
    }
  };
  re_country: LocationDto;
  re_locality: LocationDto;
  re_subLocality: LocationDto;
  propositionType: {
    id: string;
  };
  stage: {
    id: string;
  };
  dealType: {
    id: string;
  };
  propertyType: {
    id: string;
  };
  priceSale: number;
  priceSaleOld: number;
  priceRent: number;
  priceRentLongTime: number;
  isRecommended: boolean;
  isPermium: boolean;
  isSaled: boolean;
  isInvest: boolean;
  isReservation: boolean;
  onPromo: boolean;
}

interface PhotoDto {
  id: string;
  name: string;
  size: number;
  dir: string;
  mime: string;
}

interface LocationDto {
  id: string;
  name: {
    en: string;
    ru: string;
    'zh-hans': string;
    th: string;
  }
}
```

### Проект (Project)
Сущность, объединяющая объекты недвижимости

#### Основные поля:
- `id` - Уникальный идентификатор
- `name` - Название (мультиязычное)
- `description` - Описание (мультиязычное)
- `status` - Статус проекта
- `location` - Расположение
- `properties` - Связанные объекты недвижимости

### Агент (Agent)
#### Основные поля:
- `id` - Уникальный идентификатор
- `name` - Имя
- `contacts` - Контактная информация
- `properties` - Связанные объекты недвижимости

### Отзыв (Review)
#### Основные поля:
- `id` - Уникальный идентификатор
- `author` - Автор
- `text` - Текст отзыва
- `rating` - Оценка
- `propertyId` - ID объекта недвижимости

## Поисковые системы

### PropertySearch
Основной поиск недвижимости с базовыми фильтрами

### Property2Search
Расширенный поиск с дополнительными параметрами:
- Инвестиционные предложения
- Гарантированный доход
- Статус строительства
- Тип владения

### Property3Search
Специализированный поиск с поддержкой:
- Геолокации
- Сложных фильтров
- Агрегации данных
- Статистики

### RentSearch
Специализированный поиск по аренде:
- Краткосрочная аренда
- Долгосрочная аренда
- Сезонные предложения

## API и интеграции

### Экспорт данных
- Facebook Feed (XML)
- WorldVillas Feed (XML)
- Sitemap (XML)
- Custom Export (JSON/XML)

### Внешние сервисы
- Геолокация
- Конвертация валют
- Загрузка изображений
- SEO оптимизация

## Мультиязычность
Поддерживаемые языки:
- en (English)
- ru (Русский)
- zh-hans (Китайский)
- th (Тайский)

## Работа с изображениями
- Функция `s45_imgSrcR()` для получения URL изображения
- Константа `S45_IMST_1900X1000_SCALE` для масштабирования

## Переменные окружения
- `DRUPAL_ROOT` - Корневая директория Drupal
- `base_url` - Базовый URL сайта (https://indreamsphuket.com)

## Зависимости
- Drupal (основной фреймворк)
- PHP 7.4+
- MySQL/MariaDB

## Важные замечания
1. Все текстовые поля хранятся в мультиязычном формате
2. Координаты (lat/lng) обязательны для выгрузки в Facebook
3. Цены указываются в THB (тайских батах)
4. Изображения масштабируются автоматически
5. API требует авторизации
6. Максимальная цена ограничена 2 миллиардами THB
7. Номера объектов хранятся как в числовом формате, так и в строковом с ведущими нулями
8. Поддерживается экспорт в различные форматы (Facebook, WorldVillas)
9. Реализована система фильтрации по множеству параметров
10. Поддерживаются различные режимы отображения данных (полный, краткий, для карты)
11. Проект имеет модульную структуру с четким разделением ответственности
12. Поддерживается несколько типов поиска для разных сценариев использования
13. Реализована система туристических сервисов (лодки, экскурсии, клубы)
14. SEO оптимизация встроена на уровне архитектуры
15. Поддерживается система отзывов и рейтингов

## Разработка новых модулей

### Структура модуля
```typescript
/ModuleName/
  /Query/              // Запросы к БД
    ModuleQuery.php    // Основной класс запросов
    ModuleTable.php    // Структура табли��ы
  /Dto/                // Объекты передачи данных
    ModuleDto.php      // DTO объекты
  /Domain/             // Бизнес-логика
    ModuleLogic.php    // Основная логика
  /Resources/          // Ресурсы модуля
    templates/         // Шаблоны
    translations/      // Переводы
```

### Интеграция с существующей системой
1. Наследование от базовых классов
2. Использование существующих интерфейсов
3. Поддержка мультиязычности
4. Интеграция с поисковой системой
5. Поддержка экспорта данных

### Рекомендации по разработке
1. Следовать существующей архитектуре
2. Использовать DTO для передачи данных
3. Реализовывать поисковые фильтры
4. Поддерживать все языки
5. Интегрировать с существующими модулями

## Структура компонента PhuketAdminLandings

### Расположение файлов
```
sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Compo/Admin/PhuketAdminLandings/
├── PhuketAdminLandings.php         # Основной класс компонента
├── PhuketAdminLandings.tpl.php     # Шаблон компонента
├── PhuketAdminLandings.css         # Стили компонента
├── PhuketAdminLandings.js          # JavaScript компонента
└── panels/                         # Папка с панелями
    └── main.inc.php                # Основная панель компонента
```

### Регистрация компонента
В файле config/components.php:
```php
'PhuketAdminLandings' => array(
    'class' => '\Site45\Sets\Phuket\Compo\Admin\PhuketAdminLandings\PhuketAdminLandings',
    'access' => array(
        'admin' => true,
        'guest' => false
    ),
    'template' => true
),
```

### Структура основного класса
```php
namespace Site45\Sets\Phuket\Compo\Admin\PhuketAdminLandings;

use Site45\Compo\Compo;
use Site45\DtoLib\Base\LangVO;

class PhuketAdminLandings extends Compo {
    // Свойства для мультиязычности
    public $content_title;
    public $content_description;
    
    function __construct() {
        parent::__construct();
        // Инициализация мультиязычных строк
    }
    
    protected function beforeRender($props) {
        parent::beforeRender($props);
        // Проверка доступа и подключение ресурсов
    }
}
```

### Структура шаблона
```php
// PhuketAdminLandings.tpl.php
<component>
  <div id="main">
    <!-- Header -->
    <header class="navbar navbar-fixed-top navbar-shadow">
      <!-- ... -->
    </header>
    
    <!-- Content -->
    <section id="content_wrapper">
      <div id="content">
        <div class="admin-panels fade-onload">
          <?php include 'panels/main.inc.php'; ?>
        </div>
      </div>
    </section>
  </div>
</component>
```

### Структура основной панели
```php
// panels/main.inc.php
<div class="admin-form theme-primary">
  <div class="panel heading-border">
    <div class="panel-body bg-light">
      <!-- Заголовок -->
      <div class="section-divider mb40">
        <span><?php print s45_lang($this->content_title); ?></span>
      </div>
      
      <!-- Основной контент -->
      <!-- ... -->
    </div>
  </div>
</div>
```

### Ключевые особенности
1. Наследование от базового класса Compo
2. Использование мультиязычности через LangVO
3. Проверка доступа через s45_phuket_adminUsers()
4. Подключение стандартных стилей и скриптов админки
5. Использование стандартной структуры шаблона админки
6. Разделение логики и представления через panels/

### Подключаемые ресурсы
- Стили админки (theme.css, admin-forms.min.css)
- JavaScript библиотеки (bootstrap.min.js)
- Дополнительные компоненты (jbox, fa5, absadmin)