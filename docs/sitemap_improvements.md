# Рекомендации по улучшению XML Sitemap для InDreams Phuket

## Текущее состояние

Нами успешно выполнено:
- Создано 31 файл sitemap.xml, сегментированных по языкам и типам контента
- Создан индексный файл sitemap.xml с правильной структурой
- Установлен редирект с HTTP на HTTPS
- Добавлены атрибуты priority и changefreq
- Добавлен тег hreflang x-default
- Оптимизирована структура для улучшения индексации

## Выявленные проблемы

При проверке карт сайта выявлены следующие проблемы:
1. Отсутствие namespace image для sitemap с недвижимостью
2. Проблемы с доступностью некоторых URL (возвращают код 0)
3. Необходимость оптимизации размера файлов карт сайта

## Рекомендации по дальнейшему улучшению

### 1. Оптимизация изображений в sitemap

Для улучшения видимости изображений в поисковых системах:
- Исправить проблему с namespace image в sitemap-*-properties.xml
- Добавить мета-данные к изображениям (title, alt, caption)
- Добавить отдельную карту сайта для изображений

```php
// Пример правильной структуры для изображений
<image:image>
  <image:loc>https://indreamsphuket.com/path/to/image.jpg</image:loc>
  <image:title>Название изображения</image:title>
  <image:caption>Описание изображения</image:caption>
  <image:geo_location>Phuket, Thailand</image:geo_location>
</image:image>
```

### 2. Добавление видео в sitemap

Добавление информации о видео в карту сайта может значительно увеличить видимость в поисковых системах:

```php
// Пример структуры для видео
<video:video>
  <video:thumbnail_loc>https://indreamsphuket.com/thumbs/video123.jpg</video:thumbnail_loc>
  <video:title>Название видео</video:title>
  <video:description>Описание видео</video:description>
  <video:content_loc>https://indreamsphuket.com/videos/video123.mp4</video:content_loc>
  <video:duration>120</video:duration>
</video:video>
```

### 3. Оптимизация доступа к URL

- Исправить недоступные URL, которые возвращают код 0
- Добавить проверку доступности URL перед включением в sitemap
- Настроить мониторинг для регулярной проверки доступности URL

```php
// Пример кода для проверки доступности URL перед включением в sitemap
function isUrlAccessible($url) {
  $ch = curl_init($url);
  curl_setopt($ch, CURLOPT_NOBODY, true);
  curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
  curl_setopt($ch, CURLOPT_TIMEOUT, 5);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
  curl_exec($ch);
  $response_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
  curl_close($ch);
  
  return ($response_code >= 200 && $response_code < 400);
}
```

### 4. Добавление новостного sitemap

Для сайтов с регулярно обновляемым контентом рекомендуется создать news sitemap:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">
  <url>
    <loc>https://indreamsphuket.com/news/article123</loc>
    <news:news>
      <news:publication>
        <news:name>InDreams Phuket</news:name>
        <news:language>en</news:language>
      </news:publication>
      <news:publication_date>2025-03-19T13:00:00Z</news:publication_date>
      <news:title>Новости рынка недвижимости Пхукета</news:title>
    </news:news>
  </url>
</urlset>
```

### 5. Добавление мобильной карты сайта

Для улучшения индексации мобильной версии можно добавить отдельный sitemap с указанием мобильных URL:

```php
function generateMobileSitemap() {
  $output = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
  $output .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
                  xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0">' . "\n";
  
  // Получить все URL
  $urls = getAllUrls();
  
  foreach ($urls as $url) {
    $output .= '  <url>' . "\n";
    $output .= '    <loc>' . $url . '</loc>' . "\n";
    $output .= '    <mobile:mobile/>' . "\n";
    $output .= '  </url>' . "\n";
  }
  
  $output .= '</urlset>';
  file_put_contents(DRUPAL_ROOT . '/sitemap-mobile.xml', $output);
}
```

### 6. Настройка регулярного обновления

- Настроить cron-задание для еженедельного обновления карт сайта
- Добавить автоматическое уведомление поисковых систем после обновления
- Настроить мониторинг индексации через Google Search Console

### 7. Улучшение канонизации URL

- Проверить и оптимизировать канонические URL для всех страниц
- Не включать в sitemap URL с canonical на другие страницы
- Исключить дублирующиеся URL из карты сайта

### 8. Загрузка sitemap в Google Search Console

- Загрузить основной индексный файл sitemap.xml в GSC
- Проверить ошибки индексации через отчет "Охват"
- Настроить отслеживание ошибок индексации

## Приоритеты для реализации

1. Исправить проблемы с namespace image для недвижимости
2. Проверить и исправить недоступные URL
3. Добавить отдельную карту сайта для изображений
4. Настроить регулярное обновление через cron
5. Добавить news sitemap для новостей и статей

## Ожидаемые результаты

После реализации этих рекомендаций ожидаются следующие улучшения:
- Увеличение процента индексации страниц в Google
- Улучшение видимости изображений в поиске
- Более быстрая индексация нового контента
- Уменьшение числа ошибок индексации
- Общее улучшение позиций в поисковой выдаче 