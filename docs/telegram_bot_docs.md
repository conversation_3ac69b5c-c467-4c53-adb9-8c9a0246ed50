# Интеграция Telegram-бота с сайтом InDreams Phuket

## Введение

Данная документация описывает процесс создания Telegram-бота для автоматической публикации новых объектов недвижимости с сайта InDreams Phuket в канале Telegram. Решение использует существующую систему управления недвижимостью на Drupal 7 с кастомным модулем s45_phuket.

## Архитектура решения

### Общая схема работы:

1. **Мониторинг новых объектов** - Система регулярно проверяет наличие новых объектов недвижимости
2. **Формирование сообщения** - При появлении нового объекта формируется сообщение для Telegram
3. **Отправка в Telegram** - Сообщение отправляется через Telegram Bot API
4. **Публикация в канале** - Сообщение публикуется в заданном канале или группе

### Компоненты системы:

1. **База данных Drupal** - Хранит все объекты недвижимости в таблице `_phuket_Property`
2. **PHP-скрипт** - Обнаруживает новые объекты и отправляет их в Telegram
3. **Cron задача** - Запускает скрипт с заданной периодичностью
4. **Telegram Bot API** - Интерфейс для взаимодействия с Telegram

## Структура данных недвижимости

### Основные поля объекта недвижимости

Объекты недвижимости хранятся в таблице `_phuket_Property` со следующей структурой:

| Поле | Тип | Описание |
|------|-----|----------|
| id | varchar(36) | Уникальный идентификатор |
| number | int | Номер объекта |
| created | int | Дата создания (Unix timestamp) |
| changed | int | Дата изменения (Unix timestamp) |
| published | int | Статус публикации (0/1) |
| name | text | Сериализованный объект с названиями на разных языках |
| dealType | varchar | Тип сделки (sale/rent) |
| propertyType | varchar | Тип недвижимости (villa/apartment/townhouse/penthouse/hotel) |
| price_sale | int | Цена продажи |
| price_rent | int | Цена аренды |
| price_longtime | int | Цена долгосрочной аренды |
| areaCommon | int | Общая площадь |
| bedrooms | int | Количество спален |
| bathrooms | int | Количество ванных |
| lat | float | Широта |
| lng | float | Долгота |
| propertyDto | text | Сериализованный полный объект данных (DTO) |
| propertyTeaserDto | text | Сериализованный сокращенный объект данных |

### Поля DTO объекта (PhuketPropertyDto)

Объект данных (DTO) содержит все атрибуты недвижимости:

```php
class PhuketPropertyDto extends Dto {
  public $type = 'PhuketProperty';
  public $published = -1;
  public $dealType;         // Тип сделки
  public $propertyType;     // Тип недвижимости
  public $number;           // Номер объекта
  public $name;             // Название объекта (мультиязычное)
  public $description;      // Описание (мультиязычное)
  public $priceSale;        // Цена продажи
  public $priceRent;        // Цена аренды
  public $priceRentLongTime; // Цена долгосрочной аренды
  public $areaCommon;       // Общая площадь
  public $areaPlot;         // Площадь участка
  public $bedrooms;         // Кол-во спален
  public $bathrooms;        // Кол-во ванных
  public $photos = array(); // Массив фотографий
  public $videoLink;        // Ссылка на видео
  public $re_country;       // Страна
  public $re_locality;      // Район
  public $re_subLocality;   // Подрайон
  public $re_latitude;      // Широта
  public $re_longitude;     // Долгота
  public $isRecommended;    // Рекомендуемый
  public $isPermium;        // Премиум
  public $isSaled;          // Продан
  public $isInvest;         // Инвестиционный
}
```

## Реализация Telegram-бота

### 1. Создание бота в Telegram

1. Откройте Telegram и найдите BotFather (@BotFather)
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Получите и сохраните токен бота (API key)
5. Создайте канал или группу, где будут публиковаться объявления
6. Добавьте бота в канал/группу как администратора

### 2. Скрипт для публикации объектов

Создайте PHP-скрипт `telegram_property_bot.php` в корневой директории сайта:

```php
<?php
define('DRUPAL_ROOT', getcwd());

// Настройка окружения Drupal
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Конфигурация Telegram
$bot_token = 'YOUR_BOT_TOKEN';
$channel_id = '@your_channel';  // ID канала или группы

// Период времени для поиска новых объектов (в часах)
$hours_back = 24;

// Получаем новые объекты
$new_properties = get_new_properties($hours_back);
$count = count($new_properties);

echo "Найдено $count новых объектов\n";

foreach ($new_properties as $property) {
    $message = format_property_message($property);
    $photos = get_property_photos($property);
    
    if (!empty($photos)) {
        // Отправляем первое фото с текстом
        send_telegram_photo($bot_token, $channel_id, $photos[0], $message);
        
        // Опционально: отправляем остальные фото без текста (не более 10)
        $additional_photos = array_slice($photos, 1, 9);
        if (!empty($additional_photos)) {
            send_telegram_media_group($bot_token, $channel_id, $additional_photos);
        }
    } else {
        // Если нет фото, отправляем только текст
        send_telegram_message($bot_token, $channel_id, $message);
    }
    
    // Добавляем паузу, чтобы избежать ограничений API
    sleep(2);
}

// Логируем результат
$log_message = date('Y-m-d H:i:s') . " - Отправлено $count объектов в Telegram\n";
file_put_contents(__DIR__ . '/telegram_bot.log', $log_message, FILE_APPEND);

/**
 * Получает новые объекты недвижимости
 */
function get_new_properties($hours_back) {
    $timestamp = time() - ($hours_back * 3600);
    
    $query = db_select('_phuket_Property', 'p')
        ->fields('p')
        ->condition('p.published', 1)
        ->condition('p.created', $timestamp, '>=')
        ->condition('p.isSaled', 0)
        ->orderBy('p.created', 'DESC');
    
    $result = $query->execute();
    $properties = [];
    
    foreach ($result as $row) {
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Форматирует сообщение для Telegram
 */
function format_property_message($property) {
    $propertyDto = unserialize($property->propertyDto);
    $feed_lang = 'ru'; // или 'en', 'th', 'zh-hans'
    
    // Получаем основные данные
    $title = $propertyDto->name->{$feed_lang};
    $description = '';
    if (!empty($propertyDto->description->{$feed_lang})) {
        $description = $propertyDto->description->{$feed_lang};
    } elseif (!empty($propertyDto->project->text->{$feed_lang})) {
        $description = $propertyDto->project->text->{$feed_lang};
    }
    $description = clean_description($description);
    $description = mb_substr($description, 0, 800) . (mb_strlen($description) > 800 ? '...' : '');
    
    // Определяем тип сделки и цену
    $deal_type = $propertyDto->dealType->id == 'sale' ? 'Продажа' : 'Аренда';
    $price = $propertyDto->dealType->id == 'sale' ? 
        format_price($property->price_sale) : 
        format_price($property->price_rent);
        
    // Определяем тип недвижимости
    $property_type = get_property_type($propertyDto->propertyType->id, $feed_lang);
    
    // Создаем сообщение
    $message = "🏡 <b>{$title}</b>\n\n";
    $message .= "💰 <b>{$price}</b> - {$deal_type}\n";
    $message .= "🏘 <b>{$property_type}</b>\n";
    $message .= "🛏 Спальни: <b>{$propertyDto->bedrooms}</b>\n";
    $message .= "🛁 Ванные: <b>{$propertyDto->bathrooms}</b>\n";
    
    if ($propertyDto->areaCommon) {
        $message .= "📏 Площадь: <b>{$propertyDto->areaCommon} м²</b>\n";
    }
    
    if ($propertyDto->areaPlot) {
        $message .= "🏞 Участок: <b>{$propertyDto->areaPlot} м²</b>\n";
    }
    
    if ($propertyDto->re_subLocality && $propertyDto->re_subLocality->name->{$feed_lang}) {
        $message .= "📍 Район: <b>{$propertyDto->re_subLocality->name->{$feed_lang}}</b>\n";
    }
    
    // Добавляем описание и ссылку
    $message .= "\n📝 {$description}\n\n";
    $message .= "🔗 <a href=\"" . s45_path_url('property/' . $propertyDto->id) . "\">Подробнее на сайте</a>\n";
    $message .= "🆔 {$propertyDto->number}";
    
    return $message;
}

/**
 * Получает тип недвижимости на нужном языке
 */
function get_property_type($type_id, $lang = 'ru') {
    $types = [
        'villa' => ['ru' => 'Вилла', 'en' => 'Villa'],
        'apartment' => ['ru' => 'Квартира', 'en' => 'Apartment'],
        'townhouse' => ['ru' => 'Таунхаус', 'en' => 'Townhouse'],
        'penthouse' => ['ru' => 'Пентхаус', 'en' => 'Penthouse'],
        'hotel' => ['ru' => 'Отель', 'en' => 'Hotel'],
    ];
    
    return isset($types[$type_id][$lang]) ? $types[$type_id][$lang] : $type_id;
}

/**
 * Очищает описание от HTML тегов
 */
function clean_description($text) {
    if (empty($text)) return '';
    $text = preg_replace('/\s+_mst\w+="[^"]*"/', '', $text);
    return strip_tags($text);
}

/**
 * Форматирует цену
 */
function format_price($price) {
    return number_format($price, 0, '.', ' ') . ' THB';
}

/**
 * Получает фотографии объекта
 */
function get_property_photos($property) {
    $propertyDto = unserialize($property->propertyDto);
    $photos = [];
    
    if (!empty($propertyDto->photos)) {
        foreach ($propertyDto->photos as $photoDto) {
            $img_url = s45_imgSrcR($photoDto, S45_IMST_1900X1000_SCALE);
            $clean_url = preg_replace('/\?itok=.+$/', '', $img_url);
            $photos[] = $clean_url;
        }
    }
    
    return $photos;
}

/**
 * Отправляет сообщение в Telegram
 */
function send_telegram_message($bot_token, $chat_id, $message) {
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    $params = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => false
    ];
    
    return send_telegram_request($url, $params);
}

/**
 * Отправляет фото с текстом в Telegram
 */
function send_telegram_photo($bot_token, $chat_id, $photo_url, $caption) {
    $url = "https://api.telegram.org/bot{$bot_token}/sendPhoto";
    $params = [
        'chat_id' => $chat_id,
        'photo' => $photo_url,
        'caption' => $caption,
        'parse_mode' => 'HTML'
    ];
    
    return send_telegram_request($url, $params);
}

/**
 * Отправляет группу медиа в Telegram
 */
function send_telegram_media_group($bot_token, $chat_id, $photo_urls) {
    $url = "https://api.telegram.org/bot{$bot_token}/sendMediaGroup";
    
    $media = [];
    foreach ($photo_urls as $index => $photo_url) {
        $media[] = [
            'type' => 'photo',
            'media' => $photo_url
        ];
    }
    
    $params = [
        'chat_id' => $chat_id,
        'media' => json_encode($media)
    ];
    
    return send_telegram_request($url, $params);
}

/**
 * Отправляет запрос к Telegram API
 */
function send_telegram_request($url, $params) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $params);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return ['response' => $response, 'http_code' => $http_code];
}
```

### 3. Настройка CRON задачи

Для автоматического запуска скрипта создайте задачу CRON:

```bash
# Запуск каждый час
0 * * * * /usr/bin/php /var/www/www-root/data/www/indreamsphuket.com/telegram_property_bot.php >> /var/www/www-root/data/www/indreamsphuket.com/telegram_bot_cron.log 2>&1
```

Можно настроить через панель управления хостингом или напрямую через команду `crontab -e`.

## Адаптация скрипта для специальных требований

### 1. Отправка сообщений при создании объектов недвижимости

Для отправки уведомлений сразу после создания объекта можно добавить хук в функцию сохранения:

1. Откройте файл `sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Domain/PhuketPropertyAR.php`
2. Найдите метод `afterSave()` или `save()`
3. Добавьте код для вызова функции отправки сообщения:

```php
/**
 * Выполняется после сохранения объекта
 */
public function afterSave() {
    parent::afterSave();
    
    // Проверяем, новый ли это объект (а не обновление)
    if ($this->isNewRecord) {
        // Отправляем уведомление в Telegram
        $this->sendTelegramNotification();
    }
}

/**
 * Отправляет уведомление в Telegram
 */
protected function sendTelegramNotification() {
    // Загружаем полный объект для доступа ко всем данным
    $propertyDto = \Site45\Sets\Phuket\Query\PropertySearch\PhuketPropertyQuery::create(['id' => $this->id])
        ->getFullPropertyDto();
    
    // Формируем сообщение
    $message = $this->formatTelegramMessage($propertyDto);
    
    // Отправляем уведомление
    $this->sendTelegramMessage($message);
    
    // Отправляем фото, если есть
    if (!empty($propertyDto->photos)) {
        $photo_url = s45_imgSrcR($propertyDto->photos[0], S45_IMST_1900X1000_SCALE);
        $this->sendTelegramPhoto($photo_url);
    }
}
```

### 2. Настройка формата сообщений

Вы можете настроить формат сообщений в функции `format_property_message()`, например:

```php
function format_property_message($property) {
    // ... существующий код ...
    
    // Добавим теги и категории
    if (!empty($propertyDto->tags)) {
        $tags = [];
        foreach ($propertyDto->tags as $tag) {
            $tags[] = "#{$tag->id}";
        }
        $message .= "\n" . implode(' ', $tags);
    }
    
    // Добавим информацию о возврате инвестиций
    if ($propertyDto->isInvest && $propertyDto->garantDohod) {
        $message .= "\n💹 ROI: <b>{$propertyDto->garantDohod}%</b>";
        if ($propertyDto->garantDohodLet) {
            $message .= " на <b>{$propertyDto->garantDohodLet}</b> " . 
                ($propertyDto->garantDohodLet == 1 ? "год" : "лет");
        }
    }
    
    return $message;
}
```

### 3. Добавление кнопок и интерактивных элементов

Telegram поддерживает inline-кнопки, которые можно добавить к сообщению:

```php
function send_telegram_message_with_buttons($bot_token, $chat_id, $message, $property_id) {
    $url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    // Создаем кнопки
    $keyboard = [
        'inline_keyboard' => [
            [
                [
                    'text' => '🔍 Подробнее',
                    'url' => s45_path_url('property/' . $property_id)
                ],
                [
                    'text' => '💬 Задать вопрос',
                    'url' => 'https://t.me/indreams_support_bot'
                ]
            ]
        ]
    ];
    
    $params = [
        'chat_id' => $chat_id,
        'text' => $message,
        'parse_mode' => 'HTML',
        'disable_web_page_preview' => false,
        'reply_markup' => json_encode($keyboard)
    ];
    
    return send_telegram_request($url, $params);
}
```

## Мониторинг и отладка

### Журналирование

Добавьте подробное журналирование для отслеживания работы бота:

```php
function log_message($message, $level = 'info') {
    $log_file = __DIR__ . '/telegram_bot.log';
    $date = date('Y-m-d H:i:s');
    $log_entry = "[$date] [$level] $message\n";
    file_put_contents($log_file, $log_entry, FILE_APPEND);
}
```

### Ограничения API Telegram

- Не более 30 сообщений в секунду в группы и каналы
- Не более 20 сообщений в минуту в приватные чаты
- Максимальный размер сообщения: 4096 символов
- Максимальное количество медиа в одной группе: 10

### Советы по оптимизации

1. Добавьте задержку `sleep(2)` между отправками сообщений
2. Ограничьте длину описания до 800-1000 символов
3. Оптимизируйте изображения перед отправкой
4. Используйте медиагруппы для отправки нескольких фото

## Заключение

Интеграция Telegram-бота с сайтом InDreams Phuket позволит автоматически публиковать новые объекты недвижимости в канале Telegram, что повысит видимость объектов и упростит информирование клиентов о новых предложениях.

При необходимости можно дополнительно расширить функциональность бота, добавив:
- Интерактивные опросы и формы
- Фильтрацию объектов по параметрам
- Отправку уведомлений о снижении цен
- Интеграцию с CRM-системами 

## Архитектура и план разработки Telegram-бота для InDreams Phuket

## 1. Архитектура решения

### 1.1. Многоуровневая архитектура системы

```
┌───────────────────┐      ┌───────────────────┐      ┌───────────────────┐
│                   │      │                   │      │                   │
│  Сайт на Drupal   │◄────►│  PHP-сервис бота  │◄────►│  Telegram API     │
│  (источник данных)│      │  (обработчик)     │      │  (целевая система)│
│                   │      │                   │      │                   │
└───────────────────┘      └───────────────────┘      └───────────────────┘
        ▲                           ▲                           ▲
        │                           │                           │
        ▼                           ▼                           ▼
┌───────────────────┐      ┌───────────────────┐      ┌───────────────────┐
│                   │      │                   │      │                   │
│   MySQL/MariaDB   │      │   Файловая система│      │   Telegram канал  │
│   (хранение)      │      │   (логи, кэш)     │      │   (публикация)    │
│                   │      │                   │      │                   │
└───────────────────┘      └───────────────────┘      └───────────────────┘ 