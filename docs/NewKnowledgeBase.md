# New Knowledge Base
- Server date settings can affect sitemap dates and should be checked when generating files
- Using CLI context for sitemap generation may not have access to HTTP_HOST server variables
- Hardcoding critical values for sitemap generation is more reliable than relying on environment variables
- URL patterns with /s45/ prefix should be normalized for better SEO performance
- XML sitemap segmentation by content type and language improves Google's crawl efficiency
- Using sitemap index format allows better organization of large sites
- Including image tags in sitemaps increases chances of appearing in image search results

This file documents new insights and knowledge gained about the codebase during development.

## 2024-03-27

### Drupal-специфичные знания
- Функция `s45_lang()` требует передачи параметров по ссылке (&$langObject), и не может принимать литералы массивов напрямую
- Для объектов перевода нужно использовать класс `LangVO`, который можно инициализировать напрямую через `new LangVO()`
- При создании языковых переменных лучше присваивать значения отдельным свойствам объекта вместо использования статического метода `LangVO::create()`
- Ошибка "Cannot pass parameter 1 by reference" часто возникает из-за попытки передать литерал массива по ссылке
- Модуль YouTube интеграции требует параметров enablejsapi и origin для корректной работы API

### YouTube и интеграция видео
- Видео YouTube требуют параметра `enablejsapi=1` для получения событий с iframe
- Безопасная интеграция с YouTube требует указания `origin` параметра, содержащего домен сайта
- Для обработки событий YouTube необходим безопасный обработчик `postMessage` с проверкой источника
- События `onStateChange` можно использовать для отслеживания воспроизведения, паузы и завершения видео

### Переключение валют
- Система валют site45 использует атрибут `data-currency-id` для идентификации элементов
- Для мультивалютности нужно иметь согласованные обработчики во всех компонентах
- Использование общего JavaScript-файла с функциями конвертации валют повышает согласованность отображения
- Стилизация валютных переключателей должна соответствовать общему дизайну сайта

### Решение проблем с циклическими запросами
- Скрипт image-tester.js может создавать циклические запросы при неверной конфигурации
- При отладке сетевых запросов важно использовать фильтры в инструментах разработчика
- Скрипты должны иметь механизм прекращения запросов при определенных условиях

### Модальные окна и формы
- При создании стилизованных форм можно использовать функцию `drupal_get_form()` с последующей настройкой через массив `$form`
- Для форм логина используются атрибуты `#prefix` и `#suffix` для добавления HTML-обертки
- Атрибуты формы и кнопок настраиваются через массивы `#attributes['class'][]`
- CSS-анимации повышают пользовательский опыт при взаимодействии с модальными окнами

## 2024-05-14

### Blog and Article Component Structure
- В компонентах PhuketArticleAboutD2 и PhuketBlogAboutD2 весь текстовый контент помещается в блок с классом `blog-desc__txt`
- По умолчанию все заголовки в тексте статей (h1, h2) конвертируются в h3, что делает все заголовки одинакового размера
- В файлах PhuketArticleAboutD2.tpl.php и PhuketBlogAboutD2.tpl.php содержится код для замены заголовков:
  ```php
  $descr = str_replace('h1', 'h3', $descr);
  $descr = str_replace('H1', 'h3', $descr);
  $descr = str_replace('h2', 'h3', $descr);
  ```
- Функция strip_tags ограничивает разрешенные теги, создавая проблему при добавлении новых типов элементов в контент
- Для исправления иерархии заголовков нужно отключить эту замену и добавить стили для заголовков всех уровней
- Общие стили для заголовков находятся в файле styles.min.css с медиа-запросами для разных разрешений экрана
- Для переопределения стилей заголовков в статьях можно создать отдельный CSS файл с высоким весом в drupal_add_css

## 2023-03-18

### Cloudflare and Search Engine Bots
- Cloudflare's Bot Fight Mode can block legitimate search engine crawlers like Googlebot
- Cloudflare security features (WAF, rate limiting) can return 403 errors for legitimate bot traffic
- Cloudflare API can be used to programmatically create firewall rules and page rules
- Google's crawlers can be verified by checking their ASN (Autonomous System Number) which includes 15169, 16550, etc.
- Cloudflare page rules with "Cache Level: Bypass" and "Security Level: Essentially Off" can be applied to specific URL patterns
- Cloudflare identifies bots using User-Agent strings, IP address reputation, and behavioral patterns
- Multiple search engines require different handling (Google, Bing, Yandex, Baidu)
- Troubleshooting 403 errors requires checking both Cloudflare settings and server configuration
- Sitemap accessibility is critical for proper search engine indexing across all site languages
- Proper sitemap configuration includes both robots.txt entries and direct notification to search engines

## 2025-03-18

### Sitemap Generation System
- The site uses a custom sitemap generation system in `s45_phuket_sitemap.inc`
- Sitemaps are generated per language with the language code in the filename (e.g., sitemap_en.xml)
- The generator uses `$GLOBALS['language']->language` to determine which language sitemap to create
- There are two ways to generate sitemaps:
  - Web interface via `/sitemapgen?key=qqww33` with the current site language
  - CLI script by setting `$GLOBALS['language']->language` manually
- The `PhuketSitemap` class handles the actual generation for each content type
- Important server variables needed for CLI generation: `$_SERVER['REMOTE_ADDR']` and `$_SERVER['HTTP_HOST']`
- The sitemap includes hreflang tags for all languages (ru, en, th, zh)
- Content types included in the sitemap: properties, locations, news, articles, services, boats, excursions, projects, and static pages
- Dynamic URLs (like search pages) are limited to 40,000 entries per sitemap
- Chinese language uses 'zh-hans' code internally but 'zh' in the sitemap
- Russian sitemap is typically larger due to more content in that language
- Server-side generation uses about 120MB of memory and takes 2-3 minutes per language
- All four language sitemaps (ru, en, th, zh) have similar structure but language-specific URLs
- The sitemap generation is designed to run as a cron job for regular updates
- A script to update all four language sitemaps sequentially helps maintain consistency
- The PhuketSitemap::getItem() method handles hreflang tag generation for all four languages

## 2024-05-10

### Git Repository Management
- The repository had issues with large SQL backup files (5.3GB) causing push failures
- Using `git filter-branch` helped remove large files from git history without losing recent commits
- The `.gitignore` file should exclude all backup files (*.sql) and other large binary files
- When updating `.gitignore`, it's important to remember that it doesn't affect files already tracked by git
- For files already committed that should be ignored, use `git rm --cached <file>` to stop tracking them
- Large repositories benefit from periodic garbage collection with `git gc --prune=now`
- Force pushing (`git push --force`) should be used carefully and only when necessary to rewrite history

## 2024-03-18

- The site uses a custom S45 module system for property management with specific SEO handling in s45_phuket_seo.inc
- Hreflang implementation is incomplete, missing x-default tags and proper language path handling
- Property content is structured differently across language versions, requiring standardization
- Site's caching strategy relies heavily on Drupal core caching rather than leveraging advanced modules like AdvAgg and Memcache
- Current XML sitemap configuration doesn't properly segment content by language
- Critical path analysis reveals that hreflang implementation and caching optimization have the highest impact-to-effort ratio for SEO improvements
- The multilingual structure of the site requires careful coordination between technical implementations and content strategies
- Resource allocation for SEO improvements needs to balance developer time (for technical fixes) with content specialist effort (for content optimization)
- Performance benchmarking before implementation is critical for measuring the impact of SEO changes
- The site would benefit from a phased approach to improvements, with technical fixes implemented before content enhancements

## 2024-04-28

### PDF Generation System
- The system uses mPDF library for PDF generation of property details
- PDF files are stored in directories named by property ID (e.g., `/files/site4/FileStore4/phuket/pdf/8283`)
- The filename pattern is `property-{display}-{propertyNumber}-{language}.pdf` where display can be "guest", "my", or "clean"
- Cached PDFs can cause outdated information to be displayed even when property data is updated
- The `s45_phuket_pdf.inc` file contains the main PDF generation function `s45_phuket_pdf()`
- The content is rendered using `s45_render('PhuketPdf', array(...))` which uses the PhuketPdf component
- Using HTTP 301 redirects can cause browsers to aggressively cache PDFs, causing stale content to be served
- Always delete existing PDF files before regeneration to ensure fresh content is shown
- Using 302 redirects instead of 301 prevents aggressive browser caching
- Adding timestamp parameter (?t=timestamp) to PDF URLs forces browsers to reload content instead of using cached version
- Both server-side caching (existing PDF files) and client-side caching (browser cache) need to be addressed for proper PDF refreshing

## PhuketScreens

- The screens displayed on street are loaded from URLs with pattern `/s45/PhuketScreens/[ids]/[type]/[args]`
- Each screen type (e.g., `buy_now`, `hot_sale`) has specific CSS styling in PhuketPropScreen.css
- Color variables are defined as CSS variables (--color-gold, --color-red, etc.)
- These screens benefit significantly from caching as they are static content displayed to potential customers

## Caching Implementation

- The site uses a multi-level caching approach:
  - Apache-level caching with Cache-Control headers
  - Drupal-level caching with hook_init for dynamic header control
  - Client-side caching with localStorage for offline or repeat access
- When implementing caching, it's important to differentiate between anonymous and authenticated users

## Site Structure and Architecture
- The site uses a custom component system based on classes in `sites/all/modules/__s45/`
- Different content types are managed in their own modules with a similar structure
- The s45_phuket module contains most of the custom functionality
- Database connections are abstracted through AR (Active Record) classes
- Domain configuration is managed in `sites/all/modules/__s45/s45_base/_repo/Sites.s45.json`
- New domains must be added to both sites.php and Sites.s45.json files

## Screen System
- The screens displayed on street are loaded from URLs with pattern `/s45/PhuketScreens/[ids]/[type]/[args]`
- Each screen type (e.g., `buy_now`, `hot_sale`) has specific CSS styling in PhuketPropScreen.css
- Screen URLs can be moved to a separate subdomain (screens.indreamsphuket.com) to prevent analytics pollution
- The screen system is designed to display property information in a format suitable for TV/display screens

## S45 System Knowledge Base

- Domain configuration is managed in `sites/all/modules/__s45/s45_base/_repo/Sites.s45.json`
- New domains must be added to both sites.php and Sites.s45.json files
- Primary module containing most custom functionality is `s45_phuket`
- Code Architecture structured as Components under S45\Sets
- Content types are managed through custom Component system
- Database connections are abstracted through Active Record classes
- Static files (CSS/JS) and screen assets must be enqueued through respective Content Types
- Yandex Maps static API can be used as a free alternative to Google Maps, without requiring an API key
- Yandex Static Maps supports marker customization with the `pt` parameter (format: `lon,lat,marker_type`)
- Static map implementations are faster and use less resources than interactive maps
## 2025-03-18 - Cloudflare and Search Engine Bot Access
- Cloudflare's Bot Fight Mode can block legitimate search engine crawlers
- Cloudflare firewall rules can be created specifically to allow search engine bots
- Google crawlers can be verified by checking their ASN numbers (15169, etc.)
- Page Rules can be created to bypass security checks for specific URL patterns

## 2025-03-18 - Cloudflare and Search Engine Bot Access
- Cloudflare's Bot Fight Mode can block legitimate search engine crawlers
- Cloudflare firewall rules can be created specifically to allow search engine bots
- Google crawlers can be verified by checking their ASN numbers (15169, etc.)
- Page Rules can be created to bypass security checks for specific URL patterns

## Website Structure

- The website is built on Drupal with custom modules for real estate functionality
- Custom modules are located in `sites/all/modules/__s45/s45_phuket/`
- The site supports multiple languages: Russian (ru), English (en), Thai (th), and Chinese (zh)

## Sitemap Implementation

- The site uses a custom sitemap implementation rather than standard Drupal XML Sitemap module
- Core sitemap functionality is managed by `PhuketSitemap.php` class in `sites/all/modules/__s45/s45_phuket/classes/Site45/Sets/Phuket/Funcs/`
- There are 31 sitemap XML files segmented by language and content type
- Sitemap generation is handled by separate PHP scripts (`update_sitemap.php`, `update_sitemap_ru.php`, etc.)
- The sitemaps are organized in a hierarchical structure with a main index file
- The namespace for images is declared in the sitemaps but not actively used in the XML output
- The system contains functionality to filter URLs based on robots.txt rules
- The sitemap generator supports various content types: properties, projects, articles, pages, locations, news, services, boats, excursions
- The `getPropertyImages()` method exists for supporting image sitemap entries but isn't being fully utilized

## Technical Infrastructure

- The site runs on nginx/1.20.2
- The system has a cron script capability but doesn't automatically update sitemaps
- The site doesn't currently ping search engines after sitemap updates
- The robots.txt file contains 120 disallow directives

## Sitemap System

- The custom sitemap implementation is optimized for multi-language support (ru, en, th, zh)
- The PhuketSitemap class has a built-in method for generating a sitemap index file
- The system already had image support in the getItem method, but it wasn't fully utilized
- Sitemap files are segmented by both language and content type
- The system handles alternate language links with hreflang attributes
- The validateSitemap method provides built-in validation capabilities
- The generateSitemapIndex method handles creating the master sitemap index file
- Sitemaps automatically include canonical and alternate language URLs for each page

## Image Processing

- Property images are loaded via field_get_items('node', $property, 'field_images')
- Property images are stored with title and alt attributes that can be used for image:title and image:caption
- Image URLs are automatically converted from http to https for security

## Cron and Automation

- The s45_phuket module supports Drupal's hook_cron implementation
- The system uses a variable tracking mechanism to prevent excessive updates
- The update_all_sitemaps.php script supports sequential updates of all language versions
- Cron operations use proper error handling and logging

## Search Engine Integration

- The Ping API endpoints for Google, Bing, and Yandex use different URL formats
- Successful ping responses return HTTP status codes in the 200-299 range
- The system can track ping success/failure for each search engine separately
- drupal_http_request function provides a reliable way to make API requests

## Performance Considerations

- Sitemap files can become quite large (50MB+) with all property listings
- The queryLimit parameter controls how many entries are included per content type
- Sitemap generation for large sites can take significant processing time
- Splitting sitemaps by content type and language improves maintainability

## JavaScript и Ajax

- JavaScript-компоненты поиска используют jQuery UI autocomplete для поисковых подсказок
- Убедитесь, что все запросы AJAX используют относительные пути вместо абсолютных доменов
- При переносе сайта или смене доменов проверяйте источники автозавершения в JavaScript
- Компоненты поиска расположены в нескольких местах: PhuketFrontHeader, PhuketFrontPromo и PhuketSearchForm
- Ошибки в консоли браузера, связанные с 404 для `/s45PhuketWordSearch` могут указывать на проблемы с доменами

## Статические страницы и формы

- Для создания статических HTML-страниц можно использовать прямое размещение в корне сайта
- Директория /docs/ создана для размещения интерактивных форм контрактов и документов
- Для обеспечения независимости таких страниц от Drupal необходимо использовать свои CSS и JavaScript
- Файл .htaccess помогает настроить корректную обработку запросов к статической директории
- Рекомендуется использовать относительные пути к ресурсам в HTML-файлах для совместимости с разными доменами

# Knowledge Base

This document contains important information and insights about the codebase.

## Images and Lazy Loading

- The site uses data-src and data-srcset attributes for lazy loading images, particularly in the Blazy module
- Lazy loading implementation is found in several modules, most notably in sites/all/modules/1blazy
- Property media component uses data-src for iframe lazy loading with Fancybox integration
- IntersectionObserver API can be used to enhance lazy loading performance on modern browsers

## Performance

- CloudFlare CDN is used for content delivery, but Rocket Loader feature can cause jQuery conflicts
- jQuery loading order is critical, especially for plugins like multiselect
- Critical CSS can be added using drupal_add_html_head with the #tag 'style' to improve initial render
- Font preloading with preload rel attribute can improve font loading performance
- Lazy loading images can dramatically improve page load time, especially for image-heavy pages

## JavaScript

- s45_Debug is a custom debugging utility used throughout the codebase
- jQuery stability is critical as many components depend on it
- Plugin conflicts can be mitigated by ensuring proper loading order and providing fallbacks
- jQuery multiselect is used for form elements and can break if not properly initialized

## Drupal Integration

- The site uses Drupal 7 with a custom theme and module structure
- AdvAgg module is used for CSS/JS aggregation but requires proper configuration
- Cloudflare integration requires proper API authentication (now using API tokens rather than keys)
- Cache clearing in Drupal should ideally also clear Cloudflare cache
- Custom modules are stored in sites/all/modules/custom

## Error Handling

- JavaScript errors related to undefined functions can be mitigated with fallback implementations
- Client-side error monitoring can help detect and report issues early
- Defensive coding practices like checking for existence before calling can prevent common errors

## Custom Components

- The codebase uses a component-based structure with module namespaces
- Custom S45 modules are prefixed with s45_ or stored in __s45 directory
- Property media components are implemented in custom templates with Fancybox integration

## Caching

- Multiple levels of caching exist: Drupal's internal caching, AdvAgg module, and CloudFlare CDN
- Clearing cache may require action at multiple levels
- Image styles should be properly configured for caching and optimization

## API Integration

- CloudFlare API now uses token-based authentication rather than email/key
- Proper error handling and logging should be implemented for API calls
- API responses should be checked for success before proceeding

## Code Organization

- The codebase follows a modular approach with custom modules
- Module settings are stored in variables table using variable_get/set functions
- Admin UI components follow Drupal's Form API structure

## Content Security Policy (CSP)

- CSP is more effective when implemented as an HTTP header rather than a meta tag, as meta tags must be in the document's <head> to be effective
- When working with Yandex.Metrika, it's important to explicitly allow connections to all Yandex domains (yandex.ru, yandex.net, mc.yandex.ru, yandexmetrica.com) in the CSP
- JavaScript APIs like fetch and XMLHttpRequest can be intercepted by other scripts, which can cause analytics tools like Yandex.Metrika to fail
- The Yandex.Metrika counter is available through window.Ya._metrika.counters when properly loaded
- For Drupal sites, using drupal_add_http_header() is the preferred way to set security headers like CSP

## Google Maps Integration
- Google Maps API keys should be carefully protected and restricted in the Google Cloud Console
- Replacing a Google Maps API key across multiple files requires careful search and replacement
- A protection layer that intercepts all API requests can help manage and replace API keys
- Strategies for API key replacement:
  - Intercept script creation (document.createElement)
  - Intercept attribute setting (element.setAttribute)
  - Intercept DOM operations (appendChild, insertBefore)
  - Scan existing elements periodically (via setInterval)
- The default maps location for Phuket is lat: 7.8804, lng: 98.3923
- The new Google Maps API key AIzaSyDTEXdJu8eCqJCK8FsJbQYcfhAklQ61QjU replaces the old one AIzaSyBYixChT1ElrnTQ4W5KgAdbnKIWrMJTbWo

## Google Maps User Consent
- Requiring explicit user confirmation before loading Google Maps API can significantly reduce API costs
- User consent can be stored in localStorage to persist across page visits
- A combination of CSS and JavaScript creates responsive map placeholders until user consent is granted
- Using MutationObserver API helps detect dynamically added map containers
- The system automatically applies user consent to all map instances once granted
- Control flow between user confirmation and map API:
  1. Set `_mapConfirmationActive = true` by default (no maps load)
  2. Check localStorage for previous consent
  3. Show placeholders with "Show Map" buttons for all map containers
  4. When user clicks button, set localStorage and load API only once
  5. Update all map containers based on the user action
- Creating event-based communication (`mapConsentGranted` event) provides loose coupling between components
- Using SVG icons in the CSS offers lightweight map indicators for placeholders
- DOM interception methods are used to block API loading until user consents
- Special handling for dynamic elements like "Посмотреть на карте" (View on map) buttons:
  1. Intercept click event at the capture phase to prevent default action
  2. Check for existing confirmation in localStorage before proceeding
  3. Show confirmation dialog if user hasn't confirmed yet
  4. On confirmation, manually load Google Maps API if not already loaded
  5. Use synthetic events and setTimeout to properly trigger original handlers 
  6. Track artificial events with custom properties to prevent infinite loops
  7. Different element types (spans, anchors) require different handling strategies
- The UserConfirmation object handles consent persistence and dialog presentation
- Adding artificial delay (setTimeout) ensures proper event sequence for map loading
- Implementing events like map_api_requested provides hooks for other components

## OpenStreetMap Integration
- OpenStreetMap can serve as a fallback option when Google Maps is unavailable
- Leaflet.js is a powerful library for rendering OpenStreetMap tiles
- A conditional loading mechanism can help decide whether to use Google Maps or OpenStreetMap
- Setting global flags like window._useOpenStreetMap and window._googleMapsDisabled helps control behavior
- Lazy-loading OpenStreetMap dependencies improves performance

## Script Blocking Techniques
- DOM API перехват позволяет блокировать загрузку проблемных скриптов
- Перехват методов document.createElement, element.appendChild и element.insertBefore предотвращает добавление нежелательных скриптов
- Проблемные скрипты можно идентифицировать по части URL или содержимому
- Многоуровневый подход включает:
  1. Блокировку при создании элемента script
  2. Блокировку при установке атрибута src
  3. Блокировку при добавлении скрипта в DOM
  4. Удаление уже существующих проблемных скриптов
  5. Перехват и подавление ошибок от проблемных скриптов
- Подход с MutationObserver помогает отслеживать динамически добавляемые скрипты
- Удаление объектов типа window.osp или window.SendPulse предотвращает их нежелательное выполнение
- Стратегия временной отсрочки (setTimeout) для повторной проверки помогает с асинхронно загружаемыми скриптами

## jQuery Protection
- jQuery protection should create backup copies of critical jQuery methods
- Monitoring window.jQuery for overwrites can catch when third-party scripts replace jQuery
- Creating fallback implementations for common plugins helps maintain site functionality
- Error handler interception can identify specific errors from Rocket Loader or other sources
- Multiple self-healing mechanisms:
  - Check at intervals if jQuery still exists
  - Monitor jQuery.fn.on and other critical methods
  - Restore from backups when needed
  - Create stub implementations when proper implementations are not available

## Мониторинг запросов к API

- **Performance Observer API** может использоваться для отслеживания и анализа сетевых запросов, включая их параметры, размер и время выполнения
- Для отслеживания запросов к API можно перехватывать стандартные методы XMLHttpRequest и fetch
- Google Maps API имеет разные типы запросов (maps, geocoding, places, directions и т.д.), каждый из которых следует отслеживать отдельно

## Особенности работы с Google Maps API

- API-ключи Google Maps строго привязаны к домену и могут быть заменены на лету
- Существует несколько типов запросов к Google Maps API, каждый из которых тарифицируется по-разному
- Для оптимизации расходов на Google Maps API важно отслеживать и анализировать типы и количество запросов

## Безопасность

- API-ключи следует защищать от несанкционированного использования путем ограничения домена и IP-адреса
- Модификация URL для подмены API-ключей позволяет защитить ключи от кражи и несанкционированного использования

# Новые знания о кодовой базе

## Верстка и фронтенд
- Сайт использует различные цветовые схемы для разных типов контента (--katya-color, --ksenia-color и т.д.)
- Существует поддержка для мобильных устройств, но требуется отдельная оптимизация для разных размеров экранов
- Файлы HTML могут содержать как чистый JavaScript, так и CSS для быстрой разработки малых интерфейсов

## Оптимизации для мобильных устройств
- Для iPhone 16 Pro Max и подобных устройств с большими экранами требуется особая адаптация интерфейса
- Использование медиа-запросов с ориентацией на конкретные диапазоны размеров экрана (390px-430px) дает лучшие результаты
- Детектирование типа устройства через JavaScript и добавление соответствующих классов к DOM улучшает пользовательский опыт

## Производительность
- Задержки в анимациях могут улучшить восприятие пользовательского интерфейса
- Использование классов CSS для показа/скрытия элементов работает лучше, чем прямое манипулирование стилями через JavaScript
- Кэширование данных в localStorage используется для сохранения авторизации пользователя

## Архитектура сайта
- Сайт использует компонентный подход к разработке
- Модули разделены по функциональности и структурированы в разные директории
- Для обработки запросов используется модульная структура с обработчиками на стороне сервера

## Архитектура нового дизайна сайта

### Компоненты с суффиксом D2
1. Компоненты с суффиксом D2 (например, PhuketGalleryD2, PhuketPropMediaD2) созданы специально для новой темы дизайна и используют обновленную структуру HTML и CSS классы.
2. При создании новых компонентов для нового дизайна рекомендуется также использовать суффикс D2 и брать за основу существующие D2-компоненты.

### Обработка ошибок загрузки изображений
1. Все изображения в D2-компонентах должны иметь атрибут `onerror` для замены на placeholder в случае ошибки. Стандартный код для этого:
   ```php
   onerror="this.onerror=null;this.src='/sites/all/modules/__s45/Compo/Common/PhuketGallery/img/placeholder.jpg';"
   ```

2. Для полноразмерных изображений в jBox также требуется обработка ошибок, которая реализуется через JavaScript-код:
   ```javascript
   var img = new Image();
   img.onerror = function() {
     // Показать placeholder вместо оригинального изображения
   };
   img.src = originalUrl;
   ```

### Стили изображений в новом дизайне
1. `S45_IMST_1900X1000_SCALE` - основной стиль для полноразмерных изображений в лайтбоксе
2. `S45_IMST_600X600_CROP` - стиль для миниатюр в галереях и карточках

### Интеграция с библиотекой jBox
1. jBox используется для отображения увеличенных изображений и требует вызова `s45_add_jbox()` в начале шаблона
2. Атрибут `data-jbox-image` используется для группировки изображений в галерее
3. При возникновении проблем с загрузкой в jBox рекомендуется реализовать предварительную проверку изображения через JavaScript

## Beach Filtering System
- The PhuketOptionQuery class handles beach (subLocality) filtering in the search interface
- The hideEmptyBeaches property controls whether beaches with few properties are hidden
- Property counts are calculated dynamically using the _phuket_Property table
- Beaches with fewer than 4 properties are filtered out when hideEmptyBeaches is true
- The filtering happens in the exec() method before returning search results
- The system preserves the beach data in the database while only hiding it from search results

## JavaScript Error Handling
- Swiper слайдеры требуют правильного порядка инициализации
- Важно проверять наличие DOM элементов перед инициализацией
- Cloudflare Rocket Loader может влиять на порядок загрузки скриптов
- События слайдера должны корректно отключаться при уничтожении
- Логирование помогает отслеживать жизненный цикл компонентов

## Галерея изображений
- В компоненте `PhuketPropertySliderD2` реализовано две версии галереи: плиточная и полноэкранная
- Плиточная галерея теперь поддерживает навигацию с помощью стрелок для переключения фото
- Для добавления навигационных элементов использован JavaScript, который динамически встраивает элементы в DOM
- Стрелки навигации используют те же SVG-иконки, что и другие элементы интерфейса
- Анимация смены изображений реализована с помощью CSS-переходов

# База знаний по проекту

## Архитектура модулей s45

- Модульная система s45 имеет четкую иерархию, где s45_base является фундаментальным модулем, на котором построены все остальные
- Система использует собственную компонентную архитектуру, где компоненты хранятся в структуре `Site45/Sets/[SetName]/Compo/`
- Для хранения данных используется комбинация JSON-файлов и таблиц Drupal
- Модуль s45_phuket содержит более 140 компонентов, разделенных по функциональным категориям
- Каждый компонент включает PHP-класс, TPL-шаблон, JS и CSS файлы
- Административный интерфейс построен на базе 42 специальных компонентов в директории Admin
- Для работы с сущностями используется собственный слой запросов, а не Entity API Drupal

## Многоязычность

- Система использует собственный механизм многоязычного контента через объекты LangVO
- Функция s45_lang() является ключевой для получения текста на нужном языке
- Поддерживаются русский, английский, китайский и тайский языки
- Для китайского и тайского языков реализована специальная обработка при отсутствии перевода

## Паттерны проектирования

- Модуль s45_base использует паттерны Repository, Factory, DTO и VO
- PageId - ключевой механизм для контекстной работы компонентов в рамках страницы
- Компоненты используют шаблон Composite для построения интерфейса
- Store является реализацией паттерна State для хранения состояния между запросами

## Интеграция с Drupal

- Система использует xautoload для загрузки классов с пространствами имен
- hook_menu реализует все маршруты системы
- Для работы с компонентами используется специальный API через CompoApi2
- Шаблоны TPL встраиваются в тему Drupal через собственный механизм рендеринга

## Миграция на Drupal 10

- Для миграции потребуется переход с PHP 5.4 на PHP 8.1+ со строгой типизацией
- TPL-шаблоны должны быть преобразованы в Twig-шаблоны
- Компонентная система может быть преобразована в плагины Drupal 10
- Хуки Drupal 7 должны быть заменены на сервисы и подписчики событий Drupal 10
- Для поддержания текущей функциональности нужно использовать Paragraphs, Layout Builder и Block Types
- Наиболее сложная часть миграции - трансформация запросов из модуля Query в Entity API

## Сторонние библиотеки

- Модуль s45_vendor централизованно управляет всеми внешними библиотеками
- В проекте используются Bootstrap 4.4.1, jQuery UI, Vue.js, Select2 и другие библиотеки
- При миграции на Drupal 10 все библиотеки должны быть обновлены до совместимых версий
- Система использует собственный механизм подключения библиотек вместо Libraries API

## 2024-05-12

### Performance Optimization
- The custom component system in s45 can benefit greatly from static caching for frequently used components
- Performance bottlenecks are primarily in database queries and component rendering
- The s45_base module acts as a central hub for all component operations, making it a strategic target for optimization
- Custom API requests lack effective caching mechanisms, leading to redundant processing

### Security Architecture
- User input validation is inconsistent across the codebase, with direct usage of $_POST variables in some places
- The site's file handling system could benefit from isolating user-uploaded content
- API endpoints lack proper authentication and authorization mechanisms
- Security logging is minimal and doesn't provide sufficient information for threat analysis

### System Architecture
- The CustomAPI2 class serves as the backbone for component interactions
- The s45 system relies heavily on JSON storage for configuration, which impacts performance
- The PDF generation system executes synchronously, causing potential timeouts on large properties
- Query classes could benefit from implementing an ORM-like pattern with standardized caching

## 2025-03-27: Image Error Handling and Prevention

1. The Swiper slider library continuously attempts to reload images during updates, even when they've previously failed to load.

2. Implementing a global tracking system for failed image URLs prevents repeated error requests:
   ```javascript
   Drupal.behaviors.missingImageHandler = {
     // Store failed image URLs to prevent repeated attempts
     failedUrls: {},
     
     // Process all images on the page
     attach: function (context, settings) {
       // Implementation details...
     }
   };
   ```

3. Overriding the Swiper.prototype.update method allows for intercepting image loading attempts and replacing them with placeholders before errors occur:
   ```javascript
   // Save original Swiper update method
   const originalUpdate = Swiper.prototype.update;
   
   // Override update method
   Swiper.prototype.update = function() {
     const result = originalUpdate.apply(this, arguments);
     // Check and replace images with placeholders as needed
     return result;
   };
   ```

4. Adding the HTML loading="lazy" attribute to images significantly reduces initial page load requests and improves performance.

## 2025-03-27: Патчинг jBox для предотвращения ошибок с невалидными изображениями

### Проблема
При использовании jBox.Image для отображения галереи изображений возникает ошибка `TypeError: Cannot read properties of undefined (reading 'length')` в методе `showImage`. Это происходит, когда пользователь нажимает на изображение для просмотра, но внутренние данные галереи не были правильно инициализированы.

### Причина
Ошибка вызвана тем, что jBox пытается получить доступ к свойству `length` объекта, который является `undefined`. Это может происходить в следующих случаях:
1. Изображение не загрузилось корректно, но атрибут `data-jbox-image` остался
2. В галерее не инициализирован массив изображений
3. При навигации по галерее происходит обращение к несуществующим элементам

### Решение

1. **Патч метода showImage**: Заменяем оригинальный метод jBox.Image.showImage на обертку, которая проверяет наличие всех необходимых объектов:

```javascript
if (typeof jBox !== 'undefined' && jBox.prototype.showImage) {
    const originalShowImage = jBox.prototype.showImage;
    jBox.prototype.showImage = function(img) {
        try {
            // Проверяем наличие необходимых данных перед вызовом оригинального метода
            if (img !== 'open' && (!this.currentImage || !this.currentImage.gallery || 
                !this.images || !this.images[this.currentImage.gallery])) {
                console.warn('jBox.Image: Missing gallery information, cannot show image');
                return;
            }
            
            // Вызываем оригинальный метод
            originalShowImage.call(this, img);
        } catch (error) {
            console.error('Error in jBox.Image.showImage:', error);
        }
    };
}
```

2. **Правильная инициализация изображений**: Перед созданием галереи jBox проверяем, что изображения валидны:

```javascript
function initializeGalleryImages() {
    const galleryImages = document.querySelectorAll('.objectCard-slider img, .modalGallery-slider img');
    
    galleryImages.forEach(function(img) {
        if (img.src && img.complete && img.naturalWidth > 0) {
            img.setAttribute('data-jbox-image', 'gallery1');
            img.onerror = function() {
                this.removeAttribute('data-jbox-image');
                this.src = '/путь/к/плейсхолдеру.jpg';
            };
        }
    });
}
```

3. **Дополнительные проверки в jBox**: Добавление обработчиков событий для отслеживания ошибок:

```javascript
new jBox('Image', {
    // ... остальные настройки ...
    onInit: function() {
        console.log('jBox gallery initialized');
    },
    onOpen: function() {
        console.log('jBox image opened');
    },
    onError: function() {
        console.error('jBox image error occurred');
    }
});
```

### Вывод
Этот подход позволяет избежать ошибок JavaScript без модификации исходных файлов библиотеки. Патч должен быть добавлен в основной файл JavaScript сайта и выполняться перед инициализацией галереи jBox.

## Валюты

На сайте используются два механизма переключения валют:
1. Основной переключатель - компонент `PhuketMainMenuD2` в шапке сайта, который управляет глобальным состоянием валюты.
2. Локальные переключатели - например, в карточке объекта, которые должны быть синхронизированы с основным.

Для обеспечения синхронизации используется система событий:
- При изменении основной валюты генерируется событие `currency_changed` с данными о новой валюте
- Локальные переключатели подписаны на это событие и обновляют своё состояние
- Локальные переключатели при клике программно вызывают клик на соответствующем элементе основного переключателя

Ключевые данные:
- Элементы основного переключателя имеют атрибуты `data-currency-id`, `data-currency-rate` и `data-currency-current`
- Локальные переключатели используют атрибут `data-currency` и класс `active` для отображения текущей валюты

## Architecture & System Design
- The property photos download system uses both client-side JavaScript and a standalone PHP script instead of a Drupal menu callback, allowing for direct file streaming without Drupal's overhead
- The system architecture follows a clean-architecture pattern with domain objects (PhuketPropertyAR) that can be loaded via their IDs

## Debugging & Troubleshooting
- При скачивании файлов через iframe в Drupal веб-браузер может блокировать скачивание, если заголовки HTTP не соответствуют ожидаемым или если текущий пользователь не имеет разрешений
- Для работы с большими наборами изображений лучше использовать потоковую передачу данных и обработку chunk-ами
- Функция ob_clean() помогает избежать проблем с лишними данными в ответе при скачивании файлов
- Слишком строгая проверка HTTP_REFERER может блокировать законные запросы из административной панели
