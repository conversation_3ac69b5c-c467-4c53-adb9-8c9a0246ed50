# Интеграция Telegram-бота с сайтом InDreams Phuket (Часть 2)

## Реализация Telegram-бота

### 1. Создание бота в Telegram

1. Откройте Telegram и найдите BotFather (@BotFather)
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания бота
4. Получите и сохраните токен бота (API key)
5. Создайте канал или группу, где будут публиковаться объявления
6. Добавьте бота в канал/группу как администратора

### 2. Скрипт для публикации объектов

Создайте PHP-скрипт `telegram_property_bot.php` в корневой директории сайта:

```php
<?php
define('DRUPAL_ROOT', getcwd());

// Настройка окружения Drupal
$_SERVER['HTTP_HOST'] = 'indreamsphuket.com';
$_SERVER['REMOTE_ADDR'] = '127.0.0.1';
$_SERVER['HTTP_USER_AGENT'] = 'console';
$GLOBALS['base_url'] = 'https://indreamsphuket.com';

require_once DRUPAL_ROOT . '/includes/bootstrap.inc';
drupal_bootstrap(DRUPAL_BOOTSTRAP_FULL);

// Конфигурация Telegram
$bot_token = 'YOUR_BOT_TOKEN';
$channel_id = '@your_channel';  // ID канала или группы

// Период времени для поиска новых объектов (в часах)
$hours_back = 24;

// Получаем новые объекты
$new_properties = get_new_properties($hours_back);
$count = count($new_properties);

echo "Найдено $count новых объектов\n";

foreach ($new_properties as $property) {
    $message = format_property_message($property);
    $photos = get_property_photos($property);
    
    if (!empty($photos)) {
        // Отправляем первое фото с текстом
        send_telegram_photo($bot_token, $channel_id, $photos[0], $message);
        
        // Опционально: отправляем остальные фото без текста (не более 10)
        $additional_photos = array_slice($photos, 1, 9);
        if (!empty($additional_photos)) {
            send_telegram_media_group($bot_token, $channel_id, $additional_photos);
        }
    } else {
        // Если нет фото, отправляем только текст
        send_telegram_message($bot_token, $channel_id, $message);
    }
    
    // Добавляем паузу, чтобы избежать ограничений API
    sleep(2);
}

// Логируем результат
$log_message = date('Y-m-d H:i:s') . " - Отправлено $count объектов в Telegram\n";
file_put_contents(__DIR__ . '/telegram_bot.log', $log_message, FILE_APPEND);
```

### 3. Основные функции скрипта

Добавьте следующие функции в скрипт:

```php
/**
 * Получает новые объекты недвижимости
 */
function get_new_properties($hours_back) {
    $timestamp = time() - ($hours_back * 3600);
    
    $query = db_select('_phuket_Property', 'p')
        ->fields('p')
        ->condition('p.published', 1)
        ->condition('p.created', $timestamp, '>=')
        ->condition('p.isSaled', 0)
        ->orderBy('p.created', 'DESC');
    
    $result = $query->execute();
    $properties = [];
    
    foreach ($result as $row) {
        $properties[] = $row;
    }
    
    return $properties;
}

/**
 * Форматирует сообщение для Telegram
 */
function format_property_message($property) {
    $propertyDto = unserialize($property->propertyDto);
    $feed_lang = 'ru'; // или 'en', 'th', 'zh-hans'
    
    // Получаем основные данные
    $title = $propertyDto->name->{$feed_lang};
    $description = '';
    if (!empty($propertyDto->description->{$feed_lang})) {
        $description = $propertyDto->description->{$feed_lang};
    } elseif (!empty($propertyDto->project->text->{$feed_lang})) {
        $description = $propertyDto->project->text->{$feed_lang};
    }
    $description = clean_description($description);
    $description = mb_substr($description, 0, 800) . (mb_strlen($description) > 800 ? '...' : '');
    
    // Определяем тип сделки и цену
    $deal_type = $propertyDto->dealType->id == 'sale' ? 'Продажа' : 'Аренда';
    $price = $propertyDto->dealType->id == 'sale' ? 
        format_price($property->price_sale) : 
        format_price($property->price_rent);
        
    // Определяем тип недвижимости
    $property_type = get_property_type($propertyDto->propertyType->id, $feed_lang);
    
    // Создаем сообщение
    $message = "🏡 <b>{$title}</b>\n\n";
    $message .= "💰 <b>{$price}</b> - {$deal_type}\n";
    $message .= "🏘 <b>{$property_type}</b>\n";
    $message .= "🛏 Спальни: <b>{$propertyDto->bedrooms}</b>\n";
    $message .= "🛁 Ванные: <b>{$propertyDto->bathrooms}</b>\n";
    
    if ($propertyDto->areaCommon) {
        $message .= "📏 Площадь: <b>{$propertyDto->areaCommon} м²</b>\n";
    }
    
    if ($propertyDto->areaPlot) {
        $message .= "🏞 Участок: <b>{$propertyDto->areaPlot} м²</b>\n";
    }
    
    if ($propertyDto->re_subLocality && $propertyDto->re_subLocality->name->{$feed_lang}) {
        $message .= "📍 Район: <b>{$propertyDto->re_subLocality->name->{$feed_lang}}</b>\n";
    }
    
    // Добавляем описание и ссылку
    $message .= "\n📝 {$description}\n\n";
    $message .= "🔗 <a href=\"" . s45_path_url('property/' . $propertyDto->id) . "\">Подробнее на сайте</a>\n";
    $message .= "🆔 {$propertyDto->number}";
    
    return $message;
} 