/**
 * Интерактивная форма договора - основные функции
 * <AUTHOR> Phuket
 * @version 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    initForm();
});

/**
 * Инициализация формы и обработчиков событий
 */
function initForm() {
    // Кнопка предпросмотра договора
    document.getElementById('previewBtn').addEventListener('click', previewContract);
    
    // Кнопка генерации PDF
    document.getElementById('generatePdfBtn').addEventListener('click', generatePDF);
    
    // Валидация числовых полей (только цифры и точка)
    document.getElementById('propertyPrice').addEventListener('input', function(e) {
        this.value = this.value.replace(/[^\d.]/g, '');
    });
    
    // Валидация телефона
    document.getElementById('buyerPhone').addEventListener('input', function(e) {
        this.value = this.value.replace(/[^\d+()-]/g, '');
    });
}

/**
 * Собирает данные формы и возвращает объект с ними
 * @returns {Object} Объект с данными формы
 */
function collectFormData() {
    return {
        buyerName: document.getElementById('buyerName').value,
        buyerPassport: document.getElementById('buyerPassport').value,
        buyerEmail: document.getElementById('buyerEmail').value,
        buyerPhone: document.getElementById('buyerPhone').value,
        propertyType: document.getElementById('propertyType').value,
        propertyAddress: document.getElementById('propertyAddress').value,
        propertyPrice: document.getElementById('propertyPrice').value,
        paymentTerms: document.getElementById('paymentTerms').value,
        additionalTerms: document.getElementById('additionalTerms').value
    };
}

/**
 * Проверяет заполнение обязательных полей формы
 * @param {Object} data - Данные формы
 * @returns {boolean} Результат проверки
 */
function validateFormData(data) {
    const requiredFields = [
        'buyerName', 'buyerPassport', 'buyerEmail', 'buyerPhone',
        'propertyType', 'propertyAddress', 'propertyPrice', 'paymentTerms'
    ];
    
    const emptyFields = requiredFields.filter(field => !data[field]);
    
    if (emptyFields.length > 0) {
        alert('Пожалуйста, заполните все обязательные поля');
        return false;
    }
    
    // Проверка формата email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.buyerEmail)) {
        alert('Пожалуйста, введите корректный email адрес');
        return false;
    }
    
    return true;
}

/**
 * Генерирует текст договора на основе данных формы
 * @param {Object} data - Данные формы
 * @returns {string} HTML-код договора
 */
function generateContractText(data) {
    // Преобразование типа недвижимости в текст
    let propertyTypeText = '';
    switch(data.propertyType) {
        case 'apartment': propertyTypeText = 'апартаментов'; break;
        case 'villa': propertyTypeText = 'виллы'; break;
        case 'house': propertyTypeText = 'дома'; break;
        case 'land': propertyTypeText = 'земельного участка'; break;
    }
    
    // Преобразование условий оплаты в текст
    let paymentTermsText = '';
    switch(data.paymentTerms) {
        case 'full': paymentTermsText = 'полная оплата единовременно'; break;
        case 'installment': paymentTermsText = 'оплата в рассрочку'; break;
        case 'mortgage': paymentTermsText = 'ипотечный кредит'; break;
    }
    
    // Форматирование текущей даты
    const today = new Date();
    const formattedDate = today.toLocaleDateString('ru-RU', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
    
    // Генерация HTML-кода договора
    return `
        <h3>ДОГОВОР КУПЛИ-ПРОДАЖИ</h3>
        <p>Таиланд, о. Пхукет</p>
        <p>Дата: ${formattedDate}</p>
        <p><strong>Покупатель:</strong> <span class="highlight">${data.buyerName}</span>, паспорт: <span class="highlight">${data.buyerPassport}</span></p>
        <p><strong>Контактная информация покупателя:</strong> Email: <span class="highlight">${data.buyerEmail}</span>, Тел: <span class="highlight">${data.buyerPhone}</span></p>
        <p><strong>Продавец:</strong> ООО "ИнДримс Пхукет", в лице генерального директора Иванова И.И., действующего на основании Устава</p>
        <p><strong>Предмет договора:</strong> Покупатель приобретает у Продавца <span class="highlight">${propertyTypeText}</span>, расположенного по адресу: <span class="highlight">${data.propertyAddress}</span>.</p>
        <p><strong>Стоимость:</strong> <span class="highlight">${data.propertyPrice}</span> USD (долларов США)</p>
        <p><strong>Условия оплаты:</strong> <span class="highlight">${paymentTermsText}</span></p>
        ${data.additionalTerms ? `<p><strong>Дополнительные условия:</strong> <span class="highlight">${data.additionalTerms}</span></p>` : ''}
        <p><strong>Подписи сторон:</strong></p>
        <p>Покупатель: _________________ / ${data.buyerName} /</p>
        <p>Продавец: _________________ / Иванов И.И. /</p>
    `;
}

/**
 * Обработчик клика по кнопке предпросмотра договора
 */
function previewContract() {
    // Собираем данные формы
    const data = collectFormData();
    
    // Проверяем данные
    if (!validateFormData(data)) {
        return;
    }
    
    // Получаем элементы превью
    const preview = document.getElementById('agreementPreview');
    const content = document.getElementById('agreementContent');
    
    // Генерируем текст договора
    const agreementText = generateContractText(data);
    
    // Вставляем текст договора в превью
    content.innerHTML = agreementText;
    preview.style.display = 'block';
    
    // Прокручиваем к превью
    preview.scrollIntoView({ behavior: 'smooth' });
}

/**
 * Обработчик клика по кнопке генерации PDF
 * В будущем будет интегрирован с библиотекой jsPDF
 */
function generatePDF() {
    alert('Функция создания PDF будет доступна в ближайшее время');
}

/**
 * Сохраняет данные формы в localStorage
 * @param {Object} data - Данные формы
 */
function saveFormData(data) {
    localStorage.setItem('contractFormData', JSON.stringify(data));
}

/**
 * Загружает данные формы из localStorage
 * @returns {Object|null} Данные формы или null, если данных нет
 */
function loadFormData() {
    const savedData = localStorage.getItem('contractFormData');
    return savedData ? JSON.parse(savedData) : null;
} 