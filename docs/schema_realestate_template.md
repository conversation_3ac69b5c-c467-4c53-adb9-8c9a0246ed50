# Schema.org RealEstateListing Implementation Guide for InDreams Phuket

This guide provides detailed instructions for implementing structured data using Schema.org's RealEstateListing markup on your Drupal 7 property listings to improve search visibility and enhance rich results in search engines.

## Table of Contents

1. [Benefits of Schema.org RealEstateListing Markup](#benefits-of-schemaorg-realestate-markup)
2. [Implementation Methods](#implementation-methods)
3. [Basic RealEstateListing Template](#basic-realestate-listing-template)
4. [Advanced Property Features Markup](#advanced-property-features-markup)
5. [Location and Neighborhood Markup](#location-and-neighborhood-markup)
6. [Property Availability and Price Markup](#property-availability-and-price-markup)
7. [Image Gallery and Floor Plan Markup](#image-gallery-and-floor-plan-markup)
8. [Integration with Drupal 7](#integration-with-drupal-7)
9. [Testing and Validation](#testing-and-validation)
10. [Implementation Checklist](#implementation-checklist)

## Benefits of Schema.org RealEstate Markup

Implementing structured data for your property listings provides several key benefits:

- Enhanced search results with rich snippets showing price, bedrooms, bathrooms, etc.
- Improved visibility in real estate-specific search queries
- Better indexing of property details by search engines
- Higher click-through rates from search results
- Potential for inclusion in specialized search features
- Better organization of property data for voice search results

## Implementation Methods

There are three primary methods for implementing Schema.org markup in Drupal 7:

### 1. JSON-LD (Recommended)

JSON-LD is the preferred method for implementing Schema.org markup as it separates the structured data from your HTML markup.

```php
/**
 * Implements hook_preprocess_node().
 */
function s45_phuket_preprocess_node(&$variables) {
  if ($variables['type'] == 'property' && $variables['view_mode'] == 'full') {
    // Generate JSON-LD for property listing
    $variables['jsonld'] = s45_phuket_generate_property_jsonld($variables['node']);
  }
}

/**
 * Add JSON-LD script to page head.
 */
function s45_phuket_html_head_alter(&$head_elements) {
  if (isset($GLOBALS['jsonld_property']) && !empty($GLOBALS['jsonld_property'])) {
    $head_elements['schema_realestate'] = array(
      '#type' => 'html_tag',
      '#tag' => 'script',
      '#attributes' => array('type' => 'application/ld+json'),
      '#value' => $GLOBALS['jsonld_property'],
    );
  }
}
```

### 2. Microdata

Microdata embeds Schema.org markup directly within your HTML elements.

```html
<div itemscope itemtype="https://schema.org/RealEstateListing">
  <h1 itemprop="name">3 Bedroom Villa with Sea View in Kamala</h1>
  <!-- More property details with microdata attributes -->
</div>
```

### 3. RDFa

RDFa is similar to Microdata but uses a different attribute syntax.

```html
<div vocab="https://schema.org/" typeof="RealEstateListing">
  <h1 property="name">3 Bedroom Villa with Sea View in Kamala</h1>
  <!-- More property details with RDFa attributes -->
</div>
```

## Basic RealEstateListing Template

Here's a comprehensive JSON-LD template for a basic property listing:

```json
{
  "@context": "https://schema.org",
  "@type": "RealEstateListing",
  "name": "3 Bedroom Sea View Villa in Kamala, Phuket",
  "description": "Luxurious 3-bedroom villa with panoramic sea views located in the prestigious area of Kamala, Phuket. Features private infinity pool, modern kitchen, and spacious living areas.",
  "url": "https://indreamsphuket.com/property/3-bedroom-sea-view-villa-kamala",
  "datePosted": "2023-05-15T08:00:00+07:00",
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "https://indreamsphuket.com/property/3-bedroom-sea-view-villa-kamala"
  },
  "image": [
    "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-exterior.jpg",
    "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-pool.jpg",
    "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-interior.jpg"
  ],
  "provider": {
    "@type": "RealEstateAgent",
    "name": "InDreams Phuket",
    "logo": {
      "@type": "ImageObject",
      "url": "https://indreamsphuket.com/sites/default/files/logo.png",
      "width": 180,
      "height": 60
    },
    "priceRange": "$$$$",
    "telephone": "+66 76 123 456",
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "123 Thepkrasattri Road",
      "addressLocality": "Phuket",
      "addressRegion": "Phuket",
      "postalCode": "83110",
      "addressCountry": "TH"
    },
    "url": "https://indreamsphuket.com"
  },
  "offers": {
    "@type": "Offer",
    "price": 25000000,
    "priceCurrency": "THB",
    "availability": "https://schema.org/InStock",
    "validFrom": "2023-05-15T08:00:00+07:00"
  }
}
```

## Advanced Property Features Markup

Enhance your basic listing with detailed property features:

```json
{
  "@context": "https://schema.org",
  "@type": "RealEstateListing",
  "name": "3 Bedroom Sea View Villa in Kamala, Phuket",
  
  "numberOfBedrooms": 3,
  "numberOfBathrooms": 4,
  "floorSize": {
    "@type": "QuantitativeValue",
    "value": 350,
    "unitCode": "MTK",
    "unitText": "Square Meters"
  },
  "lotSize": {
    "@type": "QuantitativeValue",
    "value": 800,
    "unitCode": "MTK",
    "unitText": "Square Meters"
  },
  "amenityFeature": [
    {
      "@type": "LocationFeatureSpecification",
      "name": "Swimming Pool",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Air Conditioning",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Outdoor Kitchen",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Garden",
      "value": true
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Parking",
      "value": "2 Cars"
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Security System",
      "value": true
    }
  ],
  "yearBuilt": 2022,
  "propertyType": "Villa",
  "constructionStatus": "Completed"
}
```

## Location and Neighborhood Markup

Add detailed location information to your property listing:

```json
{
  "@context": "https://schema.org",
  "@type": "RealEstateListing",
  "name": "3 Bedroom Sea View Villa in Kamala, Phuket",
  
  "address": {
    "@type": "PostalAddress",
    "streetAddress": "88 Moo 3",
    "addressLocality": "Kamala",
    "addressRegion": "Phuket",
    "postalCode": "83150",
    "addressCountry": "TH"
  },
  "geo": {
    "@type": "GeoCoordinates",
    "latitude": 7.956684,
    "longitude": 98.283901
  },
  "publicTransport": "15 minutes to Kamala Bus Station",
  "hasMap": "https://www.google.com/maps?q=7.956684,98.283901",
  
  "containedInPlace": {
    "@type": "Place",
    "name": "Kamala",
    "description": "Kamala is a laid-back beach area on Phuket's west coast, known for its relaxed atmosphere and beautiful beach.",
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": 7.9571,
      "longitude": 98.2825
    }
  },
  
  "amenityFeature": [
    {
      "@type": "LocationFeatureSpecification",
      "name": "Beach Proximity",
      "value": "10 minutes walk"
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Shopping",
      "value": "5 minutes drive to Kamala Shopping Center"
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Airport",
      "value": "30 minutes drive to Phuket International Airport"
    },
    {
      "@type": "LocationFeatureSpecification",
      "name": "Restaurants",
      "value": "Multiple restaurants within walking distance"
    }
  ]
}
```

## Property Availability and Price Markup

Provide detailed pricing information including special offers:

```json
{
  "@context": "https://schema.org",
  "@type": "RealEstateListing",
  "name": "3 Bedroom Sea View Villa in Kamala, Phuket",
  
  "offers": {
    "@type": "Offer",
    "price": 25000000,
    "priceCurrency": "THB",
    "priceValidUntil": "2023-12-31",
    "availability": "https://schema.org/InStock",
    "validFrom": "2023-05-15T08:00:00+07:00",
    "seller": {
      "@type": "RealEstateAgent",
      "name": "InDreams Phuket",
      "url": "https://indreamsphuket.com"
    },
    "itemOffered": {
      "@type": "Residence",
      "name": "3 Bedroom Sea View Villa",
      "numberOfRooms": 8
    }
  },
  
  "leaseLength": {
    "@type": "QuantitativeValue",
    "value": 30,
    "unitText": "years"
  },
  
  "specialOffer": {
    "@type": "Offer",
    "name": "Early Buyer Discount",
    "description": "10% discount for buyers who purchase before August 2023",
    "discount": "10%",
    "validFrom": "2023-05-15T00:00:00+07:00",
    "validThrough": "2023-07-31T23:59:59+07:00"
  }
}
```

## Image Gallery and Floor Plan Markup

Enhance your property listing with detailed image and floor plan information:

```json
{
  "@context": "https://schema.org",
  "@type": "RealEstateListing",
  "name": "3 Bedroom Sea View Villa in Kamala, Phuket",
  
  "image": [
    {
      "@type": "ImageObject",
      "contentUrl": "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-exterior.jpg",
      "name": "Villa Exterior",
      "description": "Front view of the villa with garden",
      "width": 1200,
      "height": 800
    },
    {
      "@type": "ImageObject",
      "contentUrl": "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-pool.jpg",
      "name": "Infinity Pool",
      "description": "Infinity pool with sea view",
      "width": 1200,
      "height": 800
    },
    {
      "@type": "ImageObject",
      "contentUrl": "https://indreamsphuket.com/sites/default/files/properties/villa-kamala-living.jpg",
      "name": "Living Room",
      "description": "Spacious living room with modern furniture",
      "width": 1200,
      "height": 800
    }
  ],
  
  "subjectOf": {
    "@type": "Map",
    "mapType": "VenueMap",
    "name": "Floor Plan",
    "url": "https://indreamsphuket.com/sites/default/files/floor-plans/villa-kamala-floorplan.pdf",
    "description": "Detailed floor plan of the 3 Bedroom Villa showing room layouts and dimensions"
  },
  
  "video": {
    "@type": "VideoObject",
    "name": "Villa Tour Video",
    "description": "Walkthrough video of the 3 Bedroom Sea View Villa in Kamala",
    "thumbnailUrl": "https://indreamsphuket.com/sites/default/files/videos/villa-kamala-thumbnail.jpg",
    "contentUrl": "https://indreamsphuket.com/sites/default/files/videos/villa-kamala-tour.mp4",
    "embedUrl": "https://www.youtube.com/embed/abcdefghijk",
    "uploadDate": "2023-05-20T08:00:00+07:00",
    "duration": "PT3M42S"
  }
}
```

## Integration with Drupal 7

Here's a comprehensive implementation for your Drupal 7 site:

### Step 1: Create Helper Function in Module

Add this function to your `s45_phuket_seo.inc` file:

```php
/**
 * Generate JSON-LD for a property node.
 *
 * @param object $node
 *   The property node object.
 *
 * @return string
 *   JSON-LD markup for the property.
 */
function s45_phuket_generate_property_jsonld($node) {
  // Basic property information
  $schema = array(
    '@context' => 'https://schema.org',
    '@type' => 'RealEstateListing',
    'name' => $node->title,
    'description' => field_get_items('node', $node, 'body')[0]['value'],
    'url' => url('node/' . $node->nid, array('absolute' => TRUE)),
    'datePosted' => date('c', $node->created),
    'mainEntityOfPage' => array(
      '@type' => 'WebPage',
      '@id' => url('node/' . $node->nid, array('absolute' => TRUE)),
    ),
  );
  
  // Add provider (real estate agency) info
  $schema['provider'] = array(
    '@type' => 'RealEstateAgent',
    'name' => 'InDreams Phuket',
    'logo' => array(
      '@type' => 'ImageObject',
      'url' => url('sites/all/themes/s45/logo.png', array('absolute' => TRUE)),
      'width' => 180,
      'height' => 60,
    ),
    'telephone' => '+66 76 123 456',
    'email' => '<EMAIL>',
    'address' => array(
      '@type' => 'PostalAddress',
      'streetAddress' => '123 Thepkrasattri Road',
      'addressLocality' => 'Phuket',
      'addressRegion' => 'Phuket',
      'postalCode' => '83110',
      'addressCountry' => 'TH',
    ),
    'url' => url('<front>', array('absolute' => TRUE)),
  );
  
  // Add property features
  $bedrooms = field_get_items('node', $node, 'field_bedrooms');
  if (!empty($bedrooms)) {
    $schema['numberOfBedrooms'] = (int) $bedrooms[0]['value'];
  }
  
  $bathrooms = field_get_items('node', $node, 'field_bathrooms');
  if (!empty($bathrooms)) {
    $schema['numberOfBathrooms'] = (int) $bathrooms[0]['value'];
  }
  
  $area = field_get_items('node', $node, 'field_area');
  if (!empty($area)) {
    $schema['floorSize'] = array(
      '@type' => 'QuantitativeValue',
      'value' => (int) $area[0]['value'],
      'unitCode' => 'MTK',
      'unitText' => 'Square Meters',
    );
  }
  
  $land_area = field_get_items('node', $node, 'field_land_area');
  if (!empty($land_area)) {
    $schema['lotSize'] = array(
      '@type' => 'QuantitativeValue',
      'value' => (int) $land_area[0]['value'],
      'unitCode' => 'MTK',
      'unitText' => 'Square Meters',
    );
  }
  
  // Add amenities
  $schema['amenityFeature'] = array();
  
  // Check for pool
  $has_pool = field_get_items('node', $node, 'field_pool');
  if (!empty($has_pool) && $has_pool[0]['value']) {
    $schema['amenityFeature'][] = array(
      '@type' => 'LocationFeatureSpecification',
      'name' => 'Swimming Pool',
      'value' => true,
    );
  }
  
  // Add other amenities as needed
  $amenities = field_get_items('node', $node, 'field_amenities');
  if (!empty($amenities)) {
    foreach ($amenities as $amenity) {
      $term = taxonomy_term_load($amenity['tid']);
      $schema['amenityFeature'][] = array(
        '@type' => 'LocationFeatureSpecification',
        'name' => $term->name,
        'value' => true,
      );
    }
  }
  
  // Add location data
  $location = field_get_items('node', $node, 'field_location');
  if (!empty($location)) {
    $location_term = taxonomy_term_load($location[0]['tid']);
    
    // Get coordinates (assuming you store lat/lng in fields)
    $lat = field_get_items('node', $node, 'field_latitude');
    $lng = field_get_items('node', $node, 'field_longitude');
    
    if (!empty($lat) && !empty($lng)) {
      $schema['geo'] = array(
        '@type' => 'GeoCoordinates',
        'latitude' => (float) $lat[0]['value'],
        'longitude' => (float) $lng[0]['value'],
      );
      
      $schema['hasMap'] = 'https://www.google.com/maps?q=' . $lat[0]['value'] . ',' . $lng[0]['value'];
    }
    
    $schema['containedInPlace'] = array(
      '@type' => 'Place',
      'name' => $location_term->name,
    );
    
    // Add address
    $schema['address'] = array(
      '@type' => 'PostalAddress',
      'addressLocality' => $location_term->name,
      'addressRegion' => 'Phuket',
      'addressCountry' => 'TH',
    );
  }
  
  // Add price information
  $price = field_get_items('node', $node, 'field_price');
  if (!empty($price)) {
    $schema['offers'] = array(
      '@type' => 'Offer',
      'price' => (int) $price[0]['value'],
      'priceCurrency' => 'THB',
      'availability' => 'https://schema.org/InStock',
      'validFrom' => date('c', $node->created),
    );
  }
  
  // Add images
  $images = field_get_items('node', $node, 'field_images');
  if (!empty($images)) {
    $schema['image'] = array();
    foreach ($images as $image) {
      $image_url = file_create_url($image['uri']);
      $schema['image'][] = $image_url;
    }
  }
  
  // Add property type
  $property_type = field_get_items('node', $node, 'field_property_type');
  if (!empty($property_type)) {
    $type_term = taxonomy_term_load($property_type[0]['tid']);
    $schema['propertyType'] = $type_term->name;
  }
  
  return json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);
}
```

### Step 2: Implement Preprocess Function

Add this to your module file or a preprocess include file:

```php
/**
 * Implements hook_preprocess_node().
 */
function s45_phuket_preprocess_node(&$variables) {
  if ($variables['type'] == 'property' && $variables['view_mode'] == 'full') {
    // Generate and store JSON-LD for property listing
    $jsonld = s45_phuket_generate_property_jsonld($variables['node']);
    $GLOBALS['jsonld_property'] = $jsonld;
  }
}
```

### Step 3: Implement Head Alter Hook

```php
/**
 * Implements hook_html_head_alter().
 */
function s45_phuket_html_head_alter(&$head_elements) {
  // Add JSON-LD to head if available
  if (isset($GLOBALS['jsonld_property']) && !empty($GLOBALS['jsonld_property'])) {
    $head_elements['schema_realestate'] = array(
      '#type' => 'html_tag',
      '#tag' => 'script',
      '#attributes' => array('type' => 'application/ld+json'),
      '#value' => $GLOBALS['jsonld_property'],
      '#weight' => 100,
    );
  }
}
```

## Testing and Validation

After implementing Schema.org markup, validate it using these tools:

1. [Google's Structured Data Testing Tool](https://search.google.com/test/rich-results)
2. [Schema.org Validator](https://validator.schema.org/)
3. [Structured Data Linter](http://linter.structured-data.org/)

Common validation errors to watch for:

- Missing required properties
- Invalid property values
- Incorrect data types
- Syntax errors in JSON-LD
- Nested objects not properly defined

## Implementation Checklist

Use this checklist to ensure complete implementation:

- [ ] Basic property information (name, description, URL)
- [ ] Property features (bedrooms, bathrooms, size)
- [ ] Price and availability information
- [ ] Location data (address, coordinates)
- [ ] Agent/agency information
- [ ] Property images (properly tagged)
- [ ] Property amenities
- [ ] Floor plans (if available)
- [ ] Video tours (if available)
- [ ] Special offers (if applicable)
- [ ] Validation with testing tools
- [ ] Integration with Drupal node template
- [ ] Cache clearing after implementation

## Advanced Implementation Tips

1. **Automatic Updates**: Ensure your schema data automatically updates when property details change.

2. **Schema Variations**: Create different schema templates for different property types:
   - For Sale properties
   - For Rent properties
   - New Developments
   - Commercial Properties

3. **Language Variants**: If you have multilingual content, ensure schema data matches the page language.

4. **Combined Schemas**: Consider combining RealEstateListing with other schema types for richer data:
   ```json
   {
     "@context": "https://schema.org",
     "@type": ["Product", "RealEstateListing"],
     "name": "3 Bedroom Villa",
     "offers": {
       "@type": "Offer",
       "price": 25000000,
       "priceCurrency": "THB"
     }
   }
   ```

5. **Performance Considerations**: For large sites with many properties, consider:
   - Caching generated schema data
   - Only including the most important properties
   - Using asynchronous loading for schema data

By following this implementation guide, you'll have comprehensive Schema.org RealEstateListing markup that will enhance your property listings' visibility in search engines and improve the likelihood of rich results. 