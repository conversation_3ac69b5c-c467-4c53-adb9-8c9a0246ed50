# Roo Project Rules for indreamsphuket.com (v3 - Optimized)

These rules guide development and interaction with the `indreamsphuket.com` codebase located in `/var/www/www-root/data/www/indreamsphuket.com`. This is a **Drupal 7** project with significant custom modules (`s45_*`), a custom UI component system (`Compo/`), Event Sourcing patterns, Read Models, and numerous root-level scripts. **Adherence to these specific rules is crucial due to the project's complexity, technical debt, and production status.**

## General Principles

*   **Clean Code:** Adhere to clean code standards. Write concise, readable, and maintainable code.
*   **Best Practices:** Always follow general programming best practices and Drupal 7 best practices.
*   **No Placeholders:** Implement all required functionality. Do not leave placeholders like `// TODO: Implement this` or placeholder methods (`ShouldDoSomething`). Provide complete code blocks; never use `// ... rest of the code ...` or similar omissions.
*   **Curly Braces:** Use curly braces `{}` for all conditional and loop constructs (`if`, `else`, `for`, `while`, etc.), even single-line ones.
*   **File Existence:** Always check for file existence before attempting to read from or write to them, unless using a tool guaranteed to handle creation (like `write_to_file`).
*   **Code Removal:** Do not remove existing code, comments, or commented-out code unless specifically requested or necessary for a refactoring task, and clearly explain the reason.

## Drupal 7 Specifics

*   **Drupal APIs:** Utilize Drupal 7's built-in APIs whenever possible (e.g., Form API, Database API, Cache API, Theme API, User API, Node API, Entity API, Queue API, Batch API). **Avoid bypassing core APIs with custom implementations unless absolutely necessary and well-justified.**
*   **Database:**
    *   **Use the Drupal 7 Database API (`db_select`, `db_insert`, `db_update`, `db_delete`, `db_query` with placeholders) for ALL database interactions.** Avoid direct SQL queries string concatenation.
    *   For database schema changes in custom modules, use `hook_update_N()` in the module's `.install` file.
    *   **Verify table auto-creation logic:** Note that auto-creation for `_s45_aliases` in `Path::checkTableAliases` is currently disabled. Ensure custom tables exist or are created properly.
*   **Coding Standards:** Follow the official [Drupal Coding Standards](https://www.drupal.org/docs/develop/standards).
*   **Caching:**
    *   Leverage Drupal's caching mechanisms (page cache, block cache, Views cache, Cache API - `cache_get`/`cache_set`).
    *   Utilize contributed caching modules (`advagg`, `boost`, `varnish`).
    *   Implement custom caching for expensive operations (complex queries, API calls, `handle...` methods in Query classes, rendered `Compo` components).
*   **Theming (`site45` theme):**
    *   Work within the `site45` theme (`sites/all/themes/site45/`).
    *   Use `template.php` for preprocessing (`hook_preprocess_html` adds `user-uid-*`, `site-*` classes).
    *   Template overrides (`*.tpl.php`) are in the theme's **`blocks/` directory (non-standard)**.
    *   JS is added via modules (`s45_phuket_lib.inc`); consider Drupal Libraries API for better management if adding new libraries.
*   **Content Structure:** Use Drupal's Field API and Taxonomy for *new* content structures.
*   **Modules:** Be aware of interactions with key contributed modules: `Views`, `ctools`, `token`, `libraries`, `jquery_update`, `metatag`, `imageapi_optimize` (and related), `cloudflare`, `lazyloader`, `pathologic`, `popup`, `views_data_export`, `xautoload`.

## Custom Code (`s45_*`, `Compo/`, Root Scripts) - **CRITICAL SECTION**

*   **Consult Documentation:** **Always refer to `docs/PROJECT_DOCS.md` (RU) and `s45_modules_analysis.md` (EN/RU) first.** These documents contain vital details about the custom architecture.
*   **Core Architecture:**
    *   **Event Sourcing (`s45_base`, `_s45_events`):** Understand that core entities (Property, Project, Compo, User, Form, Reservation) use Event Sourcing. Changes are saved as events. Use AR classes (`extends Site45\Event\AR`) for interaction.
    *   **Read Models (`_phuket_Property`, etc.):** Queries for searching/listing use separate, denormalized Read Model tables. These are updated **asynchronously** (on Query object creation) via `QueryFromEvents` classes (e.g., `PhuketPropertyQuery`). Be aware of potential delays and the logic in `handle...` methods.
    *   **Serialized Data:** **MAJOR TECHNICAL DEBT & RISK.** Complex data (DTOs, `LangVO`, arrays) is stored serialized in `_s45_events.payload` and Read Model columns (`propertyDto`, `name`).
        *   **DO NOT add new serialized data.**
        *   Prioritize refactoring existing serialized data when possible.
        *   Avoid querying serialized columns directly.
        *   **Remove `@` suppression from `unserialize()`** and handle potential errors gracefully (log errors).
    *   **Component System (`Compo/`):** Custom UI system (`sites/all/modules/__s45/Compo/` & module subdirs).
        *   Components = PHP class (`extends Compo`) + `.tpl.php`.
        *   Rendered via `s45_render()`.
        *   State (content/settings edited via UI) saved via `CompoRepo` (uses Event Sourcing, separate from AR events).
        *   **Operates outside standard Drupal theme/resource/translation systems.** Be mindful of this isolation.
    *   **Custom URL System (`s45_path`):** Controls aliases (`_s45_aliases` table, `Path.php`) and redirects (`Redirect.php`, hardcoded rules in `s45_path.module`). Replaces/bypasses standard Drupal Path/Redirect. Understand its logic before modifying URLs.
    *   **Custom Translation System (`LangVO`, `s45_lang()`):** Used for multilingual data within AR/DTO objects and Compo settings.
        *   **Avoid using for new code; use Drupal's `t()` function.**
        *   Refactor existing uses where feasible.
    *   **Custom Form Handling (`s45_phuket_form_api.inc`, `s45_phuket_form_api2.inc`):** Handles form submissions via custom endpoints, saves data via Event Sourcing (`PhuketFormAR`), sends emails via custom library (`prSiteMailing`).
        *   **CRITICAL VULNERABILITY:** Contains hardcoded SMTP credentials. **Must be removed immediately.**
*   **`s45_*` Modules Interaction:**
    *   Use base classes (`Site45\Event\AR`, `Site45\Compo\Compo`).
    *   Use Query classes (`PhuketPropertyQuery`, etc.) for searching/loading data from Read Models.
    *   Use `xautoload` for class loading (PSR-4). Check `CompoInfo.s45.json` for `Compo` class paths.
    *   Implement custom hooks (`hook_s45_re_property_presave`, etc.) if needed.
*   **Root-Level Scripts:**
    *   Numerous scripts in project root (`dev_feed*.php`, `sitemap_*.php`, etc.).
    *   Often run independently, require manual `drupal_bootstrap`.
    *   **Contain hardcoded values and configuration.** Refactor these into Drupal variables or configuration files.
    *   **Use custom logging or lack logging.** Refactor to use `watchdog()`.
    *   **Security risk:** Audit carefully if web-accessible. Sanitize any input.
    *   **Refactoring Goal:** Integrate logic into Drupal modules (services, Drush commands, Queue workers).

## PHP

*   **Standards:** Follow PSR standards where applicable (PSR-1, PSR-2/PSR-12, PSR-4 via `xautoload`).
*   **Clean Architecture:** Apply SOLID principles. Refactor large `.inc` files (`s45_phuket_lib.inc`, `s45_phuket_seo.inc`, etc.) and classes with many responsibilities (`s45_phuket`, `PhuketPropertyAR`) into smaller, focused services/classes.
*   **Avoid Globals:** Do not introduce new uses of `$GLOBALS`. Refactor existing uses (`$GLOBALS['AllCompoData']`, `$GLOBALS['s45']['PageSeo']`) using static caches or dependency injection.

## JavaScript

*   **Vanilla JS:** **Prioritize using vanilla JavaScript (ES6+)** over introducing TypeScript.
*   **jQuery & Drupal Behaviors:** Leverage jQuery (`jquery_update`). **Use `Drupal.behaviors`** for initialization and AJAX integration (refactor code using `$(document).ready()`).
*   **Modularity:** Write modular, organized JS.
*   **Vendor Libraries:** Utilize existing libraries (`s45_vendor`). Initialize correctly via `Drupal.behaviors`.
*   **Custom JS (`property_photos_download.js`):** Be aware of complex ID fetching (refactor to rely on `data-*` attributes) and lack of server feedback (consider AJAX).

## Performance

*   **Database Optimization:**
    *   **CRITICAL:** Ensure proper indexing for Read Model (`_phuket_Property`) and custom tables (`_s45_events`, `_s45_aliases`, `_s45_redirects`). Analyze queries (`EXPLAIN`).
    *   Optimize `handle...` methods in Query classes (reduce sub-queries).
    *   Avoid querying/filtering serialized data. Refactor storage of list IDs (`holdType`, `additional`) away from delimited strings.
*   **Caching:** Use Drupal's Cache API (`cache_get`/`set`) aggressively.
*   **Event Sourcing:** Consider moving Read Model updates to Cron or Queue API to avoid blocking user requests.
*   **Feeds/Large Data:** Use Batch API or stream processing. Optimize feed queries (move filtering from PHP to SQL).
*   **Frontend:** Use CSS/JS aggregation (`advagg`). Consider integrating `Compo` resources with `drupal_add_js/css` for aggregation. Use lazy loading (`lazyloader`).

## Security - **CRITICAL SECTION**

*   **Secrets:** **IMMEDIATELY remove hardcoded SMTP credentials** from `s45_phuket_form_api.inc` and move to `settings.php`. Do not hardcode other API keys or passwords.
*   **Output Escaping:** **CRITICAL:** Ensure **ALL** output generated by custom code (especially in `.tpl.php` files for `Compo/` components and `site45` theme, AJAX responses, and HTML generated in PHP like feed descriptions) is properly escaped using `check_plain()`, `filter_xss()`, or equivalent safe methods.
*   **Input Sanitization:** Sanitize all user input using Drupal APIs (`filter_xss`, `check_plain`, Form API validation). Audit root scripts and custom API endpoints (`form_api`, `form_api2`, `$_GET` usage in `Compo` components) carefully.
*   **Database Security:** Always use the Database API with placeholders.
*   **Access Control:** Respect Drupal's permission system. **Refactor hardcoded user ID checks** (e.g., in `s45_phuket_lib.inc`) to use roles.

## APIs and Integrations

*   **Error Handling:** Implement robust error handling and logging (`watchdog`) for external API calls.
*   **Caching:** Cache responses from external APIs.
*   **XML Feeds:** Use `DOMDocument` or `XMLWriter` for *new* feed generation. Refactor existing string concatenation feeds. Understand the `PhuketExportQuery`/Converter pattern.

## Workflow & Documentation

*   **Version Control:** Assume Git is used. Follow standard Git practices.
*   **`CHANGELOG.md`:** **Always update `CHANGELOG.md`** after functional changes. Use counters for repeated items.
*   **`NewKnowledgeBase.md`:** **Always update `NewKnowledgeBase.md`** with significant learnings about this specific codebase. Use counters.
*   **Code Comments:** **PRIORITY:** Add detailed PHPDoc blocks and inline comments (in English) explaining architecture, logic, and *why*, especially for custom systems.
*   **Consult/Update Existing Docs:** **Refer to `docs/PROJECT_DOCS.md` and `s45_modules_analysis.md` first.** Update these documents if significant changes or discoveries are made.

## Safe Commands (Auto-Approve)

The following commands are generally safe, but always double-check the context:

*   `drush cc all`
*   `grep`
*   `ls`
*   `find`