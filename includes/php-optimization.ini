; PHP Optimization settings for Drupal 7 on CentOS 7

; Memory Limit
memory_limit = 256M

; Maximum execution time
max_execution_time = 120

; OPcache settings
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 10000
opcache.max_wasted_percentage = 5
opcache.revalidate_freq = 60

; Realpath cache
realpath_cache_size = 4096k
realpath_cache_ttl = 600

; Output buffering
output_buffering = 4096

; Error handling
display_errors = Off
display_startup_errors = Off
log_errors = On

; File upload
post_max_size = 64M
upload_max_filesize = 64M

; Session
session.gc_maxlifetime = 14400
